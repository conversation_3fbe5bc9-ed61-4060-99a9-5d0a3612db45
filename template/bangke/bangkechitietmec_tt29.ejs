<style>
	.container-pdf{font-family: 'Times New Roman';width:auto;height: auto;margin:0 auto;font-size:8px}
	.title{font-size:8px; margin-bottom:30px}
	.title-2{font-size:8px; margin-top:30px}
  .title-sub{font-size:8px; margin-top:5px}
	.line{width:120px; height:2px; background:#000; margin:10px auto 30px}
	.mt3{margin-top:3px}
	.pl-20{padding-left:20px}
	.text-center{text-align:center;}
	.text-right{text-align:right;}
	.tbl-detail{border:solid 1px #333;border-collapse:collapse;width:100%; margin-top:20px;font-size:8px;}
	.tbl-detail th,.tbl-detail td{border:solid 1px #333;vertical-align: top;padding:3px;font-weight:bold;font-size:8px;}
	.tbl-sign{border-collapse:collapse;width:100%;margin-top:20px}
	.signal-area{width:33%;text-align:right;vertical-align: top;height:130px;font-size:8px;}
</style>
<div class="container-pdf">
	<p class="text-right"><b>Mẫu số HQ 06a-BKCTHH</b></p>
  <p class="mt3"><b>CÔNG TY CỔ PHẦN CHUYỂN PHÁT NHANH HÀNG HÓA SÀI GÒN</b></p>
	<div class="title-2 text-center"><b>BẢNG KÊ CHI TIẾT HÀNG HÓA</b></div>
	<div class="title-sub text-center"><b>(HÀNG XUẤT KHẨU)</b></div>
	<br/>
	<div class="mt3"><b>Số hiệu phương tiện vận tải hàng hóa:</b></div>
	<div class="mt3"><b>Số hiệu phương tiện chứa hàng(số container,...)(nếu có):</b></div>
	<div class="mt3"><b>Số hiệu niêm phong hải quan:</b></div>
	<div>
		<table class="tbl-detail">
			<thead>
				<tr>
					<th style="width:8%">STT</th>
					<th style="width:11%">Họ tên, địa</br>chỉ người XK</th>
					<th style="width:6%">Số vận</br>đơn</th>
					<th style="width:10%">Tên hàng</th>
					<th style="width:10%">Số lượng</br>kiện gói</th>
					<th style="width:10%">Đơn vị</br>tính</th>
					<th style="width:10%">Số lượng</th>
					<th style="width:10%">Giá trị</br>tính thuế</th>
					<th style="width:10%">Trọng</br>lượng</th>
					<th style="width:10%">Xuất xứ</th>
					<th style="width:10%">Ghi chú</th>
				</tr>
				<tr>
					<th>(1)</th>
					<th>(2)</th>
					<th>(3)</th>
					<th>(4)</th>
					<th>(5)</th>
					<th>(6)</th>
					<th>(7)</th>
					<th>(8)</th>
					<th>(9)</th>
					<th>(10)</th>
					<th>(11)</th>
				</tr>
			</thead>
			<tbody>
				<%
          let totalPrice = 0;
          let i = 1;
          let cache = new Set();
          for(const key in MICs) {
            MICs[key].forEach(function(mic){
            if(mic['exportDetails'].length > 0) {
              mic['exportDetails'].forEach(function(detail){  %>
                <tr>
                  <td><%= (!cache.has(mic['HAWBClearance']))?i:'' %></td>
                  <td>
                    <div style="word-break: break-word;"><%= mic['exporterFullName'];%></div>
                    <div style="word-break: break-word;"><%= mic['addressOfExporter'];%></div>
                  </td>
                  <td><%= mic['HAWBClearance'];%><br/><%= mic['declarationNo'];%></td>
                  <td style="word-break: break-word;"><%= (itemLanguage == 'vi') ? detail['itemNameVN'] : detail['itemName']%></td>
                  <td>1</td>
                  <td><%= detail['unitPriceCurrencyCode']%></td>
                  <td><%= Number(detail['quantity1']).toFixed(0)%></td>
                  <td><%= (detail['unitPriceCurrencyCode'] == 'VND') ? Number(detail['invoiceValue']).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : (Number(detail['invoiceUnitPrice']) * Number(mic['manifest']['valueClearanceVND'])).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") %></td>
                  <td><%= Number(detail['weightKG']).toFixed(2) %>kg</td>
                  <td><%= detail['placeOfOrigin']?detail['placeOfOrigin']:detail['placeOfOriginCode']%></td>
                  <td><%= mic['notes']!=null?mic['notes'].replace(/(.{10})/g, "$1\n"):'' %></td>
                </tr>
              <% 
                if(!cache.has(mic['HAWBClearance'])) {
                  cache.add(mic['HAWBClearance']);
                  i++ 
                }
              });
            }
            totalPrice = totalPrice + Number(mic['priceVND']);
          %>
          <% }) %>
        <% } %>
			</tbody>
			<!--<tfoot>
				<tr>
					<td style="text-align:center" colspan="8">Thuế suất thuế GTGT</td>
					<td style="text-align:center" colspan="4">10%</td>
				</tr>
        <tr>
					<td style="text-align:center" colspan="8">Số tiền thuế GTGT</td>
					<td style="text-align:center" colspan="4"><%= totalPrice > 0 ? (Number(totalPrice) * 0.1).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0 %></td>
				</tr>
			</tfoot>-->
		</table>
	</div>
	<div>
		<table class="tbl-sign">
			<tbody>
				<tr>
					<td class="signal-area">
						<div><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;, Ngày&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tháng&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;năm&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></div>
            </br>
						<div><b>NGƯỜI KHAI HẢI QUAN</b></div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>