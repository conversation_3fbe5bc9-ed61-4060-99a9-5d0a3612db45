<html>
<head>
  <meta charset="utf8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    .grid-print {font-family: 'Times New Roman', Times, serif;font-size:11px;color:#000;}
    .text-center{
        text-align:center
    }
    .product {
        width:100%;
        border-collapse: collapse;
        margin-top:20px;
        font-size: 11px;
        font-family: 'Times New Roman', Times, serif;
    }
    .product thead th {
        border:thin  solid #000 !important;
        background: #eaeaea;
        font-weight:700;
        font-size: 12px;
        color: #000;
        text-align: center;
        padding: 6px 5px;
    }
    .product tbody tr td {
        border: thin  solid #000 !important;
        padding: 6px 5px;
        text-align: center;
    }
    @media print {
      .grid-print {
        margin: 0;
        height: 100%;
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="grid-print">
    <div id="content">
      <div style="margin:10px !important; font-size:16px"><b>CÔNG TY CỔ PHẦN CPNHH SÀI GÒN</b></div>
      <div class="text-center" style="margin:5px !important; font-size:14px"><b><%= title %></b></div>
      <div class="text-center" style="margin:5px !important; font-size:10px"><b><%= date %></b></div>
      <table autosize="1" class="product">
        <thead>
          <tr>
            <th style="width:8%;"><b>STT</b></th>
            <th style="width:15%"><b>Số vận đơn</b></th>
            <th style="width:15%"><b>Số tờ khai</b></th>
            <th style="width:9%"><b>Loại</b></th>
            <th style="width:9%"><b>Số kiện</b></th>
            <th style="width:9%"><b>Trọng <br>lượng</b></th>
            <th style="width:20%"><b>Ra cổng</b></th>
            <th style="width:15%"><b>Code</b></th>
          </tr>
        </thead>
          <tbody>
            <%  
              const totalHawb = manifests.length;
              let totalP = 0;
              let totalW = 0;
              manifests.forEach(function (manifest, index) {
            %>
              <tr>
                  <td><%= index+1 %></td>
                  <td><%= manifest['HAWB'] %></td>
                  <td><%= manifest['declarationNo'] %></td>
                  <td><%= manifest['classify'] %></td>
                  <td><%= manifest['cargoPiece'] %></td>
                  <td><%= Number(manifest['weight']).toFixed(2).replace(".", ",") %></td>
                  <td><%= manifest['dateCheckout'] %></td>
                  <td></td>
              </tr>
              <% 
                  totalP += manifest['cargoPiece'];
                  totalW += Number(manifest['weight']);
                });
              %>
              <tr>
                <td colspan="4">
                  <b>Tổng cộng: <%= totalHawb %> số vận đơn</b>
                </td>
                <td>
                  <b><%= totalP %></b>
                </td>
                <td>
                  <b><%= Number(totalW).toFixed(2).replace(".", ",") %></b>
                </td>
                <td></td>
                <td></td>
            </tr>
          </tbody>
      </table>
    </div>
  </div>
</body>
</html>