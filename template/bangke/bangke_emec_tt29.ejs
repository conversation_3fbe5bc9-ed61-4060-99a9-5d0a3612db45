<html>
<head>
  <meta charset="utf8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    html, body {
      margin: 0;
      padding: 0;
      font-family: 'Times New Roman';
      font-weight: 500;
      font-size: 8px;
      -webkit-print-color-adjust: exact;
      box-sizing: border-box;
    }
    /* Set page margins */
    @page {
      margin: 20mm;
    }
    /* Fixed header and footer (will show on every printed page) */
    .pdf-header, .pdf-footer {
      position: fixed;
      left: 0;
      right: 0;
      background: white;
    }
    .pdf-header {
      top: 0;
      /* Adjust height as needed for your header content */
      height: 70mm;
    }
    .pdf-footer {
      bottom: 0;
      /* Adjust height as needed for your footer content */
      height: 40mm;
    }
    /* Main content area: add top/bottom margins to avoid overlapping header/footer */
    .pdf-content {
      margin: 80mm 10mm 50mm 10mm; /* top, right, bottom, left */
    }
    .grid-print {
      position: relative;
      height: 90mm;
      width: 50mm;
      display: block;
      page-break-after: auto;
      margin: 50px;
    }
    .mb-15{
        margin-bottom:15px
    }
    table{
        width:100%;
        border-collapse: collapse;
        margin-top:14px;
    }
    table{-fs-table-paginate:paginate;border-collapse: collapse}
    .tbl-header tbody tr td{
        font-weight:700;
        font-size:13px;
        vertical-align: top;
    }
    .table{
        border-top:1px solid #000;
        border-collapse: collapse;
        display: table-header-group;
        font-size: 8px;
    }
    .table thead th{
        border:1px solid #000;
        background-color: #eaeaea !important;
        font-weight:700;
        font-size: 8px;
        color: #000;
        text-align: center;
        padding: 6px 3px;
        border-top:0;
    }
    .table tbody tr td {
        border: 1px solid #000;
        padding: 6px 3px;
        text-align: center;
        font-size: 8px;
    }
    .text-right{text-align:right}
    .text-center{text-align:center}
    .text-left{text-align:left }
    @media print {
      .grid-print {
        margin: 0;
        height: 100%;
        width: 100%;
      }
    }
  </style>
</head>
<body>
<div class="grid-print">
  <%
    const allowLength = 199;
    if(MICs) {
  %>
  <table class="tbl-header">
    <tbody>
      <tr>
        <td>
          <table style="width:310px">
            <tr>
              <td class="text-center"><b>CÔNG TY CỔ PHẦN CHUYỂN PHÁT <br> NHANH HÀNG HÓA SÀI GÒN</b></td>
            </tr>
            <tr>
              <td class="text-center">
                  <div style="border-top:1px solid;width:50px; height:10px;margin:10px auto 0"></div>
              </td>
            </tr>
            <tr>
              <td class="text-center" style="font-weight: 400;">Số: ............./BK-SCE</td>
            </tr>
          </table>
        </td>
        <td class="text-right">
          <b>Mẫu số HQ 02-BKTKTGT</b>
        </td>
      </tr>
      <tr>
        <td colspan="2" class="text-center">
          <div style="margin-top:20px"><b>BẢNG KÊ <%= subTitle %></b></div>
          <div><b><%= label %></b></div>
        </td>
      </tr>
    </tbody>
  </table>
    <table class="table" autosize="1">
      <thead>
          <tr>
              <th style="width:4%;"><b>STT</b></th>
              <th style="width:13%"><b>Số tờ khai</b></th>
              <th style="width:14%"><b>Số vận đơn</b></th>
              <th style="width:10px"><b>Tên hàng</b></th>
              <th style="width:6%"><b>Đơn vị tính</b></th>
              <th style="width:6%"><b>Số lượng</b></th>
              <th style="width:5%"><b>Số kiện</b></th>
              <th style="width:8%"><b>Trị giá (VND)</b></th>
              <th style="width:10%"><b>Ghi chú</b></th>
          </tr>
          <tr>
              <th style="width:4%"><b>(1)</b></th>
              <th style="width:13%"><b>(2)</b></th>
              <th style="width:14%"><b>(3)</b></th>
              <th style="width:10px"><b>(4)</b></th>
              <th style="width:6%"><b>(5)</b></th>
              <th style="width:6%"><b>(6)</b></th>
              <th style="width:5%"><b>(7)</b></th>
              <th style="width:8%"><b>(8)</b></th>
              <th style="width:10%"><b>(9)</b></th>
          </tr>
      </thead>
      <tbody>
        <%
          let i = 1;
          let no = 1;
          let totalPrice = 0;
          let totalWeight = 0;
          let totalPcs = 0;
          for(const key in MICs) {
          let totalPriceBox = 0;
          let totalPcsBox = 0;
          let totalWeightBox = 0;
          
          if(MICs[key].length == 0) {
            continue;
          }
          MICs[key].forEach(function(mic){
        %>
          <tr>
            <td><%= i; %></td>
            <td>
              <div><%= mic['declarationNo'] %></div>
            </td>
            <td>
              <%= mic['HAWBClearance'] %>
            </td>
            <td style="text-align:left;">
              <%
                const itemNameVN = [];
                const totalDetail = mic['exportDetails'].length;
                if(totalDetail > 0) {
                  const totalString = Math.round(allowLength / totalDetail);
                  mic['exportDetails'].forEach(function(detail){
                    itemNameVN.push(String(detail['itemNameVN']).substr(0, totalString).trim());
                  });
                }
              %>
              <div style="page-break-after:break-all;"><%= itemNameVN.join(',').replace(/(.{20})/g, "$1\n") %></div>
            </td>
            <td>
              <%= "KGM" %>
            </td>
            <td>
              <%= mic['cargoWeightGross'] > 0 ? Number(mic['cargoWeightGross']).toFixed(2) : 0 %>
            </td>
            <td>
              <%= mic['cargoPiece'] %>
            </td>
            <td class="text-right">
              <%= mic['priceVND'] > 0 ? Number(mic['priceVND']).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0 %>
            </td>
            <td>
              <%= mic['notes']!=null?mic['notes'].replace(/(.{10})/g, "$1\n"):'' %>
            </td>
        </tr>
        <%
          totalPcsBox = totalPcsBox + Number(mic['cargoPiece']);
          totalWeightBox = totalWeightBox + Number(mic['cargoWeightGross']);
          totalPriceBox = totalPriceBox + Number(mic['priceVND']);

          totalPrice = totalPrice + Number(mic['priceVND']);
          totalWeight = totalWeight + Number(mic['cargoWeightGross']);
          totalPcs = totalPcs + Number(mic['cargoPiece']);
          i++;
        %>
        <% }); %>
        <% if(hasFooter == false) { %>
        <tr style="background-color: #e9ecef;">
          <td style="text-align:center;" colspan="5"><b>Tổng cộng:</b></td>
          <td><b><%= totalWeightBox> 0 ? Number(totalWeightBox).toFixed(2).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0 %></b></td>
          <td><b><%= totalPcsBox %></b></td>
          <td class="text-right"><b><%= totalPriceBox > 0 ? String(totalPriceBox).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0 %></b></td>
          <td></td>
        </tr>
        <% } %>
        <% no = no + 1 } %>
        <tr style="background-color: #e9ecef;">
          <td style="text-align:center;" colspan="5"><b>Tổng trị giá tính thuế</b></td>
          <td><b><%= totalWeight ? Number(totalWeight).toFixed(2) : 0 %></b></td>
          <td><b><%= totalPcs %></b></td>
          <td class="text-right"><b><%= totalPrice > 0 ? String(totalPrice).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0 %></b></td>
          <td></td>
        </tr>
        <tr style="background-color: #e9ecef;">
          <td style="text-align:center;" colspan="5"><b>Thuế xuất thuế GTGT (%):</b></td>
          <td style="text-align:center;" colspan="4"><b>10%</b></td>
        </tr>
        <tr style="background-color: #e9ecef;">
          <td style="text-align:center;" colspan="5"><b>Số tiền thuế GTGT (%):</b></td>
          <td style="text-align:center;" colspan="4"><b><%= totalPrice > 0 ? (Number(totalPrice) * 0.1).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0 %></b></td>
        </tr>
      </tbody>
    </table>
    <div style="margin-top:8px; height:50px; font-size: 8px;">Ý kiến của cơ quan hải quan :</div>
    <table>
      <tbody>
        <tr>
          <td style="width:25%" class="text-center">
            <table style="font-size: 8px;">
              <tr>
                <td class="text-center" style="padding:0 0 5px;color:#fff">Text</td>
              </tr>
              <tr>
                <td class="text-center" style="padding:0 0 3px"><b>DOANH NGHIỆP LẬP BẢN KÊ</b></td>
              </tr>
              <tr>
                <td class="text-center" style="height:50px;vertical-align: top;"><i>(Ký, đóng dấu)</i></td>
              </tr>
            </table>
          </td> 
          <!--<td style="width:25%" class="text-center">
            <table style="font-size: 8px;">
              <tr>
                <td class="text-center" style="padding:0 0 5px;color:#fff">Text</td>
              </tr>
              <tr>
                <td class="text-center" style="padding:0 0 3px"><b>Ý kiến của cơ quan hải quan:</b></td>
              </tr>
              <tr>
                <td class="text-center" style="height:50px;vertical-align: top;color:#fff"><i>Text</i></td>
              </tr>
            </table>
          </td> -->
          <td style="width:30%;font-size: 8px;" class="text-center">
            <table style="font-size: 8px;">
              <tr>
                <% let dateObj = new Date(); %>
                <td class="text-center" style="padding:0 0 5px"> ..................., Ngày <%= dateObj.getDate() %> tháng <%= dateObj.getMonth() + 1 %> năm <%= dateObj.getFullYear() %></td>
              </tr>
              <tr>
                <td class="text-center" style="padding:0 0 3px"><b>CQHQ XÁC NHẬN HÀNG ĐỦ ĐIỀU KIỆN QUA KHU VỰC GIÁM SÁT</b></td>
              </tr>
              <tr>
                <td class="text-center" style="height:50px;vertical-align: top;"><i>(Ký, đóng dấu công chức)</i></td>
              </tr>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  <% } else {%>
    <div>Không có dữ liệu</div>
  <%}%>
</div>
</body>
</html>