<style>
	.container-pdf{font-family:'Times New Roman';width:auto;height:auto;margin:0 auto;font-size:8px}
	.title{font-size:8px; margin-bottom:30px}
	.title-2{font-size:8px; margin-top:30px}
	.line{width:120px; height:2px; background:#000; margin:10px auto 30px}
	.mt3{margin-top:3px}
	.pl-20{padding-left:20px}
	.text-center{text-align:center;}
	.text-right{text-align:right;}
	.tbl-detail{border:solid 1px #333;border-collapse:collapse;width:100%; margin-top:20px;font-size:7px;}
	.tbl-detail th,.tbl-detail td{border:solid 1px #333;vertical-align: top;padding:3px;font-weight:bold;font-size:5px;}
	.tbl-sign{border-collapse:collapse;width:100%;margin-top:20px}
	.signal-area{width:33%;text-align:center;vertical-align: top;height:130px;font-size:8px;}
</style>
<div class="container-pdf">
	<div class="title text-center"><b><PERSON><PERSON> lục 2</b></div>
	<% if (companyName) {%>
	<div class="mt3"><b>1. <%= companyName %></b></div>
	<% } else {%>
	<div class="mt3"><b>1. Tên doanh nghiệp: .......................</b></div>
	<% }%>
	<div class="mt3"><b>2. Số: <% if(no) { %><%= no %><% } else { %> ..........................<% } %></b></div>
	<div class="text-right"><b><i>......., ngày.......tháng.........năm.........</i></b></div>
	<div class="title-2 text-center"><b>BẢNG KÊ VẬN ĐƠN/TỜ KHAI XUẤT KHẨU</b></div>
	<div class="line"></div>
	<div class="mt3"><b>3. Kèm theo Tờ khai hải quan vận chuyển số <% if(ola) { %><%= ola %><% } else { %> .................................<% } %> <% if(date) { %> ngày <%= date %><% } else { %>....................................<% } %>, đăng ký tại Chi cục Hải quan <% if(clearanceName) { %><%= clearanceName %>.<% } else { %> ........................................................<% } %></b></div>
	<div class="mt3"><b>4. Số lượng phương tiện vận chuyển hàng hóa thuộc tờ khai.......................................................................................................</b></div>
	<div>
		<table class="tbl-detail">
			<thead>
				<tr>
					<th style="width:1%">STT</th>
					<th style="width:3%">Số vận đơn/Số tờ <br> khai xuất khẩu</th>
					<th style="width:30%">Tên hàng</th>
					<th style="width:5%">Số hiệu Container, số kiện, gói</th>
					<th style="width:5%">Số chì hãng vận chuyển</th>
					<th style="width:5%">Số hiệu niêm phong hải quan</th>
					<th style="width:5%">Nội dung sửa đổi bổ sung</th>
					<th style="width:5%">Xác nhận sửa đổi của công chức</th>
					<th style="width:5%">Số PTVC hàng hóa</th>
				</tr>
				<tr>
					<th>(5)</th>
					<th>(6)</th>
					<th>(7)</th>
					<th>(8)</th>
					<th>(9)</th>
					<th>(10)</th>
					<th>(11)</th>
					<th>(12)</th>
					<th>(13)</th>
				</tr>
			</thead>
			<tbody>
				<%
					const allowLength = 199;
					HAWBs.forEach(function(HAWB, index){
					let productName = [];
					if(isImport) {
						const totalDetail = HAWB['importDetailItems'].length;
						const totalString = Math.round(allowLength / totalDetail);
						HAWB['importDetailItems'].forEach(function(detail){
							if(detail['itemNameVN']){
								productName.push(String(detail['itemNameVN']).substr(0, totalString).trim());
							}
						});
					} else {
						const totalDetail = HAWB['exportDetailItems'].length;
						const totalString = Math.round(allowLength / totalDetail);
						HAWB['exportDetailItems'].forEach(function(detail){
							if(detail['itemNameVN']){
								productName.push(String(detail['itemNameVN']).substr(0, totalString).trim());
							}
						});
					}
				%>
				<tr>
					<td style="text-align:center"><%= ++index %></td>
					<td><%= HAWB['HAWBClearance'] %> </br> <%= HAWB['declarationNo'] %></td>
					<% if(productName.length > 0) {%>
					<td><%= productName.join(',').replace(/(.{20})/g, "$1\n") %></td>
					<%} else {%>
						<td></td>
					<%}%>
					<td><%= HAWB['cargoPiece'] %></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
				</tr>
				<%})%>
			</tbody>
			<tfoot>
				<tr>
					<td>Tổng số</td>
					<td style="text-align:center"><%= total %></td>
					<% if(weight > 0 && boxes > 0) {%>
						<td style="text-align:center" colspan="7">(<%= boxes %> kiện, <%= weight %> Kgs)</td>
					<% } if(weight == 0 && boxes > 0) { %>
						<td style="text-align:center" colspan="7">(<%= boxes %> kiện)</td>
					<% } if(weight > 0 && boxes == 0) { %>
						<td style="text-align:center" colspan="7">(<%= weight %> Kgs)</td>
					<% } %>
				</tr>
			</tfoot>
		</table>
	</div>
	<div>
		<table class="tbl-sign">
			<tbody>
				<tr>
					<td class="signal-area">
						<div><b>14. Đại diện Doanh nghiệp kê khai</b></div>
						<div class="mt3"><b><i>(ký tên, đóng dấu)</i></b></div>
						<div><img src="https://portal.globex.vn/images/signature.jpg" width="100" alt="signature" /></div>
					</td>
					<td class="signal-area">
						<div><b>15. Xác nhận của CCHQ nơi đi <br>Tên chi cục Hải quan nơi đi</b></div>
						<div class="mt3"><b><i>(ký tên, đóng dấu công chức)</i></b></div>
					</td>
					<td class="signal-area">
						<div><b>16. Xác nhận của CCHQ nơi đến <br>Tên chi cục Hải quan nơi đến</b></div>
						<div class="mt3"><b><i>(ký, đóng dấu xác nhận)</i></b></div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	
	<div class="note">
		<div><b>Ghi chú:</b></div>
		<div class="mt3"><b>1. Phần doanh nghiệp khai:</b></div>
		<div class="mt3 pl-20">(1): Tên của doanh nghiệp vận chuyển.</div>
		<div class="mt3 pl-20">(2): Số của Bảng kê vận đơn/Tờ khai xuất khẩu tối đa 35 ký tự do doanh nghiệp lập để quản lý.</div>
		<div class="mt3 pl-20">(3): Số, ngày tờ khai vận chuyển độc lập và tên Chi cục Hải quan mà Bảng kê hàng hóa cần khai báo.</div>
	</div>
</div>