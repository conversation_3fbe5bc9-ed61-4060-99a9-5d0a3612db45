<html>
<head>
  <meta charset="utf8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    html, body {
      margin: 0;
      padding: 0;
      font-family: 'Times New Roman';
      font-weight: 500;
      font-size: 8px;
      -webkit-print-color-adjust: exact;
      box-sizing: border-box;
    }

    .grid-print {
      position: relative;
      height: 90mm;
      width: 50mm;
      display: block;
      page-break-after: auto;
      margin: 50px;
    }

    .table{width:100%;border-collapse: collapse;font-size:8px;}
    .tbl-header{font-family: 'Arial';font-size:8px;}
    .tbl-header .td1{width:40%;text-align:center}
    .tbl-header .td2{width:37%;}
    .tbl-header .td3{width:23%;text-align:right}
    .title{text-align:center; margin-top:30px;}

    .tbl-product{margin-top:20px;font-size:6px}
    .tbl-product thead th{border:1px solid #000;padding:2px}
    .tbl-product tbody td{border:1px solid #000;padding:2px;}

    .note{height:60px; margin-top:5px}
    .tbl-footer td{width:50%;text-align:center}
  @media print {
    .grid-print {
      margin: 0;
      height: 100%;
      width: 100%;
    }
  }
  </style>
</head>
<body>
  <div class="grid-print">
    <table class="table tbl-header">
      <tbody>
          <tr>
              <td class="td1">
                  <div><b>CÔNG TY CỔ PHẦN CHUYỂN PHÁT NHANH HÀNG HÓA SÀI GÒN</b></div>
                  <div><b>-------</b></div>
                  <div>Số: ……………../</div>
              </td>
              <td class="td2"></td>
              <td class="td3">
                  <b>Mẫu số HQ 05-BLKHH</b>
              </td>
          </tr>
      </tbody>
    </table>
    <div class="title"><b>BẢN LƯỢC KHAI HÀNG HÓA</b></div>
    <table class="table tbl-product">
      <thead>
          <tr>
              <th style="width:2%">S<br>T<br>T</th>
              <th style="width:10%">Số vận đơn</th>
              <th colspan="2" style="width:26%">Họ tên, địa chỉ, số CMND (nếu có)</th>
              <th style="width:20%">Tên hàng</th>
              <th style="width:8%">Mã số hàng hóa (nếu có)</th>
              <th style="width:4%">Xuất xứ</th>
              <th style="width:4%">Số kiện</th>
              <th style="width:7%">Trị giá nguyên tệ</th>
              <th style="width:6%">Đơn giá nguyên tệ</th>
              <th style="width:7%">Trị giá (VND)</th>
              <th style="width:8%">Ghi chú</th>
          </tr>
          <tr>
            <th style="width:2%"></th>
            <th style="width:10%"></th>
            <th style="width:13">Người gửi</th>
            <th style="width:13%">Người nhận</th>
            <th style="width:20%"></th>
            <th style="width:8%"></th>
            <th style="width:4%"></th>
            <th style="width:4%"></th>
            <th style="width:7%"></th>
            <th style="width:6%"></th>
            <th style="width:7%"></th>
            <th style="width:8%"></th>
        </tr>
      </thead>
      <tbody>
        <%
          let i = 1;
          let cache = new Set();
          for(const key in MICs) {
          MICs[key].forEach(function(mic){
          if(mic['importDetails'].length > 0) {
            mic['importDetails'].forEach(function(detail){  %>
              <tr>
                <td><%= (!cache.has(mic['HAWBClearance']))?i:'' %></td>
                <td><%= mic['HAWBClearance'];%></td>
                <td>
                  <div style="word-break: break-word;"><%= mic['consignorName'];%></div>
                  <div style="word-break: break-word;"><%= mic['address1'];%></div>
                </td>
                <td>
                  <div style="word-break: break-word;"><%= mic['importerFullName'];%></div>
                  <div style="word-break: break-word;"><%= mic['addressOfImporter'];%></div>
                </td>
                <td style="word-break: break-word;"><%= (itemLanguage == 'vi') ? detail['itemNameVN'] : detail['itemName']%></td>
                <td><%= detail['HSCode']%></td>
                <td><%= detail['placeOfOrigin']?detail['placeOfOrigin']:detail['placeOfOriginCode']%></td>
                <!--<td><%= Number(detail['quantity1']).toFixed(0)%></td>-->
                <td>1</td>
                <td><%= Number(detail['invoiceValue']).toFixed(0)%></td>
                <td><%= detail['unitPriceCurrencyCode']%></td>
                <td><%= (detail['unitPriceCurrencyCode'] == 'VND') ? Number(detail['invoiceValue']).toFixed(0) : (Number(detail['invoiceUnitPrice']) * Number(mic['manifest']['valueClearanceVND'])).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") %></td>
                <td></td>
              </tr>
            
            <% 
              if(!cache.has(mic['HAWBClearance'])) {
                cache.add(mic['HAWBClearance']);
                i++ 
              }
              
            });
          }
        %>
        <% }) %>
        <% } %>
      </tbody>
    </table>
    <div class="note"><b>Xác nhận kết quả kiểm tra:</b></div>
        <table class="table tbl-footer">
            <tbody>
                <tr>
                    <td><i>………. ngày .... tháng .... năm 20....</i></td>
                    <td><i>………. ngày .... tháng .... năm 20....</i></td>
                </tr>
                <tr>
                    <td><b>CÔNG CHỨC HẢI QUAN</b></td>
                    <td><b>CÔNG TY CHUYỂN PHÁT NHANH</b></td>
                </tr>
                <tr>
                    <td><i>(Ký, đóng dấu công chức)</i></td>
                    <td><i>(ký, ghi rõ họ tên, đóng dấu)</i></td>
                </tr>
            </tbody>
        </table>
  </div>
</body>
</html>