<style>
	.container-pdf{font-family: 'Times New Roman'; width: auto; height: auto; margin:0 auto; font-size:8px}
	.title{width:100%; font-size:8px; margin-bottom:30px}
	.title-2{font-size:8px; margin-top:30px}
	.line{width:120px; height:2px; background:#000; margin:10px auto 30px}
	.mt3{margin-top:3px; font-size:10px;}
	.pl-20{padding-left:20px}
	.text-center{text-align:center;}
	.text-right{text-align:right;}
	.tbl-detail{border:solid 1px #333;border-collapse:collapse;width:100%; margin-top:20px;font-size:8px;}
	.tbl-detail th,.tbl-detail td{border:solid 1px #333;vertical-align: top;padding:3px;font-weight:bold; font-size:8px}
	.blank{width:calc(100% - 255px);display: inline-block;}
	.signature{width:250px;margin-top:10px;height:100px;display: inline-block;}
</style>
<div class="container-pdf">
	<div class="title text-center"><b><PERSON><PERSON> lục 1</b></div>
	<% if (companyName) {%>
	<div class="mt3"><b>1. <%= companyName %></b></div>
	<% } else {%>
	<div class="mt3"><b>1. Tên doanh nghiệp: .......................</b></div>
	<% }%>
	<div class="mt3"><b>2. Số: <% if(no) { %><%= no %><% } else { %> ..........................<% } %></b></div>
	<div class="text-right"><b><i>......., ngày.......tháng.........năm.........</i></b></div>
	<div class="title-2 text-center"><b>BẢNG KÊ VẬN ĐƠN/TỜ KHAI XUẤT KHẨU</b></div>
	<div class="line"></div>
	<div><b>3. Kèm theo Tờ khai hải quan vận chuyển số <% if(ola) { %><%= ola %><% } else { %> ............................<% } %> <% if(date) { %>ngày <%= date %><% } else { %>....................................<% } %>, đăng ký tại Chi cục Hải quan <% if(clearanceName) { %><%= clearanceName %>.<% } else { %> ..................................................<% } %></b></div>
	<div>
		<table class="tbl-detail">
			<thead>
				<tr>
					<th style="width:5%">STT <br>(4)</th>
					<th style="width:10%">Số vận đơn/Số tờ khai xuất khẩu<br>(5)</th>
					<th style="width:10%">Tên hàng<br>(6)</th>
					<th style="width:10%">Nội dung sửa đổi bổ sung<br> Vận đơn hoặc tờ khai <br>(7)</th>
					<th style="width:10%">Xác nhận sửa đổi<br> của công chức <br> (8)</th>
				</tr>
			</thead>
			<tbody>
				<%
					const allowLength = 199;
					HAWBs.forEach(function(HAWB, index){
					let productName = [];
					if(isImport) {
						const totalDetail = HAWB['importDetailItems'].length;
						const totalString = Math.round(allowLength / totalDetail);
						HAWB['importDetailItems'].forEach(function(detail){
							if(detail['itemNameVN']){
								productName.push(String(detail['itemNameVN']).substr(0, totalString).trim());
							}
						});
					} else {
						const totalDetail = HAWB['exportDetailItems'].length;
						const totalString = Math.round(allowLength / totalDetail);
						HAWB['exportDetailItems'].forEach(function(detail){
							if(detail['itemNameVN']){
								productName.push(String(detail['itemNameVN']).substr(0, totalString).trim());
							}
						});
					}
				%>
				<tr>
					<td style="text-align:center"><%= ++index %></td>
					<td><%= HAWB['HAWBClearance'] %></td>
					<% if(productName.length > 0) {%>
					<td><%= productName.join(',').replace(/(.{20})/g, "$1\n") %></td>
					<%} else {%>
						<td></td>
					<%}%>
					<td></td>
					<td></td>
				</tr>
				<%})%>
			</tbody>
			<tfoot>
				<tr>
					<td>Tổng số</td>
					<td style="text-align:center"><%= total %></td>
					<% if(weight > 0 && boxes > 0) {%>
					<td style="text-align:center" colspan="3">(<%= boxes %> kiện, <%= weight %> Kgs)</td>
					<% } if(weight == 0 && boxes > 0) { %>
						<td style="text-align:center" colspan="3">(<%= boxes %> kiện)</td>	
					<% } if(weight > 0 && boxes == 0) { %>
						<td style="text-align:center" colspan="3">(<%= weight %> Kgs)</td>
					<% } %>
				</tr>
			</tfoot>
		</table>
	</div>
	<div>
		<div class="blank"></div>
		<div class="signature text-center">
			<div><b>8. Doanh nghiệp kê khai</b></div>
			<div>(ký tên, đóng dấu)</div>
			<div><img src="https://portal.globex.vn/images/signature.jpg" width="100" alt="signature" /></div>
		</div>
	</div>
	<div class="note">
		<div><b>Ghi chú:</b></div>
		<div class="mt3 pl-20">(1): Tên của doanh nghiệp vận chuyển.</div>
		<div class="mt3 pl-20">(2): Số của Bảng kê vận đơn/Tờ khai xuất khẩu tối đa 35 ký tự do doanh nghiệp lập để quản lý.</div>
		<div class="mt3 pl-20">(3): Số, ngày tờ khai vận chuyển độc lập và tên Chi cục Hải quan mà Bảng kê vận đơn/Tờ khai xuất khẩu khai cần khai báo.</div>
		<div class="mt3 pl-20">(4): Ghi số thứ tự của từng vận đơn hoặc tờ khai xuất khẩu cần khai báo trên tờ khai vận chuyển độc lập.</div>
		<div class="mt3 pl-20">(5): Số hiệu của từng vận đơn hoặc tờ khai xuất khẩu.</div>
		<div class="mt3 pl-20">(6): Tên hàng.</div>
		<div class="mt3 pl-20">(7): Khai sửa đổi, bổ sung số vận đơn/Tờ khai xuất khẩu đã khai ở mục (5) khi phát hiện sai sót trong quá trình kiểm tra hồ sơ.</div>
		<div class="mt3 pl-20">(8): Dành cho cơ quan Hải quan xác nhận khi chấp thuận nội dung sửa đổi, bổ sung. Công chức hải quan ký tên, đóng dấu tương ứng tại từng nội dung sửa đổi.</div>
	</div>
</div>