build_k8:
  stage: build
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  script:
    # - mkdir -p /kaniko/.docker
    # - echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(printf "%s:%s" "${CI_REGISTRY_USER}" "${CI_REGISTRY_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfilee"
      --destination "${DOCKER_IMGNAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}"
      --destination "${DOCKER_IMGNAME}-${CI_COMMIT_BRANCH}:latest"
  environment:
    name: ${CI_COMMIT_BRANCH}
  tags:
    - tms
    - ${CI_COMMIT_BRANCH}
  only:
    - development
    - master

deploy_operation:
  image: registry.globex.vn/kubectl:latest
  stage: deploy
  script:
    # - env
    # - kubectl get pods
    - kubectl set image deployments/"${K8S_DEPLOYMENT}" "${K8S_CONTAINERNAME}"="${DOCKER_IMGNAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}"
    - kubectl rollout status deployment ${K8S_DEPLOYMENT}
  environment:
    name: ${CI_COMMIT_BRANCH}/ops
  tags:
    - tms
    - ${CI_COMMIT_BRANCH}
  only:
    - development
    - master

deploy_reporting:
  image: registry.globex.vn/kubectl:latest
  stage: deploy
  script:
    # - env
    # - kubectl get pods
    - kubectl set image deployments/"${K8S_DEPLOYMENT_REPORTING}" "${K8S_CONTAINERNAME}"="${DOCKER_IMGNAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}"
    - kubectl rollout status deployment ${K8S_DEPLOYMENT_REPORTING}
  environment:
    name: ${CI_COMMIT_BRANCH}/report
  tags:
    - tms
    - ${CI_COMMIT_BRANCH}
  only:
    - master
    - development

deploy_cron:
  image: registry.globex.vn/kubectl:latest
  stage: deploy
  script:
    # - env
    # - kubectl get pods
    # - kubectl set image deployments/"${K8S_DEPLOYMENT_CRON}" "ommapi"="${DOCKER_IMGNAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}"
    - kubectl set image deployments/"${K8S_DEPLOYMENT_CRON_CLEARANCE}" "${K8S_CONTAINERNAME}"="${DOCKER_IMGNAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}"
    - kubectl rollout status deployment ${K8S_DEPLOYMENT_CRON}
  environment:
    name: ${CI_COMMIT_BRANCH}/cron
  tags:
    - tms
    - ${CI_COMMIT_BRANCH}
  only:
    - master

stages:
  - build
  - deploy
