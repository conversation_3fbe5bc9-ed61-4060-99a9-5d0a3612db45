{"name": "clearance-api", "version": "1.0.0", "main": "server.js", "author": "SBP", "license": "MIT", "scripts": {"dev": "tsc-watch --onSuccess \"node ./build/server.js\"", "start": "ts-node ./build/server.js", "watch": "tsc -w -p .", "debug": "nodemon --watch ./build --inspect=0.0.0.0:9233 --nolazy ./build/server.js", "build": "tsc -p .", "alter-file": "npx sequelize-cli migration:create --name", "alter-db": "npx sequelize-cli db:migrate", "sync-db": "npx sequelize-cli db:migrate --env production"}, "devDependencies": {"@hapi/joi": "^17.1.0", "@types/amqplib": "^0.5.13", "@types/compression": "^1.0.1", "@types/cors": "^2.8.6", "@types/cron": "^1.7.2", "@types/ejs": "^3.1.0", "@types/exceljs": "^1.3.0", "@types/express": "^4.17.2", "@types/ftp": "^0.3.31", "@types/ftps": "^1.1.0", "@types/html-pdf": "^3.0.0", "@types/joi": "^14.3.4", "@types/lodash": "^4.14.159", "@types/minio": "^7.0.11", "@types/mongoose": "^5.7.36", "@types/multer": "^1.4.3", "@types/node-telegram-bot-api": "^0.50.4", "@types/redis": "^2.8.16", "@types/validator": "^13.0.0", "eslint": "^6.8.0", "ts-node": "3.3.0", "tsc-watch": "^4.1.0", "tsconfig-paths": "^3.9.0", "typescript": "3.6.4"}, "dependencies": {"@types/bcryptjs": "^2.4.2", "@types/bluebird": "^3.5.29", "@types/hapi__joi": "^16.0.9", "@types/jsonwebtoken": "^8.3.7", "@types/pg": "^7.14.1", "amqplib": "^0.6.0", "axios": "^0.19.2", "basic-ftp": "^4.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cors": "^2.8.5", "cron": "^1.8.2", "dotenv": "^8.2.0", "ejs": "^3.1.6", "exceljs": "^4.3.0", "express": "^4.17.1", "express-winston": "^4.0.5", "ftp": "^0.3.10", "html-pdf": "^3.0.1", "http-status": "^1.4.2", "jsonwebtoken": "^8.5.1", "minio": "^7.0.25", "moment": "^2.27.0", "mongoose": "^5.9.28", "multer": "^1.4.2", "node-telegram-bot-api": "^0.51.0", "nodemon": "^2.0.4", "pg": "^8.5.1", "pg-hstore": "^2.3.3", "redis": "^3.0.2", "reflect-metadata": "^0.1.10", "sequelize": "^6.5.0", "winston": "^3.3.3", "winston-mongodb": "^5.0.5", "xml-js": "^1.6.11"}}