version: '3'

services:
  db:
    image: postgres
    container_name: tms-db
    volumes:
      - $HOME/Public/data/postgresql/data:/var/lib/postgresql/data
      #- /Users/<USER>/Documents/database/postgres/data:/var/lib/postgresql/data
    restart: always  
    environment:
      POSTGRES_USER: api
      POSTGRES_PASSWORD: api
      POSTGRES_DB: api
    expose:
      - "5432"
    ports:
      - "5432:5432"
  redis:
    image: "redis:alpine"
    container_name: redis_authen
    command: redis-server --requirepass abc@123
    ports:
      - "6379:6379"
    volumes:
      - $HOME/Public/data/redis:/data
    restart: always
  api:
    image: tms-api
    container_name: tms-api
    ports:
    - "3001:3001"
    - "9229:9229"
    volumes:
      - .:/src
      - /src/node_modules
    links:
    - "db:db"
    - "redis:redis"
    restart: always