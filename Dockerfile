FROM registry.globex.vn/node:12.14-alpine

### TIMEZONE
ENV TIME_ZONE=Asia/Ho_Chi_Minh
### ENVIROMENT
ENV NODE_ENV=local
ENV SERVER_PORT=3003
### DATABASE CONFIGURE
##### LOCAL
ENV DB_HOST=**********
ENV DB_USERNAME=nd5mJ65R
ENV DB_PASSWORD=dMurLt93FQ2VKxxa
ENV DB_NAME=ommapi
### SECRET KEY
ENV SECRET_HR_AUTHEN_KEY=hR5Bpg#s3712eT@2oS0
ENV SECRET_AUTHEN_KEY=5Bpg#s12c@2oS0@7MS-o1125
ENV INTERNAL_AUTHEN_KEY=iN73rn4l!5Bpg#s12c@2oS0@7MS-o1125
ENV DOMAIN=clearance
### RABBITMQ
ENV RABBIT_HOST=**********
ENV RABBIT_PORT=5672
ENV RABBIT_USERNAME=globex
ENV RABBIT_PASSWORD=L9kFkmBhWXJEaSaB
ENV RABBIT_VHOST='/globex_omm'
### MONGO
ENV MONGO_HOST=mongodb
ENV MONGO_PORT=27017
ENV MONGO_USERNAME=admin
ENV MONGO_PASSWORD=Abc123
ENV MONGO_NAME=admin
ENV MONGODB_COLLECTION=clearancelogger
### FTP
ENV FTP_HOST=**************
ENV FTP_USERNAME=tms
ENV FTP_PASSWORD=WmAVSHLhjARe63Zs
### TELEGRAM LOG ERROR
ENV TELEGRAM_ERROR_TOKEN=**********************************************
ENV TELEGRAM_ERROR_CHAT_ID=-392692439
### ENV FOR GATEWAY
ENV DOMAIN_GATEWAY=gateway

### CREATE DIRECTORY FOR THE CONTAINER
WORKDIR /src

# RUN touch .env
RUN yarn add ts-node -g
RUN yarn add sequelize-cli -g
COPY package*.json /src/
COPY yarn.lock /src/

# INSTALL ALL PACKAGES
RUN yarn install

# Copy all other source code to work directory
COPY . /src/

ADD localtime /etc/localtime

# Start
CMD [ "yarn", "dev" ]

#EXpose port
EXPOSE 3003 9233
