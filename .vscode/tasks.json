{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "label": "tsc-watch",
      "command": "npm",
      "args": ["run", "watch"],
      "type": "shell",
      "isBackground": true,
      "group": "build",
      "problemMatcher": "$tsc-watch",
      "presentation": {
        "reveal": "always"
      }
    },
    {
      "type": "docker-build",
      "label": "docker-build",
      "platform": "node",
      "dockerBuild": {
        "dockerfile": "${workspaceFolder}/Dockerfile",
        "context": "${workspaceFolder}",
        "pull": true
      }
    },
    {
      "type": "docker-run",
      "label": "docker-run: debug",
      "dependsOn": ["docker-build"],
      "dockerRun": {
        "env": {
          "DEBUG": "*",
          "NODE_ENV": "development"
        }
      },
      "node": {
        "enableDebugging": true
      }
    },
    {
      "label": "build_js",
      "type": "typescript",
      "tsconfig": "tsconfig.json",
      "problemMatcher": ["$tsc"],
      "group": {
        "kind": "build",
        "isDefault": true
      }
    }
  ]
}
