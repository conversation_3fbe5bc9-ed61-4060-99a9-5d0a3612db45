{"version": "0.2.0", "configurations": [{"type": "node", "request": "attach", "name": "Attach to Docker", "preLaunchTask": "tsc-watch", "protocol": "auto", "port": 9233, "restart": true, "localRoot": "${workspaceFolder}/build", "remoteRoot": "/src/build", "outFiles": ["${workspaceFolder}/build/**/*.js"], "skipFiles": ["<node_internals>/**/*.js"]}, {"name": "Docker Node.js Launch", "type": "docker", "request": "launch", "preLaunchTask": "docker-run: debug", "node": {"localRoot": "${workspaceFolder}/build", "remoteRoot": "/src/build", "sourceMaps": true, "outFiles": ["${workspaceFolder}/build/**/*.js"], "skipFiles": ["<node_internals>/**/*.js"]}}, {"type": "node", "request": "launch", "name": "Launch Project", "runtimeVersion": "10.16.3", "preLaunchTask": "build_js", "program": "${workspaceFolder}/server.ts", "outFiles": ["${workspaceFolder}/build/*.js"]}]}