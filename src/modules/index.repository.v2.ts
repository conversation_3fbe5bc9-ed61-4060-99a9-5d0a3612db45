'use strict'

import Optional from '../parser/optional';
import EMessage from '../emuns/messages';
import { ModelCtor, Op } from 'sequelize';
import ECofigure from "../emuns/configures";
import moment from 'moment';
import EConfigure from '../emuns/configures';
import GroupWhere from '../parser/groupWhere';
import { Database } from '../database';
import { BaseModel } from '../models/base.model';

class BaseRepository<T extends BaseModel> {
  protected model: ModelCtor<T>;
  private sequelize: any;
  constructor(model: any) {
    this.sequelize = Database.Sequelize;
    this.model = model;
  }

  public async getOne(id: number): Promise<T | null> {
    if (id >= 0 && Number(id)) {
      return await this.model.findOne({
        where: {
          id
        },
      });
    } else {
      throw new Error(EMessage.PARAMETER_WRONG_TYPE);
    }
  }

  public async getOneObj(obj: any): Promise<T | null> {
    try {
      return await this.model.findOne({
        where: obj
      });
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async getOneOptional(optional: Optional) {
    try {
      const objQuery: any = {};
      if (optional.getAttributes().length > 0) {
        objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
      }
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }
      if (optional.getGroupWhere()) {
        const groupWhere: GroupWhere = optional.getGroupWhere();
        const objWhere: any = this.handleGroupWhere(groupWhere);
        objQuery[ECofigure.WHERE] = objWhere;
      }
      if (optional.getRelation().length > 0) {
        this.model = this.model.scope(optional.getRelation());
      }

      if (optional.getOrderby().length > 0) {
        const orderby: Array<any> = [];
        optional.getOrderby().map(element => {
          const key: string = element.getKey();
          const value: string = element.getValue();
          return orderby.push([key, value])
        });
        objQuery[ECofigure.ORDER_BY] = orderby;
      }

      if (optional.getGroup().length > 0) {
        objQuery[ECofigure.GROUP] = optional.getGroup();
        objQuery[EConfigure.RAW] = true;
      }
      const objData = await this.model.findOne(objQuery);

      if (optional.getRelation().length > 0) {
        this.model = this.model.scope(undefined);
      }
      return objData;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async updateDeleteOne(id: number) {
    if (id >= 0 && Number(id)) {
      const data = {
        'isDeleted': true,
        'deletedAt': moment().format(EConfigure.FULL_TIME)
      }
      const objData = await this.model.update(data, {
        where: {
          id
        },
      });
      return objData;
    } else {
      throw new Error(EMessage.PARAMETER_WRONG_TYPE);
    }
  }

  public async updateDelete(optional: Optional) {
    try {
      const data = {
        'isDeleted': true,
        'deletedAt': moment().format(EConfigure.FULL_TIME)
      }
      const objQuery: any = {};
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }
      const objUpdated = await this.model.update(data, objQuery);
      return objUpdated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async updateDeleteTrx(optional: Optional, trx: any) {
    try {
      const data = {
        'isDeleted': true,
        'deletedAt': moment().format(EConfigure.FULL_TIME)
      }
      const objQuery: any = {};
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }
      objQuery[ECofigure.TRANSACTION] = trx;
      const objUpdated = await this.model.update(data, objQuery);
      return objUpdated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async destroyOne(id: number) {
    if (id >= 0 && Number(id)) {
      const objData = await this.model.destroy({
        where: {
          id
        },
      });
      return objData;
    } else {
      throw new Error(EMessage.PARAMETER_WRONG_TYPE);
    }
  }

  public async destroyData(optional: Optional) {
    try {
      const objQuery: any = {};
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }
      const objDeleted = await this.model.destroy(objQuery);
      return objDeleted;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async destroyDataObj(objQuery: any) {
    try {
      const objDeleted = await this.model.destroy({
        where: objQuery,
      });
      return objDeleted;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async destroyDataTrx(optional: Optional, trx: any) {
    try {
      const objQuery: any = {};
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }
      objQuery[ECofigure.TRANSACTION] = trx;
      const objDeleted = await this.model.destroy(objQuery);
      return objDeleted;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async updateData(data: any, optional: Optional): Promise<any> {
    try {
      const objQuery: any = {};
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }
      const objUpdated = await this.model.update(data, objQuery);
      return objUpdated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async updateDataTrx(data: any, optional: Optional, trx: any) {
    try {
      const objQuery: any = {};
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }

      if (optional.getGroupWhere()) {
        const groupWhere: GroupWhere = optional.getGroupWhere();
        const objWhere: any = this.handleGroupWhere(groupWhere);
        objQuery[ECofigure.WHERE] = objWhere;
      }

      objQuery[ECofigure.TRANSACTION] = trx;
      const objUpdated = await this.model.update(data, objQuery);
      return objUpdated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async countData(optional: Optional): Promise<any> {
    try {
      const objQuery: any = {};
      if (optional.getAttributes().length > 0) {
        objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
      }
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }
      if (optional.getGroupWhere()) {
        const groupWhere: GroupWhere = optional.getGroupWhere();
        const objWhere: any = this.handleGroupWhere(groupWhere);
        objQuery[ECofigure.WHERE] = objWhere;
      }
      this.model = this.model.scope(undefined);
      return await this.model.count(objQuery);
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async countDataTrx(optional: Optional, trx: any) {
    try {
      const objQuery: any = {};
      if (optional.getAttributes().length > 0) {
        objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
      }
      if (optional.getWhere()) {
        const objWhere = this.handleWhere(optional.getWhere());
        objQuery[ECofigure.WHERE] = objWhere;
      }
      objQuery[ECofigure.TRANSACTION] = trx;
      const amount = await this.model.count(objQuery);
      return amount;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async createData(data: any) {
    try {
      const objCreated = await this.model.create(data);
      return objCreated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async createBulk(data: Array<any>) {
    try {
      const objCreated = await this.model.bulkCreate(data);
      return objCreated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async createBulkTrx(data: Array<any>, trx: any) {
    try {
      const objCreated = await this.model.bulkCreate(data, { transaction: trx });
      return objCreated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async createDataTrx(data: any, trx: any) {
    try {
      const objCreated = await this.model.create(data, { transaction: trx });
      return objCreated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async getOneById(id: number, fields: string[], optional: Optional): Promise<any> {
    if (id >= 0 && Number(id)) {
      const query: any = {};
      if (fields.length > 0) {
        query['attributes'] = fields
      }
      query['where'] = {
        id
      }
      const objData = await this.model.scope(optional.getRelation()).findOne(
        query
        // include: { all: true, nested: true }
      );
      // const objData = objModel.findAll({ include: { all: true, nested: true }});
      return objData;
    } else {
      throw new Error(EMessage.PARAMETER_WRONG_TYPE);
    }
  }

  public async getLimit(optional: Optional): Promise<any> {
    const objQuery: any = {};
    if (optional.getAttributes().length > 0) {
      objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
    }
    if (optional.getWhere().length > 0) {
      const objWhere = this.handleWhere(optional.getWhere());
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getGroupWhere()) {
      const groupWhere: GroupWhere = optional.getGroupWhere();
      const objWhere: any = this.handleGroupWhere(groupWhere);
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getAttributes().length > 0) {
      objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
    }

    if (optional.getLimit() > 0) {
      objQuery[ECofigure.LIMIT] = optional.getLimit();
    }

    if (optional.getOffset() > 0) {
      const skip: number = (optional.getOffset() === 1) ? 0 : (optional.getOffset() - 1) * optional.getLimit();
      objQuery[ECofigure.OFFSET] = skip;
    }

    if (optional.getOrderby().length > 0) {
      const orderby: Array<any> = [];
      optional.getOrderby().map(element => {
        const key: string = element.getKey();
        const value: string = element.getValue();
        return orderby.push([key, value])
      });
      objQuery[ECofigure.ORDER_BY] = orderby;
    }

    if (optional.getGroup().length > 0) {
      objQuery[ECofigure.GROUP] = optional.getGroup();
      objQuery[EConfigure.RAW] = true;
    }

    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(optional.getRelation());
    }

    const data = await this.model.findAll(objQuery);

    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(undefined);
    }
    return data;
  }

  public async getAll(optional: Optional): Promise<[T[], any]> {
    const objQuery: any = {};
    if (optional.getWhere().length > 0) {
      const objWhere = this.handleWhere(optional.getWhere());
      objQuery[ECofigure.WHERE] = objWhere;
    }
    if (optional.getGroupWhere()) {
      const groupWhere: GroupWhere = optional.getGroupWhere();
      const objWhere: any = this.handleGroupWhere(groupWhere);
      objQuery[ECofigure.WHERE] = objWhere;
    }
    const total = this.model.count(objQuery);
    if (optional.getLimit() > 0) {
      objQuery[ECofigure.LIMIT] = optional.getLimit();
    }
    if (optional.getOffset() > 0) {
      const skip: number = (optional.getOffset() === 1) ? 0 : (optional.getOffset() - 1) * optional.getLimit();
      objQuery[ECofigure.OFFSET] = skip;
    }
    if (optional.getOrderby().length > 0) {
      const orderby: Array<any> = [];
      optional.getOrderby().map(element => {
        const key: string = element.getKey();
        const value: string = element.getValue();
        return orderby.push([key, value])
      });
      objQuery[ECofigure.ORDER_BY] = orderby;
    }
    if (optional.getGroup().length > 0) {
      objQuery[ECofigure.GROUP] = optional.getGroup();
      objQuery[EConfigure.RAW] = true;
    }
    if (optional.getAttributes().length > 0) {
      objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
    }
    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(optional.getRelation());
    }
    const data = this.model.findAll(objQuery);
    const [result, counter] = await Promise.all([data, total]);
    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(undefined);
    }
    return [result, counter];
  }



  public async queryAll(optional: Optional): Promise<T[]> {
    const objQuery: any = {};
    if (optional.getWhere().length > 0) {
      const objWhere = this.handleWhere(optional.getWhere());
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getGroupWhere()) {
      const groupWhere: GroupWhere = optional.getGroupWhere();
      const objWhere: any = this.handleGroupWhere(groupWhere);
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getAttributes().length > 0) {
      objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
    }

    if (optional.getOrderby().length > 0) {
      const orderby: Array<any> = [];
      optional.getOrderby().map(element => {
        const key: string = element.getKey();
        const value: string = element.getValue();
        return orderby.push([key, value])
      });
      objQuery[ECofigure.ORDER_BY] = orderby;
    }

    if (optional.getGroup().length > 0) {
      objQuery[ECofigure.GROUP] = optional.getGroup();
      objQuery[EConfigure.RAW] = true;
    }

    if (optional.getQueryRaw()) {
      objQuery[EConfigure.RAW] = true;
    }

    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(optional.getRelation());
    }

    // if(optional.getLimit() > 0) {
    //   objQuery[ECofigure.LIMIT] = optional.getLimit();
    // }

    // if(optional.getOffset() > 0) {
    //   const skip: number = (optional.getOffset() === 1) ? 0 : (optional.getOffset() - 1) * optional.getLimit();
    //   objQuery[ECofigure.OFFSET] = skip;
    // }

    const data = await this.model.findAll(objQuery);

    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(undefined);
    }
    return data;
  }

  public async queryAllRaw(optional: Optional): Promise<any> {
    const objQuery: any = {};
    if (optional.getWhere().length > 0) {
      const objWhere = this.handleWhere(optional.getWhere());
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getGroupWhere()) {
      const groupWhere: GroupWhere = optional.getGroupWhere();
      const objWhere: any = this.handleGroupWhere(groupWhere);
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getAttributes().length > 0) {
      objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
    }

    if (optional.getOrderby().length > 0) {
      const orderby: Array<any> = [];
      optional.getOrderby().map(element => {
        const key: string = element.getKey();
        const value: string = element.getValue();
        return orderby.push([key, value])
      });
      objQuery[ECofigure.ORDER_BY] = orderby;
    }

    if (optional.getGroup().length > 0) {
      objQuery[ECofigure.GROUP] = optional.getGroup();
    }

    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(optional.getRelation());
    }
    objQuery[EConfigure.RAW] = true;

    const data = await this.model.findAll(objQuery);
    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(undefined);
    }
    return data;
  }

  public async queryRaw(optional: Optional): Promise<any> {
    const objQuery: any = {};
    if (optional.getWhere().length > 0) {
      const objWhere = this.handleWhere(optional.getWhere());
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getGroupWhere()) {
      const groupWhere: GroupWhere = optional.getGroupWhere();
      const objWhere: any = this.handleGroupWhere(groupWhere);
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getAttributes().length > 0) {
      objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
    }

    if (optional.getOrderby().length > 0) {
      const orderby: Array<any> = [];
      optional.getOrderby().map(element => {
        const key: string = element.getKey();
        const value: string = element.getValue();
        return orderby.push([key, value])
      });
      objQuery[ECofigure.ORDER_BY] = orderby;
    }

    if (optional.getGroup().length > 0) {
      objQuery[ECofigure.GROUP] = optional.getGroup();
    }

    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(optional.getRelation());
    }
    objQuery[EConfigure.RAW] = true;

    const data = await this.model.findAll(objQuery);
    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(undefined);
    }
    return data;
  }

  public async queryOneRaw(optional: Optional): Promise<any> {
    const objQuery: any = {};
    if (optional.getWhere().length > 0) {
      const objWhere = this.handleWhere(optional.getWhere());
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getGroupWhere()) {
      const groupWhere: GroupWhere = optional.getGroupWhere();
      const objWhere: any = this.handleGroupWhere(groupWhere);
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getAttributes().length > 0) {
      objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
    }

    if (optional.getOrderby().length > 0) {
      const orderby: Array<any> = [];
      optional.getOrderby().map(element => {
        const key: string = element.getKey();
        const value: string = element.getValue();
        return orderby.push([key, value])
      });
      objQuery[ECofigure.ORDER_BY] = orderby;
    }

    if (optional.getGroup().length > 0) {
      objQuery[ECofigure.GROUP] = optional.getGroup();
    }

    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(optional.getRelation());
    }
    objQuery[EConfigure.RAW] = true;

    const data = await this.model.findOne(objQuery);
    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(undefined);
    }
    return data;
  }


  public async queryAllPaging(optional: Optional): Promise<any> {
    const objQuery: any = {};
    if (optional.getWhere().length > 0) {
      const objWhere = this.handleWhere(optional.getWhere());
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getAttributes().length > 0) {
      objQuery[ECofigure.ATTRIBUTES] = optional.getAttributes();
    }

    if (optional.getGroupWhere()) {
      const groupWhere: GroupWhere = optional.getGroupWhere();
      const objWhere: any = this.handleGroupWhere(groupWhere);
      objQuery[ECofigure.WHERE] = objWhere;
    }

    if (optional.getOrderby().length > 0) {
      const orderby: Array<any> = [];
      optional.getOrderby().map(element => {
        const key: string = element.getKey();
        const value: string = element.getValue();
        return orderby.push([key, value])
      });
      objQuery[ECofigure.ORDER_BY] = orderby;
    }

    if (optional.getGroup().length > 0) {
      objQuery[ECofigure.GROUP] = optional.getGroup();
      objQuery[EConfigure.RAW] = true;
    }

    if (optional.getQueryRaw()) {
      objQuery[EConfigure.RAW] = true;
    }

    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(optional.getRelation());
    }

    if (optional.getLimit() > 0) {
      objQuery[ECofigure.LIMIT] = optional.getLimit();
    }

    if (optional.getOffset() > 0) {
      const skip: number = (optional.getOffset() === 1) ? 0 : (optional.getOffset() - 1) * optional.getLimit();
      objQuery[ECofigure.OFFSET] = skip;
    }

    const data = await this.model.findAll(objQuery);
    if (optional.getRelation().length > 0) {
      this.model = this.model.scope(undefined);
    }
    return data;
  }

  handleGroupWhere(groupWhere: any): Array<any> {
    const arrWhere: Array<any> = [];
    const groups: any = groupWhere.getGroupWhere();
    let objQuery: any = null;
    groups.forEach((element: any) => {
      const where = this.handleWhere(element);
      arrWhere.push(where);
    });
    if (groupWhere.getRoot() === ECofigure.OR) {
      objQuery = {
        [Op[ECofigure.OR]]: arrWhere
      };
    }
    if (groupWhere.getRoot() === ECofigure.AND) {
      objQuery = {
        [Op[ECofigure.AND]]: arrWhere
      };
    }
    return objQuery;
  }

  handleWhere(arrWhere: any) {
    let orClause = {
      [Op[ECofigure.OR]]: [] as any
    };
    let andClause = {
      [Op[ECofigure.AND]]: [] as any
    }
    arrWhere.forEach((element: any) => {
      if (Array.isArray(element)) {
        element.forEach((item: any) => {
          const groups: any = item.getGroupWhere();
          const arrWhere: Array<any> = [];
          groups.forEach((element: any) => {
            const where = this.handleWhere(element);
            arrWhere.push(where);
          });
          if (item.getRoot() === ECofigure.AND) {
            andClause[Op[ECofigure.AND]].push(arrWhere);
          }
          if (item.getRoot() === ECofigure.OR) {
            orClause[Op[ECofigure.OR]].push(arrWhere);
          }
        });
      } else {
        let query: any = this.handleQuery(element);
        const clause = element.getClause();
        if (clause === ECofigure.AND && query) {
          andClause[Op[ECofigure.AND]].push(query);
        } else if (clause === ECofigure.OR && query) {
          orClause[Op[ECofigure.OR]].push(query);
        }
      }
    });
    let objFinal: any;
    if (andClause[Op[ECofigure.AND]].length > 0 && orClause[Op[ECofigure.OR]].length > 0) {
      objFinal = { ...andClause, ...orClause };
    } else {
      if (andClause[Op[ECofigure.AND]].length > 0) {
        objFinal = { ...andClause };
      }
      if (orClause[Op[ECofigure.OR]].length > 0) {
        objFinal = { ...orClause };
      }
    }
    return objFinal;
  }

  private handleQuery(element: any): any {
    let query: any;
    const key = element.getKey();
    const operation = element.getOperator();
    let value: any = element.getValue();
    switch (operation) {
      case ECofigure.EQUAL:
        query = {
          [key]: {
            [Op.eq]: value
          }
        }
        break;
      case ECofigure.NOT_EQUAL:
        query = {
          [key]: {
            [Op.ne]: value
          }
        }
        break;
      case ECofigure.EQUAL_NUMBER:
        query = {
          [key]: {
            [Op.eq]: Number(value)
          }
        }
        break;
      case ECofigure.NOT_EQUAL_NUMBER:
        query = {
          [key]: {
            [Op.ne]: Number(value)
          }
        }
        break;
      case ECofigure.GREATER_THAN:
        query = {
          [key]: {
            [Op.gt]: value
          }
        }
        break;
      case ECofigure.GREATER_THAN_EQUAL:
        query = {
          [key]: {
            [Op.gte]: value
          }
        }
        break;
      case ECofigure.LESS_THAN:
        query = {
          [key]: {
            [Op.lt]: value
          }
        }
        break;
      case ECofigure.LESS_THAN_EQUAL:
        query = {
          [key]: {
            [Op.lte]: value
          }
        }
        break;
      case ECofigure.LIKE:
        query = {
          [key]: {
            [Op.like]: value
          }
        }
        break;
      case ECofigure.ILIKE:
        query = {
          [key]: {
            [Op.iLike]: `%${value}%`
          }
        }
        break;
      case ECofigure.ILIKE_START:
        query = {
          [key]: {
            [Op.iLike]: `${value}%`
          }
        }
        break;
      case ECofigure.ILIKE_END:
        query = {
          [key]: {
            [Op.iLike]: `%${value}`
          }
        }
        break;
      case ECofigure.IS:
        if (value === '' || value === 'null') {
          value = null;
        }
        query = {
          [key]: {
            [Op.is]: value
          }
        }
        break;
      case ECofigure.NOT:
        if (value === '' || value === 'null') {
          value = null;
        }

        query = {
          [key]: {
            [Op.not]: value
          }
        }
        break;
      case ECofigure.OR:
        query = {
          [key]: {
            [Op.or]: [value]
          }
        }
        break;
      case ECofigure.IN:
        query = {
          [key]: {
            [Op.in]: value.split(',')
          }
        }
        break;
      case ECofigure.NOT_IN:
        query = {
          [key]: {
            [Op.notIn]: value.split(',')
          }
        }
        break;
      case ECofigure.BETWEEN:
        query = {
          [key]: {
            [Op.between]: value.split(',')
          }
        }
        break;
      case ECofigure.NOT_BETWEEN:
        query = {
          [key]: {
            [Op.notBetween]: value.split(',')
          }
        }
        break;
      case ECofigure.CONTAINS:
        query = {
          [key]: {
            [Op.contains]: value.split(',')
          }
        }
        break;
      case ECofigure.ARRAY_LOWER:
        query = this.sequelize.literal(`array_lower("${key}", 1) ${value}`)
        break;
      case EConfigure.ANY:
        query = {
          [key]: {
            [Op.any]: ["2"]
          }
        }
        break;
    }
    return query;
  }
}

export default BaseRepository;