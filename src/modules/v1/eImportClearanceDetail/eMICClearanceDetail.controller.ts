'use strict';

import * as express from 'express';
import { Request, Response, NextFunction } from 'express';
import Middlewares from '../../../middleware/middlewares';
import EConfigure from '../../../emuns/configures';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import ErrorValidate from "../../../util/error.validate";
import ImportClearanceDetailService from './eMICClearanceDetail.service';
import { IResponze } from '../../../https/responze';
import HttpData from '../../../https/data';
import RedisPromise from '../../../util/redis.promise';
import UserService from '../user/user.service';
import ImportClearanceDetailValidate from '../importClearanceDetail/importClearanceDetail.validate';


class eMICDetailController {
  public path = '/eimportDetails';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.put(`${this.path}/micItemNameVN`, middlewares.CheckPermission('put:v1:clearance.update'), this.micItemNameVN);
    this.router.put(`${this.path}/autoItemNameVN`, middlewares.CheckPermission('put:v1:clearance.update'), this.updateAutoItemNameVN);

    this.router.post(`${this.path}/nameExisted`, this.nameExisted);
  }

  private async nameExisted(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceDetailValidate.arrNameExisted.validate(req.body, { abortEarly: false });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceDetailService = new ImportClearanceDetailService();
      const data: IResponze = await importClearanceDetailService.nameExisted(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));

    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import_detail][name_exiested]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async micItemNameVN(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceDetailValidate.arrUpdateIDAName.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const importClearanceDetailService = new ImportClearanceDetailService();
      const data: IResponze = await importClearanceDetailService.eMICItemNameVN(value, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));

    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import_detail][ida_item_nameVN]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateAutoItemNameVN(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceDetailValidate.autoItemNameVN.validate(req.body, { abortEarly: false });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const importClearanceDetailService = new ImportClearanceDetailService();
      const data: IResponze = await importClearanceDetailService.updateAutoItemNameVN(value, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));

    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import_detail][update_auto_item_nameVN]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default eMICDetailController;