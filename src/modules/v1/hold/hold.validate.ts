'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';

class TaxCodeValidate {
  static create: any = Joi.object({
    name: Joi.string().trim().required().max(255).messages({
      "any.required": `code ${EMessage.REQUIRED}`,
      "string.empty": `code ${EMessage.EMPTYED}`,
      "string.max": `code ${EMessage.MAX_255}`,
    }),
    
  });

  static arrCreate: any = Joi.array().items(TaxCodeValidate.create).unique().required();
}

export default TaxCodeValidate;
