'use strict';

import ParameterRepository from "./parameter.repository";
import BaseService from '../../index.service';
import Configure from "../../../emuns/configures";
import { Parameter } from "../../../models/index.model";

class ParameterService extends BaseService {
  private parameterRepository: ParameterRepository;
  constructor () {
    super(new ParameterRepository());
    this.parameterRepository = new ParameterRepository();
  }

  public async updateHAWB(no: string): Promise<void> {
    try {
      const whereHAWB = { 'name': Configure.HAWB_FIELD };
      const sequenceHAWB: Parameter = await this.parameterRepository.getOneObj(whereHAWB);
      if(sequenceHAWB) {
        sequenceHAWB['value'] = no;
        await sequenceHAWB.save();
      }
    } catch (error) {
      throw new Error (error as any);
    }
  }
}

export default ParameterService;