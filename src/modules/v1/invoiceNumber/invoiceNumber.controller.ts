'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import Middlewares from '../../../middleware/middlewares';
import EMessage from '../../../emuns/messages';
import EConfigure from '../../../emuns/configures';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import HttpData from '../../../https/data';
import InvoiceNumberService from "./invoiceNumber.service";

class InvoiceNumberController {
  public path = '/invoiceNumbers';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get(this.path, this.getCurrentNumber);
    this.router.put(this.path, this.updateCurrentNumber);
  }

  private async getCurrentNumber(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const invoiceNumberService: InvoiceNumberService = new InvoiceNumberService();
      const data = await invoiceNumberService.currentInvoiceNumber();
      if(data) {
        return HttpResponse.sendData(res, httpStatus.FOUND, new HttpData(true, data));
      } else {
        return HttpResponse.sendMessage(res, httpStatus.NOT_FOUND, new HttpException(true, EMessage.NOT_FOUND));
      }
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][hscode][get_one]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateCurrentNumber(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const invoiceNumberService: InvoiceNumberService = new InvoiceNumberService();
      const status: boolean = await invoiceNumberService.updateCurrentInvoiceNumber();
      console.log(status)
      if(status) {
        return HttpResponse.sendMessage(res, httpStatus.OK, new HttpException(true, EMessage.UPDATE_SUCCESS));
      } else {
        return HttpResponse.sendMessage(res, httpStatus.EXPECTATION_FAILED, new HttpException(true, EMessage.UPDATE_FAIL));
      }
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][area][delete_one]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default InvoiceNumberController;