'use strict';

import InvoiceNumberRepository from "./invoiceNumber.reporsitory";
import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import { InvoiceNumber } from "../../../models/invoiceNumber.model";
import Utilities from "../../../util/utilities";

class InvoiceNumberService extends BaseService {
  private invoiceNumberRepository: InvoiceNumberRepository;
  constructor () {
    super(new InvoiceNumberRepository());
    this.invoiceNumberRepository = new InvoiceNumberRepository();
  }

  public async currentInvoiceNumber(): Promise<any> {
    try {
      const invoiceNumber: InvoiceNumber = await this.invoiceNumberRepository.getOne(1);
      let current: number = 1;
      if(invoiceNumber) {
        current = Number(invoiceNumber['numberCode']);
      }
      const code: string = Utilities.padWithZeroes(current, 6);
      return code;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async updateCurrentInvoiceNumber(): Promise<any> {
    try {
      const invoiceNumber: InvoiceNumber = await this.invoiceNumberRepository.getOne(1);
      if(invoiceNumber) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, 'id', EConfigure.EQUAL, 1)
        ]);

        const [total] = await this.invoiceNumberRepository.updateData({ 'numberCode': Number(invoiceNumber['numberCode']) + 1 }, optional);
        if(total > 0) {
          return true;
        }
        return false;
      } else {
        await this.invoiceNumberRepository.createData({
          'numberCode': 2,
          'hubId': 5
        });
        return true;
      }
      
    } catch (error) {
      throw new Error(error as any);
    }
  }
}

export default InvoiceNumberService;