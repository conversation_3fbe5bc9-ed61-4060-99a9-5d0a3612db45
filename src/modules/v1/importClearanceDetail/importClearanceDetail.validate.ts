'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';
import Utilities from '../../../util/utilities';
import { WeightName } from '../../../emuns/weight';

class ImportClearanceDetailValidate {
  static create: any = Joi.object({
    HSCode: Joi.string().trim().max(50).allow(null).allow('').messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().required().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().allow(null).trim().messages({
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    quantity: Joi.number().integer().min(1).messages({
      "number.base": `quantity ${EMessage.NUMBER}`,
      "number.integer": `quantity ${EMessage.INTEGER}`,
      "number.min": `quantity ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    url: Joi.string().trim().allow(null).messages({
      "string.empty": `url ${EMessage.EMPTYED}}`,
    }),
    invoiceValue: Joi.number().min(0).messages({
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
      "number.min": `invoiceValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    priceQuantityUnit: Joi.string().trim().max(3).messages({
      "string.empty": `priceQuantityUnit ${EMessage.EMPTYED}`,
      "string.max": `priceQuantityUnit ${EMessage.MAX_3}`,
    }),
    currencyCode: Joi.string().trim().max(3).messages({
      "string.empty": `currencyCode ${EMessage.EMPTYED}`,
      "string.max": `currencyCode ${EMessage.MAX_3}`,
    }),
    weight: Joi.number().min(0).required().messages({
      "any.required": `weight ${EMessage.REQUIRED}`,
      "number.base": `weight ${EMessage.NUMBER}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).required().max(3).trim().messages({
      "any.required": `unitOfMass ${EMessage.REQUIRED}`,
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    productId: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.max": `productId ${EMessage.MAX_255}`,
    }),
    originalUrl: Joi.string().trim().allow(null).messages({
      "string.empty": `originalUrl ${EMessage.EMPTYED}`,
    }),
    originalProductName: Joi.string().trim().allow(null).messages({
      "string.empty": `originalProductName ${EMessage.EMPTYED}`,
    }),
    placeOfOrigin: Joi.string().trim().allow(null).allow('').max(4).messages({
      "string.empty": `placeOfOrigin ${EMessage.EMPTYED}`,
      "string.max": `placeOfOrigin ${EMessage.MAX_4}`,
    }),
  });

  static autoItemNameVN = Joi.array().min(1).items(
    Joi.object({
      id: Joi.number().integer().required().messages({
        "any.required": `id ${EMessage.REQUIRED}`,
        "number.base": `id ${EMessage.NUMBER}`,
        "number.integer": `id ${EMessage.INTEGER}`,
      }),
      HAWB: Joi.string().trim().max(30).required().messages({
        "any.required": `HAWB ${EMessage.REQUIRED}`,
        "string.empty": `HAWB ${EMessage.EMPTYED}`,
        "string.max": `HAWB ${EMessage.MAX_30}`,
      }),
      itemName: Joi.string().trim().required().messages({
        "any.required": `itemName ${EMessage.REQUIRED}`,
        "string.empty": `itemName ${EMessage.EMPTYED}`,
      }),
    })
    ).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static updateIDAName: any = Joi.object({
    id: Joi.number().integer().required().messages({
      "any.required": `id ${EMessage.REQUIRED}`,
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    HSCode: Joi.string().trim().max(50).required().messages({
      "any.required": `HSCode ${EMessage.REQUIRED}`,
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().trim().required().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().required().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    // quantityUnitCode1: Joi.string().trim().max(3).messages({
    //   "string.empty": `HAWB ${EMessage.EMPTYED}`,
    //   "string.max": `HAWB ${EMessage.MAX_3}`,
    // }),
    // priceQuantityUnit: Joi.string().trim().max(3).messages({
    //   "string.empty": `HAWB ${EMessage.EMPTYED}`,
    //   "string.max": `HAWB ${EMessage.MAX_3}`,
    // }),
    // quantity1: Joi.number().integer().min(1).messages({
    //   "number.base": `itemAmount ${EMessage.NUMBER}`,
    //   "number.integer": `itemAmount ${EMessage.INTEGER}`,
    //   "number.min": `itemAmount ${EMessage.GREATE_EQUAL_THAN_1}`,
    // }),
    // url: Joi.string().trim().messages({
    //   "string.empty": EMessage.EMPTYED,
    // }),
    // importTaxClassificationCode: Joi.string().trim().max(3).messages({
    //   "string.empty": `HAWB ${EMessage.EMPTYED}`,
    //   "string.max": `HAWB ${EMessage.MAX_3}`,
    // }),
    // invoiceValue: Joi.number().min(0).greater(0).messages({
    //   "number.base": `invoiceValue ${EMessage.NUMBER}`,
    //   "number.min": `invoiceValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    //   "number.greater": `invoiceValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    // }),
    // invoiceUnitPrice: Joi.number().min(0).greater(0).messages({
    //   "number.base": EMessage.NUMBER,
    //   "number.min": EMessage.GREATE_EQUAL_THAN_0,
    //   "number.greater": EMessage.GREATE_EQUAL_THAN_0,
    // }),
    // unitPriceCurrencyCode: Joi.string().trim().max(3).messages({
    //   "string.empty": `unitPriceCurrencyCode ${EMessage.EMPTYED}`,
    //   "string.max": `unitPriceCurrencyCode ${EMessage.MAX_3}`,
    // }),
  });

  static arrUpdateIDAName = Joi.array().min(1).items(ImportClearanceDetailValidate.updateIDAName).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static updateMICName: any = Joi.object({
    id: Joi.number().integer().required().messages({
      "any.required": `id ${EMessage.REQUIRED}`,
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    itemName: Joi.string().trim().required().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().required().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
  });

  static arrUpdateMICName = Joi.array().min(1).items(ImportClearanceDetailValidate.updateMICName).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static updateMIC: any = Joi.object({
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    HSCode: Joi.string().trim().max(50).messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    quantity1: Joi.number().integer().min(1).messages({
      "any.required": `itemAmount ${EMessage.REQUIRED}`,
      "number.base": `itemAmount ${EMessage.NUMBER}`,
      "number.integer": `itemAmount ${EMessage.INTEGER}`,
      "number.min": `itemAmount ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    url: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `url ${EMessage.EMPTYED}`,
    }),
    invoiceValue: Joi.number().messages({
      "any.required": `invoiceValue ${EMessage.REQUIRED}`,
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
    }),
    // unitPriceCurrencyCode: Joi.string().trim().max(3).messages({
    //   "string.empty": `unitPriceCurrencyCode ${EMessage.EMPTYED}`,
    //   "string.max": `unitPriceCurrencyCode ${EMessage.MAX_3}`,
    // }),
    // priceVND: Joi.number().messages({
    //   "any.required": `priceVND ${EMessage.REQUIRED}`,
    //   "number.base": `priceVND ${EMessage.NUMBER}`,
    // }),

    // quantityUnitCode1: Joi.string().trim().max(3).messages({
    //   "string.empty": `HAWB ${EMessage.EMPTYED}`,
    //   "string.max": `HAWB ${EMessage.MAX_3}`,
    // }),
    // priceQuantityUnit: Joi.string().trim().max(3).messages({
    //   "string.empty": `HAWB ${EMessage.EMPTYED}`,
    //   "string.max": `HAWB ${EMessage.MAX_3}`,
    // }),
    // importTaxClassificationCode: Joi.string().trim().max(3).messages({
    //   "string.empty": `HAWB ${EMessage.EMPTYED}`,
    //   "string.max": `HAWB ${EMessage.MAX_3}`,
    // }),
    
    invoiceUnitPrice: Joi.number().min(0).greater(0).messages({
      "number.base": EMessage.NUMBER,
      "number.min": EMessage.GREATE_EQUAL_THAN_0,
      "number.greater": EMessage.GREATE_EQUAL_THAN_0,
    }),
    weightKG: Joi.number().allow(null).allow('').messages({
      "number.base": `weightKG ${EMessage.NUMBER}`,
    }),
    productId: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.max": `productId ${EMessage.MAX_255}`,
    }),
    originalUrl: Joi.string().trim().allow(null).messages({
      "string.empty": `originalUrl ${EMessage.EMPTYED}`,
    }),
    originalProductName: Joi.string().trim().allow(null).messages({
      "string.empty": `originalProductName ${EMessage.EMPTYED}`,
    }),
    placeOfOrigin: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `placeOfOrigin ${EMessage.EMPTYED}`,
      "string.max": `placeOfOrigin ${EMessage.MAX_2}`,
    }),
    originalPlaceName: Joi.string().trim().max(100).allow(null).allow('').messages({
      "string.empty": `originalPlaceName ${EMessage.EMPTYED}`,
      "string.max": `originalPlaceName ${EMessage.MAX_100}`,
    }),
  });

  static updateIDA: any = Joi.object({
    id: Joi.number().integer().messages({
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    HSCode: Joi.string().trim().max(50).messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    url: Joi.string().trim().allow('').allow(null).messages({
      "any.required": `url ${EMessage.REQUIRED}`,
      "string.empty": EMessage.EMPTYED,
    }),
    itemName: Joi.string().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    quantity1: Joi.number().messages({
      "number.base": `quantity1 ${EMessage.NUMBER}`,
    }),
    quantity2: Joi.number().allow(null).allow('').messages({
      "number.base": `quantity2 ${EMessage.NUMBER}`,
    }),
    quantityUnitCode1: Joi.string().trim().max(6).messages({
      "string.empty": `quantityUnitCode1 ${EMessage.EMPTYED}`,
      "string.max": `quantityUnitCode1 ${EMessage.MAX_6}`,
    }),
    quantityUnitCode2: Joi.string().trim().max(6).allow(null).allow('').messages({
      "string.empty": `quantityUnitCode2 ${EMessage.EMPTYED}`,
      "string.max": `quantityUnitCode2 ${EMessage.MAX_6}`,
    }),
    invoiceValue: Joi.number().messages({
      "any.required": `invoiceValue ${EMessage.REQUIRED}`,
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
    }),
    
    importTax: Joi.number().required().messages({
      "any.required": `importTax ${EMessage.REQUIRED}`,
      "number.base": `importTax ${EMessage.NUMBER}`,
    }),
    importTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `importTaxCode ${EMessage.EMPTYED}`,
      "string.max": `importTaxCode ${EMessage.MAX_10}`,
    }),
    importTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `importTaxFree ${EMessage.EMPTYED}`,
      "string.max": `importTaxFree ${EMessage.MAX_10}`,
    }),
    VATTax: Joi.number().required().messages({
      "any.required": `VATTax ${EMessage.REQUIRED}`,
      "number.base": `VATTax ${EMessage.NUMBER}`,
    }),
    VATTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATTaxCode ${EMessage.EMPTYED}`,
      "string.max": `VATTaxCode ${EMessage.MAX_10}`,
    }),
    VATTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATTaxFree ${EMessage.EMPTYED}`,
      "string.max": `VATTaxFree ${EMessage.MAX_10}`,
    }),
    environmentTax: Joi.number().required().messages({
      "any.required": `environmentTax ${EMessage.REQUIRED}`,
      "number.base": `environmentTax ${EMessage.NUMBER}`,
    }),
    environmentTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `environmentTaxCode ${EMessage.EMPTYED}`,
      "string.max": `environmentTaxCode ${EMessage.MAX_10}`,
    }),
    environmentTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `environmentTaxFree ${EMessage.EMPTYED}`,
      "string.max": `environmentTaxFree ${EMessage.MAX_10}`,
    }),
    specialConsumptionTax: Joi.number().required().messages({
      "any.required": `specialConsumptionTax ${EMessage.REQUIRED}`,
      "number.base": `specialConsumptionTax ${EMessage.NUMBER}`,
    }),
    specialConsumptionTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `specialConsumptionTaxCode ${EMessage.EMPTYED}`,
      "string.max": `specialConsumptionTaxCode ${EMessage.MAX_10}`,
    }),
    specialConsumptionTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `specialConsumptionTaxFree ${EMessage.EMPTYED}`,
      "string.max": `specialConsumptionTaxFree ${EMessage.MAX_10}`,
    }),
    placeOfOrigin: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `placeOfOrigin ${EMessage.EMPTYED}`,
      "string.max": `placeOfOrigin ${EMessage.MAX_2}`,
    }),
    originalPlaceName: Joi.string().trim().max(100).allow(null).allow('').messages({
      "string.empty": `originalPlaceName ${EMessage.EMPTYED}`,
      "string.max": `originalPlaceName ${EMessage.MAX_100}`,
    }),

    // quantityUnitCode1: Joi.string().trim().max(3).messages({
    //   "string.empty": `HAWB ${EMessage.EMPTYED}`,
    //   "string.max": `HAWB ${EMessage.MAX_3}`,
    // }),
    unitPriceCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `unitPriceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `unitPriceCurrencyCode ${EMessage.MAX_3}`,
    }),
    weightKG: Joi.number().allow(null).allow('').messages({
      "number.base": `weightKG ${EMessage.NUMBER}`,
    }),
    // priceVND: Joi.number().messages({
    //   "any.required": `priceVND ${EMessage.REQUIRED}`,
    //   "number.base": `priceVND ${EMessage.NUMBER}`,
    // }),
    // importTaxClassificationCode: Joi.string().trim().max(3).messages({
    //   "string.empty": `HAWB ${EMessage.EMPTYED}`,
    //   "string.max": `HAWB ${EMessage.MAX_3}`,
    // }),
    
    invoiceUnitPrice: Joi.number().min(0).greater(0).messages({
      "number.base": EMessage.NUMBER,
      "number.min": EMessage.GREATE_EQUAL_THAN_0,
      "number.greater": EMessage.GREATE_EQUAL_THAN_0,
    }),
    productId: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.max": `productId ${EMessage.MAX_255}`,
    }),
    originalUrl: Joi.string().trim().allow(null).messages({
      "string.empty": `originalUrl ${EMessage.EMPTYED}`,
    }),
    originalProductName: Joi.string().trim().allow(null).messages({
      "string.empty": `originalProductName ${EMessage.EMPTYED}`,
    }),
  });

  static nameExisted: any = Joi.object({
    id: Joi.number().integer().required().messages({
      "any.required": `id ${EMessage.REQUIRED}`,
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    itemName: Joi.string().trim().required().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
  });

  static arrNameExisted = Joi.array().min(1).items(ImportClearanceDetailValidate.nameExisted).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static createImportExcel: any = Joi.object({
    HSCode: Joi.string().trim().max(50).allow(null).allow('').messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().required().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().allow(null).trim().messages({
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    quantity: Joi.number().integer().min(1).messages({
      "number.base": `quantity ${EMessage.NUMBER}`,
      "number.integer": `quantity ${EMessage.INTEGER}`,
      "number.min": `quantity ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    url: Joi.string().trim().allow(null).messages({
      "string.empty": `url ${EMessage.EMPTYED}}`,
    }),
    invoiceValue: Joi.number().allow(null).allow('').messages({
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
    }),
    invoiceUnitPrice: Joi.number().allow(null).allow('').messages({
      "number.base": `invoiceUnitPrice ${EMessage.NUMBER}`,
    }),
    priceQuantityUnit: Joi.string().trim().max(3).messages({
      "string.empty": `priceQuantityUnit ${EMessage.EMPTYED}`,
      "string.max": `priceQuantityUnit ${EMessage.MAX_3}`,
    }),
    currencyCode: Joi.string().trim().max(3).messages({
      "string.empty": `currencyCode ${EMessage.EMPTYED}`,
      "string.max": `currencyCode ${EMessage.MAX_3}`,
    }),
    weight: Joi.number().min(0).allow(null).allow('').messages({
      "number.base": `weight ${EMessage.NUMBER}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).required().max(3).trim().messages({
      "any.required": `unitOfMass ${EMessage.REQUIRED}`,
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    productId: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.max": `productId ${EMessage.MAX_255}`,
    }),
    originalUrl: Joi.string().trim().allow(null).messages({
      "string.empty": `originalUrl ${EMessage.EMPTYED}`,
    }),
    originalProductName: Joi.string().trim().allow(null).messages({
      "string.empty": `originalProductName ${EMessage.EMPTYED}`,
    }),
    placeOfOrigin: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `placeOfOrigin ${EMessage.EMPTYED}`,
      "string.max": `placeOfOrigin ${EMessage.MAX_2}`,
    }),
  });

  static updateCOM: any = Joi.object({
    id: Joi.number().integer().messages({
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(50).messages({
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    HSCode: Joi.string().trim().max(50).messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    url: Joi.string().trim().allow('').allow(null).messages({
      "any.required": `url ${EMessage.REQUIRED}`,
      "string.empty": EMessage.EMPTYED,
    }),
    itemName: Joi.string().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    quantity1: Joi.number().messages({
      "number.base": `quantity1 ${EMessage.NUMBER}`,
    }),
    quantity2: Joi.number().allow(null).allow('').messages({
      "number.base": `quantity2 ${EMessage.NUMBER}`,
    }),
    quantityUnitCode1: Joi.string().trim().max(6).messages({
      "string.empty": `quantityUnitCode1 ${EMessage.EMPTYED}`,
      "string.max": `quantityUnitCode1 ${EMessage.MAX_6}`,
    }),
    quantityUnitCode2: Joi.string().trim().max(6).allow(null).allow('').messages({
      "string.empty": `quantityUnitCode2 ${EMessage.EMPTYED}`,
      "string.max": `quantityUnitCode2 ${EMessage.MAX_6}`,
    }),
    invoiceValue: Joi.number().messages({
      "any.required": `invoiceValue ${EMessage.REQUIRED}`,
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
    }),
    
    importTax: Joi.number().messages({
      "number.base": `importTax ${EMessage.NUMBER}`,
    }),
    importTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `importTaxCode ${EMessage.EMPTYED}`,
      "string.max": `importTaxCode ${EMessage.MAX_10}`,
    }),
    importTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `importTaxFree ${EMessage.EMPTYED}`,
      "string.max": `importTaxFree ${EMessage.MAX_10}`,
    }),
    VATTax: Joi.number().messages({
      "number.base": `VATTax ${EMessage.NUMBER}`,
    }),
    VATTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATTaxCode ${EMessage.EMPTYED}`,
      "string.max": `VATTaxCode ${EMessage.MAX_10}`,
    }),
    VATTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATTaxFree ${EMessage.EMPTYED}`,
      "string.max": `VATTaxFree ${EMessage.MAX_10}`,
    }),
    environmentTax: Joi.number().messages({
      "number.base": `environmentTax ${EMessage.NUMBER}`,
    }),
    environmentTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `environmentTaxCode ${EMessage.EMPTYED}`,
      "string.max": `environmentTaxCode ${EMessage.MAX_10}`,
    }),
    environmentTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `environmentTaxFree ${EMessage.EMPTYED}`,
      "string.max": `environmentTaxFree ${EMessage.MAX_10}`,
    }),
    specialConsumptionTax: Joi.number().required().messages({
      "any.required": `specialConsumptionTax ${EMessage.REQUIRED}`,
      "number.base": `specialConsumptionTax ${EMessage.NUMBER}`,
    }),
    specialConsumptionTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `specialConsumptionTaxCode ${EMessage.EMPTYED}`,
      "string.max": `specialConsumptionTaxCode ${EMessage.MAX_10}`,
    }),
    specialConsumptionTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `specialConsumptionTaxFree ${EMessage.EMPTYED}`,
      "string.max": `specialConsumptionTaxFree ${EMessage.MAX_10}`,
    }),
    placeOfOrigin: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `placeOfOrigin ${EMessage.EMPTYED}`,
      "string.max": `placeOfOrigin ${EMessage.MAX_2}`,
    }),
    originalPlaceName: Joi.string().trim().max(100).allow(null).allow('').messages({
      "string.empty": `originalPlaceName ${EMessage.EMPTYED}`,
      "string.max": `originalPlaceName ${EMessage.MAX_100}`,
    }),
    unitPriceCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `unitPriceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `unitPriceCurrencyCode ${EMessage.MAX_3}`,
    }),
    weightKG: Joi.number().allow(null).allow('').messages({
      "number.base": `weightKG ${EMessage.NUMBER}`,
    }),
    invoiceUnitPrice: Joi.number().min(0).greater(0).messages({
      "number.base": EMessage.NUMBER,
      "number.min": EMessage.GREATE_EQUAL_THAN_0,
      "number.greater": EMessage.GREATE_EQUAL_THAN_0,
    }),
    productId: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.max": `productId ${EMessage.MAX_255}`,
    }),
    originalUrl: Joi.string().trim().allow(null).messages({
      "string.empty": `originalUrl ${EMessage.EMPTYED}`,
    }),
    originalProductName: Joi.string().trim().allow(null).messages({
      "string.empty": `originalProductName ${EMessage.EMPTYED}`,
    }),
  });
}

export default ImportClearanceDetailValidate;
