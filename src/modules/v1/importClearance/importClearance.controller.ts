'use strict';

import * as express from 'express';
import { Request, Response, NextFunction } from 'express';
import Middlewares from '../../../middleware/middlewares';
import ParserUrl from '../../../parser/url';
import ImportClearanceService from './importClearance.service';
import EConfigure from '../../../emuns/configures';
import Optional from '../../../parser/optional';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import Meta from '../../../https/meta';
import HttpData from '../../../https/data';
import ErrorValidate from '../../../util/error.validate';
import ImportClearanceValidate from './importClearance.validate';
import { IResponze } from '../../../https/responze';
import { IImportValidate } from '../../../models/mic.model';
import Utilities from '../../../util/utilities';
import RedisPromise from '../../../util/redis.promise';
import UserService from '../user/user.service';
import Where from '../../../parser/where';
import { Clearance } from '../../../xmlClearance/clearance';
import ClearanceCreateLogMongo from '../../../models/mongo/clearanceCreateLog.model';
import PdfGenerate from '../pdfGenerate/pdfGenerate.service';


class ImportClearanceController {
  public path = '/imports';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get(`${this.path}`, this.nonTaxCodeNumber);
    this.router.get(`${this.path}/mic`, this.getAllMIC);
    this.router.get(`${this.path}/ida`, this.getAllIDA);
    this.router.get(`${this.path}/getAll`, this.getAll);
    this.router.get(`${this.path}/master`, this.master);
    this.router.get(`${this.path}/managementGate`, this.managementGate);
    this.router.get(`${this.path}/totalManagementGate`, this.totalManagementGate);
    this.router.get(`${this.path}/exportManagementGate`, this.exportManagementGate);
    this.router.get(`${this.path}/clearanceReport`, this.clearanceReport);
    this.router.get(`${this.path}/clearanceReportCount`, this.clearanceReportCount);
    this.router.get(`${this.path}/clearanceReportExport`, this.clearanceReportExport);
    this.router.get(`${this.path}/holdReport`, this.getHoldReport);
    this.router.get(`${this.path}/importReport`, this.importReport);
    this.router.get(`${this.path}/holdManifest`, this.getHoldManifest);
    this.router.get(`${this.path}/externalBoxes`, this.externalBoxes);
    this.router.get(`${this.path}/storeWarehouse`, this.getStoreWarehouse);
    this.router.get(`${this.path}/reportCustom`, this.reportCustom);
    this.router.get(`${this.path}/reportCustomExport`, this.reportCustomExport);

    this.router.get(`${this.path}/mic/assignCargoName`, this.getMicAssignCargoName);
    this.router.get(`${this.path}/ida/assignCargoName`, this.getIdaAssignCargoName);
    this.router.get(`${this.path}/MAWBs`, this.getMAWBs);
    this.router.get(`${this.path}/mic/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneMIC);
    this.router.get(`${this.path}/ida/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneIDA);
    this.router.get(`${this.path}/MAWB/:MAWB`, this.getMAWB);

    this.router.get(`${this.path}/createLog`, this.createLog);
    this.router.get(`${this.path}/reportTax`, this.reportTax);

    this.router.put(`${this.path}/micToIDA`, middlewares.CheckPermission('get:v1:clearance.changeClassify'), this.changeMICToIDA);
    this.router.put(`${this.path}/idaToMIC`, middlewares.CheckPermission('get:v1:clearance.changeClassify'), this.changeIDAToMIC);
    this.router.put(`${this.path}/holdChangeClassify`, middlewares.CheckPermission('get:v1:clearance.changeClassify'), this.holdChangeClassify);

    this.router.put(`${this.path}/messageTax`, this.messageTax);
    this.router.put(`${this.path}/master`, this.updateMaster);

    this.router.put(`${this.path}/resetMIC`, middlewares.CheckPermission('put:v1:clearance.resetMIC'), this.resetMIC);
    this.router.put(`${this.path}/resetIDA`, middlewares.CheckPermission('put:v1:clearance.resetIDA'), this.resetIDA);

    this.router.put(`${this.path}/registerMIC`, middlewares.CheckPermission('put:v1:clearance.registerMIC'), this.registerMIC);
    this.router.put(`${this.path}/registerMIE`, middlewares.CheckPermission('put:v1:clearance.registerMIE'), this.registerMIE);

    this.router.put(`${this.path}/registerIDA`, middlewares.CheckPermission('put:v1:clearance.registerIDA'), this.registerIDA);
    this.router.put(`${this.path}/registerIDC`, middlewares.CheckPermission('put:v1:clearance.registerIDC'), this.registerIDC);
    this.router.put(`${this.path}/registerIIDA`, middlewares.CheckPermission('put:v1:clearance.registerIIDA'), this.registerIIDA);
    this.router.put(`${this.path}/registerIDE`, middlewares.CheckPermission('put:v1:clearance.registerIDE'), this.registerIDE);

    this.router.put(`${this.path}/holdManifest`, this.updateHoldManifest);

    this.router.put(`${this.path}/returnCargo`, this.returnCargo);

    this.router.put(`${this.path}/mic/assignCargoName`, this.micAssignCargoName);
    this.router.put(`${this.path}/ida/assignCargoName`, this.idaAssignCargoName);
    this.router.put(`${this.path}/taxCodeNumber`, middlewares.CheckPermission('put:v1:clearance.taxCodeNumber'), this.taxCodeNumber);
    this.router.put(`${this.path}/micTaxCodePint`, middlewares.CheckPermission('put:v1:clearance.taxCodeNumber'), this.micTaxCodePint);
    this.router.put(`${this.path}/micTaxPercent`, middlewares.CheckPermission('put:v1:clearance.taxCodeNumber'), this.micTaxPercent);

    this.router.put(`${this.path}/update/:HAWB`, this.update);
    this.router.put(`${this.path}/sortLane/:HAWB`, this.updateSortLane);
    this.router.put(`${this.path}/mic/:HAWB`, middlewares.CheckPermission('put:v1:clearance.update'), this.updateMIC);
    this.router.put(`${this.path}/ida/:HAWB`, middlewares.CheckPermission('put:v1:clearance.update'), this.updateIDA);
    this.router.put(`${this.path}/com/:HAWB`, middlewares.CheckPermission('put:v1:clearance.update'), this.updateCOM);
    this.router.put(`${this.path}/changeExchangeRate`, middlewares.CheckPermission('put:v1:clearance.updateExchangeRate'), this.changeExchangeRate);

    this.router.post(this.path, this.create);
    this.router.post(`${this.path}/monitorGate`, this.monitorGate);
    this.router.post(`${this.path}/mic`, this.allMIC);
    this.router.post(`${this.path}/ida`, this.allIDA);
    this.router.post(`${this.path}/filterMIC`, this.filterMIC);
    this.router.post(`${this.path}/filterIDA`, this.filterIDA);
    this.router.post(`${this.path}/storeWarehouse`, this.saveWarehouse);
    this.router.post(`${this.path}/retryMIC`, middlewares.CheckPermission('put:v1:clearance.retry'), this.retryMIC);
    this.router.post(`${this.path}/retryIDA`, middlewares.CheckPermission('put:v1:clearance.retry'), this.retryIDA);

    this.router.post(`${this.path}/pdfPrint`, this.pdfPrint);
    this.router.post(`${this.path}/pdfPickupPrint`, this.pdfPickupPrint);

    this.router.post(`${this.path}/generateInvoice`, this.generateInvoice);
    this.router.post(`${this.path}/swapInvoice`, this.swapInvoice);

    this.router.post(`${this.path}/MICGroupBox`, this.MICGroupBox);
    this.router.post(`${this.path}/IDAGroupBox`, this.IDAGroupBox);
    this.router.post(`${this.path}/importExcel`, this.importExcel);

    this.router.delete(`${this.path}/storeWarehouse`, this.deleteWarehouse);
    this.router.delete(`${this.path}/micOrida`, middlewares.CheckPermission('del:v1:clearance.micOrida'), this.micOrida);
  }

  private async generateInvoice(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.generateInvoice.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.generateInvoice(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][generateInvoice]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async swapInvoice(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.swapInvoice.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.swapInvoice(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][generateInvoice]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateSortLane(req: Request, res: Response, next: NextFunction) {
    try {
      const importClearanceService = new ImportClearanceService();
      const { HAWB } = req.params;
      const data: IResponze = await importClearanceService.updateSortLane(HAWB,);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][updateSortLane]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async changeExchangeRate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ImportClearanceValidate.changeExchangeRate.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const message: IResponze = await importClearanceService.changeExchangeRate(Utilities.convertNull(value), employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][changeExchangeRate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async reportCustomExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let hubIds: string = '';
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubIds = profile['hubs'];
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubIds = userInfo['hubs'];
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.reportCustomExport(optional, parseUrl, hubIds, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][reportCustomExport]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async reportCustom(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.reportCustom(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async pdfPickupPrint(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ImportClearanceValidate.pickupPrint.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);

        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
        if (profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];

        }
        if (userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const pdfGenerate = new PdfGenerate();
      const message: IResponze = await pdfGenerate.pdfPickupPrint(Utilities.convertNull(value), hubs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][pdfPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async exportManagementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
        if (profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
        if (userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.exportManagementGate(optional, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][management_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async totalManagementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.totalManagementGate(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][management_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async pdfPrint(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ImportClearanceValidate.historyInvoice.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
        if (profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
        if (userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const pdfGenerate = new PdfGenerate();
      value = Utilities.convertNull(value);
      const message: IResponze = await pdfGenerate.pdfPrint(value, hubs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][pdfPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async IDAGroupBox(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ImportClearanceValidate.groupBox.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      value = Utilities.convertNull(value);
      const data: IResponze = await importClearanceService.IDAGroupBox(value, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][IDAGroupBox]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async MICGroupBox(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ImportClearanceValidate.groupBox.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      value = Utilities.convertNull(value);
      const data: IResponze = await importClearanceService.MICGroupBox(value, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][MICGroupBox]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async externalBoxes(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.externalBoxes(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][externalBoxes]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMAWBs(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubTo', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubTo', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getMAWBs(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getMAWBs]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMAWB(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { MAWB } = req.params;
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getMAWB(MAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getMAWB]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async micOrida(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs } = value;
      const data: IResponze = await importClearanceService.micOrida(HAWBs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][micOrida]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async nonTaxCodeNumber(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const importClearanceService = new ImportClearanceService();
      importClearanceService.updateTaxCodeNumber(req.body.HAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(true, true));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][nonTaxCodeNumber]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async reportTax(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.reportTax(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][reportTax]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async createLog(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const data: any = await ClearanceCreateLogMongo.getAll(req.query);
      const status: boolean = data.length > 0 ? true : false;
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getAllMICIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async micTaxPercent(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.micTaxPercent.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.micTaxPercent(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][micCountPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async micTaxCodePint(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.micTaxPint.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.micCountPrint(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][micCountPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async taxCodeNumber(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.taxCodeNumber.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWB } = value;
      const data: IResponze = await importClearanceService.countPrint(HAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][countPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async retryIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const message: any = await new Clearance().getHawbData(value['HAWBs'], 'IDA');
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['isSuccess'], message['hawbSuccess'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][retryIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async retryMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const message: any = await new Clearance().getHawbData(value['HAWBs'], 'MIC');
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['isSuccess'], message['hawbSuccess'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][retryMIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getIdaAssignCargoName(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getIdaAssignCargoName(optional, employeeId);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getIdaAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);

      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMicAssignCargoName(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getMicAssignCargoName(optional, employeeId);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getMicAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async idaAssignCargoName(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs } = value;
      const data: IResponze = await importClearanceService.idaAssignCargoName(HAWBs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][idaAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async micAssignCargoName(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs } = value;
      const data: IResponze = await importClearanceService.micAssignCargoName(HAWBs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][micAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async update(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.create.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let messages: string[] = errorValidate.handleError();
        return HttpResponse.sendValidate(res, httpStatus.BAD_REQUEST, messages);
      }
      const importClearanceService = new ImportClearanceService();
      const createImport: IImportValidate = value;
      const { HAWB } = req.params;
      const data: IResponze = await importClearanceService.updateImport(HAWB, createImport);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async returnCargo(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.returnCargo.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, employeeId } = value;
      const data: IResponze = await importClearanceService.returnCargo(HAWBs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][update_master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const data: IResponze = await importClearanceService.getAllMICIDA(optional);
      const meta = new Meta(limit, optional.getOffset(), data['message']['data']['total'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data']['manifests'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getAllMICIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async importReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.importReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][importReport]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getStoreWarehouse(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getStoreWarehouse(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][getStoreWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async deleteWarehouse(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.HAWB.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWB, address } = value;
      const data: IResponze = await importClearanceService.deleteWarehouse(HAWB, address, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][deleteWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async saveWarehouse(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.storeWarehouse.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWB, address, date } = value;
      const data: IResponze = await importClearanceService.saveWarehouse(HAWB, address, employeeId, hubs, date);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][storeWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getHoldReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getHoldReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async holdChangeClassify(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.changeClassify.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, classify } = value;
      const data: IResponze = await importClearanceService.holdChangeClassify(HAWBs, classify, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][change_IDA_to_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateCOM(req: Request, res: Response, next: NextFunction) {
    try {
      let { error, value } = ImportClearanceValidate.updateCOM.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const { HAWB } = req.params;
      value = Utilities.convertNull(value);
      const data: IResponze = await importClearanceService.updateIDA(HAWB, value, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][update_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getHoldManifest(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getHoldManifest(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateHoldManifest(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.arrHoldManifest.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const { holdData, isRemoveMAWB } = value;
      const data: IResponze = await importClearanceService.holdManifest(Utilities.convertNull(holdData), isRemoveMAWB, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][update_hold_manifest]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReportExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReportExport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filterIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.filterIDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), data['message']['data']['totalManifest'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][filer_ida]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filterMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.filterMIC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), data['message']['data']['totalManifest'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][filer_mic]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async allMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getAllMIC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][all_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async allIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getAllIDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][all_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async monitorGate(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.HAWB.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWB } = value;
      const data: IResponze = await importClearanceService.monitorGate(HAWB, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][monitor_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerIDE(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await importClearanceService.registerIDE(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][register_IDE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerIIDA(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await importClearanceService.registerIIDA(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][register_IIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerIDC(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await importClearanceService.registerIDC(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][register_IDC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerIDA(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await importClearanceService.registerIDA(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][register_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerMIE(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await importClearanceService.registerMIE(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][register_MIE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerMIC(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await importClearanceService.registerMIC(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][register_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateIDA(req: Request, res: Response, next: NextFunction) {
    try {
      let { error, value } = ImportClearanceValidate.updateIDA.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const { HAWB } = req.params;
      value = Utilities.convertNull(value);
      const data: IResponze = await importClearanceService.updateIDA(HAWB, value, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][update_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateMIC(req: Request, res: Response, next: NextFunction) {
    try {
      let { error, value } = ImportClearanceValidate.updateMIC.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const { HAWB } = req.params;
      value = Utilities.convertNull(value);
      const data: IResponze = await importClearanceService.updateMIC(HAWB, value, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][update_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async resetIDA(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs } = value;
      const data: IResponze = await importClearanceService.resetIDA(HAWBs, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][reset_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async resetMIC(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs } = value;
      const data: IResponze = await importClearanceService.resetMIC(HAWBs, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][reset_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async messageTax(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.messageTax.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, status } = value;
      const data: IResponze = await importClearanceService.messageTax(HAWBs, status, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][message_tax]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async changeMICToIDA(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.changeClassify.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, classify } = value;
      const data: IResponze = await importClearanceService.changeMICToIDA(HAWBs, classify, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][change_MIC_to_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }


  private async changeIDAToMIC(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.changeClassify.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWBs, classify } = value;
      const data: IResponze = await importClearanceService.changeIDAToMIC(HAWBs, classify, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][change_IDA_to_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAllMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const [data, total] = await importClearanceService.getAllMIC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_all_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAllIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getAllIDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_all_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: any = await importClearanceService.getOneMIC(HAWB, optional);
      let status: boolean = false;
      if (data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_one_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: any = await importClearanceService.getOneIDA(HAWB, optional);
      let status: boolean = false;
      if (data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_one_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateMaster(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.arrMaster.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.updateMaster(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][update_master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async create(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.createImport(req.body);
      if (data['statusCode'] === httpStatus.BAD_REQUEST) {
        return HttpResponse.sendValidate(res, data['statusCode'], data['message']['error']);
      }
      if (data['statusCode'] === httpStatus.OK) {
        return HttpResponse.sendData(res, data['statusCode'], new HttpData(data['status'], data['message']));
      }
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReportCount(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReportCount(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report_count]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async master(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.master(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async managementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.managementGate(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][management_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async importExcel(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.createImportExcel(req.body);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][importExcel][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default ImportClearanceController;
