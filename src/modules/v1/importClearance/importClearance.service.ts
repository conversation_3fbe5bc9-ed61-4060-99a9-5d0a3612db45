'use strict';

import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import * as pdf from 'html-pdf';
import { Database } from '../../../database/index';
import ImportDetailRepository from "../importClearanceDetail/importClearanceDetail.reporsitory";
import { ClassifyName, ClassifyKey, ClassifyValidateName, ClassifyValidateKey } from "../../../emuns/classify";
import { IMIC, IMICCreate, IImportValidate, IMICMaster, MIC, IImportExcelValidate } from "../../../models/mic.model";
import { ActionKey } from "../../../emuns/action";
import { IResponze } from "../../../https/responze";
import { IImportDetailCreate, IDetailValidate, IImportDetail, ImportDetail } from "../../../models/importDetail.model";
import { WeightName, WeightKey, WeightClearance } from "../../../emuns/weight";
import Utilities from "../../../util/utilities";
import { ICountry, IHSCodeDetail, Country, CustomerPersonal, CustomerBusiness, IWarehouse, IMICMasterValidate, Hold, User, Order, IExportDetail, MEC, EDA, HistoryInvoice } from "../../../models/index.model";
import MICRepository from "./MICClearance.reporsitory";
import IDARepository from './IDAClearance.repository';
import { IDA, IIDACreate, IIDA, IIDAMaster } from '../../../models/ida.model';
import HSCodeDetailRepository from '../hscodeDetail/hscodeDetail.reporsitory';
import { Clearance } from '../../../xmlClearance/clearance';
import { ClearanceTypeId } from '../../../xmlClearance/enum';
import ExchangeRateRepository from '../exchangeRate/exchangeRate.reporsitory';
import { IExchangeRate } from '../../../models/exchangeRate.model';
import HSCodeRepository from '../hscodeDetail/hscodeDetail.reporsitory';
import ImportClearanceDetailService from '../importClearanceDetail/importClearanceDetail.service';
import moment from 'moment';
import { ICreateAction, IUpdateAction, IChangeClassify, IMessageTax, IMaster, IReset, IRegister, IHoldTransaction, IWarehouseTransaction, INoteHold, IReturnCargo, IExchangeRateTransaction } from '../../../models/importTransaction.model';
import ImportTransactionRepository from '../importTransaction/importTransaction.reporsitory';
import OrderBy from '../../../parser/orderBy';
import _ from 'lodash';
import UserRepository from '../user/user.reporsitory';
import HubRepository from '../hub/hub.reporsitory';
import ImportClearanceValidate from './importClearance.validate';
import ErrorValidate from '../../../util/error.validate';
import ClearanceCreateLogMongo from '../../../models/mongo/clearanceCreateLog.model';
import { ClearanceCreateKey } from '../../../emuns/clearanceCreate';
import ClearanceCreateLogRepository from '../clearanceCreateLog/clearanceCreateLog.reporsitory';
import httpStatus from 'http-status';
import { RabbitSendInternal } from '../../../util/rabbit';
import HoldRepository from '../hold/hold.reporsitory';
import WarehouseRepository from '../warehouse/warehouse.reporsitory';
import ShipmentRepository from '../shipment/shipment.reporsitory';
import MECRepository from '../exportClearance/MECClearance.reporsitory';
import EDARepository from '../exportClearance/EDAClearance.reporsitory';
import HistoryMonitorRepository from '../historyMonitor/historyMonitor.reporsitory';
import ejs from 'ejs';
import * as path from 'path';
import { PrintKey } from '../../../emuns/print';
import Config from '../../../util/configure';
import HistoryInvoiceService from '../historyInvoice/historyInvoice.service';
import Configure from '../../../emuns/configures';
const ExcelJS = require('exceljs');

interface IClearanceCreateLog {
  clearanceCreateLogId?: number;
  data?: any;
  message?: string;
  cause?: any;
  processResult?: any;
  handleData?: any;
  type?: number;
  classify?: any;
  isSuccess?: boolean;
}

interface IStoreWarehouse {
  HAWB?: string;
  MAWB?: string;
  HAWBClearance?: string;
  warehouseAddress?: string;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
}

interface IManagement {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: number;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  bussiness?: CustomerBusiness;
  personal?: CustomerPersonal;
  importDetails?: ImportDetail[];
  isHold?: boolean;
  reasonHold?: Hold[];
  clientName?: string;
  dateGate?: string;
  weightRound?: number;
}

interface ICheckout {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  type?: string;
  isHold?: any;
  externalBoxName?: string;
  details?: IDetail[];
}

interface IDetail {
  itemNameVN: string;
  quantity: number;
}

interface IImportReport {
  trackingNo: any;
  MAWB: any;
  origin: any;
  country: any;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: any;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: any;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: any;
  consignorName: any
  consignorAddress: any;
  consignorCityName: any;
  consignorContactName: any;
  consignorTelephone: any;
  consigneeName: any;
  consigneeAddress: any;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: any;
  consigneeTelephone: any;
  remarks: any;
  status?: any;
  statusId: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;

  customerBusiness?: any;
  customerPersonal?: any;
  dateClearanced?: any;
  orderNumber?: any;
  itemIds?: any;
  itemCount?: any;
  station?: any;
  importerCode?: any;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
  order?: Order;
  taxNumberCode?: any;
  taxNumberDate?: any;

  details?: any;
  externalBoxName?: any;
}

interface IExportReport {
  trackingNo: string;
  MAWB: string;
  origin: string;
  country: string;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: string;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: number;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: string;
  consignorName: string
  consignorAddress: string;
  consignorCityName: any;
  consignorContactName: string;
  consignorTelephone: any;
  consigneeName: string;
  consigneeAddress: string;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: string;
  consigneeTelephone: string;
  remarks: any;
  status?: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;
  taxNumberCode?: any;
  taxNumberDate?: any;
  externalBoxName?: any;
}

interface IHold {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IImportDetail[];
  reasonDetails?: Hold[];
}

interface IGetAll {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IImportDetail[];
  inspectionKindTimes?: any;
  clearanceDeclarationTimes?: any;
  reasonDetails?: Hold[];
  isHold?: any;
  externalBoxName?: any;
  internalBoxName?: any;
  warehouseAddress?: any
}

interface IWebHookRequest {
  'orderIds': string[],
  'action': number,
  'clientId': number,
  'reasonDetailId': number | null,
  'stationOriginId': number | null,
  'stationDestinationId': number | null,
  'holdClearance': any | null
}

interface IMAWBReport {
  HAWB: string,
  HAWBClearance?: string,
  piece: number,
  weight: number,
  invoicePrice: number,
  invoiceCurrency: string,
  consignorFullName: string,
  consignorAddress: string,
  consigneeName: string,
  consigneeAddress: string,
  details: IImportDetail[] | IExportDetail[],
}

class ImportClearanceService extends BaseService {
  private sequelize: any;
  private micRepository: MICRepository;
  private idaRepository: IDARepository;
  private mecRepository: MECRepository;
  private edaRepository: EDARepository;
  private importDetailRepository: ImportDetailRepository;
  private hscodeDetailRepository: HSCodeDetailRepository;
  private hscodeRepository: HSCodeRepository;
  private importTransactionRepository: ImportTransactionRepository;
  private holdRepository: HoldRepository;
  private shipmentRepository: ShipmentRepository;
  private historyMonitorRepository: HistoryMonitorRepository;
  private historyInvoiceService: HistoryInvoiceService;
  private _sequelize: any;
  constructor() {
    super(new MICRepository());
    this.sequelize = Database.Sequelize;
    this.micRepository = new MICRepository();
    this.idaRepository = new IDARepository();
    this.mecRepository = new MECRepository();
    this.edaRepository = new EDARepository();
    this.importDetailRepository = new ImportDetailRepository();
    this.hscodeDetailRepository = new HSCodeDetailRepository();
    this.hscodeRepository = new HSCodeRepository();
    this.importTransactionRepository = new ImportTransactionRepository();
    this.holdRepository = new HoldRepository();
    this.shipmentRepository = new ShipmentRepository();
    this.historyMonitorRepository = new HistoryMonitorRepository();
    this.historyInvoiceService = new HistoryInvoiceService();
    this._sequelize = Database.Sequelize;
  }

  public async externalBoxes(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const masterExternalOptional: Optional = new Optional();
      masterExternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterExternalOptional.setAttributes(['externalBoxName']);
      masterExternalOptional.setGroup(['externalBoxName']);

      const masterInternalOptional: Optional = new Optional();
      masterInternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterInternalOptional.setAttributes(['internalBoxName']);
      masterInternalOptional.setGroup(['internalBoxName']);
      // ida
      const IDAExternalQuery = this.idaRepository.queryAll(masterExternalOptional);
      // mic
      const MICExternalQuery = this.micRepository.queryAll(masterExternalOptional);
      // ida
      const IDAInternalQuery = this.idaRepository.queryAll(masterInternalOptional);
      // mic
      const MICInternalQuery = this.micRepository.queryAll(masterInternalOptional);

      const [IDAExs, MICExs, IDAIns, MICIns] = await Promise.all([IDAExternalQuery, MICExternalQuery, IDAInternalQuery, MICInternalQuery]);
      if (IDAExs.length > 0 || MICExs.length > 0 || IDAIns.length > 0 || MICIns.length > 0) {
        let externalBoxes: any[] = [];
        externalBoxes = [...IDAExs, ...MICExs];
        if (externalBoxes.length > 0) {
          externalBoxes = [...new Set(externalBoxes.map((item: any) => item['externalBoxName']))];
        }

        let internalBoxes: any[] = [];
        internalBoxes = [...IDAExs, ...MICExs];
        if (externalBoxes.length > 0) {
          internalBoxes = [...new Set(internalBoxes.map((item: any) => item['internalBoxName']))];
        }

        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { externalBoxes, internalBoxes };
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][externalBoxes]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async reportTax(optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(
        new Where(EConfigure.AND, 'taxCodeNumber', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'dateTaxCodeNumber', EConfigure.NOT, null),
      );
      const objData = await this.idaRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][reportTax]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMIC(optional: Optional): Promise<any> {
    try {
      const objData = await this.micRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getAllMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllIDA(optional: Optional): Promise<any> {
    try {
      const objData = await this.idaRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getAllIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneMIC(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.micRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getOneMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneIDA(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.idaRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getOneIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async createImport(manifestImports: IImportValidate[]): Promise<any> {
    let transaction: any;
    let reponze: IResponze = {
      'status': false,
      'statusCode': httpStatus.OK,
      'message': {
        'message': EMessage.CREATE_FAIL,
        'data': {
          'fail': [],
          'succesfull': [],
        }
      }
    }
    let { error, value } = ImportClearanceValidate.arrCreate.validate(manifestImports, { abortEarly: false, stripUnknown: true });
    if (error) {
      let errorValidate: ErrorValidate = new ErrorValidate(error);
      let messages: string[] = errorValidate.handleError();
      reponze['statusCode'] = httpStatus.BAD_REQUEST;
      reponze['message']['error'] = messages;
      return reponze;
    }
    try {
      value = Utilities.convertNull(value);
      let HAWBs: string[] = [];
      let HAWBClearances: string[] = [];
      let processingHAWBs: string[] = [];
      let processingHAWBClearances: string[] = [];
      let storeDetail: Map<string, ImportDetail[]> = new Map()
      HAWBs = value.map((manifest: any) => {
        return manifest[EConfigure.HAWB_FIELD];
      });
      const processOptional: Optional = new Optional();
      processOptional.setAttributes([EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD]);
      processOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      const detailOptional: Optional = new Optional();
      detailOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
      ]);
      detailOptional.setOrderby([
        new OrderBy('position', EConfigure.ASCENDING)
      ])
      const micQuery: any = this.micRepository.queryAll(processOptional);
      const idaQuery: any = this.idaRepository.queryAll(processOptional);
      const detailQuery: any = this.importDetailRepository.queryAll(detailOptional);
      const [mics, idas, details] = await Promise.all([micQuery, idaQuery, detailQuery]);
      if (mics.length > 0) {
        mics.forEach((mic: IMIC) => {
          processingHAWBs.push(mic[EConfigure.HAWB_FIELD] || '');
          processingHAWBClearances.push(mic[EConfigure.HAWB_CLEARANCE_FIELD] || '');
        });
      }
      if (idas.length > 0) {
        idas.forEach((ida: IDA) => {
          processingHAWBs.push(ida[EConfigure.HAWB_FIELD] || '');
          processingHAWBClearances.push(ida[EConfigure.HAWB_CLEARANCE_FIELD] || '');
        });
      }
      if (details.length > 0) {
        details.forEach((detail: ImportDetail) => {
          const HAWB: string = detail[EConfigure.HAWB_FIELD];
          if (!storeDetail.has(HAWB)) {
            storeDetail.set(HAWB, [detail]);
          } else {
            const store: any = storeDetail.get(HAWB);
            store.push(detail);
            storeDetail.set(HAWB, store);
          }
        });
      }
      HAWBs = [];
      const dataMICs: IMICCreate[] = [];
      const dataIDAs: IIDACreate[] = [];
      const dataDetails: any[] = [];
      let micData: any;
      let idaData: any;
      let detailData: IImportDetailCreate;
      const actionCreate: number = ActionKey.CREATE;
      let createTransactions: ICreateAction[] = [];
      for (const manifest of manifestImports) {
        const cloneManifest: IImportValidate = { ...manifest };
        const orderTypeId: number = (!cloneManifest['orderTypeId'] || (cloneManifest['orderTypeId'] && cloneManifest['orderTypeId'] < EConfigure.INDEX_4)) ? EConfigure.INDEX_0 : cloneManifest['orderTypeId'];
        const HAWB: string = cloneManifest[EConfigure.HAWB_FIELD] || '';
        const HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD];
        if (!processingHAWBs.includes(HAWB)) {
          HAWBs.push(HAWB);
          HAWBClearances.push(HAWBClearance);
          let warehouse: IWarehouse = {};
          const valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
          let totalPrice: number = Number(cloneManifest['totalPrice']);
          const priceVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
          const countryCode: string = cloneManifest['consignorCountryCode'];
          const stationId: number = cloneManifest['stationId'];
          if (cloneManifest['warehouseId'] && orderTypeId < EConfigure.INDEX_4) {
            warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
          }
          if (!cloneManifest['warehouseId'] && orderTypeId >= EConfigure.INDEX_4 && cloneManifest['hubId']) {
            warehouse = await Utilities.getWarehouseByHub(cloneManifest['hubId'], orderTypeId);
          }
          const country: ICountry = await Utilities.getCountryByCode(countryCode);
          let declareCargoNameMIC: boolean = false;
          let declareCargoNameIDA: boolean = false;
          let details: IDetailValidate[] = [];
          let classifyDOC: boolean = false;
          let hasMO: boolean = false;
          if (manifest['items'].length > 0) {
            details = manifest['items'];
            details.forEach((detail: IDetailValidate, index: number) => {
              detailData = {};
              if (detail['HSCode'] && (+(detail['HSCode'].substring(0, 2)) == 85 || +(detail['HSCode'].substring(0, 2)) == 84)) {
                hasMO = true;
              }
              const cloneDetail: IDetailValidate = { ...detail };
              const itemNameEN: string = cloneDetail['itemName'] || '';
              // const checkName: string = itemNameEN;
              // if(checkName && checkName.toUpperCase().includes("DOC")) {
              //   classifyDOC = true;
              // }
              const totalPrice: number = Number(cloneDetail['invoiceValue']);
              const priceDetailVND: number = +(totalPrice * valueClearanceVND).toFixed(0);
              const currencyCode: string = cloneDetail['currencyCode'];
              detailData['HSCode'] = cloneDetail['HSCode'];
              detailData[EConfigure.HAWB_FIELD] = HAWB;
              detailData['itemName'] = itemNameEN;
              detailData['itemNameVN'] = detail['itemNameVN'];
              if (storeDetail.has(HAWB)) {
                const currentDetail: any = storeDetail.get(HAWB);
                if (currentDetail[index]) {
                  detailData['HSCode'] = currentDetail[index]['HSCode'];
                  detailData['itemNameVN'] = currentDetail[index]['itemNameVN'];
                  detailData['quantity2'] = currentDetail[index]['quantity2'];
                  detailData['quantityUnitCode2'] = currentDetail[index]['quantityUnitCode2'];
                }
              }
              if (detail['placeOfOrigin']) {
                detailData['placeOfOrigin'] = detail['placeOfOrigin'];
              } else {
                detailData['placeOfOrigin'] = countryCode;
                if (country) {
                  detailData['originalPlaceName'] = country['shortName'];
                }
              }
              if (currencyCode === EConfigure.CURRENCY_VND) {
                detailData['invoiceValue'] = +(totalPrice.toFixed(0));
                detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
              } else {
                detailData['invoiceValue'] = totalPrice;
                detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
              }
              detailData['priceVND'] = priceDetailVND;
              detailData['unitPriceCurrencyCode'] = currencyCode;
              if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
                detailData['weightKG'] = cloneManifest['weight'];
              } else {
                detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
              }
              detailData['url'] = cloneDetail['url'];
              detailData['originalUrl'] = cloneDetail['originalUrl'];
              detailData['originalProductName'] = cloneDetail['originalProductName'] ? Utilities.removeSpeialCharacter(cloneDetail['originalProductName']) : null;
              detailData['productId'] = detail['productId'];
              detailData['quantity1'] = cloneDetail['quantity'];
              detailData['quantity2'] = cloneDetail['quantity'];
              detailData['quantityUnitCode1'] = EConfigure.PIECE;
              detailData['quantityUnitCode2'] = EConfigure.PIECE;
              detailData['priceQuantityUnit'] = EConfigure.PIECE;
              detailData['quantityUnitPrice'] = EConfigure.PIECE;
              detailData['importTaxCode'] = EConfigure.IMPORT_TAX_INCENTIVES;
              detailData['VATTaxCode'] = EConfigure.VAT_TAX_INCENTIVES_VB215;
              detailData['position'] = index;
              dataDetails.push(detailData);
            });
          }
          if (cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND < EConfigure.INDEX_1000000) {
            if (dataDetails.length > 0) {
              declareCargoNameMIC = dataDetails.every((detail: IDetailValidate) => {
                return detail['itemNameVN'];
              });
            }
            micData = {
              'HAWB': HAWB,
              'HAWBClearance': HAWBClearance,
              'stationId': stationId,
              'phase': actionCreate,
              'serviceId': cloneManifest['serviceId'],
              'identity': cloneManifest['identity'],
              'threadCode': cloneManifest['threadCode'],
              'threadName': cloneManifest['threadName'],
              'threadColor': cloneManifest['threadColor'],
              'threadUrl': cloneManifest['threadUrl'],
              'orderTypeId': orderTypeId
            };
            if (declareCargoNameMIC) {
              micData['phase'] = ActionKey.UPDATE_CARGO_NAME;
            }
            if (!cloneManifest['importerCode']) {
              micData['importerCode'] = EConfigure.IMPORTER_CODE as string;
            } else {
              micData['importerCode'] = cloneManifest['importerCode'];
            }
            if (!cloneManifest['importerName']) {
              micData['importerName'] = EConfigure.IMPORTER_NAME
            } else {
              micData['importerName'] = cloneManifest['importerName'];
              micData['importerFullName'] = cloneManifest['importerName'];
            }
            micData['postCode'] = cloneManifest['importerPostCode'];
            micData['addressOfImporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['importerAddress'] as string), 0, 200);
            if (cloneManifest['importerTelephoneNumber']) {
              micData['telephoneNumberOfImporter'] = Utilities.subString(cloneManifest['importerTelephoneNumber'], 0, 20);
            }
            micData['consignorCode'] = cloneManifest['consignorCode'];
            micData['consignorName'] = cloneManifest['consignorName'];
            const addresses = Utilities.handleAddress(cloneManifest['consignorAddress'] as string);
            if (addresses) {
              for (const [key, value] of Object.entries(addresses)) {
                if (key <= '3') {
                  micData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
                }
              }
            }
            if (country) {
              micData['address4'] = country['fullName'];
            }
            micData['countryCode'] = countryCode;
            micData['loadingLocationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
            micData['MAWB'] = cloneManifest['MAWB'];
            micData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
            if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
              micData['cargoWeight'] = cloneManifest['weight'];
            } else {
              micData['cargoWeight'] = Utilities.convertKG(cloneManifest['weight'], cloneManifest['unitOfMass']);
            }
            micData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
            if (warehouse && Object.keys(warehouse).length > 0) {
              micData['customsWarehouseCode'] = warehouse['code'];
              micData['unloadingPort'] = warehouse['unloadingPortCode'];
              micData['customsOffice'] = warehouse['customsOffice'];
              micData['customsSubSection'] = warehouse['customsSubSection'];
              micData['terminalName'] = warehouse['id'];
              micData['classificationOfIndividualOrganization'] = warehouse['individualOrganization'];
            }
            if (cloneManifest['arrivalDate'] && cloneManifest['flightNo']) {
              micData['flightNo'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['arrivalDate']);
            } else {
              micData['flightNo'] = cloneManifest['flightNo'];
            }
            micData['arrivalDate'] = cloneManifest['arrivalDate'];
            micData['hubId'] = cloneManifest['hubId'];
            micData['invoicePriceKind'] = EConfigure.TYPE_A;
            micData['clientId'] = cloneManifest['clientId'];
            micData['invoicePriceCondition'] = EConfigure.DDP;
            if (micData['clientId'] == EConfigure.INDEX_1 || micData['clientId'] == EConfigure.INDEX_2) {
              micData['invoicePriceCondition'] = EConfigure.CIF;
            }
            micData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
            if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
              micData['totalInvoicePrice'] = +(totalPrice.toFixed(0));
            } else {
              micData['totalInvoicePrice'] = totalPrice;
            }
            micData['valueClearanceVND'] = valueClearanceVND;
            micData['customerBusinessId'] = cloneManifest['customerBusinessId'];
            micData['customerPersonalId'] = cloneManifest['customerPersonalId'];
            micData['notes'] = cloneManifest['note'];
            micData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
            micData['priceVND'] = priceVND;
            micData['orderId'] = cloneManifest['orderId'];
            micData['originalPrice'] = cloneManifest['originalPrice'];
            micData['currencyOriginalPrice'] = cloneManifest['currencyOriginalPrice'];
            micData['internalBoxName'] = cloneManifest['internalBoxName'];
            micData['externalBoxName'] = cloneManifest['externalBoxName'];
            micData['childOrderNumber'] = cloneManifest['childOrderNumber'];
            micData['orderNumber'] = cloneManifest['orderNumber'];
            micData['labelCustomer'] = cloneManifest['labelCustomer'];
            micData['invoiceCustomer'] = cloneManifest['invoiceCustomer'];
            if (totalPrice === 0 || (totalPrice >= 0 && classifyDOC)) {
              micData['classify'] = ClassifyName.get(ClassifyKey.DOC);
            } else {
              micData['classify'] = cloneManifest['classify'];
            }
            dataMICs.push(micData);
            //Create Transaction
            const micTraction: ICreateAction = {
              'HAWB': HAWB,
              'action': actionCreate,
              'classify': ClassifyValidateName.get(ClassifyValidateKey.MIC) as string,
              'currentClassify': micData['classify'],
              'data': micData,
              'newData': micData,
              'stationId': stationId,
            }
            createTransactions.push(micTraction);
          } else {
            if (dataDetails.length > 0) {
              declareCargoNameIDA = dataDetails.every((detail: IDetailValidate) => {
                return detail['itemNameVN'] && detail['HSCode'] && detail['HSCode'].match(/^0+/) === null && detail['HSCode'].match(/^[0-9]+$/) !== null;
              });
            }
            idaData = {
              'HAWB': HAWB,
              'HAWBClearance': HAWBClearance,
              'cargoNo': HAWBClearance,
              'stationId': stationId,
              'phase': actionCreate,
              'serviceId': cloneManifest['serviceId'],
              'identity': cloneManifest['identity'],
              'threadCode': cloneManifest['threadCode'],
              'threadName': cloneManifest['threadName'],
              'threadColor': cloneManifest['threadColor'],
              'threadUrl': cloneManifest['threadUrl'],
              'orderTypeId': orderTypeId
            };
            idaData['hubId'] = cloneManifest['hubId'];
            if (warehouse != null && warehouse['isGenerateInvoice']) {
              const taxURL = process.env.TAX_URL || 'https://devapi.globex.vn/taxcode-sync/api/';
              const no = await Utilities.callAPINonAuth(`${taxURL}invoice_number_sync`);
              // const no: number = await Utilities.getCurrentSequence(EConfigure.V5_INVOICE);
              // await Utilities.addOnSequence(no, EConfigure.INDEX_1, EConfigure.V5_INVOICE);
              idaData['invoiceNo'] = Utilities.padWithZeroes(Number(no), EConfigure.INDEX_7);
              idaData['invoiceDate'] = cloneManifest['invoiceDate'];
            }
            if (declareCargoNameIDA) {
              idaData['phase'] = ActionKey.UPDATE_CARGO_NAME;
            }
            idaData['MAWB'] = cloneManifest['MAWB'];
            idaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
            if (!cloneManifest['importerCode']) {
              idaData['importerCode'] = EConfigure.IMPORTER_CODE as string;
            } else {
              idaData['importerCode'] = cloneManifest['importerCode'];
            }
            if (!cloneManifest['importerName']) {
              idaData['importerName'] = EConfigure.IMPORTER_NAME;
            } else {
              idaData['importerName'] = cloneManifest['importerName'];
              idaData['importerFullName'] = cloneManifest['importerName'];
            }
            idaData['postCode'] = cloneManifest['importerPostCode'];
            idaData['addressOfImporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['importerAddress'] as string), 0, 200);
            if (cloneManifest['importerTelephoneNumber']) {
              idaData['telephoneNumberOfImporter'] = Utilities.subString(cloneManifest['importerTelephoneNumber'], 0, 20);
            }
            idaData['consignorCode'] = cloneManifest['consignorCode'];
            idaData['consignorName'] = cloneManifest['consignorName'];
            const addresses = Utilities.handleAddress(cloneManifest['consignorAddress'] as string);
            if (addresses) {
              for (const [key, value] of Object.entries(addresses)) {
                if (key <= '3') {
                  idaData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
                }
              }
            }
            if (country) {
              idaData['address4'] = country['fullName'];
            }
            idaData['countryCode'] = countryCode;
            idaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT;
            if (cloneManifest['meanOfTransportationCode']) {
              idaData['meansOfTransportationCode'] = cloneManifest['meanOfTransportationCode'];
            }
            idaData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
            idaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
            idaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
            idaData['codeOfExtendingDueDateForPayment'] = EConfigure.TYPE_D;
            idaData['clientId'] = cloneManifest['clientId'];
            idaData['termOfPayment'] = EConfigure.KHONGTT;
            idaData['invoicePriceConditionCode'] = EConfigure.DDP;
            if (idaData['clientId'] == EConfigure.INDEX_1 || idaData['clientId'] == EConfigure.INDEX_2) {
              idaData['termOfPayment'] = EConfigure.KC;
              idaData['invoicePriceConditionCode'] = EConfigure.CIF;
            }
            idaData['declarationKindCode'] = EConfigure.H11;
            idaData['arrivalDate'] = cloneManifest['arrivalDate'];
            if (warehouse && Object.keys(warehouse).length > 0) {
              if (orderTypeId >= 4) {
                idaData['declarationKindCode'] = warehouse['kindleCode'];
                idaData['arrivalDate'] = moment().format(EConfigure.DAY_TIME);
              }
              idaData['customsWarehouseCode'] = warehouse['code'];
              idaData['plannedDeclarantCode'] = warehouse['agencyCode'];
              idaData['unloadingPortCode'] = warehouse['unloadingPortCode'];
              idaData['unloadingPortName'] = warehouse['unloadingPortCode'];
              idaData['customsOffice'] = warehouse['customsOffice'];
              idaData['customsSubSection'] = warehouse['customsSubSection'];
              idaData['feeClearance'] = warehouse['feePrice'];
              idaData['terminalName'] = warehouse['id'];
              idaData['classificationOfIndividualOrganization'] = warehouse['individualOrganization'];
            }
            if (cloneManifest['arrivalDate'] && cloneManifest['flightNo']) {
              idaData['loadingVesselAircraftName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['arrivalDate']);
            } else {
              idaData['loadingVesselAircraftName'] = cloneManifest['flightNo'];
            }
            idaData['loadingLocationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
            idaData['loadingLocationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
            if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
              idaData['cargoWeightGross'] = cloneManifest['weight'];
            } else {
              idaData['cargoWeightGross'] = Utilities.convertKG(cloneManifest['weight'], cloneManifest['unitOfMass']);
            }
            idaData['weightUnitCodeGross'] = WeightClearance.get('kg');
            idaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
            if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
              totalPrice = +(totalPrice.toFixed(0));
              idaData['totalInvoicePrice'] = totalPrice;
              idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
              idaData['totalOfTaxValue'] = totalPrice;
              idaData['totalOfProportional'] = totalPrice;
            } else {
              idaData['totalInvoicePrice'] = totalPrice;
              idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
              idaData['totalOfTaxValue'] = totalPrice;
              idaData['totalOfProportional'] = totalPrice;
            }
            idaData['valueClearanceVND'] = valueClearanceVND;
            idaData['customerBusinessId'] = cloneManifest['customerBusinessId'];
            idaData['customerPersonalId'] = cloneManifest['customerPersonalId'];
            idaData['notes'] = cloneManifest['note'];
            idaData['classify'] = cloneManifest['classify'];
            idaData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
            idaData['priceVND'] = priceVND;
            idaData['taxPayer'] = EConfigure.TAX_PAYER;
            idaData['invoicePriceKindCode'] = EConfigure.TYPE_A;
            idaData['orderId'] = cloneManifest['orderId'];
            idaData['originalPrice'] = cloneManifest['originalPrice'];
            idaData['currencyOriginalPrice'] = cloneManifest['currencyOriginalPrice'];
            idaData['internalBoxName'] = cloneManifest['internalBoxName'];
            idaData['externalBoxName'] = cloneManifest['externalBoxName'];
            idaData['childOrderNumber'] = cloneManifest['childOrderNumber'];
            idaData['orderNumber'] = cloneManifest['orderNumber'];
            idaData['labelCustomer'] = cloneManifest['labelCustomer'];
            idaData['invoiceCustomer'] = cloneManifest['invoiceCustomer'];
            if (hasMO) {
              idaData['otherLawCode'] = EConfigure.MO;
            }
            dataIDAs.push(idaData);
            const idaTraction: ICreateAction = {
              'HAWB': HAWB,
              'action': actionCreate,
              'classify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
              'currentClassify': idaData['classify'],
              'data': idaData,
              'newData': idaData,
              'stationId': stationId
            }
            createTransactions.push(idaTraction);
          }
        }
      };
      if (dataMICs.length > 0 || dataIDAs.length > 0) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.NOT_IN, processingHAWBs.join())
        ]);
        try {
          transaction = await this._sequelize.transaction();
          await Promise.all([
            this.micRepository.destroyDataTrx(optional, transaction),
            this.idaRepository.destroyDataTrx(optional, transaction),
            this.importDetailRepository.destroyDataTrx(optional, transaction)
          ]);
          const [micCreated, idaCreated, detailCreated] = await Promise.all([
            this.micRepository.createBulkTrx(dataMICs, transaction),
            this.idaRepository.createBulkTrx(dataIDAs, transaction),
            this.importDetailRepository.createBulkTrx(dataDetails, transaction),
            this.importTransactionRepository.createBulkTrx(createTransactions, transaction),
          ]);
          await transaction.commit();
          if (micCreated.length > 0 || idaCreated.length > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.CREATED_DATA;
            reponze['message']['data'] = {
              'succesfull': HAWBClearances,
              'fail': processingHAWBClearances
            }
          }
        } catch (error) {
          await transaction.rollback();
          console.log('---- create imports error: %o', error);
          reponze['message']['data']['fail'].push(...HAWBClearances, ...processingHAWBClearances)
        }
      } else {
        reponze['message']['data']['fail'].push(...HAWBClearances, ...processingHAWBClearances)
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][createImport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateMaster(manifestMasters: any[]): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      let failHAWBs: string[] = [];
      let successHAWBs: string[] = [];
      transaction = await this._sequelize.transaction();
      await Promise.all(manifestMasters.map(async (manifestMaster: any) => {
        const cloneMaster: IMICMasterValidate = { ...manifestMaster };
        const HAWB: string = manifestMaster[EConfigure.HAWB_FIELD];
        const HAWBClearance: string = manifestMaster[EConfigure.HAWB_CLEARANCE_FIELD];
        const MAWB: any = cloneMaster['MAWB'] ? String(cloneMaster['MAWB']) : null;
        let warehouse: IWarehouse = {};
        const arrivalDate: any = cloneMaster['arrivalDate'] ? String(cloneMaster['arrivalDate']) : null;
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWBClearance),
          new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.IN, `${ActionKey.CREATE}, ${ActionKey.UPDATE_CARGO_NAME}`),
          new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ]);
        let flightCode: any = null;
        if (cloneMaster['arrivalDate']) {
          flightCode = Utilities.flightCode(String(cloneMaster['flightNo']), String(cloneMaster['arrivalDate']));
        }
        if (cloneMaster['warehouseId']) {
          warehouse = await Utilities.getWarehouse(Number(cloneMaster['warehouseId']));
        }
        const hubId: any = cloneMaster['hubId'];
        const micUpdate: IMICMaster = {
          'MAWB': MAWB,
          'arrivalDate': arrivalDate,
          'flightNo': flightCode,
          'hubId': hubId
        }
        const idaUpdate: IIDAMaster = {
          'MAWB': MAWB,
          'arrivalDate': arrivalDate,
          'loadingVesselAircraftName': flightCode,
          'hubId': hubId
        }
        if (cloneMaster['meanOfTransportationCode']) {
          idaUpdate['meansOfTransportationCode'] = cloneMaster['meanOfTransportationCode'];
        }
        if (warehouse && Object.keys(warehouse).length > 0) {
          //warehouse MIC
          micUpdate['customsWarehouseCode'] = warehouse['code'];
          micUpdate['unloadingPort'] = warehouse['unloadingPortCode'];
          micUpdate['customsOffice'] = warehouse['customsOffice'];
          micUpdate['customsSubSection'] = warehouse['customsSubSection'];
          micUpdate['classificationOfIndividualOrganization'] = Number(warehouse['individualOrganization']);
          micUpdate['terminalName'] = String(warehouse['id']);

          //warehouse IDA
          idaUpdate['customsWarehouseCode'] = String(warehouse['code']);
          idaUpdate['plannedDeclarantCode'] = String(warehouse['agencyCode']);
          idaUpdate['unloadingPortCode'] = String(warehouse['unloadingPortCode']);
          idaUpdate['unloadingPortName'] = String(warehouse['unloadingPortCode']);
          idaUpdate['customsOffice'] = String(warehouse['customsOffice']);
          idaUpdate['customsSubSection'] = String(warehouse['customsSubSection']);
          idaUpdate['feeClearance'] = Number(warehouse['feePrice']);
          idaUpdate['terminalName'] = String(warehouse['id']);
          idaUpdate['classificationOfIndividualOrganization'] = Number(warehouse['individualOrganization']);
        }
        const [mictotal] = await this.micRepository.updateDataTrx(micUpdate, optional, transaction);
        const [idatotal] = await this.idaRepository.updateDataTrx(idaUpdate, optional, transaction);
        if (mictotal > 0 || idatotal > 0) {
          const master: IMaster = {
            'HAWB': HAWBClearance,
            'MAWB': MAWB,
            'action': ActionKey.UPDATE_MASTER,
            'hubId': hubId,
          }
          await this.importTransactionRepository.createDataTrx(master, transaction);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
          successHAWBs.push(HAWBClearance);
        } else {
          reponze['status'] = false;
          failHAWBs.push(HAWBClearance);
        }
      }));
      await transaction.commit();

      reponze['message']['data'] = {
        'succesfull': successHAWBs,
        'fail': failHAWBs
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][updateMaster]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async changeMICToIDA(HAWBs: string[], classify: string, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      const MICOptional: Optional = new Optional();
      MICOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        MICOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const infoMICs: IMIC[] = await this.micRepository.queryAll(MICOptional);
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_CLASSIFY_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      let idaData: IIDACreate;
      let dataIDAs: IIDACreate[] = [];
      let changeTransactions: any[] = [];
      if (infoMICs.length > 0) {
        if (classify === ClassifyValidateName.get(ClassifyValidateKey.DOC) || classify === ClassifyValidateName.get(ClassifyValidateKey.MIC)) {
          await Promise.all(infoMICs.map(async (infoMic: IMIC) => {
            const HAWB: string = String(infoMic[EConfigure.HAWB_FIELD]);
            const HAWBClearance: string = String(infoMic[EConfigure.HAWB_CLEARANCE_FIELD]);
            if (!infoMic['declarationNo'] || !infoMic['inspectionKindClassification'] || infoMic['phase'] !== ActionKey.ACCEPT_CLEARANCE) {
              const classifyName: any = (classify === ClassifyValidateName.get(ClassifyValidateKey.MIC)) ? ClassifyName.get(ClassifyKey.PAR) : ClassifyName.get(ClassifyKey.DOC);
              const updateData: any = {
                'classify': classifyName,
                'isHold': false,
                'reasonIds': null
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB)
              ]);
              const [total] = await this.micRepository.updateData(updateData, updateOptional);
              if (total > 0) {
                const changeTransaction: IChangeClassify = {
                  'HAWB': HAWBClearance,
                  'action': ActionKey.CHANGE_CLASSIFY,
                  'classify': EConfigure.MIC as string,
                  'employeeId': employeeId,
                  'currentClassify': infoMic['classify'] === EConfigure.PAR ? String(EConfigure.MIC) : String(EConfigure.DOC),
                  'changeClassify': classify === EConfigure.MIC ? String(EConfigure.MIC) : String(EConfigure.DOC),
                }
                if (infoMic['isHold']) {
                  const transactionHold: IHoldTransaction = {
                    HAWB: HAWBClearance,
                    action: ActionKey.UN_HOLD,
                    employeeId: employeeId,
                  }
                  changeTransactions.push(transactionHold);
                }
                changeTransactions.push(changeTransaction);
                await this.importTransactionRepository.createBulk(changeTransactions),
                  reponze['message']['data']['succesfull'].push(HAWBClearance);
              } else {
                reponze['message']['data']['fail'].push({
                  'HAWB': HAWBClearance,
                  'cause': EMessage.UPDATE_FAIL
                });
              }
            } else {
              reponze['message']['data']['fail'].push({
                'HAWB': HAWBClearance,
                'cause': `Đang ở trạng thái ${String(infoMic['phase_name']['vi']).toLowerCase()}`
              });
            }
          }));
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
        } else {
          const HAWBClearances: string[] = [];
          const classifyName: any = (classify === ClassifyValidateName.get(ClassifyValidateKey.IDA)) ? ClassifyName.get(ClassifyKey.PAR) : ClassifyName.get(ClassifyKey.COM);
          for (const infoMIC of infoMICs) {
            const cloneInfoMIC: IMIC = infoMIC;
            const HAWB: string = String(cloneInfoMIC[EConfigure.HAWB_FIELD]);
            const HAWBClearance: string = String(cloneInfoMIC[EConfigure.HAWB_CLEARANCE_FIELD]);
            if (!cloneInfoMIC['declarationNo'] || !cloneInfoMIC['inspectionKindClassification'] || cloneInfoMIC['phase'] !== EConfigure.ACCEPTED_CLEARANCE) {
              const hubId: number = Number(cloneInfoMIC['hubId']);
              const warehouse: IWarehouse = await Utilities.getWarehouseByHub(hubId, Number(cloneInfoMIC['orderTypeId']));
              idaData = {
                'HAWB': HAWB,
                'HAWBClearance': HAWBClearance,
                'cargoNo': HAWBClearance,
                'stationId': Number(cloneInfoMIC[EConfigure.STATION_ID_FIELD]),
                'phase': ActionKey.CREATE,
                'serviceId': Number(cloneInfoMIC['serviceId']),
              };
              idaData['declarationNo'] = cloneInfoMIC['declarationNo'];
              idaData['MAWB'] = cloneInfoMIC['MAWB'];


              idaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT;
              idaData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
              idaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;

              idaData['importerCode'] = cloneInfoMIC['importerCode'];
              idaData['importerName'] = cloneInfoMIC['importerName'];
              idaData['importerFullName'] = cloneInfoMIC['importerFullName'];
              idaData['postCode'] = cloneInfoMIC['postCode'];
              idaData['addressOfImporter'] = cloneInfoMIC['addressOfImporter'];
              idaData['telephoneNumberOfImporter'] = cloneInfoMIC['telephoneNumberOfImporter'];
              idaData['consignorName'] = cloneInfoMIC['consignorName'];
              idaData['address1'] = cloneInfoMIC['address1'];
              idaData['address2'] = cloneInfoMIC['address2'];
              idaData['address3'] = cloneInfoMIC['address3'];
              idaData['address4'] = cloneInfoMIC['address4'];
              idaData['countryCode'] = cloneInfoMIC['countryCode'];
              idaData['cargoPiece'] = cloneInfoMIC['cargoPiece'];
              idaData['cargoWeightGross'] = cloneInfoMIC['cargoWeight'];

              idaData['hubId'] = cloneInfoMIC['hubId'];
              idaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
              idaData['invoicePriceKindCode'] = EConfigure.TYPE_A;

              idaData['taxPayer'] = EConfigure.TAX_PAYER;
              idaData['codeOfExtendingDueDateForPayment'] = EConfigure.TYPE_D;

              idaData['clientId'] = cloneInfoMIC['clientId'];
              idaData['termOfPayment'] = EConfigure.KHONGTT;
              idaData['invoicePriceConditionCode'] = EConfigure.DDP;
              if (idaData['clientId'] == EConfigure.INDEX_1 || idaData['clientId'] == EConfigure.INDEX_2) {
                idaData['termOfPayment'] = EConfigure.KC;
                idaData['invoicePriceConditionCode'] = EConfigure.CIF;
              }
              idaData['customerBusinessId'] = cloneInfoMIC['customerBusinessId'];

              idaData['customerPersonalId'] = cloneInfoMIC['customerPersonalId'];
              idaData['originalOrderNumberClient'] = cloneInfoMIC['originalOrderNumberClient'];
              idaData['weightUnitCodeGross'] = WeightClearance.get('kg');

              idaData['orderId'] = cloneInfoMIC['orderId'];
              idaData['declarationKindCode'] = EConfigure.H11;
              if (warehouse) {
                if (Number(cloneInfoMIC['orderTypeId']) >= 4) {
                  idaData['declarationKindCode'] = warehouse['kindleCode'];
                }
                idaData['plannedDeclarantCode'] = warehouse['agencyCode'];
                idaData['feeClearance'] = Number(warehouse['feePrice']);
                idaData['terminalName'] = String(warehouse['id']);
              }

              idaData['customsWarehouseCode'] = cloneInfoMIC['customsWarehouseCode'];
              idaData['unloadingPortCode'] = cloneInfoMIC['unloadingPort'];
              idaData['unloadingPortName'] = cloneInfoMIC['unloadingPort'];

              idaData['loadingLocationCode'] = cloneInfoMIC['loadingLocationCode'];
              idaData['loadingLocationName'] = cloneInfoMIC['loadingLocationCode'];

              idaData['customsOffice'] = cloneInfoMIC['customsOffice'];
              idaData['customsSubSection'] = cloneInfoMIC['customsSubSection'];
              idaData['loadingVesselAircraftName'] = cloneInfoMIC['flightNo'];

              idaData['arrivalDate'] = cloneInfoMIC['arrivalDate'];

              idaData['invoiceCurrencyCode'] = cloneInfoMIC['invoiceCurrencyCode'];
              idaData['totalInvoicePrice'] = cloneInfoMIC['totalInvoicePrice'];
              idaData['totalOfProportionalDistributionOnTaxValue'] = cloneInfoMIC['totalInvoicePrice'];
              idaData['totalOfTaxValue'] = cloneInfoMIC['totalInvoicePrice'];
              idaData['totalOfProportional'] = cloneInfoMIC['totalInvoicePrice'];

              idaData['valueClearanceVND'] = cloneInfoMIC['valueClearanceVND'];
              idaData['notes'] = cloneInfoMIC['notes'];
              idaData['classify'] = classifyName;
              idaData['priceVND'] = cloneInfoMIC['priceVND'];

              idaData['inspectionKindClassification'] = cloneInfoMIC['inspectionKindClassification'];
              idaData['dateCheckin'] = cloneInfoMIC['dateCheckin'];
              idaData['dateClearanced'] = cloneInfoMIC['dateClearanced'];
              idaData['dateCheckout'] = cloneInfoMIC['dateCheckout'];

              idaData['warehouseAddress'] = cloneInfoMIC['warehouseAddress'];
              idaData['warehouseCheckin'] = cloneInfoMIC['warehouseCheckin'];
              idaData['warehouseCheckout'] = cloneInfoMIC['warehouseCheckout'];


              idaData['orderId'] = cloneInfoMIC['orderId'];
              idaData['originalPrice'] = cloneInfoMIC['originalPrice'];
              idaData['currencyOriginalPrice'] = cloneInfoMIC['currencyOriginalPrice'];
              idaData['internalBoxName'] = cloneInfoMIC['internalBoxName'];
              idaData['externalBoxName'] = cloneInfoMIC['externalBoxName'];
              idaData['childOrderNumber'] = cloneInfoMIC['childOrderNumber'];
              idaData['orderNumber'] = cloneInfoMIC['orderNumber'];
              idaData['currentStation'] = cloneInfoMIC['currentStation'];

              idaData['invoiceCustomer'] = cloneInfoMIC['invoiceCustomer'];
              idaData['labelCustomer'] = cloneInfoMIC['labelCustomer'];
              idaData['identity'] = cloneInfoMIC['identity'];

              idaData['threadCode'] = cloneInfoMIC['threadCode'];
              idaData['threadName'] = cloneInfoMIC['threadName'];
              idaData['threadColor'] = cloneInfoMIC['threadColor'];
              idaData['threadUrl'] = cloneInfoMIC['threadUrl'];
              idaData['orderTypeId'] = cloneInfoMIC['orderTypeId'];

              const no: number = await Utilities.getCurrentSequence(EConfigure.V5_INVOICE);
              await Utilities.addOnSequence(no, EConfigure.INDEX_1, EConfigure.V5_INVOICE);

              idaData['invoiceNo'] = Utilities.padWithZeroes(no, EConfigure.INDEX_7);
              idaData['invoiceDate'] = moment().format(EConfigure.DAY_TIME);

              dataIDAs.push(idaData);
              HAWBClearances.push(HAWBClearance);

              const changeTransaction: IChangeClassify = {
                'HAWB': HAWBClearance,
                'action': ActionKey.CHANGE_CLASSIFY,
                'classify': EConfigure.MIC as string,
                'employeeId': employeeId,
                'currentClassify': cloneInfoMIC['classify'] === EConfigure.PAR ? String(EConfigure.MIC) : String(EConfigure.DOC),
                'changeClassify': classify === EConfigure.IDA ? String(EConfigure.IDA) : String(EConfigure.COM),
              }
              if (cloneInfoMIC['isHold']) {
                const transactionHold: IHoldTransaction = {
                  'HAWB': HAWBClearance,
                  'action': ActionKey.UN_HOLD,
                  'employeeId': employeeId,
                }
                changeTransactions.push(transactionHold);
              }
              changeTransactions.push(changeTransaction);
              if (classifyName === ClassifyName.get(ClassifyKey.COM)) {
                Utilities.updatePartnerStatusHAWB([HAWB], 113);
              }
            } else {
              reponze['message']['data']['fail'].push({
                'HAWB': HAWBClearance,
                'cause': `Đang ở trạng thái ${String(cloneInfoMIC['phase_name']['vi']).toLowerCase()}`
              });
            }
          }
          const detailOptional: Optional = new Optional();
          detailOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          ]);
          try {
            if (dataIDAs.length > 0) {
              transaction = await this._sequelize.transaction();
              const [micDeleted, idaCreated] = await Promise.all([
                this.micRepository.destroyDataTrx(MICOptional, transaction),
                this.idaRepository.createBulkTrx(dataIDAs, transaction),
                this.importTransactionRepository.createBulkTrx(changeTransactions, transaction),
              ]);
              await transaction.commit();
              if (idaCreated.length > 0) {
                reponze['status'] = true;
                reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
                reponze['message']['data']['succesfull'].push(...HAWBClearances);
              }
            }
          } catch (error) {
            await transaction.rollback();
            console.log('---- MIC to IDA error: %o', error);
            reponze['message']['data']['fail'].push(...HAWBClearances);
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][changeMICToIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async changeIDAToMIC(HAWBs: string[], classify: string, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      const IDAOptional: Optional = new Optional();
      IDAOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        IDAOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const infoIDAs: IDA[] = await this.idaRepository.queryAll(IDAOptional);
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_CLASSIFY_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      let micData: IMICCreate;
      let dataMICs: IMICCreate[] = [];
      let changeTransactions: any[] = [];
      if (infoIDAs.length > 0) {
        if (classify === ClassifyValidateName.get(ClassifyValidateKey.COM) || classify === ClassifyValidateName.get(ClassifyValidateKey.IDA)) {
          const classifyName: any = (classify === ClassifyValidateName.get(ClassifyValidateKey.IDA)) ? ClassifyName.get(ClassifyKey.PAR) : ClassifyName.get(ClassifyKey.COM);
          await Promise.all(infoIDAs.map(async (infoIDA: IIDA) => {
            const HAWB: string = String(infoIDA[EConfigure.HAWB_FIELD]);
            const HAWBClearance: string = String(infoIDA[EConfigure.HAWB_CLEARANCE_FIELD]);
            if (!infoIDA['declarationNo'] || !infoIDA['inspectionKindClassification'] || infoIDA['phase'] !== ActionKey.ACCEPT_CLEARANCE) {
              let updateData: any = {
                'classify': classifyName,
                'isHold': false,
                'reasonIds': null
              }

              if (classifyName === ClassifyName.get(ClassifyKey.COM)) {
                updateData['termOfPayment'] = EConfigure.TTR;
              } else {
                updateData['termOfPayment'] = EConfigure.KHONGTT;
                if (updateData['clientId'] != 1) {
                  updateData['termOfPayment'] = EConfigure.KC;
                }
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB)
              ]);
              const [total] = await this.idaRepository.updateData(updateData, updateOptional);
              if (total > 0) {
                const changeTransaction: IChangeClassify = {
                  'HAWB': HAWBClearance,
                  'action': ActionKey.CHANGE_CLASSIFY,
                  'classify': EConfigure.IDA as string,
                  'employeeId': employeeId,
                  'currentClassify': infoIDA['classify'] === EConfigure.PAR ? String(EConfigure.IDA) : String(EConfigure.COM),
                  'changeClassify': classify === EConfigure.IDA ? String(EConfigure.IDA) : String(EConfigure.COM),
                }
                if (infoIDA['isHold']) {
                  const transactionHold: IHoldTransaction = {
                    'HAWB': HAWBClearance,
                    'action': ActionKey.UN_HOLD,
                    'employeeId': employeeId,
                  }
                  changeTransactions.push(transactionHold);
                }
                changeTransactions.push(changeTransaction);
                await this.importTransactionRepository.createBulk(changeTransactions);
                reponze['message']['data']['succesfull'].push(HAWBClearance);

                if (classifyName === ClassifyName.get(ClassifyKey.COM)) {
                  Utilities.updatePartnerStatusHAWB([HAWB], 113);
                }
              } else {
                reponze['message']['data']['fail'].push(HAWBClearance);
              }
            } else {
              reponze['message']['data']['fail'].push({
                'HAWB': HAWBClearance,
                'cause': `Đang ở trạng thái ${String(infoIDA['phase_name']['vi']).toLowerCase()}`
              });
            }

          }));
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
        } else {
          const HAWBClearances: string[] = [];
          const classifyName: any = (classify === ClassifyValidateName.get(ClassifyValidateKey.MIC)) ? ClassifyName.get(ClassifyKey.PAR) : ClassifyName.get(ClassifyKey.DOC);
          for (const infoIDA of infoIDAs) {
            const cloneInfoIDA: IIDA = infoIDA;
            const HAWB: string = String(cloneInfoIDA[EConfigure.HAWB_FIELD]);
            const HAWBClearance: string = String(cloneInfoIDA[EConfigure.HAWB_CLEARANCE_FIELD]);
            if (!cloneInfoIDA['declarationNo'] || !cloneInfoIDA['inspectionKindClassification'] || cloneInfoIDA['phase'] !== ActionKey.ACCEPT_CLEARANCE) {
              const stationId: number = Number(cloneInfoIDA[EConfigure.STATION_ID_FIELD]);
              micData = {
                'HAWB': HAWB,
                'HAWBClearance': HAWBClearance,
                'stationId': stationId,
                'phase': ActionKey.CREATE,
                'serviceId': Number(cloneInfoIDA['serviceId']),
                'clientId': cloneInfoIDA['clientId'],
              };
              micData['declarationNo'] = cloneInfoIDA['declarationNo'];
              micData['importerCode'] = cloneInfoIDA['importerCode'];
              micData['importerName'] = cloneInfoIDA['importerName'];
              micData['importerFullName'] = cloneInfoIDA['importerFullName'];

              micData['postCode'] = cloneInfoIDA['postCode'];
              micData['addressOfImporter'] = cloneInfoIDA['addressOfImporter'];
              micData['telephoneNumberOfImporter'] = cloneInfoIDA['telephoneNumberOfImporter'];
              micData['consignorName'] = cloneInfoIDA['consignorName'];
              micData['address1'] = cloneInfoIDA['address1'];
              micData['address2'] = cloneInfoIDA['address2'];
              micData['address3'] = cloneInfoIDA['address3'];
              micData['address4'] = cloneInfoIDA['address4'];
              micData['countryCode'] = cloneInfoIDA['countryCode'];
              micData['loadingLocationCode'] = cloneInfoIDA['loadingLocationCode'];
              micData['unloadingPort'] = cloneInfoIDA['unloadingPortCode'];
              micData['MAWB'] = cloneInfoIDA['MAWB'];
              micData['cargoPiece'] = cloneInfoIDA['cargoPiece'];
              micData['cargoWeight'] = cloneInfoIDA['cargoWeightGross'];
              micData['customsWarehouseCode'] = cloneInfoIDA['customsWarehouseCode'];
              micData['customsOffice'] = cloneInfoIDA['customsOffice'];
              micData['customsSubSection'] = cloneInfoIDA['customsSubSection'];
              micData['flightNo'] = cloneInfoIDA['loadingVesselAircraftName'];
              micData['arrivalDate'] = cloneInfoIDA['arrivalDate'];

              micData['invoiceCurrencyCode'] = cloneInfoIDA['invoiceCurrencyCode'];
              micData['totalInvoicePrice'] = cloneInfoIDA['totalInvoicePrice'];
              micData['valueClearanceVND'] = cloneInfoIDA['valueClearanceVND'];
              micData['notes'] = cloneInfoIDA['notes'];
              micData['classify'] = classifyName;
              micData['priceVND'] = cloneInfoIDA['priceVND'];

              micData['customerBusinessId'] = cloneInfoIDA['customerBusinessId'];
              micData['customerPersonalId'] = cloneInfoIDA['customerPersonalId'];
              micData['hubId'] = cloneInfoIDA['hubId'];
              micData['classificationOfIndividualOrganization'] = cloneInfoIDA['classificationOfIndividualOrganization'];
              micData['terminalName'] = cloneInfoIDA['terminalName'];
              micData['invoicePriceKind'] = cloneInfoIDA['invoicePriceKindCode'];
              micData['invoicePriceCondition'] = cloneInfoIDA['invoicePriceConditionCode'];

              micData['inspectionKindClassification'] = cloneInfoIDA['inspectionKindClassification'];
              micData['dateCheckin'] = cloneInfoIDA['dateCheckin'];
              micData['dateClearanced'] = cloneInfoIDA['dateClearanced'];
              micData['dateCheckout'] = cloneInfoIDA['dateCheckout'];

              micData['warehouseAddress'] = cloneInfoIDA['warehouseAddress'];
              micData['warehouseCheckin'] = cloneInfoIDA['warehouseCheckin'];
              micData['warehouseCheckout'] = cloneInfoIDA['warehouseCheckout'];

              micData['orderId'] = cloneInfoIDA['orderId'];
              micData['originalPrice'] = cloneInfoIDA['originalPrice'];
              micData['currencyOriginalPrice'] = cloneInfoIDA['currencyOriginalPrice'];
              micData['internalBoxName'] = cloneInfoIDA['internalBoxName'];
              micData['externalBoxName'] = cloneInfoIDA['externalBoxName'];
              micData['childOrderNumber'] = cloneInfoIDA['childOrderNumber'];
              micData['orderNumber'] = cloneInfoIDA['orderNumber'];
              micData['currentStation'] = cloneInfoIDA['currentStation'];

              micData['invoiceCustomer'] = cloneInfoIDA['invoiceCustomer'];
              micData['labelCustomer'] = cloneInfoIDA['labelCustomer'];
              micData['identity'] = cloneInfoIDA['identity'];

              micData['threadCode'] = cloneInfoIDA['threadCode'];
              micData['threadName'] = cloneInfoIDA['threadName'];
              micData['threadColor'] = cloneInfoIDA['threadColor'];
              micData['threadUrl'] = cloneInfoIDA['threadUrl'];
              micData['orderTypeId'] = cloneInfoIDA['orderTypeId'];

              dataMICs.push(micData);
              HAWBClearances.push(HAWBClearance);

              const changeTransaction: IChangeClassify = {
                'HAWB': HAWBClearance,
                'action': ActionKey.CHANGE_CLASSIFY,
                'classify': EConfigure.IDA as string,
                'employeeId': employeeId,
                'currentClassify': cloneInfoIDA['classify'] === EConfigure.PAR ? String(EConfigure.IDA) : String(EConfigure.COM),
                'changeClassify': classify === EConfigure.MIC ? String(EConfigure.MIC) : String(EConfigure.DOC),
              }
              if (cloneInfoIDA['isHold']) {
                const transactionHold: IHoldTransaction = {
                  'HAWB': HAWBClearance,
                  'action': ActionKey.UN_HOLD,
                  'employeeId': employeeId,
                }
                changeTransactions.push(transactionHold);
              }
              changeTransactions.push(changeTransaction);

            } else {
              reponze['message']['data']['fail'].push({
                'HAWB': HAWBClearance,
                'cause': `Đang ở trạng thái ${String(cloneInfoIDA['phase_name']['vi']).toLowerCase()}`
              });
            }

          }
          const detailOptional: Optional = new Optional();
          detailOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          ]);
          try {
            if (dataMICs.length > 0) {
              transaction = await this._sequelize.transaction();
              const [idaDeleted, micCreated] = await Promise.all([
                this.idaRepository.destroyDataTrx(IDAOptional, transaction),
                this.micRepository.createBulkTrx(dataMICs, transaction),
                this.importTransactionRepository.createBulkTrx(changeTransactions, transaction),
              ])
              await transaction.commit();
              if (micCreated.length > 0) {
                reponze['status'] = true;
                reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
                reponze['message']['data']['succesfull'].push(...HAWBClearances);
              }
            }
          } catch (error) {
            await transaction.rollback();
            console.log('---- MIC to IDA error: %o', error);
            reponze['message']['data']['fail'].push(...HAWBClearances)
          }
        }

      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][changeIDAToMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async messageTax(HAWBs: string[], status: boolean, employeeId: number, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const dataUpdate: any = {
        'isMessageTax': status
      }
      const [total] = await this.idaRepository.updateData(dataUpdate, optional);
      if (total > 0) {
        let transactions = HAWBs.map((HAWB: string) => {
          const messageTax: IMessageTax = {
            'HAWB': HAWB,
            'action': ActionKey.MESSAGE_TAX,
            'isMessageTax': true,
            'employeeId': employeeId,
          }
          return messageTax;
        });
        await this.importTransactionRepository.createBulk(transactions);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][messageTax]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async resetMIC(HAWBs: string[], employeeId: number, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optional.setRelation(['importDetails']);
      const dataUpdate: any = {
        'phase': ActionKey.UPDATE_CARGO_NAME,
        'declarationNo': null,
        'inspectionKindClassification': null,
        'dateClearanced': null

      }
      const MICs: MIC[] = await this.micRepository.queryAll(optional);
      const [total]: any = await this.micRepository.updateData(dataUpdate, optional);
      if (total > 0) {
        let transactions: IReset[] = [];
        if (MICs.length > 0) {
          transactions = MICs.map((MIC: IMIC) => {
            const reset: IReset = {
              'HAWB': String(MIC[EConfigure.HAWB_CLEARANCE_FIELD]),
              'employeeId': employeeId,
              'action': ActionKey.RESET_MIC,
              'data': MIC,
              'newData': dataUpdate,
              'classify': String(EConfigure.MIC),
              'currentClassify': String(EConfigure.MIC)
            }
            return reset;
          });
          await this.importTransactionRepository.createBulk(transactions);
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][resetMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async resetIDA(HAWBs: string[], employeeId: number, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optional.setRelation(['importDetails']);
      const dataUpdate: any = {
        'phase': ActionKey.UPDATE_CARGO_NAME,
        'declarationNo': null,
        'inspectionKindClassification': null,
        'dateClearanced': null
      }

      const IDAs: IDA[] = await this.idaRepository.queryAll(optional);
      const [total]: any = await this.idaRepository.updateData(dataUpdate, optional);
      if (total > 0) {
        let transactions: IReset[] = [];
        if (IDAs.length > 0) {
          transactions = IDAs.map((IDA: IIDA) => {
            const reset: IReset = {
              'HAWB': String(IDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'employeeId': employeeId,
              'action': ActionKey.RESET_IDA,
              'data': IDA,
              'newData': dataUpdate,
              'classify': String(IDA['classify']),
              'currentClassify': String(IDA['classify'])
            }
            return reset;
          });
          await this.importTransactionRepository.createBulk(transactions);
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][resetIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateMIC(HAWB: string, value: any, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optional.setRelation(['importDetails']);
      const mic: IMIC = await this.micRepository.getOneOptional(optional);
      if (mic) {
        const importTransaction: any[] = [];
        const HAWBClearance: string = String(mic[EConfigure.HAWB_CLEARANCE_FIELD]);
        if (mic['phase'] === ActionKey.ACCEPT_CLEARANCE) {
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': HAWBClearance,
            'cause': `Đang ở trạng thái ${String(mic['phase_name']['vi']).toLowerCase()}`
          });
        } else {
          transaction = await this._sequelize.transaction();
          const micUpdate: IMICCreate = value;
          const clearanceVND: any = value['valueClearanceVND'] ? (Number(value['valueClearanceVND'])) : 0;
          const micDetailUpdates: IImportDetailCreate[] = value['importDetails'];
          const currencyCode: string = value['invoiceCurrencyCode'];

          micUpdate['valueClearanceVND'] = clearanceVND;
          if (micUpdate['totalInvoicePrice']) {
            micUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(micUpdate['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(micUpdate['totalInvoicePrice'])).toFixed(0);
          } else {
            micUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(mic['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(mic['totalInvoicePrice'])).toFixed(0);
          }

          if (currencyCode === EConfigure.CURRENCY_VND) {
            micUpdate['totalInvoicePrice'] = +(Number(micUpdate['totalInvoicePrice']).toFixed(0));
          } else {
            micUpdate['totalInvoicePrice'] = micUpdate['totalInvoicePrice'];
          }

          if (micUpdate['freight'] === 0) {
            micUpdate['freight'] = null;
            micUpdate['freightCurrency'] = null;
          }
          if (micUpdate['insuranceAmount'] === 0) {
            micUpdate['insuranceAmount'] = null;
            micUpdate['insuranceCurrency'] = null;
          }

          if ((mic['phase'] === ActionKey.CREATE || mic['phase'] === ActionKey.UPDATE_CARGO_NAME) && value['importerName']) {
            micUpdate['importerFullName'] = value['importerName'];
          }

          if (mic['noteHold'] !== micUpdate['noteHold']) {
            const noteHold: INoteHold = {
              'HAWB': HAWBClearance,
              'employeeId': employeeId,
              'action': ActionKey.NOTE_HOLD,
              'noteHold': micUpdate['noteHold']
            }
            importTransaction.push(noteHold);
          }
          if (_.isEqual(mic['reasonIds'], micUpdate['reasonIds'] ? String(micUpdate['reasonIds']).split(',').map(Number) : null) === false) {
            const transactionHold: IHoldTransaction = {
              'HAWB': HAWBClearance,
              'action': ActionKey.HOLD,
              'holdId': micUpdate['reasonIds'],
              'employeeId': employeeId,
            }
            importTransaction.push(transactionHold);
            Utilities.updatePartnerStatusHAWB([HAWB], 112);
          }
          micUpdate['reasonIds'] = micUpdate['reasonIds'] ? String(micUpdate['reasonIds']).split(',').map(Number) : null;

          // micUpdate['cargoWeight'] = Utilities.roundHalf(Number(micUpdate['cargoWeight']), 0.5);
          micUpdate['cargoWeight'] = Number(micUpdate['cargoWeight']);

          const micOptional: Optional = new Optional();
          micOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB)
          ]),
            await this.micRepository.updateDataTrx(micUpdate, micOptional, transaction);
          if (micDetailUpdates.length > 0) {
            await this.importDetailRepository.destroyData(micOptional);
            await Promise.all(micDetailUpdates.map(async (micDetailUpdate: IImportDetailCreate, index: number) => {
              let itemNameVN: string = Utilities.removeSpeialCharacter(micDetailUpdate['itemNameVN'] as string);
              itemNameVN = Utilities.removeGeneralToken(itemNameVN);

              micDetailUpdate['itemNameVN'] = itemNameVN;

              micDetailUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(micDetailUpdate['invoiceValue']) * clearanceVND).toFixed(0) : +(Number(micDetailUpdate['invoiceValue'])).toFixed(0);

              if (currencyCode === EConfigure.CURRENCY_VND) {
                micDetailUpdate['invoiceValue'] = +(Number(micDetailUpdate['invoiceValue']).toFixed(0));
                // micDetailUpdate['invoiceUnitPrice'] = +(Number(micDetailUpdate['invoiceValue'])/Number(micDetailUpdate['quantity1'])).toFixed(0);
              } else {
                micDetailUpdate['invoiceValue'] = micDetailUpdate['invoiceValue'];
                // micDetailUpdate['invoiceUnitPrice'] = Number(micDetailUpdate['invoiceValue'])/Number(micDetailUpdate['quantity1']);
              }
              micDetailUpdate['unitPriceCurrencyCode'] = micUpdate['invoiceCurrencyCode'];
              micDetailUpdate['position'] = index;

              const checkHSCodeDetail: Optional = new Optional();
              checkHSCodeDetail.setWhere([
                new Where(EConfigure.AND, 'name', EConfigure.EQUAL, micDetailUpdate['itemName']),
                new Where(EConfigure.AND, 'nameVN', EConfigure.EQUAL, itemNameVN),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);
              if (micDetailUpdate['HSCode']) {
                checkHSCodeDetail.getWhere().push(new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, micDetailUpdate['HSCode']))
              }
              const hsCodeDetail: IHSCodeDetail = await this.hscodeDetailRepository.getOneOptional(checkHSCodeDetail);
              if (!hsCodeDetail) {
                const newHSCodeDetail: any = {
                  'name': micDetailUpdate['itemName'],
                  'nameVN': itemNameVN
                }
                if (micDetailUpdate['HSCode']) {
                  newHSCodeDetail['hsCode'] = micDetailUpdate['HSCode'];
                }
                await this.hscodeDetailRepository.createData(newHSCodeDetail);
              }
              micDetailUpdate['weightKG'] = micDetailUpdate['weightKG'] ? micDetailUpdate['weightKG'] : null;
              // const optional: Optional = new Optional();
              // optional.setWhere([
              //   new Where(EConfigure.AND, 'id', EConfigure.EQUAL, micDetailUpdate['id']),
              //   new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, micDetailUpdate[EConfigure.HAWB_FIELD])
              // ]);
              // thiết lập lại các giá trị thuế về null và 0
              micDetailUpdate = Utilities.taxAndCollection(micDetailUpdate, null);
              // await this.importDetailRepository.updateDataTrx(micDetailUpdate, optional, transaction);
              await this.importDetailRepository.createDataTrx(micDetailUpdate, transaction);
            }));
          }
          await transaction.commit();
          const updateTransaction: IUpdateAction = {
            'HAWB': HAWB,
            'employeeId': employeeId,
            'classify': EConfigure.MIC as string,
            'currentClassify': mic['classify'] as string,
            'action': ActionKey.UPDATE_MIC,
            'data': mic,
            'newData': value,
          }
          importTransaction.push(updateTransaction);
          await this.importTransactionRepository.createBulk(importTransaction);
          const importDetailService = new ImportClearanceDetailService();
          await importDetailService.checkUpdateItemNameVN([HAWB]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][updateMIC]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async updateIDA(HAWB: string, value: any, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const ida: IIDA = await this.idaRepository.getOneOptional(optional);
      if (ida) {
        const HAWBClearance: string = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
        if (ida['phase'] === ActionKey.ACCEPT_CLEARANCE && ida['classify'] === ClassifyName.get(ClassifyKey.PAR)) {
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': HAWBClearance,
            'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
          });
        } else {
          const idaUpdate: IIDA = value;
          const clearanceVND: any = value['valueClearanceVND'] ? (Number(value['valueClearanceVND'])) : 0;
          const idaDetailUpdates: IImportDetailCreate[] = value['importDetails'];
          const freight: number = Number(idaUpdate['freight']);
          const currencyCode: string = String(idaUpdate['invoiceCurrencyCode']);
          const importTransaction: any = [];
          idaUpdate['valueClearanceVND'] = clearanceVND;
          if (idaUpdate['totalInvoicePrice']) {
            idaUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(idaUpdate['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(idaUpdate['totalInvoicePrice'])).toFixed(0);
          } else {
            idaUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(ida['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(ida['totalInvoicePrice'])).toFixed(0);
          }
          if (idaUpdate['feeClearance']) {
            idaUpdate['feeClearance'] = +(Number(idaUpdate['feeClearance']).toFixed(0));
          }

          if (currencyCode === EConfigure.CURRENCY_VND) {
            idaUpdate['totalInvoicePrice'] = +(Number(idaUpdate['totalInvoicePrice']).toFixed(0));
            idaUpdate['totalOfProportionalDistributionOnTaxValue'] = +(Number(idaUpdate['totalInvoicePrice']).toFixed(0));
            idaUpdate['totalOfTaxValue'] = +(Number(idaUpdate['totalInvoicePrice']).toFixed(0));
            idaUpdate['totalOfProportional'] = +(Number(idaUpdate['totalInvoicePrice']).toFixed(0));
          } else {
            idaUpdate['totalInvoicePrice'] = idaUpdate['totalInvoicePrice'];
            idaUpdate['totalOfProportionalDistributionOnTaxValue'] = idaUpdate['totalInvoicePrice'];
            idaUpdate['totalOfTaxValue'] = idaUpdate['totalInvoicePrice'];
            idaUpdate['totalOfProportional'] = idaUpdate['totalInvoicePrice'];
          }

          if ((ida['phase'] === ActionKey.CREATE || ida['phase'] === ActionKey.UPDATE_CARGO_NAME) && value['importerName']) {
            idaUpdate['importerFullName'] = value['importerName'];
          }
          if (freight && freight > 0) {
            if (idaUpdate['freightCurrencyCode'] && idaUpdate['freightCurrencyCode'] !== currencyCode) {
              const exchangeRateOptional: Optional = new Optional();
              exchangeRateOptional.setWhere([
                new Where(EConfigure.AND, 'currency', EConfigure.EQUAL, idaUpdate['freightCurrencyCode']),
              ])
              const exchangeRate: IExchangeRate = await new ExchangeRateRepository().queryOneRaw(exchangeRateOptional);
              if (exchangeRate) {
                idaUpdate['freightExchangeRate'] = exchangeRate['valueClearance'];
              }
            } else {
              idaUpdate['freightExchangeRate'] = clearanceVND;
              idaUpdate['freightCurrencyCode'] = currencyCode;
            }
          } else {
            idaUpdate['freight'] = null;
            idaUpdate['freightExchangeRate'] = null;
            idaUpdate['freightCurrencyCode'] = null;
          }

          if (ida['noteHold'] !== idaUpdate['noteHold']) {
            const noteHold: INoteHold = {
              'HAWB': HAWBClearance,
              'employeeId': employeeId,
              'action': ActionKey.NOTE_HOLD,
              'noteHold': idaUpdate['noteHold']
            }
            importTransaction.push(noteHold);
          }

          if (_.isEqual(ida['reasonIds'], idaUpdate['reasonIds'] ? String(idaUpdate['reasonIds']).split(',').map(Number) : null) === false) {
            const transactionHold: IHoldTransaction = {
              'HAWB': HAWBClearance,
              'action': ActionKey.HOLD,
              'holdId': idaUpdate['reasonIds'],
              'employeeId': employeeId,
            }
            importTransaction.push(transactionHold);
            Utilities.updatePartnerStatusHAWB([HAWB], 112);
          }
          idaUpdate['reasonIds'] = idaUpdate['reasonIds'] ? String(idaUpdate['reasonIds']).split(',').map(Number) : null;

          if (ida['classify'] === ClassifyName.get(ClassifyKey.COM)) {
            if (!idaUpdate['dateClearanced']) {
              idaUpdate['dateClearanced'] = moment().format(EConfigure.FULL_TIME);
            }
            idaUpdate['phase'] = ActionKey.ACCEPT_CLEARANCE;
          }
          // idaUpdate['cargoWeightGross'] = Utilities.roundHalf(Number(idaUpdate['cargoWeightGross']), 0.5);
          idaUpdate['cargoWeightGross'] = Number(idaUpdate['cargoWeightGross']);

          const idaOptional: Optional = new Optional();
          idaOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          ]);
          transaction = await this._sequelize.transaction();
          await this.idaRepository.updateDataTrx(idaUpdate, idaOptional, transaction);
          if (idaDetailUpdates.length > 0) {
            await this.importDetailRepository.destroyData(idaOptional);
            await Promise.all(idaDetailUpdates.map(async (idaDetailUpdate: any, index: number) => {
              if (idaDetailUpdate['HSCode'] && (idaDetailUpdate['HSCode'].substring(0, 2) == 85 || idaDetailUpdate['HSCode'].substring(0, 2) == 84)) {
                idaUpdate['otherLawCode'] = EConfigure.MO;
              }

              let itemNameVN: string = Utilities.removeSpeialCharacter(idaDetailUpdate['itemNameVN']);
              itemNameVN = Utilities.removeGeneralToken(itemNameVN);
              idaDetailUpdate['itemNameVN'] = itemNameVN;
              // idaDetailUpdate['placeOfOrigin'] = idaUpdate['countryCode'];
              idaDetailUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(idaDetailUpdate['invoiceValue']) * clearanceVND).toFixed(0) : +(Number(idaDetailUpdate['invoiceValue'])).toFixed(0);
              // idaDetailUpdate['unitPriceCurrencyCode'] = currencyCode;

              if (currencyCode === EConfigure.CURRENCY_VND) {
                idaDetailUpdate['invoiceValue'] = +(Number(idaDetailUpdate['invoiceValue']).toFixed(0));
                // idaDetailUpdate['invoiceUnitPrice'] = +(Number(idaDetailUpdate['invoiceValue'])/Number(idaDetailUpdate['quantity1'])).toFixed(0);
              } else {
                idaDetailUpdate['invoiceValue'] = idaDetailUpdate['invoiceValue'];
                // idaDetailUpdate['invoiceUnitPrice'] = Number(idaDetailUpdate['invoiceValue'])/Number(idaDetailUpdate['quantity1']);
              }
              idaDetailUpdate['priceQuantityUnit'] = idaDetailUpdate['quantityUnitCode1'];
              idaDetailUpdate['quantityUnitPrice'] = idaDetailUpdate['quantityUnitCode1'];
              idaDetailUpdate['priceQuantityUnit'] = idaDetailUpdate['quantityUnitCode1'];
              idaDetailUpdate['position'] = index;

              if (!idaDetailUpdate['importTaxCode']) {
                idaDetailUpdate['importTaxCode'] = EConfigure.IMPORT_TAX_INCENTIVES;
              }

              if (!idaDetailUpdate['VATTaxCode']) {
                idaDetailUpdate['VATTaxCode'] = EConfigure.VAT_TAX_INCENTIVES_VB215;
              }

              if (!idaDetailUpdate['quantity2']) {
                idaDetailUpdate['quantity2'] = idaDetailUpdate['quantity1'];
              }

              if (!idaDetailUpdate['quantityUnitCode2']) {
                idaDetailUpdate['quantityUnitCode2'] = idaDetailUpdate['quantityUnitCode1'];
              }

              idaDetailUpdate['weightKG'] = idaDetailUpdate['weightKG'] ? idaDetailUpdate['weightKG'] : null;
              idaDetailUpdate['url'] = idaDetailUpdate['url'] ? idaDetailUpdate['url'] : null;

              if (freight && freight > 0) {
                const rateFreight: number = freight / (Number(idaUpdate['totalInvoicePrice']) / Number(idaDetailUpdate['invoiceValue']));
                const priceFreight = (Number(idaUpdate['freightExchangeRate']) > 0) ? +(rateFreight * Number(idaUpdate['freightExchangeRate'])).toFixed(0) : +rateFreight.toFixed(0);
                idaDetailUpdate['priceVND'] = +(Number(idaDetailUpdate['priceVND']) + priceFreight).toFixed(0);
              }
              const checkHSCodeDetail: Optional = new Optional();
              checkHSCodeDetail.setWhere([
                new Where(EConfigure.AND, 'name', EConfigure.EQUAL, idaDetailUpdate['itemName']),
                new Where(EConfigure.AND, 'nameVN', EConfigure.EQUAL, itemNameVN),
                new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, idaDetailUpdate['HSCode']),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);

              const checkHSCode: Optional = new Optional();
              checkHSCode.setWhere([
                new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, idaDetailUpdate['HSCode']),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);
              const hscodeDetailQuery: any = this.hscodeDetailRepository.getOneOptional(checkHSCodeDetail);
              const hscodeQuery: any = this.hscodeRepository.getOneOptional(checkHSCode);
              let [hscodeDetail, hscode] = await Promise.all([hscodeDetailQuery, hscodeQuery]);
              if (!hscode) {
                const newHSCode: any = {
                  'hsCode': idaDetailUpdate['HSCode'],
                  'importTaxCode': idaDetailUpdate['importTaxCode'],
                  'importTaxValue': idaDetailUpdate['importTax'],
                  'VATCode': idaDetailUpdate['VATTaxCode'],
                  'VATValue': idaDetailUpdate['VATTax'],
                  'specialConsumptionTaxCode': idaDetailUpdate['specialConsumptionTaxCode'],
                  'specialConsumptionTaxValue': idaDetailUpdate['specialConsumptionTax'],
                  'environmentTaxCode': idaDetailUpdate['environmentTaxCode'],
                  'environmentTaxPrice': idaDetailUpdate['environmentTax'],
                }
                hscode = await this.hscodeRepository.createDataTrx(newHSCode, transaction);
              } else {
                hscode['importTaxCode'] = idaDetailUpdate['importTaxCode'],
                  hscode['importTaxValue'] = idaDetailUpdate['importTax'];
                hscode['VATCode'] = idaDetailUpdate['VATTaxCode'],
                  hscode['VATValue'] = idaDetailUpdate['VATTax'];
                hscode['specialConsumptionTaxCode'] = idaDetailUpdate['specialConsumptionTaxCode'],
                  hscode['specialConsumptionTaxValue'] = idaDetailUpdate['specialConsumptionTax'];
                hscode['environmentTaxCode'] = idaDetailUpdate['environmentTaxCode'],
                  hscode['environmentTaxPrice'] = idaDetailUpdate['environmentTax'];
                await hscode.save();
              }
              if (!hscodeDetail) {
                const newHSCodeDetail: any = {
                  'name': idaDetailUpdate['itemName'],
                  'nameVN': idaDetailUpdate['itemNameVN'],
                  'quantityUnitCode1': idaDetailUpdate['priceQuantityUnit'],
                  'priceQuantityUnit': idaDetailUpdate['priceQuantityUnit'],
                  'hsCode': idaDetailUpdate['HSCode'],

                  'importTaxCode': idaDetailUpdate['importTaxCode'],
                  'importTaxValue': idaDetailUpdate['importTax'],
                  'VATCode': idaDetailUpdate['VATTaxCode'],
                  'VATValue': idaDetailUpdate['VATTax'],
                  'specialConsumptionTaxCode': idaDetailUpdate['specialConsumptionTaxCode'],
                  'specialConsumptionTaxValue': idaDetailUpdate['specialConsumptionTax'],
                  'environmentTaxCode': idaDetailUpdate['environmentTaxCode'],
                  'environmentTaxPrice': idaDetailUpdate['environmentTax'],

                }
                await this.hscodeDetailRepository.createDataTrx(newHSCodeDetail, transaction);
              } else {
                hscodeDetail['quantityUnitCode1'] = idaDetailUpdate['priceQuantityUnit'];
                hscodeDetail['priceQuantityUnit'] = idaDetailUpdate['priceQuantityUnit'];

                hscodeDetail['importTaxCode'] = idaDetailUpdate['importTaxCode'];
                hscodeDetail['importTaxValue'] = idaDetailUpdate['importTax'];
                hscodeDetail['VATCode'] = idaDetailUpdate['VATTaxCode'];
                hscodeDetail['VATValue'] = idaDetailUpdate['VATTax'];
                hscodeDetail['specialConsumptionTaxCode'] = idaDetailUpdate['specialConsumptionTaxCode'];
                hscodeDetail['specialConsumptionTaxValue'] = idaDetailUpdate['specialConsumptionTax'];
                hscodeDetail['environmentTaxCode'] = idaDetailUpdate['environmentTaxCode'];
                hscodeDetail['environmentTaxPrice'] = idaDetailUpdate['environmentTax'];
                hscodeDetail.save();
              }
              idaDetailUpdate = Utilities.taxAndCollection(idaDetailUpdate, hscode);
              await this.importDetailRepository.createDataTrx(idaDetailUpdate, transaction);
            }));

          }
          await transaction.commit();
          const updateTransaction: IUpdateAction = {
            'HAWB': HAWB,
            'employeeId': employeeId,
            'action': ActionKey.UPDATE_IDA,
            'classify': EConfigure.IDA as string,
            'currentClassify': ida['classify'] as string,
            'data': ida,
            'newData': value,
          }
          importTransaction.push(updateTransaction);
          await this.importTransactionRepository.createBulk(importTransaction);
          const importDetailService = new ImportClearanceDetailService()
          await importDetailService.updateTotalTax([HAWB]);
          await importDetailService.checkUpdateItemNameVN([HAWB]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][updateIDA]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async registerMIC(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.micRepository.queryAll(serviceOptional);
      const MICQuery: any = this.micRepository.queryAll(optional);
      const [MICs, sameService] = await Promise.all([MICQuery, sameServiceQuery]);
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      const typeAction: string = EConfigure.MIC;
      const action: number = ActionKey.SUBMIT_CUSTOM_CLEARANCE;
      let transactions: IRegister[] = [];

      if (MICs.length > 0) {
        MICs.forEach((mic: IMIC, index: number) => {
          foundHAWB[String(mic[EConfigure.HAWB_FIELD])] = String(mic[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(mic[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (mic['phase'] === ActionKey.CREATE
            || (mic['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE && mic['isError'] === false)
            || mic['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE
            || mic['phase'] === ActionKey.ACCEPT_CLEARANCE
            || mic['phase'] === ActionKey.INSPECTION_KIND
          ) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(mic['phase_name']['vi']).toLowerCase()}`
            });
          }
          if (mic['isHold']) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            });
          }
          if (mic['priceVND'] && mic['priceVND'] > EConfigure.INDEX_1000000) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.PRICE_THAN_1M,
            });
          }
          const register: IRegister = {
            'HAWB': String(mic[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.MIC as string,
            'currentClassify': String(mic['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, true, ClearanceTypeId.ChinhThuc, HAWBs, isPrioritize);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isError': false,
                'isECUSError': false
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.micRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
                Utilities.updateDeclaration(HAWBs)
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][registerMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerMIE(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.micRepository.queryAll(serviceOptional);
      const MICQuery: any = this.micRepository.queryAll(optional);
      const [MICs, sameService] = await Promise.all([MICQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'MIE';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (MICs.length > 0) {
        MICs.forEach((mic: IMIC, index: number) => {
          foundHAWB[String(mic[EConfigure.HAWB_FIELD])] = String(mic[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(mic[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (mic['phase'] === ActionKey.CREATE
            || mic['phase'] === ActionKey.UPDATE_CARGO_NAME
            || mic['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE
            || (mic['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && mic['isError'] === false)
            || mic['phase'] === ActionKey.ACCEPT_CLEARANCE
          ) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(mic['phase_name']['vi']).toLowerCase()}`
            });
          }
          if (mic['isHold']) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            });
          }
          const register: IRegister = {
            'HAWB': String(mic[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.MIC as string,
            'currentClassify': String(mic['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, true, ClearanceTypeId.ChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.micRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][registerMIE]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIDA(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.idaRepository.queryAll(serviceOptional);
      const IDAQuery: any = this.idaRepository.queryAll(optional);
      const [IDAs, sameService] = await Promise.all([IDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_INFOMATION;
      const typeAction: string = 'IDA';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (IDAs.length > 0) {
        IDAs.forEach((ida: IIDA, index: number) => {
          foundHAWB[String(ida[EConfigure.HAWB_FIELD])] = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(ida[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (ida['phase'] === ActionKey.CREATE
            || ida['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE
            || ida['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE
            || ida['phase'] === ActionKey.INSPECTION_KIND
            || ida['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
            });
          }
          if (ida['isHold']) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            });
          }
          const register: IRegister = {
            'HAWB': String(ida[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.IDA as string,
            'currentClassify': String(ida['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.Tam, HAWBs, isPrioritize);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isError': false,
                'isECUSError': false
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);

              await Promise.all([
                this.idaRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
                Utilities.updateDeclaration(HAWBs)
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][registerIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIDC(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.idaRepository.queryAll(serviceOptional);
      const IDAQuery: any = this.idaRepository.queryAll(optional);
      const [IDAs, sameService] = await Promise.all([IDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'IDC';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (IDAs.length > 0) {
        IDAs.forEach((ida: IIDA, index: number) => {
          foundHAWB[String(ida[EConfigure.HAWB_FIELD])] = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
          if (ida['phase'] === ActionKey.CREATE
            || ida['phase'] === ActionKey.UPDATE_CARGO_NAME
            || ida['phase'] === ActionKey.SUBMIT_INFOMATION
            || (ida['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE && ida['isError'] === false)
            || ida['phase'] === ActionKey.INSPECTION_KIND
            || ida['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
            });
          }
          if (ida['isHold']) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            });
          }
          const register: IRegister = {
            'HAWB': String(ida[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.IDA as string,
            'currentClassify': String(ida['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.ChinhThuc, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isIDCed': true,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);

              await Promise.all([
                this.idaRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][registerIDC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIIDA(HAWBs: string[], isPrioritize: any, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.idaRepository.queryAll(serviceOptional);
      const IDAQuery: any = this.idaRepository.queryAll(optional);
      const [IDAs, sameService] = await Promise.all([IDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      let typeAction: string = 'IDA0';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (IDAs.length > 0) {
        IDAs.forEach((ida: IIDA, index: number) => {
          foundHAWB[String(ida[EConfigure.HAWB_FIELD])] = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(ida[EConfigure.HAWB_CLEARANCE_FIELD]));
          let cause: any = null;
          if (!ida['isIDCed'] || ida['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
            }
          }
          if (ida['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && ida['isEditProcessing'] === true) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang chờ thông điệp ECUS`
            }
          }
          if (ida['times'] === 9) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Sỗ lần gửi cập nhật vượt quá quy định 9 lần`
            }
          }
          if (ida['isHold']) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            }
          }
          if (cause) {
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push(cause);
          }
          typeAction = `${typeAction}${(ida['times']) ? Number(ida['times']) + 1 : 1}`;
          const register: IRegister = {
            'HAWB': String(ida[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.IDA as string,
            'currentClassify': String(ida['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.ChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              typeAction = 'IDA0';
              const now: string = moment().format(EConfigure.FULL_TIME);
              await Promise.all(IDAs.map(async (ida: IDA) => {
                ida['phase'] = action;
                ida['dateAction'] = now;
                ida['typeAction'] = `${typeAction}${(ida['times']) ? Number(ida['times']) + 1 : 1}`;
                ida['isEditProcessing'] = true;
                ida['isIIDAed'] = true;
                await ida.save()
              }));
              await this.importTransactionRepository.createBulk(transactions);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][registerIIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIDE(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.idaRepository.queryAll(serviceOptional);
      const IDAQuery: any = this.idaRepository.queryAll(optional);
      const [IDAs, sameService] = await Promise.all([IDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'IDE';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (IDAs.length > 0) {
        IDAs.forEach((ida: IIDA, index: number) => {
          foundHAWB[String(ida[EConfigure.HAWB_FIELD])] = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(ida[EConfigure.HAWB_CLEARANCE_FIELD]))
          let cause: any = null;
          if (ida['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
            }
          }
          if (ida['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && ida['isEditProcessing'] === true) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang chờ thông điệp ECUS`
            }
          }
          if (ida['isIIDAed'] === false) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': 'Chưa khai báo thời khai IDA0x'
            }
          }
          if (ida['isHold']) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            }
          }
          if (cause) {
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push(cause);
          }

          const register: IRegister = {
            'HAWB': String(ida[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.IDA as string,
            'currentClassify': String(ida['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.XacNhanChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'typeAction': typeAction,
                'isIIDAed': false,
                'isEditProcessing': true,
                'dateAction': now,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.idaRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][registerIDE]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async monitorGate(HAWB: string, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.OR, 'declarationNo', EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optional.setRelation(['importDetails', 'country']);
      const IDAQuery: any = this.idaRepository.getOneOptional(optional);
      const MICQuery: any = this.micRepository.getOneOptional(optional);
      const [IDA, MIC] = await Promise.all([IDAQuery, MICQuery]);
      let checkoutData: ICheckout = {};
      if (MIC) {
        const mic: IMIC = MIC;
        const details: any = mic['importDetails'];
        checkoutData = {
          'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
          'MAWB': mic['MAWB'],
          'declarationType': String(EConfigure.MIC),
          'declarationNo': mic['declarationNo'],
          'clearanceInspection': String(mic['inspectionKindClassification']),
          'dateCheckout': mic['dateCheckout'],
          'dateCheckin': mic['dateCheckin'],
          'dateClearanced': mic['dateClearanced'],
          'cargoPiece': mic['cargoPiece'],
          'weight': mic['cargoWeight'],
          'classify': mic['classify'] as string,
          'country': mic['country'],
          'isHold': mic['isHold'],
        }
        if (details.length > 0) {
          checkoutData['details'] = details.map((detail: any) => {
            const importDetail: IDetail = {
              'itemNameVN': detail['itemNameVN'],
              'quantity': detail['quantity1']
            }
            return importDetail
          });
        }
        reponze['status'] = true;
        reponze['message'] = {
          'message': EMessage.FOUND,
          'data': checkoutData
        }
      }
      if (IDA) {
        const ida: IIDA = IDA;
        const details: any = ida['importDetails'];
        checkoutData = {
          'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
          'MAWB': ida['MAWB'],
          'declarationType': 'IDA',
          'declarationNo': ida['declarationNo'],
          'clearanceInspection': ida['inspectionKindClassification'],
          'dateCheckout': ida['dateCheckout'],
          'dateCheckin': ida['dateCheckin'],
          'dateClearanced': ida['dateClearanced'],
          'cargoPiece': ida['cargoPiece'],
          'weight': ida['cargoWeightGross'],
          'classify': ida['classify'] as string,
          'country': ida['country'],
          'isHold': ida['isHold'],
        }
        if (details.length > 0) {
          checkoutData['details'] = details.map((detail: any) => {
            const importDetail: IDetail = {
              'itemNameVN': detail['itemNameVN'],
              'quantity': detail['quantity1']
            }
            return importDetail
          });
        }
        reponze['status'] = true;
        reponze['message'] = {
          'message': EMessage.FOUND,
          'data': checkoutData
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][monitorGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportCount(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'total': 0,
            'totalCheckin': 0,
            'totalCheckout': 0,
            'totalWarehouse': 0
          }
        }
      }
      const optionalCheckin: Optional = new Optional();
      optionalCheckin.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'dateCheckin', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalCheckout: Optional = new Optional();
      optionalCheckout.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'dateCheckout', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        // new Where(EConfigure.AND, 'dateCheckin', EConfigure.NOT, null),
      ]);

      // ida
      const IDATotalCheckinQuery = this.idaRepository.countData(optionalCheckin);
      const IDATotalCheckoutQuery = this.idaRepository.countData(optionalCheckout);
      const IDATotalQuery = this.idaRepository.countData(optionalTotal);
      // mic
      const MICTotalCheckinQuery = this.micRepository.countData(optionalCheckin);
      const MICTotalCheckoutQuery = this.micRepository.countData(optionalCheckout);
      const MICTotalQuery = this.micRepository.countData(optionalTotal);

      const [IDATotal, MICTotal, IDATotalCheckin, IDATotalCheckout, MICTotalCheckin, MICTotalCheckout] =
        await Promise.all([IDATotalQuery, MICTotalQuery, IDATotalCheckinQuery, IDATotalCheckoutQuery, MICTotalCheckinQuery, MICTotalCheckoutQuery]);
      const totalCheckin: number = Number(IDATotalCheckin) + Number(MICTotalCheckin);
      const totalCheckout: number = Number(IDATotalCheckout) + Number(MICTotalCheckout);
      const totalWarehouse: number = totalCheckin - totalCheckout
      const total: number = Number(IDATotal) + Number(MICTotal);

      reponze['status'] = true;
      reponze['message']['message'] = EMessage.FOUND;
      reponze['message']['data'] = {
        'totalCheckin': totalCheckin,
        'totalCheckout': totalCheckout,
        'totalWarehouse': (totalWarehouse > 0) ? totalWarehouse : 0,
        'total': total,
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][clearanceReportCount]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
          }
        }
      }

      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        // new Where(EConfigure.AND, 'dateCheckout', EConfigure.NOT, null),
        // new Where(EConfigure.AND, 'dateCheckin', EConfigure.NOT, null),
      ])

      // ida
      const IDAQuery = this.idaRepository.queryAll(optionalTotal);

      // mic
      const MICQuery = this.micRepository.queryAll(optionalTotal);

      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (IDAs.length > 0 || MICs.length > 0) {
        let micData: ICheckout[] = [];
        let idaData: ICheckout[] = [];
        let manifests: ICheckout[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IIDA) => {
            const ida: ICheckout = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': IDA['MAWB'],
              'declarationNo': IDA['declarationNo'],
              'clearanceInspection': IDA['inspectionKindClassification'],
              'dateCheckout': IDA['dateCheckout'],
              'dateCheckin': IDA['dateCheckin'],
              'dateClearanced': IDA['dateClearanced'],
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'classify': IDA['classify'],
              'type': String(EConfigure.IDA),
              'externalBoxName': IDA['externalBoxName'],
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: IMIC) => {
            const mic: ICheckout = {
              'HAWB': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': MIC['MAWB'],
              'declarationNo': MIC['declarationNo'],
              'clearanceInspection': String(MIC['inspectionKindClassification']),
              'dateCheckout': MIC['dateCheckout'],
              'dateCheckin': MIC['dateCheckin'],
              'dateClearanced': MIC['dateClearanced'],
              'cargoPiece': MIC['cargoPiece'],
              'weight': MIC['cargoWeight'],
              'classify': MIC['classify'],
              'type': String(EConfigure.MIC),
              'externalBoxName': MIC['externalBoxName'],
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': manifests.length,
          'manifests': manifests,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][clearanceReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async master(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const masterOptional: Optional = new Optional();
      masterOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'MAWB', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterOptional.setAttributes(['MAWB']);
      masterOptional.setGroup(['MAWB']);
      // ida
      const IDAQuery = this.idaRepository.queryAll(masterOptional);
      // mic
      const MICQuery = this.micRepository.queryAll(masterOptional);
      let masters: any[] = [];
      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (IDAs.length > 0 || MICs.length > 0) {
        masters = [...IDAs, ...MICs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][master]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async managementGate(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      let queryAll: number = 0;
      optional.getWhere().forEach((where: Where) => {
        if (where.getKey() === 'classify') {
          if (where.getValue() === ClassifyValidateName.get(ClassifyValidateKey.DOC)) {
            queryAll = 1;
          }
          if (where.getValue() === ClassifyValidateName.get(ClassifyValidateKey.MIC)) {
            where.setValue(ClassifyName.get(ClassifyKey.PAR));
            queryAll = 1;
          }
          if (where.getValue() === ClassifyValidateName.get(ClassifyValidateKey.COM)) {
            queryAll = 2;
          }
          if (where.getValue() === ClassifyValidateName.get(ClassifyValidateKey.IDA)) {
            where.setValue(ClassifyName.get(ClassifyKey.PAR));
            queryAll = 2;
          }
        }
      });
      optional.setRelation(['customerBusiness', 'customerPersonal', 'importDetails', 'holds']);
      let MICs: MIC[] = [];
      let IDAs: IDA[] = [];

      if (queryAll === 2) {
        IDAs = await this.idaRepository.getAll(optional);
      }
      if (queryAll === 1) {
        MICs = await this.micRepository.getAll(optional);
      }
      if (queryAll === 0) {
        const IDAQuery = this.idaRepository.queryAll(optional);
        const MICQuery = this.micRepository.queryAll(optional);
        [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      }
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IIDA) => {
            const ida: IManagement = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': IDA['MAWB'],
              'declarationNo': IDA['declarationNo'],
              'clearanceInspection': Number(IDA['inspectionKindClassification']),
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'dateCheckout': IDA['dateCheckout'],
              'dateCheckin': IDA['dateCheckin'],
              'dateClearanced': IDA['dateClearanced'],
              'bussiness': IDA['customerBusiness'],
              'personal': IDA['customerPersonal'],
              'declarationType': ClassifyValidateName.get(ClassifyValidateKey.IDA),
              'classify': IDA['classify'],
              'importDetails': IDA['importDetails'],
              'isHold': IDA['isHold'],
              'reasonHold': IDA['holds']
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: IMIC) => {
            const mic: IManagement = {
              'HAWB': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': MIC['MAWB'],
              'declarationNo': MIC['declarationNo'],
              'clearanceInspection': Number(MIC['inspectionKindClassification']),
              'dateCheckout': MIC['dateCheckout'],
              'dateCheckin': MIC['dateCheckin'],
              'dateClearanced': MIC['dateClearanced'],
              'cargoPiece': MIC['cargoPiece'],
              'weight': MIC['cargoWeight'],
              'bussiness': MIC['customerBusiness'],
              'personal': MIC['customerPersonal'],
              'declarationType': ClassifyValidateName.get(ClassifyValidateKey.MIC),
              'classify': MIC['classify'],
              'importDetails': MIC['importDetails'],
              'isHold': MIC['isHold'],
              'reasonHold': MIC['holds']
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][managementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filterMIC(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      // mic
      const MICQuery = this.micRepository.getAll(optional);
      const MICTotalCreateQuery = this.micRepository.countData(optionalCreate);
      const MICTotalUpdateCarageNameQuery = this.micRepository.countData(optionalUpdateCarageName);
      const MICTotalSendClearanceQuery = this.micRepository.countData(optionalSendClearance);
      const MICTotalEditClearanceQuery = this.micRepository.countData(optionalEditClearance);
      const MICTotalInspectionKindQuery = this.micRepository.countData(optionalInspectionKind);
      const MICTotalAcceptClearanceQuery = this.micRepository.countData(optionalAcceptClearance);
      const MICErrorQuery = this.micRepository.countData(optionalError);

      const [[MICs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError] =
        await Promise.all([MICQuery, MICTotalCreateQuery, MICTotalUpdateCarageNameQuery, MICTotalSendClearanceQuery, MICTotalEditClearanceQuery, MICTotalInspectionKindQuery,
          MICTotalAcceptClearanceQuery, MICErrorQuery]);
      if (MICs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': MICs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][filterMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filterIDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalRegisterTemp': 0,
            'totalSentTemp': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalRegisterTemp: Optional = new Optional();
      optionalRegisterTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalSentTemp: Optional = new Optional();
      optionalSentTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SENT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      // ida
      const IDAQuery = this.idaRepository.getAll(optional);
      const IDATotalCreateQuery = this.idaRepository.countData(optionalCreate);
      const IDATotalUpdateCarageNameQuery = this.idaRepository.countData(optionalUpdateCarageName);
      const IDATotalRegisterTempQuery = this.idaRepository.countData(optionalRegisterTemp);
      const IDATotalSentTempQuery = this.idaRepository.countData(optionalSentTemp);
      const IDATotalSendClearanceQuery = this.idaRepository.countData(optionalSendClearance);
      const IDATotalEditClearanceQuery = this.idaRepository.countData(optionalEditClearance);
      const IDATotalInspectionKindQuery = this.idaRepository.countData(optionalInspectionKind);
      const IDATotalAcceptClearanceQuery = this.idaRepository.countData(optionalAcceptClearance);
      const IDAErrorQuery = this.idaRepository.countData(optionalError);

      const [[EDAs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError, registerTemp, sentTemp] =
        await Promise.all([IDAQuery, IDATotalCreateQuery, IDATotalUpdateCarageNameQuery, IDATotalSendClearanceQuery, IDATotalEditClearanceQuery, IDATotalInspectionKindQuery,
          IDATotalAcceptClearanceQuery, IDAErrorQuery, IDATotalRegisterTempQuery, IDATotalSentTempQuery]);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': EDAs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalRegisterTemp': registerTemp,
          'totalSentTemp': sentTemp,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][filterIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportExport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }

      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ])
      optionalQuery.setRelation(['holds', 'manifest'])

      // ida
      const IDAQuery = this.idaRepository.queryAll(optionalQuery);
      // mic
      const MICQuery = this.micRepository.queryAll(optionalQuery);

      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (IDAs.length > 0 || MICs.length > 0) {
        let manifests: any[] = [];
        if (IDAs.length > 0) {
          IDAs.forEach((IDA: IIDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            const ida: IExportReport = {
              'trackingNo': String(IDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'MAWB': String(IDA['MAWB']),
              'origin': String(IDA['countryCode']),
              'country': String(IDA['countryCode']),
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': IDA['manifest'] ? IDA['manifest']['weightChargeKG'] : null,
              'manisfestImported': String(IDA['createdAt']),
              'arDate': String(IDA['dateCheckin']),
              'cdsDate': String(IDA['declarationPlannedDate']),
              'crDate': String(IDA['dateCheckout']),
              'cdsNo': String(IDA['declarationNo']),
              'importType': (IDA['classify'] === ClassifyName.get(ClassifyKey.PAR)) ? String(EConfigure.H11) : 'MẬU DỊCH',
              'lane': Number(IDA['inspectionKindClassification']),
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : IDA['phase_name']['vi'],
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'duty': fee,
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': String(EConfigure._H11),
              'consignorName': String(IDA['consignorName']),
              'consignorAddress': String(IDA['address1']),
              'consignorCityName': null,
              'consignorContactName': String(IDA['consignorName']),
              'consignorTelephone': null,
              'consigneeName': String(IDA['importerFullName']),
              'consigneeAddress': String(IDA['addressOfImporter']),
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': String(IDA['importerFullName']),
              'consigneeTelephone': String(IDA['telephoneNumberOfImporter']),
              'remarks': null,
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        if (MICs.length > 0) {
          MICs.forEach((MIC: IMIC) => {
            const mic: IExportReport = {
              'trackingNo': String(MIC[EConfigure.HAWB_CLEARANCE_FIELD]),
              'MAWB': String(MIC['MAWB']),
              'origin': String(MIC['countryCode']),
              'country': String(MIC['countryCode']),
              'pcs': Number(MIC['cargoPiece']),
              'trueWeight': Number(MIC['cargoWeight']),
              'volWeight': MIC['manifest'] ? MIC['manifest']['weightChargeKG'] : null,
              'manisfestImported': String(MIC['createdAt']),
              'arDate': String(MIC['dateCheckin']),
              'cdsDate': String(MIC['declarationPlannedDate']),
              'crDate': String(MIC['dateCheckout']),
              'cdsNo': String(MIC['declarationNo']),
              'importType': (MIC['classify'] === ClassifyName.get(ClassifyKey.PAR)) ? EConfigure.MIC : MIC['classify'],
              'lane': Number(MIC['inspectionKindClassification']),
              'customsStatus': (MIC['isHold'] && MIC['holds'] && MIC['holds'].length > 0) ? MIC['holds'][0]['name'] : MIC['phase_name']['vi'],
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MIC['totalInvoicePrice']),
              'importTax': 0,
              'VAT': 0,
              'duty': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': String(EConfigure._MIC),
              'consignorName': String(MIC['consignorName']),
              'consignorAddress': String(MIC['address1']),
              'consignorCityName': null,
              'consignorContactName': String(MIC['consignorName']),
              'consignorTelephone': null,
              'consigneeName': String(MIC['importerFullName']),
              'consigneeAddress': String(MIC['addressOfImporter']),
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': String(MIC['importerFullName']),
              'consigneeTelephone': String(MIC['telephoneNumberOfImporter']),
              'remarks': null,
              'taxNumberCode': null,
              'taxNumberDate': null,
              'externalBoxName': MIC['externalBoxName'],
            }
            manifests.push(mic);
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][clearanceReportExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async holdManifest(data: any[], isRemoveMAWB: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL
        }
      }
      const HAWBs: string[] = [];
      await Promise.all(data.map(async (element: any) => {
        const HAWB: string = element[EConfigure.HAWB_FIELD];
        const reasonIds: any = element['reasonIds'] ? String(element['reasonIds']).split(',') : null
        HAWBs.push(HAWB)
        const isHold: boolean = element['isHold'];
        const dataUpdate: any = {
          'isHold': isHold,
          'reasonIds': reasonIds,
        }
        const transactionHold: IHoldTransaction = {
          'HAWB': HAWB,
          'action': isHold ? ActionKey.HOLD : ActionKey.UN_HOLD,
          'holdId': element['reasonIds'],
          'employeeId': employeeId,
        }
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        if (hubs) {
          optional.getWhere().push(
            new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
          )
        }
        await this.micRepository.updateData(dataUpdate, optional);
        await this.idaRepository.updateData(dataUpdate, optional);
        await this.importTransactionRepository.createData(transactionHold);
        Utilities.updatePartnerStatusHAWB([HAWB], 112);
        this.handlePushWebHook([HAWB], isHold ? EConfigure.HOLD_CLEANCE : EConfigure.UNHOLD_CLEANCE, reasonIds)
        return true;
      }));
      if (isRemoveMAWB) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        let updateClearance: any = {
          "MAWB": null,
          "employeeHandle": employeeId
        }
        await Promise.all([
          Utilities.callTMMApi('manifests/removeMAWB', EConfigure.PUT_METHOD, { HAWBs }),
          this.idaRepository.updateData(updateClearance, optional),
          this.micRepository.updateData(updateClearance, optional),
        ]);
      }

      reponze['status'] = true;
      reponze['message']['message'] = EMessage.SUCCESS;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][holdManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getHoldManifest(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.setRelation(['country', 'importDetailItems', 'holds']);
      const IDAQuery = this.idaRepository.queryAll(optional);
      const MICQuery = this.micRepository.queryAll(optional);

      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);


      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IIDA) => {
            const ida: IHold = {
              "HAWBClearance": IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": IDA[EConfigure.HAWB_FIELD],
              "declarationNo": IDA['declarationNo'],
              "MAWB": IDA['MAWB'],
              "invoiceValue": IDA['totalInvoicePrice'],
              "invoiceCurrency": IDA['invoiceCurrencyCode'],
              "priceVND": IDA['priceVND'],
              "country": IDA['country'],
              "cargoPiece": IDA['cargoPiece'],
              "weight": IDA['cargoWeightGross'],
              "massOfWeight": IDA['weightUnitCodeGross'],
              "inspectionKindClassification": IDA['inspectionKindClassification'],
              "phase": IDA['phase_name'],
              "classify": IDA['classify'],
              "type": String(EConfigure.IDA),
              "details": IDA['importDetailItems'],
              "reasonDetails": IDA['holds']
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: IMIC) => {
            const mic: IHold = {
              "HAWBClearance": MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": MIC[EConfigure.HAWB_FIELD],
              "declarationNo": MIC['declarationNo'],
              "MAWB": MIC['MAWB'],
              "invoiceValue": MIC['totalInvoicePrice'],
              "invoiceCurrency": MIC['invoiceCurrencyCode'],
              "priceVND": MIC['priceVND'],
              "country": MIC['country'],
              "cargoPiece": MIC['cargoPiece'],
              "weight": MIC['cargoWeight'],
              "massOfWeight": WeightClearance.get('kg'),
              "inspectionKindClassification": MIC['inspectionKindClassification'],
              "phase": MIC['phase_name'],
              "classify": MIC['classify'],
              "type": String(EConfigure.MIC),
              "details": MIC['importDetailItems'],
              "reasonDetails": MIC['holds']
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getHoldManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }


  public async holdChangeClassify(HAWBs: string[], classify: string, employeeId: number, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_CLASSIFY_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      await Promise.all([
        this.changeMICToIDA(HAWBs, classify, employeeId, hubs),
        this.changeIDAToMIC(HAWBs, classify, employeeId, hubs)
      ]);
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][holdChangeClassify]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getHoldReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }

      const optionalHoldQuery: Optional = new Optional();
      optionalHoldQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      optionalHoldQuery.setRelation(['holds', 'manifest', 'importDetailItems', 'station', 'customerBusiness', 'customerPersonal']);

      const optionalCOMQuery: Optional = new Optional();
      optionalCOMQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      // ida
      const IDAHoldQuery = this.idaRepository.queryAll(optionalHoldQuery);
      const IDACOMQuery = this.idaRepository.queryAll(optionalCOMQuery);
      // mic
      const MICHoldQuery = this.micRepository.queryAll(optionalHoldQuery);

      const [holdIDAs, holdMICs, comIDAs] = await Promise.all([IDAHoldQuery, MICHoldQuery, IDACOMQuery]);
      if (holdIDAs.length > 0 || holdMICs.length > 0 || comIDAs.length > 0) {

        let manifests: any[] = [];
        if (comIDAs.length > 0) {
          comIDAs.forEach((IDA: IDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: ImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }
            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'cdsNoCustomer': IDA['declarationNoCustomer'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': EConfigure.COM,
              'customsStatus': null,
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),

              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'totalTax': IDA['totalTax'],
              'priceVND': IDA['priceVND'],

              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': 'Khác',
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': IDA['phase_name']['vi'],
              'currency': IDA['invoiceCurrencyCode'],

              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        if (holdIDAs.length > 0) {
          holdIDAs.forEach((IDA: IDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: ImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }
            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': EConfigure.HOLD,
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),

              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'totalTax': IDA['totalTax'],
              'priceVND': IDA['priceVND'],

              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H11,
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': EConfigure.HOLD,
              'cdsNoCustomer': null,
              'currency': IDA['invoiceCurrencyCode'],

              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        if (holdMICs.length > 0) {
          holdMICs.forEach((MIC: MIC) => {
            let productId: any = [];
            let details: ImportDetail[] = MIC['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (MIC['manifest']) {
              orderNumber = MIC['manifest']['orderNumber']
              if (MIC['manifest']['originalOrderNumberClient']) {
                orderNumber = MIC['manifest']['originalOrderNumberClient']
              }
            }
            const mic: IImportReport = {
              'MAWB': MIC['MAWB'],
              'trackingNo': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': MIC['declarationNo'],
              'cdsNoCustomer': null,
              'lane': MIC['inspectionKindClassification'] ? String(MIC['inspectionKindClassification']).substring(0, 1) : null,
              'origin': MIC['countryCode'] ? `${MIC['countryCode']}${EConfigure.G}` : null,
              'country': MIC['countryCode'],
              'pcs': Number(MIC['cargoPiece']),
              'trueWeight': Number(MIC['cargoWeight']),
              'volWeight': Number(MIC['cargoWeight']),
              'manisfestImported': MIC['createdAt'],
              'arDate': MIC['dateCheckin'],
              'cdsDate': MIC['declarationPlannedDate'],
              'crDate': MIC['dateCheckout'],
              'importType': EConfigure.HOLD,
              'customsStatus': (MIC['holds'] && MIC['holds'].length > 0) ? MIC['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MIC['totalInvoicePrice']),
              'importTax': 0,
              'VAT': 0,
              'specialConsumptionTax': 0,
              'environmentTax': 0,
              'duty': 0,
              'totalTax': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'priceVND': MIC['priceVND'],
              'notes': EConfigure._MIC,
              'consignorName': MIC['consignorName'],
              'consignorAddress': `${MIC['address1']}${MIC['address2'] ? MIC['address2'] : ''}${MIC['address3'] ? MIC['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': MIC['consignorName'],
              'consignorTelephone': null,
              'consigneeName': MIC['importerFullName'],
              'consigneeAddress': MIC['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': MIC['importerFullName'],
              'consigneeTelephone': MIC['telephoneNumberOfImporter'],
              'remarks': null,
              'status': EConfigure.HOLD,
              'currency': MIC['invoiceCurrencyCode'],

              'customerBusiness': MIC['customerBusiness'],
              'customerPersonal': MIC['customerPersonal'],
              'dateClearanced': MIC['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': MIC['station'] ? MIC['station'] : null,
              'importerCode': MIC['importerCode'],
              'statusId': MIC['phase'],
              'warehouseCheckin': MIC['warehouseCheckin'],
              'warehouseCheckout': MIC['warehouseCheckout'],
              'taxNumberCode': null,
              'taxNumberDate': null,
              'externalBoxName': MIC['externalBoxName'],
            }
            manifests.push(mic);
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getHoldReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async saveWarehouse(HAWB: string, address: string, employeeId: number, hubs: any, date: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const IDAQuery: any = this.idaRepository.getOneOptional(optional);
      const MICQuery: any = this.micRepository.getOneOptional(optional);
      const [IDA, MIC] = await Promise.all([IDAQuery, MICQuery]);
      const updateWarehouse: any = {
        'warehouseAddress': address,
        'warehouseCheckout': null
      }
      const now: string = moment().format(EConfigure.TIME);
      if (MIC) {
        const mic: MIC = MIC;
        // if(!mic['warehouseCheckin']) {
        updateWarehouse['warehouseCheckin'] = `${date} ${now}`;
        // }

        const [total] = await this.micRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          const transactionWarehouse: IWarehouseTransaction = {
            'HAWB': HAWB,
            'action': ActionKey.SAVE_WAREHOUSE,
            'employeeId': employeeId,
            'warehouseAddress': address,
          }
          await this.importTransactionRepository.createData(transactionWarehouse);
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }

      }
      if (IDA) {
        const ida: IDA = IDA;
        // if(!ida['warehouseCheckin']) {
        updateWarehouse['warehouseCheckin'] = `${date} ${now}`;
        // }
        const [total] = await this.idaRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          const transactionWarehouse: IWarehouseTransaction = {
            'HAWB': HAWB,
            'action': ActionKey.SAVE_WAREHOUSE,
            'employeeId': employeeId,
            'warehouseAddress': address,
          }
          await this.importTransactionRepository.createData(transactionWarehouse);
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][storeWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async deleteWarehouse(HAWB: string, address: string, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const IDAQuery: any = this.idaRepository.getOneOptional(optional);
      const MICQuery: any = this.micRepository.getOneOptional(optional);
      const [IDA, MIC] = await Promise.all([IDAQuery, MICQuery]);
      const updateWarehouse: any = {
        'warehouseCheckout': moment().format(EConfigure.FULL_TIME)
      }

      if (MIC) {
        const mic: MIC = MIC;
        if (!mic['warehouseCheckin']) {
          reponze['message'] = {
            'message': EMessage.MANIFEST_NOT_CHECKIN_WAREHOUSE,
            'data': HAWB
          }
          return reponze;
        }
        const [total] = await this.micRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          const transactionWarehouse: IWarehouseTransaction = {
            'HAWB': HAWB,
            'action': ActionKey.DELETE_WAREHOUSE,
            'employeeId': employeeId,
            'warehouseAddress': address,
          }
          await this.importTransactionRepository.createData(transactionWarehouse);
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }

      }
      if (IDA) {
        const ida: IDA = IDA;
        if (!ida['warehouseCheckin']) {
          reponze['message'] = {
            'message': EMessage.MANIFEST_NOT_CHECKIN_WAREHOUSE,
            'data': HAWB
          }
          return reponze;
        }
        const [total] = await this.idaRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          const transactionWarehouse: IWarehouseTransaction = {
            'HAWB': HAWB,
            'action': ActionKey.DELETE_WAREHOUSE,
            'employeeId': employeeId,
            'warehouseAddress': address,
          }
          await this.importTransactionRepository.createData(transactionWarehouse);
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][deleteWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async importReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      optional.setRelation(['holds', 'manifest', 'importDetailItems', 'station', 'customerBusiness', 'customerPersonal', 'order']);
      const IDAQuery = this.idaRepository.queryAll(optional);
      const MICQuery = this.micRepository.queryAll(optional);
      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: ImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }

            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': IDA['isHold'] ? EConfigure.HOLD : (IDA['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.H11 : EConfigure.COM),
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'priceVND': IDA['priceVND'],
              'totalTax': IDA['totalTax'],
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H11,
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? EConfigure.HOLD : IDA['phase_name']['vi'],
              'cdsNoCustomer': null,
              'currency': IDA['invoiceCurrencyCode'],

              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'order': IDA['order'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
              'details': details,
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: MIC) => {
            let productId: any = [];
            let details: ImportDetail[] = MIC['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (MIC['manifest']) {
              orderNumber = MIC['manifest']['orderNumber']
              if (MIC['manifest']['originalOrderNumberClient']) {
                orderNumber = MIC['manifest']['originalOrderNumberClient']
              }
            }
            const mic: IImportReport = {
              'MAWB': MIC['MAWB'],
              'trackingNo': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': MIC['declarationNo'],
              'cdsNoCustomer': null,
              'lane': MIC['inspectionKindClassification'] ? String(MIC['inspectionKindClassification']).substring(0, 1) : null,
              'origin': MIC['countryCode'] ? `${MIC['countryCode']}${EConfigure.G}` : null,
              'country': MIC['countryCode'],
              'pcs': Number(MIC['cargoPiece']),
              'trueWeight': Number(MIC['cargoWeight']),
              'volWeight': Number(MIC['cargoWeight']),
              'manisfestImported': MIC['createdAt'],
              'arDate': MIC['dateCheckin'],
              'cdsDate': MIC['declarationPlannedDate'],
              'crDate': MIC['dateCheckout'],
              'importType': MIC['isHold'] ? EConfigure.HOLD : (MIC['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.MIC : MIC['classify']),
              'customsStatus': (MIC['isHold'] && MIC['holds'] && MIC['holds'].length > 0) ? MIC['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MIC['totalInvoicePrice']),
              'importTax': 0,
              'VAT': 0,
              'specialConsumptionTax': 0,
              'environmentTax': 0,
              'duty': 0,
              'priceVND': MIC['priceVND'],
              'totalTax': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': EConfigure._MIC,
              'consignorName': MIC['consignorName'],
              'consignorAddress': `${MIC['address1']}${MIC['address2'] ? MIC['address2'] : ''}${MIC['address3'] ? MIC['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': MIC['consignorName'],
              'consignorTelephone': null,
              'consigneeName': MIC['importerFullName'],
              'consigneeAddress': MIC['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': MIC['importerFullName'],
              'consigneeTelephone': MIC['telephoneNumberOfImporter'],
              'remarks': null,
              'status': (MIC['isHold'] && MIC['holds'] && MIC['holds'].length > 0) ? EConfigure.HOLD : MIC['phase_name']['vi'],
              'currency': MIC['invoiceCurrencyCode'],

              'customerBusiness': MIC['customerBusiness'],
              'customerPersonal': MIC['customerPersonal'],
              'dateClearanced': MIC['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': MIC['station'] ? MIC['station'] : null,
              'importerCode': MIC['importerCode'],
              'statusId': MIC['phase'],
              'warehouseCheckin': MIC['warehouseCheckin'],
              'warehouseCheckout': MIC['warehouseCheckout'],
              'order': MIC['order'],
              'taxNumberCode': null,
              'taxNumberDate': null,
              'externalBoxName': MIC['externalBoxName'],
              'details': details,
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][importReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getStoreWarehouse(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      )
      const IDAQuery = this.idaRepository.queryAll(optional);
      const MICQuery = this.micRepository.queryAll(optional);
      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IDA) => {
            const ida: IStoreWarehouse = {
              'HAWBClearance': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'HAWB': IDA[EConfigure.HAWB_FIELD],
              'MAWB': IDA['MAWB'],
              'warehouseAddress': IDA['warehouseAddress'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: MIC) => {
            const mic: IStoreWarehouse = {
              'HAWBClearance': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'HAWB': MIC[EConfigure.HAWB_FIELD],
              'MAWB': MIC['MAWB'],
              'warehouseAddress': MIC['warehouseAddress'],
              'warehouseCheckin': MIC['warehouseCheckin'],
              'warehouseCheckout': MIC['warehouseCheckout'],
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getStoreWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMICIDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': [{
            'manifests': [],
            'total': 0,
          }]
        }
      }
      const limit: number = optional.getLimit();
      let half: number = 0;
      if (limit > 0) {
        half = Number(limit / 2);
        optional.setLimit(half);
      }
      optional.setRelation(['country', 'importDetailItems', 'holds']);
      const IDAQuery = this.idaRepository.getAll(optional);
      const MICQuery = this.micRepository.getAll(optional);

      let [[IDAs, totalIDA], [MICs, totalMIC]] = await Promise.all([IDAQuery, MICQuery]);
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          const total: number = MICs.length;
          if (half != total) {
            const remain: number = half + (half - total);
            optional.setLimit(remain);
            const addIDAs: IDA[] = await this.idaRepository.queryAllPaging(optional);
            if (addIDAs.length > 0) {
              IDAs = addIDAs;
            }
          }
          idaData = IDAs.map((IDA: IIDA) => {
            const ida: IGetAll = {
              "HAWBClearance": IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": IDA[EConfigure.HAWB_FIELD],
              "declarationNo": IDA['declarationNo'],
              "MAWB": IDA['MAWB'],
              "invoiceValue": IDA['totalInvoicePrice'],
              "invoiceCurrency": IDA['invoiceCurrencyCode'],
              "priceVND": IDA['priceVND'],
              "country": IDA['country'],
              "cargoPiece": IDA['cargoPiece'],
              "weight": IDA['cargoWeightGross'],
              "massOfWeight": IDA['weightUnitCodeGross'],
              "inspectionKindClassification": IDA['inspectionKindClassification'],
              "phase": IDA['phase_name'],
              "classify": IDA['classify'],
              "type": String(EConfigure.IDA),
              "details": IDA['importDetailItems'],
              "reasonDetails": IDA['holds'],
              "inspectionKindTimes": IDA['inspectionKindTimes'],
              "clearanceDeclarationTimes": IDA['clearanceDeclarationTimes'],
              "isHold": IDA['isHold'],
              "externalBoxName": IDA['externalBoxName'],
              "internalBoxName": IDA['internalBoxName'],
              "warehouseAddress": IDA['warehouseAddress'],
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          const total: number = IDAs.length;
          if (half != total) {
            const remain: number = half + (half - total);
            optional.setLimit(remain);
            const addMICs: MIC[] = await this.micRepository.queryAllPaging(optional);
            if (addMICs.length > 0) {
              MICs = addMICs;
            }
          }
          micData = MICs.map((MIC: IMIC) => {
            const mic: IGetAll = {
              "HAWBClearance": MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": MIC[EConfigure.HAWB_FIELD],
              "declarationNo": MIC['declarationNo'],
              "MAWB": MIC['MAWB'],
              "invoiceValue": MIC['totalInvoicePrice'],
              "invoiceCurrency": MIC['invoiceCurrencyCode'],
              "priceVND": MIC['priceVND'],
              "country": MIC['country'],
              "cargoPiece": MIC['cargoPiece'],
              "weight": MIC['cargoWeight'],
              "massOfWeight": WeightClearance.get('kg'),
              "inspectionKindClassification": MIC['inspectionKindClassification'],
              "phase": MIC['phase_name'],
              "classify": MIC['classify'],
              "type": String(EConfigure.MIC),
              "details": MIC['importDetailItems'],
              "reasonDetails": MIC['holds'],
              "inspectionKindTimes": MIC['inspectionKindTimes'],
              "clearanceDeclarationTimes": MIC['clearanceDeclarationTimes'],
              "isHold": MIC['isHold'],
              "externalBoxName": MIC['externalBoxName'],
              "internalBoxName": MIC['internalBoxName'],
              "warehouseAddress": MIC['warehouseAddress'],
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': totalIDA + totalMIC
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getAllMICIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async returnCargo(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }

      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'isHold', 'dateCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'dateCheckout', EConfigure.EQUAL, null),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const IDAQuery: any = this.idaRepository.queryAll(optional);
      const MICQuery: any = this.micRepository.queryAll(optional);
      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      let now: string = moment().format(EConfigure.FULL_TIME);
      let transactions: any[] = [];
      let updateData: any = {
        'isHold': false,
        'isDeleted': true,
        'phase': ActionKey.RETURN_CARGO_DELIVERY,
        'warehouseCheckout': now,
      }
      if (MICs.length > 0) {
        await Promise.all(MICs.map(async (mic: MIC) => {
          const HAWB: string = mic['HAWB'];
          const optionalUpdate: Optional = new Optional();
          optionalUpdate.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          ]);
          if (mic['isHold']) {
            const transactionHold: IHoldTransaction = {
              'HAWB': HAWB,
              'action': ActionKey.UN_HOLD,
              'holdId': null,
              'employeeId': employeeId,
            }
            transactions.push(transactionHold);
          }
          updateData['warehouseCheckin'] = now;
          if (mic['dateCheckin']) {
            updateData['warehouseCheckin'] = mic['dateCheckin'];
          }
          const transactionReturn: IReturnCargo = {
            'HAWB': HAWB,
            'action': ActionKey.RETURN_CARGO_DELIVERY,
            'employeeId': employeeId,
          }
          transactions.push(transactionReturn);
          await this.micRepository.updateData(updateData, optionalUpdate);
          await this.importTransactionRepository.createBulk(transactions);
        }));
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      if (IDAs.length > 0) {
        await Promise.all(IDAs.map(async (ida: IDA) => {
          const HAWB: string = ida['HAWB'];
          const optionalUpdate: Optional = new Optional();
          optionalUpdate.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          ]);
          if (ida['isHold']) {
            const transactionHold: IHoldTransaction = {
              'HAWB': HAWB,
              'action': ActionKey.UN_HOLD,
              'holdId': null,
              'employeeId': employeeId,
            }
            transactions.push(transactionHold);
          }
          updateData['warehouseCheckin'] = now;
          if (ida['dateCheckin']) {
            updateData['warehouseCheckin'] = ida['dateCheckin'];
          }
          const transactionReturn: IReturnCargo = {
            'HAWB': HAWB,
            'action': ActionKey.RETURN_CARGO_DELIVERY,
            'employeeId': employeeId,
          }
          transactions.push(transactionReturn);
          await this.idaRepository.updateData(updateData, optionalUpdate);
          await this.importTransactionRepository.createBulk(transactions);
        }));
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][returnCargo]', (error as Error).message);
      throw new Error(error as any);
    }
  }


  public async updateImport(HAWB: string, manifestImport: IImportValidate): Promise<any> {
    let transaction: any;
    try {
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.EQUAL, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const detailOptional: Optional = new Optional();
      detailOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
      ]);
      detailOptional.setOrderby([new OrderBy('position', EConfigure.ASCENDING)]);
      const micQuery: any = this.micRepository.getOneOptional(optional);
      const idaQuery: any = this.idaRepository.getOneOptional(optional);
      const detailQuery: any = this.importDetailRepository.queryAll(detailOptional);
      const [mic, ida, details] = await Promise.all([micQuery, idaQuery, detailQuery]);
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': {
            'fail': null,
            'succesfull': null,
          }
        }
      }
      if (mic || ida) {
        let micData: any;
        let idaData: any;
        let importTraction: ICreateAction;
        const dataDetails: any[] = [];
        let detailData: IImportDetailCreate;

        const cloneManifest: IImportValidate = { ...manifestImport };
        const orderTypeId: number = (!cloneManifest['orderTypeId'] || (cloneManifest['orderTypeId'] && cloneManifest['orderTypeId'] < EConfigure.INDEX_4)) ? EConfigure.INDEX_0 : cloneManifest['orderTypeId'];

        const cloneDetails = cloneManifest['items'];
        const totalCloneDetails: number = cloneDetails.length;
        const HAWB: string = cloneManifest[EConfigure.HAWB_FIELD];
        const HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD];
        let warehouse: IWarehouse = {};
        const valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
        let totalPrice: number = Number(cloneManifest['totalPrice']);
        const priceVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
        const countryCode: string = cloneManifest['consignorCountryCode'];
        const stationId: number = cloneManifest['stationId'];

        if (cloneManifest['warehouseId'] && orderTypeId < EConfigure.INDEX_4) {
          warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
        }
        if (!cloneManifest['warehouseId'] && orderTypeId >= EConfigure.INDEX_4 && cloneManifest['hubId']) {
          warehouse = await Utilities.getWarehouseByHub(cloneManifest['hubId'], orderTypeId);
        }
        const country: ICountry = await Utilities.getCountryByCode(countryCode);
        if (mic) {
          micData = {
            'HAWB': HAWB,
            'HAWBClearance': HAWBClearance,
            'stationId': stationId,
            'phase': mic['phase'],
            'serviceId': cloneManifest['serviceId'],
            'identity': cloneManifest['identity'],
            'threadCode': cloneManifest['threadCode'],
            'threadName': cloneManifest['threadName'],
            'threadColor': cloneManifest['threadColor'],
            'threadUrl': cloneManifest['threadUrl'],
            'orderTypeId': orderTypeId
          };

          if (!cloneManifest['importerCode']) {
            micData['importerCode'] = EConfigure.IMPORTER_CODE as string;
          } else {
            micData['importerCode'] = cloneManifest['importerCode'];
          }

          if (!cloneManifest['importerName']) {
            micData['importerName'] = EConfigure.IMPORTER_NAME
          } else {
            micData['importerName'] = cloneManifest['importerName'];
            micData['importerFullName'] = cloneManifest['importerName'];
          }

          micData['postCode'] = cloneManifest['importerPostCode'];
          micData['addressOfImporter'] = Utilities.handleRemoveSpaceComma(cloneManifest['importerAddress'] as string);
          micData['telephoneNumberOfImporter'] = Utilities.subString(cloneManifest['importerTelephoneNumber'], 0, 20);
          micData['consignorCode'] = cloneManifest['consignorCode'];
          micData['consignorName'] = cloneManifest['consignorName'];
          const addresses = Utilities.handleAddress(cloneManifest['consignorAddress'] as string);
          if (addresses) {
            for (const [key, value] of Object.entries(addresses)) {
              if (key <= '3') {
                micData[`address${key}`] = Utilities.removeSpeialCharacter(String(value));
              }
            }
          }
          if (country) {
            micData['address4'] = country['fullName'];
          }
          micData['countryCode'] = countryCode;
          micData['loadingLocationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
          micData['MAWB'] = cloneManifest['MAWB'];
          micData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
          if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
            micData['cargoWeight'] = cloneManifest['weight'];
          } else {
            micData['cargoWeight'] = Utilities.convertKG(cloneManifest['weight'], cloneManifest['unitOfMass']);
          }
          micData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
          if (warehouse && Object.keys(warehouse).length > 0) {
            micData['customsWarehouseCode'] = warehouse['code'];
            micData['unloadingPort'] = warehouse['unloadingPortCode'];
            micData['customsOffice'] = warehouse['customsOffice'];
            micData['customsSubSection'] = warehouse['customsSubSection'];
            micData['terminalName'] = warehouse['id'];
            micData['classificationOfIndividualOrganization'] = warehouse['individualOrganization'];
          }
          if (cloneManifest['arrivalDate'] && cloneManifest['flightNo']) {
            micData['flightNo'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['arrivalDate']);
          } else {
            micData['flightNo'] = cloneManifest['flightNo'];
          }
          micData['arrivalDate'] = cloneManifest['arrivalDate'];
          micData['hubId'] = cloneManifest['hubId'];

          micData['invoicePriceKind'] = EConfigure.TYPE_A;
          micData['clientId'] = cloneManifest['clientId'];
          micData['invoicePriceCondition'] = EConfigure.DDP;
          if (micData['clientId'] == EConfigure.INDEX_1 || micData['clientId'] == EConfigure.INDEX_2) {
            micData['invoicePriceCondition'] = EConfigure.CIF;
          }
          micData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
          if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
            micData['totalInvoicePrice'] = +(totalPrice.toFixed(0));
          } else {
            micData['totalInvoicePrice'] = totalPrice;
          }
          micData['valueClearanceVND'] = valueClearanceVND;
          micData['customerBusinessId'] = cloneManifest['customerBusinessId'];
          micData['customerPersonalId'] = cloneManifest['customerPersonalId'];
          micData['notes'] = cloneManifest['note'];
          micData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
          micData['priceVND'] = priceVND;


          micData['orderId'] = cloneManifest['orderId'];

          micData['originalPrice'] = cloneManifest['originalPrice'];
          micData['currencyOriginalPrice'] = cloneManifest['currencyOriginalPrice'];
          micData['internalBoxName'] = cloneManifest['internalBoxName'];
          micData['externalBoxName'] = cloneManifest['externalBoxName'];
          micData['childOrderNumber'] = cloneManifest['childOrderNumber'];
          micData['orderNumber'] = cloneManifest['orderNumber'];

          micData['labelCustomer'] = cloneManifest['labelCustomer'];
          micData['invoiceCustomer'] = cloneManifest['invoiceCustomer'];

          micData['dateCheckin'] = mic['dateCheckin'];

          if (totalPrice === 0) {
            micData['classify'] = ClassifyName.get(ClassifyKey.DOC);
          } else {
            micData['classify'] = cloneManifest['classify'];
          }

          importTraction = {
            'HAWB': HAWB,
            'action': ActionKey.RE_CREATE,
            'classify': ClassifyValidateName.get(ClassifyValidateKey.MIC) as string,
            'currentClassify': micData['classify'],
            'data': micData,
            'newData': micData,
            'stationId': stationId,
          }
        } else {
          idaData = {
            'HAWB': HAWB,
            'HAWBClearance': HAWBClearance,
            'cargoNo': HAWBClearance,
            'stationId': stationId,
            'phase': ida['phase'],
            'serviceId': cloneManifest['serviceId'],
            'identity': cloneManifest['identity'],
            'threadCode': cloneManifest['threadCode'],
            'threadName': cloneManifest['threadName'],
            'threadColor': cloneManifest['threadColor'],
            'threadUrl': cloneManifest['threadUrl'],
            'orderTypeId': orderTypeId
          };

          idaData['MAWB'] = cloneManifest['MAWB'];
          idaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
          if (!cloneManifest['importerCode']) {
            idaData['importerCode'] = EConfigure.IMPORTER_CODE as string;
          } else {
            idaData['importerCode'] = cloneManifest['importerCode'];
          }
          if (!cloneManifest['importerName']) {
            idaData['importerName'] = EConfigure.IMPORTER_NAME;
          } else {
            idaData['importerName'] = cloneManifest['importerName'];
            idaData['importerFullName'] = cloneManifest['importerName'];
          }
          idaData['postCode'] = cloneManifest['importerPostCode'];
          idaData['addressOfImporter'] = Utilities.handleRemoveSpaceComma(cloneManifest['importerAddress'] as string);
          idaData['telephoneNumberOfImporter'] = Utilities.subString(cloneManifest['importerTelephoneNumber'], 0, 20);
          idaData['consignorCode'] = cloneManifest['consignorCode'];
          idaData['consignorName'] = cloneManifest['consignorName'];
          const addresses = Utilities.handleAddress(cloneManifest['consignorAddress'] as string);
          if (addresses) {
            for (const [key, value] of Object.entries(addresses)) {
              if (key <= '3') {
                idaData[`address${key}`] = Utilities.removeSpeialCharacter(String(value));
              }
            }
          }
          if (country) {
            idaData['address4'] = country['fullName'];
          }
          idaData['countryCode'] = countryCode;

          idaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT;
          if (cloneManifest['meanOfTransportationCode']) {
            idaData['meansOfTransportationCode'] = cloneManifest['meanOfTransportationCode'];
          }

          idaData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
          idaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
          idaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
          idaData['codeOfExtendingDueDateForPayment'] = EConfigure.TYPE_D;

          idaData['clientId'] = cloneManifest['clientId'];
          idaData['termOfPayment'] = EConfigure.KHONGTT;
          idaData['invoicePriceConditionCode'] = EConfigure.DDP;
          if (idaData['clientId'] == EConfigure.INDEX_1 || idaData['clientId'] == EConfigure.INDEX_2) {
            idaData['termOfPayment'] = EConfigure.KC;
            idaData['invoicePriceConditionCode'] = EConfigure.CIF;
          }

          idaData['declarationKindCode'] = EConfigure.H11;
          idaData['arrivalDate'] = cloneManifest['arrivalDate'];
          if (warehouse && Object.keys(warehouse).length > 0) {
            if (orderTypeId >= EConfigure.INDEX_4) {
              idaData['declarationKindCode'] = warehouse['kindleCode'];
              idaData['arrivalDate'] = moment().format(EConfigure.DAY_TIME);
            }
            idaData['customsWarehouseCode'] = warehouse['code'];
            idaData['plannedDeclarantCode'] = warehouse['agencyCode'];
            idaData['unloadingPortCode'] = warehouse['unloadingPortCode'];
            idaData['unloadingPortName'] = warehouse['unloadingPortCode'];
            idaData['customsOffice'] = warehouse['customsOffice'];
            idaData['customsSubSection'] = warehouse['customsSubSection'];
            idaData['feeClearance'] = warehouse['feePrice'];
            idaData['terminalName'] = warehouse['id'];
            idaData['classificationOfIndividualOrganization'] = warehouse['individualOrganization'];
          }
          if (cloneManifest['arrivalDate'] && cloneManifest['flightNo']) {
            idaData['loadingVesselAircraftName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['arrivalDate']);
          } else {
            idaData['loadingVesselAircraftName'] = cloneManifest['flightNo'];
          }

          idaData['loadingLocationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
          idaData['loadingLocationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
          if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
            idaData['cargoWeightGross'] = cloneManifest['weight'];
          } else {
            idaData['cargoWeightGross'] = Utilities.convertKG(cloneManifest['weight'], cloneManifest['unitOfMass']);
          }
          idaData['weightUnitCodeGross'] = WeightClearance.get('kg');

          idaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
          if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
            totalPrice = +(totalPrice.toFixed(0));
            idaData['totalInvoicePrice'] = totalPrice;
            idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
            idaData['totalOfTaxValue'] = totalPrice;
            idaData['totalOfProportional'] = totalPrice;
          } else {
            idaData['totalInvoicePrice'] = totalPrice;
            idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
            idaData['totalOfTaxValue'] = totalPrice;
            idaData['totalOfProportional'] = totalPrice;
          }
          idaData['valueClearanceVND'] = valueClearanceVND;
          idaData['customerBusinessId'] = cloneManifest['customerBusinessId'];
          idaData['customerPersonalId'] = cloneManifest['customerPersonalId'];
          idaData['notes'] = cloneManifest['note'];
          idaData['classify'] = cloneManifest['classify'];
          idaData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
          idaData['hubId'] = cloneManifest['hubId'];

          idaData['priceVND'] = priceVND;
          idaData['taxPayer'] = EConfigure.TAX_PAYER;
          idaData['invoicePriceKindCode'] = EConfigure.TYPE_A;

          idaData['totalImportTax'] = ida['totalImportTax'];
          idaData['subjectVATCode'] = ida['subjectVATCode'];
          idaData['numberVAT'] = ida['numberVAT'];
          idaData['totalVATTax'] = ida['totalVATTax'];
          idaData['subjectEnvironmentCode'] = ida['subjectEnvironmentCode'];
          idaData['numberEnvironment'] = ida['numberEnvironment'];
          idaData['totalEnvironmentTax'] = ida['totalEnvironmentTax'];
          idaData['subjectSpecialConsumptionCode'] = ida['subjectSpecialConsumptionCode'];
          idaData['numberSpecialConsumption'] = ida['numberSpecialConsumption'];
          idaData['totalSpecialConsumptionTax'] = ida['totalSpecialConsumptionTax'];
          idaData['totalTax'] = ida['totalTax'];
          idaData['termOfPayment'] = ida['termOfPayment'];
          idaData['dateCheckin'] = ida['dateCheckin'];

          idaData['invoiceNo'] = ida['invoiceNo'];
          idaData['invoiceDate'] = cloneManifest['invoiceDate'];

          idaData['orderId'] = cloneManifest['orderId'];

          idaData['originalPrice'] = cloneManifest['originalPrice'];
          idaData['currencyOriginalPrice'] = cloneManifest['currencyOriginalPrice'];
          idaData['internalBoxName'] = cloneManifest['internalBoxName'];
          idaData['externalBoxName'] = cloneManifest['externalBoxName'];
          idaData['childOrderNumber'] = cloneManifest['childOrderNumber'];
          idaData['orderNumber'] = cloneManifest['orderNumber'];

          idaData['labelCustomer'] = cloneManifest['labelCustomer'];
          idaData['invoiceCustomer'] = cloneManifest['invoiceCustomer'];

          importTraction = {
            'HAWB': HAWB,
            'action': ActionKey.RE_CREATE,
            'classify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
            'currentClassify': idaData['classify'],
            'data': idaData,
            'newData': idaData,
            'stationId': stationId
          }
        }
        if (totalCloneDetails > 0) {
          cloneDetails.forEach((detail: IDetailValidate, index: number) => {
            if (detail['HSCode'] && (+(detail['HSCode'].substring(0, 2)) == 85 || +(detail['HSCode'].substring(0, 2)) == 84)) {
              idaData['otherLawCode'] = EConfigure.MO;
            }
            detailData = {};
            const cloneDetail: IDetailValidate = { ...detail };
            const totalPrice: number = Number(cloneDetail['invoiceValue']);
            const priceDetailVND: number = +(totalPrice * valueClearanceVND).toFixed(0);
            const currencyCode: string = cloneDetail['currencyCode'];

            detailData[EConfigure.HAWB_FIELD] = HAWB;
            detailData['HSCode'] = cloneDetail['HSCode'];
            detailData['itemNameVN'] = cloneDetail['itemNameVN'];
            if (details[index]) {
              detailData['HSCode'] = details[index]['HSCode'];
              detailData['itemNameVN'] = details[index]['itemNameVN'];
              detailData['quantity2'] = details[index]['quantity2'];
              detailData['quantityUnitCode2'] = details[index]['quantityUnitCode2'];
            }

            detailData['itemName'] = cloneDetail['itemName'];
            if (detail['placeOfOrigin']) {
              detailData['placeOfOrigin'] = detail['placeOfOrigin'];
            } else {
              detailData['placeOfOrigin'] = countryCode;
              if (country) {
                detailData['originalPlaceName'] = country['shortName'];
              }
            }

            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
              detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
            } else {
              detailData['invoiceValue'] = totalPrice;
              detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
            }
            detailData['priceVND'] = priceDetailVND;

            detailData['unitPriceCurrencyCode'] = currencyCode;

            if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
              detailData['weightKG'] = cloneManifest['weight'];
            } else {
              detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
            }
            detailData['url'] = cloneDetail['url'];
            detailData['originalUrl'] = cloneDetail['originalUrl'];
            detailData['originalProductName'] = cloneDetail['originalProductName'] ? Utilities.removeSpeialCharacter(cloneDetail['originalProductName']) : null;;
            detailData['productId'] = detail['productId'];

            detailData['quantity1'] = cloneDetail['quantity'];
            detailData['quantity2'] = cloneDetail['quantity'];

            detailData['quantityUnitCode1'] = EConfigure.PIECE;
            detailData['quantityUnitCode2'] = EConfigure.PIECE;

            detailData['priceQuantityUnit'] = EConfigure.PIECE;
            detailData['quantityUnitPrice'] = EConfigure.PIECE;


            detailData['importTaxCode'] = EConfigure.IMPORT_TAX_INCENTIVES;
            detailData['VATTaxCode'] = EConfigure.VAT_TAX_INCENTIVES_VB215;
            detailData['position'] = index;
            dataDetails.push(detailData);
          });
        }
        if (totalCloneDetails < details.length) {
          details.forEach((detail: ImportDetail, index: number) => {
            if (index + 1 > totalCloneDetails) {
              let importDetail: any = detail.toJSON();
              importDetail['position'] = index;
              dataDetails.push(importDetail);
            }
          });
        }
        if (micData || idaData) {
          try {
            let micCreated, idaCreated, detailCreated = null;
            transaction = await this._sequelize.transaction();
            await Promise.all([
              this.micRepository.destroyDataTrx(optional, transaction),
              this.idaRepository.destroyDataTrx(optional, transaction),
              this.importDetailRepository.destroyDataTrx(detailOptional, transaction)
            ]);
            if (micData) {
              [micCreated, detailCreated] = await Promise.all([
                this.micRepository.createDataTrx(micData, transaction),
                this.importDetailRepository.createBulkTrx(dataDetails, transaction),
                this.importTransactionRepository.createDataTrx(importTraction, transaction),
              ]);
            } else {
              [idaCreated, detailCreated] = await Promise.all([
                this.idaRepository.createDataTrx(idaData, transaction),
                this.importDetailRepository.createBulkTrx(dataDetails, transaction),
                this.importTransactionRepository.createDataTrx(importTraction, transaction),
              ]);
            }
            await transaction.commit();
            if (micCreated || idaCreated) {
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.CREATED_DATA;
              reponze['message']['data']['succesfull'] = HAWBClearance;
            }
          } catch (error) {
            await transaction.rollback();
            console.log('---- Recreate imports error: %o', error);
            reponze['message']['data']['fail'] = HAWBClearance
          }
        }
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][recreateImport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async micAssignCargoName(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'success': null,
            'assigned': [],
          },
          'error': []
        }
      }

      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'HAWBClearance', 'employeeUpdateCargoName', 'phase']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
      ]);
      optional.setRelation(['employeeUpdateCargo']);
      const MICs: MIC[] = await this.micRepository.queryAll(optional);
      if (MICs.length > 0) {
        const HAWBCreates: string[] = [];
        let transactions: any[] = [];
        MICs.forEach((mic: MIC) => {
          const HAWBClearance: string = mic[EConfigure.HAWB_CLEARANCE_FIELD] || '';
          if (mic['phase'] === ActionKey.ASSIGN_UPDATE_CARGO_NAME && mic['employeeUpdateCargoName']) {
            reponze['message']['data']['assigned'].push({
              'HAWB': HAWBClearance,
              'employee': mic['employeeUpdateCargo'],
            });
          } else {
            HAWBCreates.push(mic[EConfigure.HAWB_FIELD]);
            let transactionUpdate: IReturnCargo = {
              'HAWB': HAWBClearance,
              'action': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
              'employeeId': employeeId
            }
            transactions.push(transactionUpdate);
          }
        });
        if (HAWBCreates.length > 0) {
          const updateCargo: any = {
            'phase': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
            'employeeUpdateCargoName': employeeId
          }
          const updateOptional: Optional = new Optional();
          updateOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBCreates.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
          ])
          const [total, _] = await Promise.all([
            this.micRepository.updateData(updateCargo, updateOptional),
            this.importTransactionRepository.createBulk(transactions)
          ]);
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          if (total > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
            reponze['message']['data']['success'] = HAWBCreates;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][micAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async idaAssignCargoName(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'success': null,
            'assigned': [],
          },
          'error': []
        }
      }

      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'HAWBClearance', 'employeeUpdateCargoName', 'employeeUpdateCargo', 'phase']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      optional.setRelation(['employeeUpdateCargo']);
      const IDAs: IDA[] = await this.idaRepository.queryAll(optional);
      if (IDAs.length > 0) {
        const HAWBCreates: string[] = [];
        let transactions: any[] = [];
        IDAs.forEach((ida: IDA) => {
          const HAWBClearance: string = ida[EConfigure.HAWB_CLEARANCE_FIELD] || '';
          if (ida['phase'] === ActionKey.ASSIGN_UPDATE_CARGO_NAME && ida['employeeUpdateCargoName']) {
            reponze['message']['data']['assigned'].push({
              'HAWB': HAWBClearance,
              'employee': ida['employeeUpdateCargo'],
            });
          } else {
            HAWBCreates.push(ida[EConfigure.HAWB_FIELD]);
            let transactionUpdate: IReturnCargo = {
              'HAWB': HAWBClearance,
              'action': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
              'employeeId': employeeId
            }
            transactions.push(transactionUpdate);
          }
        });
        if (HAWBCreates.length > 0) {
          const updateCargo: any = {
            'phase': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
            'employeeUpdateCargoName': employeeId
          }
          const updateOptional: Optional = new Optional();
          updateOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBCreates.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
          ])
          const [total, tranx] = await Promise.all([
            this.idaRepository.updateData(updateCargo, updateOptional),
            this.importTransactionRepository.createBulk(transactions)
          ]);
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          if (total > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
            reponze['message']['data']['success'] = HAWBCreates;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][idaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMicAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.micRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getMicAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getIdaAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.idaRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getIdaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async micCountPrint(data: Record<string, any>): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'totalPrint']);
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const mics: MIC[] = await this.micRepository.queryAll(optional);
      if (mics.length > 0) {
        await Promise.all(mics.map(async (item: MIC) => {
          item['totalPrint'] = ++item['totalPrint']!;
          await item.save();
        }));
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][micCountPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async micTaxPercent(data: Record<string, any>[]): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      if (data.length > 0) {
        await Promise.all(data.map(async (item) => {
          const optional: Optional = new Optional();
          optional.setWhere([
            new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, item['HAWB']),
            new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, item['HAWB']),
            new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          ]);
          await this.micRepository.updateData({ 'percentTaxPrint': item['percentValue'] }, optional);
        }));
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][micTaxPercent]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async countPrint(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'totalPrint']);
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const ida: IDA = await this.idaRepository.getOneOptional(optional);
      if (ida) {
        ida['totalPrint'] = ++ida['totalPrint'];
        await ida.save();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][countPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneMICIDA(HAWB: string, optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      optional.getWhere().push(
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      );
      // ida
      const IDAQuery = this.idaRepository.getOneOptional(optional);

      // mic
      const MICQuery = this.micRepository.getOneOptional(optional);

      let [IDA, MIC] = await Promise.all([IDAQuery, MICQuery]);
      if (IDA) {
        const IDAData: IDA = IDA;
        IDAData.setDataValue('dataType', EConfigure.IDA);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = IDAData;
      }
      if (MIC) {
        const MICData: MIC = MIC;
        MICData.setDataValue('dataType', EConfigure.MIC);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = MICData;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getOneMICIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async createImportExcel(manifestImport: any): Promise<any> {
    let reponze: IResponze = {
      'status': false,
      'message': {
        'message': EMessage.CREATE_FAIL,
        'data': {
          'fail': [],
          'succesfull': []
        }
      }
    }
    let { error, value } = ImportClearanceValidate.importExcel.validate(manifestImport, { abortEarly: false });

    try {
      value = Utilities.convertNull(value);
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let messages: string[] = errorValidate.handleError();
        reponze['message']['message'] = EMessage.VALIDATE_ERROR;
        reponze['message']['error'] = messages;
        const clearanceCreateLogId: number = value['clearanceCreateLogId'];
        const mongoLog: IClearanceCreateLog = {
          "clearanceCreateLogId": clearanceCreateLogId,
          "data": value,
          "message": reponze['message']['message'],
          "cause": messages,
          "processResult": reponze,
          "type": ClearanceCreateKey.ONLY_INBOUND,
          "isSuccess": reponze['status']
        }
        ClearanceCreateLogMongo.createLog(mongoLog);
        Utilities.updateCreateLog(clearanceCreateLogId, false);
        return reponze;
      }
      const cloneManifest: IImportExcelValidate = { ...value };
      let storeDetail: Map<string, ImportDetail[]> = new Map();
      let HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD];

      const existOptional: Optional = new Optional();
      existOptional.setAttributes([EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'declarationNo']);
      existOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWBClearance),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      existOptional.setOrderby([
        new OrderBy('createdAt', EConfigure.DESCENDING)
      ]);

      const micQuery: any = this.micRepository.getOneOptional(existOptional);
      const idaQuery: any = this.idaRepository.getOneOptional(existOptional);

      const [existMIC, existIDA] = await Promise.all([micQuery, idaQuery]);

      let internalHAWB: string = Utilities.randomString(20);
      const valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
      let totalPrice: number = Number(cloneManifest['totalPrice']);
      const priceVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
      if (existMIC) {
        if ((cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND <= EConfigure.INDEX_1000000) && !existMIC['declarationNo']) {
          internalHAWB = existMIC[EConfigure.HAWB_FIELD];
        }
      }
      if (existIDA) {
        if ((cloneManifest['classify'] !== ClassifyName.get(ClassifyKey.DOC) && priceVND > EConfigure.INDEX_1000000) && !existIDA['declarationNo']) {
          internalHAWB = existIDA[EConfigure.HAWB_FIELD];
        }
      }
      if ((cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND <= EConfigure.INDEX_1000000)) {
        if (existIDA) {
          const HAWB: string = existIDA[EConfigure.HAWB_FIELD];
          const destroyOptional: Optional = new Optional();
          destroyOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
            new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null)
          ]);
          const total = await this.idaRepository.destroyData(destroyOptional);
          if (total) {
            await this.importDetailRepository.destroyDataObj({ HAWB })
          }
        }
      }
      if ((cloneManifest['classify'] !== ClassifyName.get(ClassifyKey.DOC) && priceVND > EConfigure.INDEX_1000000)) {
        if (existMIC) {
          const HAWB: string = existMIC[EConfigure.HAWB_FIELD];
          const destroyOptional: Optional = new Optional();
          destroyOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
            new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null)
          ]);
          const total = await this.micRepository.destroyData(destroyOptional);
          if (total > 0) {
            await this.importDetailRepository.destroyDataObj({ HAWB })
          }
        }
      }
      const detailOptional: Optional = new Optional();
      detailOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, internalHAWB),
      ]);
      const details: ImportDetail[] = await this.importDetailRepository.queryAll(detailOptional);

      if (details.length > 0) {
        details.forEach((detail: ImportDetail) => {
          const HAWB: string = detail[EConfigure.HAWB_FIELD];
          if (!storeDetail.has(HAWB)) {
            storeDetail.set(HAWB, [detail]);
          } else {
            const store: any = storeDetail.get(HAWB);
            store.push(detail);
            storeDetail.set(HAWB, store);
          }
        });
      }
      const dataDetails: any[] = [];
      let micData: any;
      let idaData: any;
      let detailData: IImportDetailCreate;

      const actionCreate: number = ActionKey.CREATE;

      let warehouse: IWarehouse = {};

      const countryCode: string = cloneManifest['consignorCountryCode'];
      const stationId: number = cloneManifest['stationId'];
      if (cloneManifest['warehouseId']) {
        warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
      }
      const country: ICountry = await Utilities.getCountryByCode(countryCode);
      let declareCargoNameMIC: boolean = false;
      let declareCargoNameIDA: boolean = false;
      let createDetails: IDetailValidate[] = [];
      let cause = null;
      let classifyDOC: boolean = false;
      if (cloneManifest['items'].length > 0) {
        createDetails = cloneManifest['items'];
        createDetails.forEach((detail: IDetailValidate, index: number) => {
          detail = Utilities.convertNull(detail);
          detailData = {};
          const cloneDetail: IDetailValidate = { ...detail };
          const itemNameEN: string = cloneDetail['itemName'] || '';

          // const checkName: string = itemNameEN;
          // if(checkName && checkName.toUpperCase().includes("DOC")) {
          //   classifyDOC = true;
          // }
          const currencyCode: string = cloneDetail['currencyCode'];
          if (cloneDetail['invoiceValue'] && !cloneDetail['invoiceUnitPrice']) {
            const totalPrice: number = Number(cloneDetail['invoiceValue']);
            const priceDetailVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
              detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
            } else {
              detailData['invoiceValue'] = totalPrice;
              detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
            }
            detailData['priceVND'] = priceDetailVND;
          }
          if (!cloneDetail['invoiceValue'] && cloneDetail['invoiceUnitPrice']) {
            const invoiceUnitPrice: number = Number(cloneDetail['invoiceUnitPrice']);
            const totalPrice: number = invoiceUnitPrice * Number(cloneDetail['quantity']);
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceUnitPrice'] = +(invoiceUnitPrice.toFixed(0));
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
            } else {
              detailData['invoiceUnitPrice'] = invoiceUnitPrice;
              detailData['invoiceValue'] = totalPrice;
            }
            detailData['priceVND'] = +(totalPrice * valueClearanceVND).toFixed(0);
          }
          if (cloneDetail['invoiceValue'] && cloneDetail['invoiceUnitPrice']) {
            const totalPrice: number = Number(cloneDetail['invoiceValue']);
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceUnitPrice'] = +(cloneDetail['invoiceUnitPrice'].toFixed(0));
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
            } else {
              detailData['invoiceUnitPrice'] = cloneDetail['invoiceUnitPrice'];
              detailData['invoiceValue'] = totalPrice;
            }
            detailData['priceVND'] = +(totalPrice * valueClearanceVND).toFixed(0);
          }
          detailData['HSCode'] = cloneDetail['HSCode'];
          detailData[EConfigure.HAWB_FIELD] = internalHAWB;
          detailData['itemName'] = itemNameEN;
          detailData['itemNameVN'] = detail['itemNameVN'];
          // Giữ lại data đã dịch tên hàng
          if (storeDetail.has(internalHAWB)) {
            const currentDetail: any = storeDetail.get(internalHAWB);
            if (currentDetail[index]) {
              detailData['HSCode'] = currentDetail[index]['HSCode'];
              detailData['itemNameVN'] = currentDetail[index]['itemNameVN'];
              detailData['quantity2'] = currentDetail[index]['quantity2'];
              detailData['quantityUnitCode2'] = currentDetail[index]['quantityUnitCode2'];
            }
          }
          if (detail['placeOfOrigin']) {
            detailData['placeOfOrigin'] = detail['placeOfOrigin'];
          } else {
            detailData['placeOfOrigin'] = countryCode;
            if (country) {
              detailData['originalPlaceName'] = country['shortName'];
            }
          }
          detailData['unitPriceCurrencyCode'] = currencyCode;
          if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
            detailData['weightKG'] = cloneManifest['weight'];
          } else {
            detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
          }
          detailData['url'] = cloneDetail['url'];
          detailData['originalUrl'] = cloneDetail['originalUrl'];
          detailData['originalProductName'] = cloneDetail['originalProductName'] ? Utilities.removeSpeialCharacter(cloneDetail['originalProductName']) : null;;
          detailData['productId'] = detail['productId'];
          detailData['quantity1'] = cloneDetail['quantity'];
          detailData['quantity2'] = cloneDetail['quantity'];
          detailData['quantityUnitCode1'] = EConfigure.PIECE;
          detailData['quantityUnitCode2'] = EConfigure.PIECE;
          detailData['priceQuantityUnit'] = EConfigure.PIECE;
          detailData['quantityUnitPrice'] = EConfigure.PIECE;
          detailData['importTaxCode'] = EConfigure.IMPORT_TAX_INCENTIVES;
          detailData['VATTaxCode'] = EConfigure.VAT_TAX_INCENTIVES_VB215;
          detailData['position'] = index;
          dataDetails.push(detailData);
        });
      }
      if (cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND <= EConfigure.INDEX_1000000) {
        if (dataDetails.length > 0) {
          declareCargoNameMIC = dataDetails.every((detail: IDetailValidate) => {
            return detail['itemNameVN'];
          });
        }
        micData = {
          'HAWB': internalHAWB,
          'HAWBClearance': HAWBClearance,
          'stationId': stationId,
          'phase': actionCreate,
          'serviceId': cloneManifest['serviceId'],
          'identity': cloneManifest['identity'],
        };

        if (declareCargoNameMIC) {
          micData['phase'] = ActionKey.UPDATE_CARGO_NAME;
        }
        if (!cloneManifest['importerCode']) {
          micData['importerCode'] = EConfigure.IMPORTER_CODE as string;
        } else {
          micData['importerCode'] = cloneManifest['importerCode'];
        }
        if (!cloneManifest['importerName']) {
          micData['importerName'] = EConfigure.IMPORTER_NAME
        } else {
          micData['importerName'] = cloneManifest['importerName'];
          micData['importerFullName'] = cloneManifest['importerName'];
        }
        micData['postCode'] = cloneManifest['importerPostCode'];
        micData['addressOfImporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['importerAddress'] as string), 0, 200);
        if (cloneManifest['importerTelephoneNumber']) {
          micData['telephoneNumberOfImporter'] = Utilities.subString(cloneManifest['importerTelephoneNumber'], 0, 20);
        }
        micData['consignorCode'] = cloneManifest['consignorCode'];
        micData['consignorName'] = cloneManifest['consignorName'];
        const addresses = Utilities.handleAddress(cloneManifest['consignorAddress'] as string);
        if (addresses) {
          for (const [key, value] of Object.entries(addresses)) {
            if (key <= '3') {
              micData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
            }
          }
        }
        if (country) {
          micData['address4'] = country['fullName'];
        }
        micData['countryCode'] = countryCode;
        micData['loadingLocationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        micData['MAWB'] = cloneManifest['MAWB'];
        micData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
        if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
          micData['cargoWeight'] = cloneManifest['weight'];
        } else {
          micData['cargoWeight'] = Utilities.convertKG(cloneManifest['weight'], cloneManifest['unitOfMass']);
        }
        micData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
        if (warehouse && Object.keys(warehouse).length > 0) {
          micData['customsWarehouseCode'] = warehouse['code'];
          micData['unloadingPort'] = warehouse['unloadingPortCode'];
          micData['customsOffice'] = warehouse['customsOffice'];
          micData['customsSubSection'] = warehouse['customsSubSection'];
          micData['terminalName'] = warehouse['id'];
          micData['classificationOfIndividualOrganization'] = warehouse['individualOrganization'];
        }
        if (cloneManifest['arrivalDate'] && cloneManifest['flightNo']) {
          micData['flightNo'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['arrivalDate']);
        } else {
          micData['flightNo'] = cloneManifest['flightNo'];
        }
        micData['arrivalDate'] = cloneManifest['arrivalDate'];
        micData['hubId'] = cloneManifest['hubId'];

        micData['invoicePriceKind'] = EConfigure.TYPE_A;
        micData['invoicePriceCondition'] = EConfigure.DDP;
        if (micData['clientId'] == EConfigure.INDEX_1 || micData['clientId'] == EConfigure.INDEX_2) {
          micData['invoicePriceCondition'] = EConfigure.CIF;
        }
        micData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
        if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
          micData['totalInvoicePrice'] = +(totalPrice.toFixed(0));
        } else {
          micData['totalInvoicePrice'] = totalPrice;
        }
        micData['valueClearanceVND'] = valueClearanceVND;
        micData['customerBusinessId'] = cloneManifest['customerBusinessId'];
        micData['customerPersonalId'] = cloneManifest['customerPersonalId'];
        micData['notes'] = cloneManifest['note'];
        micData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
        micData['priceVND'] = priceVND;
        micData['clientId'] = cloneManifest['clientId'];
        micData['orderId'] = cloneManifest['orderId'];
        micData['originalPrice'] = cloneManifest['originalPrice'];
        micData['currencyOriginalPrice'] = cloneManifest['currencyOriginalPrice'];
        micData['internalBoxName'] = cloneManifest['internalBoxName'];
        micData['externalBoxName'] = cloneManifest['externalBoxName'];
        micData['childOrderNumber'] = cloneManifest['childOrderNumber'];
        if (totalPrice === 0 || (totalPrice >= 0 && classifyDOC)) {
          micData['classify'] = ClassifyName.get(ClassifyKey.DOC);
        } else {
          micData['classify'] = cloneManifest['classify'];
        }

        const no: number = await Utilities.getCurrentSequence(EConfigure.V5_INVOICE);
        await Utilities.addOnSequence(no, EConfigure.INDEX_1, EConfigure.V5_INVOICE);
        micData['invoiceNo'] = Utilities.padWithZeroes(no, EConfigure.INDEX_7);
        micData['invoiceDate'] = moment().format(EConfigure.DAY_TIME);

        //Create Transaction
        const micTraction: ICreateAction = {
          'HAWB': internalHAWB,
          'action': actionCreate,
          'classify': ClassifyValidateName.get(ClassifyValidateKey.MIC) as string,
          'currentClassify': micData['classify'],
          'data': micData,
          'newData': micData,
          'stationId': stationId,
        }
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
          new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
        ]);
        try {
          const total = await this.micRepository.destroyData(optional);
          if (total > 0) {
            const optional: Optional = new Optional();
            optional.setWhere([
              new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
            ]);
            await this.importDetailRepository.destroyData(optional);
          }

          const [micCreated] = await Promise.all([
            this.micRepository.createData(micData),
            this.importDetailRepository.createBulk(dataDetails),
            this.importTransactionRepository.createData(micTraction),
          ]);
          if (micCreated) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.CREATED_DATA;
            reponze['message']['data'] = {
              'succesfull': null,
              'fail': null
            }

          }
        } catch (error) {
          console.log('---- create imports error: %o', error);
          cause = (error as any).message;
          reponze['message']['data']['fail'].push()
        }
        micData['details'] = dataDetails;
        const clearanceCreateLogId: number = value['clearanceCreateLogId'];
        const mongoLog: IClearanceCreateLog = {
          "clearanceCreateLogId": clearanceCreateLogId,
          "data": value,
          "message": reponze['message']['message'],
          "cause": cause,
          "handleData": micData,
          "processResult": reponze,
          "classify": ClassifyValidateName.get(ClassifyValidateKey.MIC) as string,
          "type": ClearanceCreateKey.ONLY_INBOUND,
          "isSuccess": reponze['status']
        }
        ClearanceCreateLogMongo.createLog(mongoLog);
        if (!cause && reponze['status']) {
          Utilities.updateCreateLog(clearanceCreateLogId, true);
        } else {
          Utilities.updateCreateLog(clearanceCreateLogId, false);
        }
      } else {
        if (dataDetails.length > 0) {
          declareCargoNameIDA = dataDetails.every((detail: IDetailValidate) => {
            return detail['itemNameVN'] && detail['HSCode'] && detail['HSCode'].match(/^0+/) === null && detail['HSCode'].match(/^[0-9]+$/) !== null;
          });
        }
        idaData = {
          'HAWB': internalHAWB,
          'HAWBClearance': HAWBClearance,
          'cargoNo': HAWBClearance,
          'stationId': stationId,
          'phase': actionCreate,
          'serviceId': cloneManifest['serviceId'],
          'identity': cloneManifest['identity'],
        };

        if (declareCargoNameIDA) {
          idaData['phase'] = ActionKey.UPDATE_CARGO_NAME;
        }
        idaData['MAWB'] = cloneManifest['MAWB'];
        idaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
        if (!cloneManifest['importerCode']) {
          idaData['importerCode'] = EConfigure.IMPORTER_CODE as string;
        } else {
          idaData['importerCode'] = cloneManifest['importerCode'];
        }
        if (!cloneManifest['importerName']) {
          idaData['importerName'] = EConfigure.IMPORTER_NAME;
        } else {
          idaData['importerName'] = cloneManifest['importerName'];
          idaData['importerFullName'] = cloneManifest['importerName'];
        }
        idaData['postCode'] = cloneManifest['importerPostCode'];
        idaData['addressOfImporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['importerAddress'] as string), 0, 200);
        if (cloneManifest['importerTelephoneNumber']) {
          idaData['telephoneNumberOfImporter'] = Utilities.subString(cloneManifest['importerTelephoneNumber'], 0, 20);
        }

        idaData['consignorCode'] = cloneManifest['consignorCode'];
        idaData['consignorName'] = cloneManifest['consignorName'];
        const addresses = Utilities.handleAddress(cloneManifest['consignorAddress'] as string);
        if (addresses) {
          for (const [key, value] of Object.entries(addresses)) {
            if (key <= '3') {
              idaData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
            }
          }
        }
        if (country) {
          idaData['address4'] = country['fullName'];
        }
        idaData['countryCode'] = countryCode;
        idaData['declarationKindCode'] = EConfigure.H11;

        idaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT;
        if (cloneManifest['meanOfTransportationCode']) {
          idaData['meansOfTransportationCode'] = cloneManifest['meanOfTransportationCode'];
        }

        idaData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
        idaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
        idaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
        idaData['codeOfExtendingDueDateForPayment'] = EConfigure.TYPE_D;

        idaData['clientId'] = cloneManifest['clientId'];
        idaData['termOfPayment'] = EConfigure.KHONGTT;
        idaData['invoicePriceConditionCode'] = EConfigure.DDP;
        if (idaData['clientId'] == EConfigure.INDEX_1 || idaData['clientId'] == EConfigure.INDEX_2) {
          idaData['termOfPayment'] = EConfigure.KC;
          idaData['invoicePriceConditionCode'] = EConfigure.CIF;
        }

        if (warehouse && Object.keys(warehouse).length > 0) {
          idaData['customsWarehouseCode'] = warehouse['code'];
          idaData['plannedDeclarantCode'] = warehouse['agencyCode'];
          idaData['unloadingPortCode'] = warehouse['unloadingPortCode'];
          idaData['unloadingPortName'] = warehouse['unloadingPortCode'];
          idaData['customsOffice'] = warehouse['customsOffice'];
          idaData['customsSubSection'] = warehouse['customsSubSection'];
          idaData['feeClearance'] = warehouse['feePrice'];
          idaData['terminalName'] = warehouse['id'];
          idaData['classificationOfIndividualOrganization'] = warehouse['individualOrganization'];
        }
        if (cloneManifest['arrivalDate'] && cloneManifest['flightNo']) {
          idaData['loadingVesselAircraftName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['arrivalDate']);
        } else {
          idaData['loadingVesselAircraftName'] = cloneManifest['flightNo'];
        }
        idaData['arrivalDate'] = cloneManifest['arrivalDate'];
        idaData['loadingLocationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        idaData['loadingLocationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
          idaData['cargoWeightGross'] = cloneManifest['weight'];
        } else {
          idaData['cargoWeightGross'] = Utilities.convertKG(cloneManifest['weight'], cloneManifest['unitOfMass']);
        }
        idaData['weightUnitCodeGross'] = WeightClearance.get('kg');

        idaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
        if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
          totalPrice = +(totalPrice.toFixed(0));
          idaData['totalInvoicePrice'] = totalPrice;
          idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
          idaData['totalOfTaxValue'] = totalPrice;
          idaData['totalOfProportional'] = totalPrice;
        } else {
          idaData['totalInvoicePrice'] = totalPrice;
          idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
          idaData['totalOfTaxValue'] = totalPrice;
          idaData['totalOfProportional'] = totalPrice;
        }

        idaData['valueClearanceVND'] = valueClearanceVND;
        idaData['customerBusinessId'] = cloneManifest['customerBusinessId'];
        idaData['customerPersonalId'] = cloneManifest['customerPersonalId'];
        idaData['notes'] = cloneManifest['note'];
        idaData['classify'] = cloneManifest['classify'];
        idaData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];

        idaData['hubId'] = cloneManifest['hubId'];

        idaData['priceVND'] = priceVND;

        idaData['taxPayer'] = EConfigure.TAX_PAYER;
        idaData['invoicePriceKindCode'] = EConfigure.TYPE_A;


        idaData['orderId'] = cloneManifest['orderId'];

        idaData['originalPrice'] = cloneManifest['originalPrice'];
        idaData['currencyOriginalPrice'] = cloneManifest['currencyOriginalPrice'];
        idaData['internalBoxName'] = cloneManifest['internalBoxName'];
        idaData['externalBoxName'] = cloneManifest['externalBoxName'];
        idaData['childOrderNumber'] = cloneManifest['childOrderNumber'];

        const no: number = await Utilities.getCurrentSequence(EConfigure.V5_INVOICE);
        await Utilities.addOnSequence(no, EConfigure.INDEX_1, EConfigure.V5_INVOICE);
        idaData['invoiceNo'] = Utilities.padWithZeroes(no, EConfigure.INDEX_7);
        idaData['invoiceDate'] = moment().format(EConfigure.DAY_TIME);

        const idaTraction: ICreateAction = {
          'HAWB': internalHAWB,
          'action': actionCreate,
          'classify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
          'currentClassify': idaData['classify'],
          'data': idaData,
          'newData': idaData,
          'stationId': stationId
        }

        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
          new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
        ]);
        try {
          const total = await this.idaRepository.destroyData(optional);
          if (total > 0) {
            const optional: Optional = new Optional();
            optional.setWhere([
              new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
            ]);
            await this.importDetailRepository.destroyData(optional);

          }
          const [idaCreated] = await Promise.all([
            this.idaRepository.createData(idaData),
            this.importDetailRepository.createBulk(dataDetails),
            this.importTransactionRepository.createData(idaTraction),
          ]);
          if (idaCreated) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.CREATED_DATA;
            reponze['message']['data'] = {
              'succesfull': null,
              'fail': null
            }
          }
        } catch (error) {
          console.log('---- create imports error: %o', error);
          cause = (error as any).message;
          reponze['message']['data']['fail'].push()
        }

        idaData['details'] = dataDetails;
        const clearanceCreateLogId: number = value['clearanceCreateLogId'];
        const mongoLog: IClearanceCreateLog = {
          "clearanceCreateLogId": clearanceCreateLogId,
          "data": value,
          "message": reponze['message']['message'],
          "cause": cause,
          "handleData": idaData,
          "processResult": reponze,
          "classify": ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
          "type": ClearanceCreateKey.ONLY_INBOUND,
          "isSuccess": reponze['status']
        }
        ClearanceCreateLogMongo.createLog(mongoLog);
        if (!cause && reponze['status']) {
          Utilities.updateCreateLog(clearanceCreateLogId, true);
        } else {
          Utilities.updateCreateLog(clearanceCreateLogId, false);
        }
      }
      return reponze;
    } catch (error) {
      const clearanceCreateLogId: number = value['clearanceCreateLogId'];
      const mongoLog: IClearanceCreateLog = {
        "clearanceCreateLogId": clearanceCreateLogId,
        "data": value,
        "message": reponze['message']['message'],
        "cause": (error as Error).message,
        "handleData": null,
        "processResult": reponze,
        "classify": null,
        "type": ClearanceCreateKey.ONLY_INBOUND,
        "isSuccess": reponze['status']
      }
      ClearanceCreateLogMongo.createLog(mongoLog);
      Utilities.updateCreateLog(clearanceCreateLogId, false);
      Utilities.sendDiscordErr('[service][import][createImportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async handlePushWebHook(HAWBs: string[], action: number, reasonIds?: any): Promise<any> {
    try {
      if (HAWBs.length > 0) {
        const optional: Optional = new Optional();
        const optionalHold: Optional = new Optional();
        let hold: any = null;
        optional.setAttributes(['orderId', 'clientId'])
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);
        optional.setGroup(['orderId', 'clientId']);
        if (reasonIds) {
          optionalHold.setAttributes(['id', 'name']);
          optionalHold.setWhere([
            new Where(EConfigure.AND, 'id', EConfigure.IN, reasonIds.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
          ]);

          hold = await this.holdRepository.getOneOptional(optionalHold);
        }
        const [mics, idas] = await Promise.all([
          this.micRepository.queryAll(optional),
          this.idaRepository.queryAll(optional),
        ]);
        if (mics.length > 0 || idas.length > 0) {
          const orders: Map<number, string[]> = new Map();
          const orderLazadas: Set<string> = new Set();
          if (mics.length > 0) {
            mics.forEach((mic: MIC) => {
              const clientId: number = Number(mic[EConfigure.CLIENT_ID_FIELD]);
              const orderId: string = mic[EConfigure.ORDER_ID_FIELD];
              orderLazadas.add(orderId);
              if (orders.has(clientId)) {
                const orderIds: string[] = orders.get(clientId) || [];
                orderIds.push(orderId);
                orders.set(clientId, orderIds)
              } else {
                orders.set(clientId, [orderId])
              }
            });
          }
          if (idas.length > 0) {
            idas.forEach((ida: IDA) => {
              const clientId: number = Number(ida[EConfigure.CLIENT_ID_FIELD]);
              const orderId: string = ida[EConfigure.ORDER_ID_FIELD];
              orderLazadas.add(orderId);
              if (orders.has(clientId)) {
                const orderIds: string[] = orders.get(clientId) || [];
                orderIds.push(orderId);
                orders.set(clientId, orderIds)
              } else {
                orders.set(clientId, [orderId])
              }
            });
          }
          let holdDetail: any = null;
          if (hold) {
            holdDetail = {
              "code": hold['id'],
              "name": hold['name']
            }
          }
          if (orders.size > 0) {
            for (const [key, value] of orders) {
              const data: IWebHookRequest = {
                'action': action,
                'clientId': key,
                'orderIds': [...new Set(value)],
                'reasonDetailId': null,
                'holdClearance': holdDetail,
                'stationOriginId': null,
                'stationDestinationId': null
              }
              await new RabbitSendInternal().send(EConfigure.PUSH_WEBHOOK_PARTNER, [data], { durable: true, autoDelete: false });

            }
          }
          if (orderLazadas.size > 0) {
            for (let item of orderLazadas) {

              const data: any = {
                'orderId': item,
                'isClearance': true,
                'statusId': EConfigure.HOLD_CLEANCE,
                'now': moment().format(EConfigure.FULL_TIME),
                'hold': holdDetail,
              }
              await new RabbitSendInternal().send(EConfigure.UPDATE_LASTEST_MANIFEST_STATUS, [data], { durable: true, autoDelete: false });
            }
          }

        }
      }
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][handlePushWebHook]', (error as Error).message);
      throw new Error(error as any);
    }
  }


  public async updateTaxCodeNumber(HAWB: string, type?: string): Promise<void> {
    try {
      const taxURL = process.env.TAX_URL || 'https://devapi.globex.vn/taxcode-sync/api/';

      console.log(`--- [UPDATE_TAX_CODE_NUMBER]: ${HAWB} - ${type}`);

      if (type == 'MIC') {
        await Utilities.callAPINonAuth(`${taxURL}mic_tax_number_sync?HAWB=${HAWB}`);
      } else {
        await Utilities.callAPINonAuth(`${taxURL}tax_number_sync?HAWB=${HAWB}`);
      }

    } catch (error) {
      console.error('[service][import][updateTaxCodeNumber]', error);
      Utilities.sendDiscordErr('[service][import][updateTaxCodeNumber]', (error as Error).message);
    }
  }

  public async micOrida(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.IN, [ActionKey.CREATE, ActionKey.UPDATE_CARGO_NAME].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      optional.setRelation(['importDetails']);
      const [idas, mics] = await Promise.all([
        this.idaRepository.queryAll(optional),
        this.micRepository.queryAll(optional),
      ]);

      if (idas.length > 0 || mics.length > 0) {
        let transactions: any[] = [];
        let destroyHAWBs: string[] = [];
        if (idas.length > 0) {
          idas.forEach((IDA: IIDA) => {
            const HAWB: string = String(IDA[EConfigure.HAWB_FIELD]);
            const transaction: any = {
              'HAWB': HAWB,
              'employeeId': employeeId,
              'action': ActionKey.DELETE_HAWB,
              'data': IDA,
              'newData': IDA,
              'classify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
              'currentClassify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
            }
            transactions.push(transaction);
            destroyHAWBs.push(HAWB);
          });
        }

        if (mics.length > 0) {
          mics.forEach((MIC: IMIC) => {
            const HAWB: string = String(MIC[EConfigure.HAWB_FIELD]);
            const transaction: any = {
              'HAWB': HAWB,
              'employeeId': employeeId,
              'action': ActionKey.DELETE_HAWB,
              'data': MIC,
              'newData': MIC,
              'classify': ClassifyValidateName.get(ClassifyValidateKey.MIC) as string,
              'currentClassify': ClassifyValidateName.get(ClassifyValidateKey.MIC) as string,
            }
            transactions.push(transaction);
            destroyHAWBs.push(HAWB);
          });
        }

        if (destroyHAWBs.length > 0) {
          const destroyOptional: Optional = new Optional();
          destroyOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, destroyHAWBs.join()),
          ]);
          await Promise.all([
            this.importTransactionRepository.createBulk(transactions),
            this.idaRepository.destroyData(destroyOptional),
            this.micRepository.destroyData(destroyOptional),
            this.importDetailRepository.destroyData(destroyOptional),
          ]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][micOrida]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMAWB(MAWB: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
          'data': {
            'shipment': null,
            'HAWBClearances': [],
            'totalHAWBClearance': 0
          }
        }
      }
      const micOptional: Optional = new Optional();
      micOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeight', 'totalInvoicePrice', 'invoiceCurrencyCode', 'importerFullName',
        'addressOfImporter', 'consignorName', 'address1', 'address2', 'address3', 'address4'
      ]);
      micOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      micOptional.setRelation(['importDetailItems']);

      const idaOptional: Optional = new Optional();
      idaOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeightGross', 'totalInvoicePrice', 'invoiceCurrencyCode', 'importerFullName',
        'addressOfImporter', 'consignorName', 'address1', 'address2', 'address3', 'address4'
      ]);
      idaOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      idaOptional.setRelation(['importDetailItems']);

      const mecOptional: Optional = new Optional();
      mecOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeight', 'totalOfTaxValue', 'currencyCodeOfTaxValue', 'exporterFullName',
        'addressOfExporter', 'consigneeName', 'address1', 'address2', 'address3', 'address4'
      ]);
      mecOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      mecOptional.setRelation(['exportDetailItems']);

      const edaOptional: Optional = new Optional();
      edaOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeightGross', 'totalInvoicePrice', 'invoiceCurrencyCode', 'exporterFullName',
        'addressOfExporter', 'consigneeName', 'address1', 'address2', 'address3', 'address4'
      ]);
      edaOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      edaOptional.setRelation(['exportDetailItems']);

      const shipmentOptional: Optional = new Optional();
      shipmentOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const [shipment, idas, mics, edas, mecs] = await Promise.all([
        this.shipmentRepository.getOneOptional(shipmentOptional),
        this.idaRepository.queryAll(idaOptional),
        this.micRepository.queryAll(micOptional),
        this.edaRepository.queryAll(edaOptional),
        this.mecRepository.queryAll(mecOptional),

      ]);
      if (shipment && (mics.length > 0 || idas.length > 0 || mecs.length > 0 || edas.length > 0)) {
        const HAWBClearances: IMAWBReport[] = [];
        if (edas.length > 0) {
          edas.forEach((element: EDA) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeightGross'],
              'invoicePrice': element['totalInvoicePrice'],
              'invoiceCurrency': element['invoiceCurrencyCode'],
              'consignorFullName': element['exporterFullName'],
              'consignorAddress': element['addressOfExporter'],
              'consigneeName': element['consigneeName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['exportDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        if (mecs.length > 0) {
          mecs.forEach((element: MEC) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeight'],
              'invoicePrice': element['totalOfTaxValue'],
              'invoiceCurrency': element['currencyCodeOfTaxValue'],
              'consignorFullName': element['exporterFullName'],
              'consignorAddress': element['addressOfExporter'],
              'consigneeName': element['consigneeName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['exportDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        if (idas.length > 0) {
          idas.forEach((element: IDA) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeightGross'],
              'invoicePrice': element['totalInvoicePrice'],
              'invoiceCurrency': element['invoiceCurrencyCode'],
              'consignorFullName': element['importerFullName'],
              'consignorAddress': element['addressOfImporter'],
              'consigneeName': element['consignorName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['importDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        if (mics.length > 0) {
          mics.forEach((element: MIC) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeight'],
              'invoicePrice': element['totalInvoicePrice'],
              'invoiceCurrency': element['invoiceCurrencyCode'],
              'consignorFullName': element['importerFullName'],
              'consignorAddress': element['addressOfImporter'],
              'consigneeName': element['consignorName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['importDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        reponze['status'] = true;
        reponze['message'] = {
          'message': EMessage.SUCCESS,
          'data': {
            'shipment': shipment,
            'HAWBClearances': HAWBClearances,
            'totalHAWBClearance': HAWBClearances.length
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getMAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMAWBs(optional: Optional): Promise<any> {
    try {
      const [data, total] = await this.shipmentRepository.getAll(optional);
      if (data.length > 0) {
        await Promise.all(data.map(async (element: any) => {
          const optinalMAWB: Optional = new Optional();
          optinalMAWB.setWhere([
            new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, element['MAWB']),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          ]);
          const [mic, mec, ida, eda] = await Promise.all([
            this.micRepository.countData(optinalMAWB),
            this.mecRepository.countData(optinalMAWB),
            this.idaRepository.countData(optinalMAWB),
            this.edaRepository.countData(optinalMAWB),
          ]);
          element.setDataValue('totalHAWB', mic + mec + ida + eda);
        }))
      }
      return [data, total];
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getMAWBs]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async MICGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const importOptional: Optional = new Optional();
      importOptional.setRelation(["importDetailItems"]);
      importOptional.setAttributes(value['select'].split(','));
      importOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);
      if (value['classify']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        importOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (value['dateCheckout']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      // mic
      const MICs = await this.micRepository.queryAll(importOptional);

      const obj: any = {};
      if (MICs.length > 0) {
        if (value['boxes'] && value['boxes'].length > 0) {
          const micExternalBoxes = _.groupBy(MICs, MIC => MIC['externalBoxName']);
          const micInternalBoxes = _.groupBy(MICs, MIC => MIC['internalBoxName']);
          value['boxes'].forEach((item: any) => {
            obj[item] = [
              ...micExternalBoxes[item] ? micExternalBoxes[item] : [],
              ...micInternalBoxes[item] ? micInternalBoxes[item] : []
            ];
          })
        } else {
          MICs.forEach((mic: MIC) => {
            let key: string = 'none';
            if (mic['externalBoxName']) {
              key = mic['externalBoxName'];
            } else if (mic['internalBoxName']) {
              key = mic['internalBoxName'];
            }
            if (obj[key]) {
              obj[key].push(mic);
            } else {
              obj[key] = [mic];
            }
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = obj;
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][MICGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async IDAGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const importOptional: Optional = new Optional();
      importOptional.setRelation(["importDetailItems"]);
      importOptional.setAttributes(value['select'].split(','));
      importOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);

      if (value['classify']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        importOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (value['dateCheckout']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      // ida
      const IDAs = await this.idaRepository.queryAll(importOptional);
      if (IDAs.length > 0) {
        const obj: any = {};
        if (value['boxes'] && value['boxes'].length > 0) {
          const idaExternalBoxes = _.groupBy(IDAs, IDA => IDA['externalBoxName']);
          const idaInternalBoxes = _.groupBy(IDAs, IDA => IDA['internalBoxName']);
          value['boxes'].forEach((item: any) => {
            obj[item] = [
              ...idaExternalBoxes[item] ? idaExternalBoxes[item] : [],
              ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []
            ];
          })
        } else {
          IDAs.forEach((ida: IDA) => {
            let key: string = 'none';
            if (ida['externalBoxName']) {
              key = ida['externalBoxName'];
            } else if (ida['internalBoxName']) {
              key = ida['internalBoxName'];
            }
            if (obj[key]) {
              obj[key].push(ida);
            } else {
              obj[key] = [ida];
            }
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = obj;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][IDAGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async totalManagementGate(optional: Optional): Promise<any> {
    try {
      let responze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      let queryAll: number = 0;
      optional.getWhere().forEach((where: Where, index: number) => {
        if (where.getKey() === 'type') {
          queryAll = Number(where.getValue());
          optional.getWhere().splice(index, EConfigure.INDEX_1);
        }
      });
      let totalWeight: number = 0, totalMIC = 0, totalIDA = 0, totalHold = 0, totalDOC = 0, totalCOM = 0, totalHAWB = 0;
      if (queryAll == 1 || queryAll == 0) {
        const micWeigthOptional: Optional = new Optional();
        micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
        micWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);

        const micTotalOtptional: Optional = new Optional();
        micTotalOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const micHoldOtptional: Optional = new Optional();
        micHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const micDOCOtptional: Optional = new Optional();
        micDOCOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const [micWeight, micTotal, micHoldTotal, DOCTotal] = await Promise.all([
          this.micRepository.queryOneRaw(micWeigthOptional),
          this.micRepository.countData(micTotalOtptional),
          this.micRepository.countData(micHoldOtptional),
          this.micRepository.countData(micDOCOtptional)
        ]);
        if (micTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(micWeight['totalMICWeigth']);
        totalMIC = micTotal;
        totalHold += micHoldTotal;
        totalDOC += DOCTotal;
        totalHAWB += micTotal;
      }
      if (queryAll == 2 || queryAll == 0) {
        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);

        const idaTotalOtptional: Optional = new Optional();
        idaTotalOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const idaHoldOtptional: Optional = new Optional();
        idaHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const idaCOMOtptional: Optional = new Optional();
        idaCOMOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);
        const [idaWeight, idaTotal, idaHoldTotal, COMTotal] = await Promise.all([
          this.idaRepository.queryOneRaw(idaWeigthOptional),
          this.idaRepository.countData(idaTotalOtptional),
          this.idaRepository.countData(idaHoldOtptional),
          this.idaRepository.countData(idaCOMOtptional)
        ]);
        if (idaTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(idaWeight['totalIDAWeigth']);
        totalIDA = idaTotal;
        totalHold += idaHoldTotal;
        totalCOM += COMTotal;
        totalHAWB += idaTotal;

      }

      if (queryAll == 3) {
        const micWeigthOptional: Optional = new Optional();
        micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
        micWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
        ]);

        const micDOCOtptional: Optional = new Optional();
        micDOCOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const [micWeight, DOCTotal] = await Promise.all([
          this.micRepository.queryOneRaw(micWeigthOptional),
          this.micRepository.countData(micDOCOtptional)
        ]);
        if (DOCTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(micWeight['totalMICWeigth']);
        totalDOC += DOCTotal;
      }

      if (queryAll == 4) {
        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
        ]);

        const idaCOMOtptional: Optional = new Optional();
        idaCOMOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const [idaWeight, COMTotal] = await Promise.all([
          this.idaRepository.queryOneRaw(idaWeigthOptional),
          this.idaRepository.countData(idaCOMOtptional)
        ]);
        if (COMTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(idaWeight['totalIDAWeigth']);
        totalCOM += COMTotal;
      }

      if (queryAll == 5) {
        const micWeigthOptional: Optional = new Optional();
        micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
        micWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const micHoldOtptional: Optional = new Optional();
        micHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const idaHoldOtptional: Optional = new Optional();
        idaHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);


        const [micWeight, idaWeight, micHoldTotal, idaHoldTotal] = await Promise.all([
          this.micRepository.queryOneRaw(micWeigthOptional),
          this.idaRepository.queryOneRaw(idaWeigthOptional),
          this.micRepository.countData(micHoldOtptional),
          this.idaRepository.countData(idaHoldOtptional),
        ]);
        if (micHoldTotal > 0 || idaHoldTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += (Number(idaWeight['totalIDAWeigth']) + Number(micWeight['totalIDAWeigth']));
        totalHold += micHoldTotal + idaHoldTotal;
      }

      responze['message']['data'] = {
        totalWeight: +(totalWeight.toFixed(2)),
        totalMIC,
        totalIDA,
        totalHold,
        totalDOC,
        totalCOM,
        totalHAWB
      }
      return responze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][managementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async exportManagementGate(optional: Optional, employeeId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      let queryAll: number = 0;
      let title: string = '';
      let MICs: MIC[] = [];
      let IDAs: IDA[] = [];
      let classify: any = null;
      let startCheckout: any = null;
      let endCheckout: any = null;
      let removeIndex: number[] = [];
      let printType: number = 0;
      let contentTitle: string = '';
      optional.getWhere().forEach((where: Where, index: number) => {
        if (where.getKey() === 'type') {
          queryAll = Number(where.getValue());
          removeIndex.push(index);
        }
        if (where.getKey() === 'statusName') {
          title = where.getValue() ? String(where.getValue()) : '';
          if (title !== 'Tất cả') {
            title = `DANH SÁCH ${title.toUpperCase()}`;
          } else {
            title = `DANH SÁCH MANIFEST`;
          }
          removeIndex.push(index);
        }
        if (where.getKey() === 'startCheckout') {
          startCheckout = where.getValue();
          removeIndex.push(index);
        }
        if (where.getKey() === 'endCheckout') {
          endCheckout = where.getValue();
          removeIndex.push(index);
        }
        if (where.getKey() === 'printType') {
          printType = where.getValue();
          removeIndex.push(index);
        }
      });
      const query: Where[] = optional.getWhere().filter((value, index) => {
        return removeIndex.indexOf(index) == -1;
      })
      const micOptional: Optional = new Optional();
      micOptional.setAttributes([`${EConfigure.HAWB_CLEARANCE_FIELD}`, "dateCheckin", "dateClearanced", "MAWB", "declarationNo", "cargoPiece", "cargoWeight", "classify", "dateCheckout", "hubId", "inspectionKindClassification"]);
      micOptional.setWhere(query);
      micOptional.setRelation(['client']);

      const idaOptional: Optional = new Optional();
      idaOptional.setAttributes([`${EConfigure.HAWB_CLEARANCE_FIELD}`, "dateCheckin", "dateClearanced", "MAWB", "declarationNo", "cargoPiece", "cargoWeightGross", "classify", "dateCheckout", "hubId", "inspectionKindClassification"]);
      idaOptional.setWhere(query);
      idaOptional.setRelation(['client']);
      if (queryAll === 0) {
        contentTitle = `${title.toLocaleLowerCase()} tất cả`;
        micOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        );
        [IDAs, MICs] = await Promise.all([
          this.idaRepository.queryAll(idaOptional),
          this.micRepository.queryAll(micOptional)
        ]);
      }
      if (queryAll === 1) {
        contentTitle = `${title.toLocaleLowerCase()} ${EConfigure.MIC}`;
        micOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        MICs = await this.micRepository.queryAll(micOptional);
      }
      if (queryAll === 2) {
        contentTitle = `${title.toLocaleLowerCase()} ${EConfigure.H11}`;
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        IDAs = await this.idaRepository.queryAll(idaOptional);
      }

      if (queryAll === 3) {
        contentTitle = `${title.toLocaleLowerCase()} ${String(EConfigure.DOC).toLocaleLowerCase()}`;
        micOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        MICs = await this.micRepository.queryAll(micOptional);
      }

      if (queryAll === 4) {
        contentTitle = `${title.toLocaleLowerCase()} ${String(EConfigure.COM).toLocaleLowerCase()}`;
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        IDAs = await this.idaRepository.queryAll(idaOptional);
      }

      if (queryAll === 5) {
        contentTitle = `${title.toLocaleLowerCase()} ${String(EConfigure.HOLD).toLocaleLowerCase()}`;
        micOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        );
        [IDAs, MICs] = await Promise.all([
          this.idaRepository.queryAll(idaOptional),
          this.micRepository.queryAll(micOptional)
        ]);;
      }
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        let hubId: number = 0;
        if (IDAs.length > 0) {
          hubId = IDAs[0]['hubId'] ? IDAs[0]['hubId'] : 0;
          let classify: string = "";
          idaData = IDAs.map((IDA: IIDA) => {
            if (IDA['isHold'] == true) {
              classify = 'HOLD';
            } else {
              if (IDA['classify'] == "PAR") {
                classify = "IDA";
              } else {
                classify = "Mậu dịch";
              }
            }
            const ida: IManagement = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'declarationNo': IDA['declarationNo'],
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'weightRound': Number(IDA['cargoWeightGross']),
              'MAWB': IDA['MAWB'],
              'classify': classify,
              'dateCheckout': IDA['dateCheckout'] ? moment(IDA['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateGate': IDA['dateCheckout'] ? moment(IDA['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateCheckin': IDA['dateCheckin'] ? moment(IDA['dateCheckin']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateClearanced': IDA['dateClearanced'] ? moment(IDA['dateClearanced']).format("DD-MM-YYYY HH:mm:ss") : '',
              'clearanceInspection': Number(IDA['inspectionKindClassification']),
              'clientName': IDA['client'] ? IDA['client']['code'] : '',
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          hubId = MICs[0]['hubId'] ? MICs[0]['hubId'] : 0;
          let classify: string = "";
          micData = MICs.map((MIC: IMIC) => {
            if (MIC['isHold'] == true) {
              classify = 'HOLD';
            } else {
              if (MIC['classify'] == "PAR") {
                classify = "MIC";
              } else {
                classify = "DOC";
              }
            }
            const mic: IManagement = {
              'HAWB': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'declarationNo': MIC['declarationNo'],
              'cargoPiece': MIC['cargoPiece'],
              'weight': MIC['cargoWeight'],
              'weightRound': Number(MIC['cargoWeight']),
              'MAWB': MIC['MAWB'],
              'classify': classify,
              'dateCheckout': MIC['dateCheckout'] ? moment(MIC['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateGate': MIC['dateCheckout'] ? moment(MIC['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateCheckin': MIC['dateCheckin'] ? moment(MIC['dateCheckin']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateClearanced': MIC['dateClearanced'] ? moment(MIC['dateClearanced']).format("DD-MM-YYYY HH:mm:ss") : '',
              'clearanceInspection': Number(MIC['inspectionKindClassification']),
              'clientName': MIC['client'] ? MIC['client']['code'] : '',
            }
            return mic;
          });
        }

        manifests = [...micData, ...idaData];
        if (manifests.length > 0 && (printType == PrintKey.MANAGEMENT_PDF || printType == PrintKey.MANAGEMENT_EXCEL)) {
          const createHistory = await this.historyMonitorRepository.createData({
            title: `${contentTitle}_${moment().format("YYMMDD_HHmmssSS")}`,
            employeeId: employeeId,
            printType: printType,
            hubId: hubId,
            isSucess: false,
          });

          if (printType == PrintKey.MANAGEMENT_PDF) {
            this.exportPDF(manifests, createHistory, classify, startCheckout, endCheckout, title);
          } else {
            this.exportExcel(manifests, createHistory);
          }
          reponze['message']['data'] = createHistory;
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][exportManagementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async exportExcel(manifests: any, createHistory: any): Promise<void> {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("sheet 1", { properties: { tabColor: { argb: 'FFC0000' } } });

      worksheet.columns = [
        { header: "Mã vận đơn", key: "HAWB", width: 18 },
        { header: "Số tờ khai", key: "declarationNo", width: 16 },
        { header: "MAWB", key: "MAWB", width: 16 },
        { header: "Loại hình", key: "classify", width: 10 },
        { header: "Kiện", key: "cargoPiece", width: 5 },
        { header: "Trọng lượng", key: "weightRound", width: 15 },
        { header: "Ra cổng", key: "dateGate", width: 20 },
        { header: "Thông quan", key: "dateClearanced", width: 20 },
        { header: "VNS luồng", key: "clearanceInspection", width: 14 },
        { header: "Checkin", key: "dateCheckin", width: 20 },
        { header: "Checkout", key: "dateCheckout", width: 20 },
        { header: "Khách hàng", key: "clientName", width: 14 },
        { header: "Lý do", key: "note", width: 10 },
      ];
      worksheet.getRow(1).eachCell((cell: any) => {
        cell.font = { bold: true, name: 'Calibri' };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '96C8FB' },
          bgColor: { argb: '96C8FB' },
        };
        cell.alignment = { vertical: 'middle', horizontal: 'center' },
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
      });
      worksheet.addRows(manifests);

      let filename = `DS_MANIFEST_RA_CONG_${moment().format(EConfigure.DAY_TIME)}_${moment().format("X")}.xlsx`;
      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}/${filename}`)
      createHistory.isSucess = true;
      createHistory.successDate = moment().format(EConfigure.FULL_TIME);
      createHistory.link = `${new Config().clearanceDomain()}/clearance/invoice/${filename}`;
      createHistory.save();
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][exportManagementGate][exportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async exportPDF(manifests: any, createHistory: any, classify: any, startCheckout: any, endCheckout: any, title: any): Promise<void> {
    try {
      const config: pdf.CreateOptions = {
        "format": "A4",
        "orientation": "portrait",
        "border": {
          "top": "8px",
          "right": "10px",
          "bottom": "20px",
          "left": "10px"
        },
        timeout: 300000
      }

      const now: string = moment().format('DD-MM-YYYY');
      let date: string = '';
      let filename = `bangkeracong${classify ? '_' + classify : ''}_${moment().format("X")}.pdf`;
      let pathTemplate = `../../../../../template/bangke/bangkeracong.ejs`;

      if (startCheckout && startCheckout) {
        if (moment(startCheckout).isSame(endCheckout, 'date')) {
          date = `Ngày ${moment(startCheckout).format('DD-MM-YYYY')}`;
        } else {
          date = `Từ ngày ${moment(startCheckout).format('DD-MM-YYYY')} đến ngày ${moment(endCheckout).format('DD-MM-YYYY')}`;
        }
      }

      config['footer'] = {
        "height": "10mm",
        "contents": {
          default: `<table width="100%;" style="font-size: 8px;">
            <tr>
              <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
              <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
            </tr>
          </table>`,
        }
      }
      let template = await ejs.renderFile(path.join(__dirname, pathTemplate), { manifests, title, date }, { async: true });

      pdf.create(template, config).toFile(`${EConfigure.FOLDER_INVOICE}/${filename}`, function (err, res) {
        if (res) {
          createHistory.isSucess = true;
          createHistory.successDate = moment().format(EConfigure.FULL_TIME);
          createHistory.link = `${new Config().clearanceDomain()}/clearance/invoice/${filename}`;
          createHistory.save();
        }
        if (err) {
          Utilities.sendDiscordErr('[service][PdfGenerate][create][pdfPrint]', err.toString());
        }
      });
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][exportManagementGate][exportPDF]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async reportCustom(optional: Optional): Promise<any> {
    try {
      let responze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
          }
        }
      }

      const optionalInOut: Optional = new Optional();
      optional.getWhere().forEach((where: Where) => {
        if ((where.getKey() !== 'dateCheckin' || where.getKey() !== 'dateCheckout') && (where.getValue() !== 'null')) {
          optionalInOut.getWhere().push(where);
        }
      });

      const micWeigthOptional: Optional = new Optional();
      micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
      micWeigthOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
      ]);

      const micTotalOtptional: Optional = new Optional();
      micTotalOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
      ]);

      const micHoldOtptional: Optional = new Optional();
      micHoldOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
      ]);

      const micDOCOtptional: Optional = new Optional();
      micDOCOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
      ]);

      const micCheckInOtptional: Optional = new Optional();
      micCheckInOtptional.setWhere([
        ...optionalInOut.getWhere(),
      ]);

      const micCheckOutOtptional: Optional = new Optional();
      micCheckOutOtptional.setWhere([
        ...optionalInOut.getWhere(),
      ]);
      ////
      const idaWeigthOptional: Optional = new Optional();
      idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
      idaWeigthOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
      ]);

      const idaTotalOtptional: Optional = new Optional();
      idaTotalOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
      ]);

      const idaHoldOtptional: Optional = new Optional();
      idaHoldOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
      ]);

      const idaCOMOtptional: Optional = new Optional();
      idaCOMOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
      ]);

      const idaCheckInOtptional: Optional = new Optional();
      idaCheckInOtptional.setWhere([
        ...optionalInOut.getWhere(),
      ]);

      const idaCheckOutOtptional: Optional = new Optional();
      idaCheckOutOtptional.setWhere([
        ...optionalInOut.getWhere(),
      ]);

      const [idaWeight, idaTotal, idaHoldTotal, COMTotal, idaCheckin, idaCheckout, micWeight, micTotal, micHoldTotal, DOCTotal, micCheckin, micCheckout] = await Promise.all([
        this.idaRepository.queryOneRaw(idaWeigthOptional),
        this.idaRepository.countData(idaTotalOtptional),
        this.idaRepository.countData(idaHoldOtptional),
        this.idaRepository.countData(idaCOMOtptional),
        this.idaRepository.countData(idaCheckInOtptional),
        this.idaRepository.countData(idaCheckOutOtptional),


        this.micRepository.queryOneRaw(micWeigthOptional),
        this.micRepository.countData(micTotalOtptional),
        this.micRepository.countData(micHoldOtptional),
        this.micRepository.countData(micDOCOtptional),
        this.micRepository.countData(micCheckInOtptional),
        this.micRepository.countData(micCheckOutOtptional),
      ]);
      let totalWeight: number = 0, totalHold: number = 0;

      if (micTotal > 0 || idaTotal > 0) {
        responze['status'] = true;
        responze['message']['message'] = EMessage.FOUND;
      }
      totalWeight += (Number(idaWeight['totalIDAWeigth']) + Number(micWeight['totalMICWeigth']));
      totalHold += micHoldTotal + idaHoldTotal;

      responze['message']['data'] = {
        "totalWeight": +(totalWeight.toFixed(2)),
        "totalMIC": micTotal,
        "totalIDA": idaTotal,
        "totalHold": totalHold,
        "totalDOC": DOCTotal,
        "totalCOM": COMTotal,
        "totalHAWB": idaTotal + micTotal,
        "totalCheckin": idaCheckin + micCheckin,
        "totalCheckout": idaCheckout + micCheckout,
      }
      return responze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][reportCustom]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async reportCustomExport(optional: Optional, parseUrl: any, hubIds: string, employeeId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      let excelFields: string = '';
      let MAWB: string = '';
      let hubId: number = 0;
      if (hubIds.split(',').length == 1) {
        hubId = Number(hubIds);
      }
      optional.getWhere().forEach((where: Where, index: number) => {
        if (where.getKey() === 'excelFields') {
          excelFields = String(where.getValue());
          optional.getWhere().splice(index, EConfigure.INDEX_1);
        }
        if (where.getKey() === "MAWB") {
          MAWB = String(where.getValue());
        }
      });
      const createHistory = await this.historyInvoiceService.create({
        "MAWB": MAWB ? MAWB : employeeId,
        "employeeId": employeeId,
        "printType": PrintKey.REPORT_CUSTOM_IMPORT,
        "hubId": hubId,
        "isSucess": false,
        "dataSearch": parseUrl['params'],
        "note": null,
      });

      this.handleExportExcel(optional, createHistory, excelFields);
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.SUCCESS;
      reponze['message']['data'] = createHistory;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][reportCustomExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async handleExportExcel(optional: Optional, createHistory: HistoryInvoice, excelFields: string): Promise<void> {
    try {
      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ])
      optionalQuery.setRelation(['holds', 'manifest']);
      const [IDAs, MICs] = await Promise.all([
        this.idaRepository.queryAll(optionalQuery),
        this.micRepository.queryAll(optionalQuery)
      ]);
      if (IDAs.length > 0 || MICs.length > 0) {
        let manifests: any[] = [];
        if (IDAs.length > 0) {
          IDAs.forEach((IDA: IIDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            const ida: IExportReport = {
              'trackingNo': String(IDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'MAWB': String(IDA['MAWB']),
              'origin': String(IDA['countryCode']),
              'country': String(IDA['countryCode']),
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': IDA['manifest'] ? IDA['manifest']['weightChargeKG'] : null,
              'manisfestImported': String(IDA['createdAt']),
              'arDate': String(IDA['dateCheckin']),
              'cdsDate': String(IDA['declarationPlannedDate']),
              'crDate': String(IDA['dateCheckout']),
              'cdsNo': String(IDA['declarationNo']),
              'importType': (IDA['classify'] === ClassifyName.get(ClassifyKey.PAR)) ? String(EConfigure.H11) : 'MẬU DỊCH',
              'lane': Number(IDA['inspectionKindClassification']),
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : IDA['phase_name']['vi'],
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'duty': fee,
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': String(EConfigure._H11),
              'consignorName': String(IDA['consignorName']),
              'consignorAddress': String(IDA['address1']),
              'consignorCityName': null,
              'consignorContactName': String(IDA['consignorName']),
              'consignorTelephone': null,
              'consigneeName': String(IDA['importerFullName']),
              'consigneeAddress': String(IDA['addressOfImporter']),
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': String(IDA['importerFullName']),
              'consigneeTelephone': String(IDA['telephoneNumberOfImporter']),
              'remarks': null,
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        if (MICs.length > 0) {
          MICs.forEach((MIC: IMIC) => {
            const mic: IExportReport = {
              'trackingNo': String(MIC[EConfigure.HAWB_CLEARANCE_FIELD]),
              'MAWB': String(MIC['MAWB']),
              'origin': String(MIC['countryCode']),
              'country': String(MIC['countryCode']),
              'pcs': Number(MIC['cargoPiece']),
              'trueWeight': Number(MIC['cargoWeight']),
              'volWeight': MIC['manifest'] ? MIC['manifest']['weightChargeKG'] : null,
              'manisfestImported': String(MIC['createdAt']),
              'arDate': String(MIC['dateCheckin']),
              'cdsDate': String(MIC['declarationPlannedDate']),
              'crDate': String(MIC['dateCheckout']),
              'cdsNo': String(MIC['declarationNo']),
              'importType': (MIC['classify'] === ClassifyName.get(ClassifyKey.PAR)) ? EConfigure.MIC : MIC['classify'],
              'lane': Number(MIC['inspectionKindClassification']),
              'customsStatus': (MIC['isHold'] && MIC['holds'] && MIC['holds'].length > 0) ? MIC['holds'][0]['name'] : MIC['phase_name']['vi'],
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MIC['totalInvoicePrice']),
              'importTax': 0,
              'VAT': 0,
              'duty': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': String(EConfigure._MIC),
              'consignorName': String(MIC['consignorName']),
              'consignorAddress': String(MIC['address1']),
              'consignorCityName': null,
              'consignorContactName': String(MIC['consignorName']),
              'consignorTelephone': null,
              'consigneeName': String(MIC['importerFullName']),
              'consigneeAddress': String(MIC['addressOfImporter']),
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': String(MIC['importerFullName']),
              'consigneeTelephone': String(MIC['telephoneNumberOfImporter']),
              'remarks': null,
              'taxNumberCode': null,
              'taxNumberDate': null,
              'externalBoxName': MIC['externalBoxName'],
            }
            manifests.push(mic);
          });
        }

        manifests.forEach((manifest: any, index: number) => {
          manifest['no'] = index + EConfigure.INDEX_1;
        });

        const mapFields: any = {
          'trackingNo': { header: "Tracking no.", key: "trackingNo", width: 18 },
          'MAWB': { header: "MAWB", key: "MAWB", width: 18 },
          'origin': { header: "Origin", key: "origin", width: 18 },
          'country': { header: "Country", key: "country", width: 18 },
          'pcs': { header: "Pcs", key: "pcs", width: 18 },
          'trueWeight': { header: "True weight", key: "trueWeight", width: 18 },
          'volWeight': { header: "Vol weight", key: "volWeight", width: 18 },
          'manisfestImported': { header: "Manisfest imported", key: "manisfestImported", width: 18 },
          'arDate': { header: "AR date", key: "arDate", width: 18 },
          'cdsDate': { header: "CDS date", key: "cdsDate", width: 18 },
          'crDate': { header: "CR date", key: "crDate", width: 18 },
          'cdsNo': { header: "CDS no.", key: "cdsNo", width: 18 },
          'importType': { header: "Import type", key: "importType", width: 18 },
          'lane': { header: "Lane", key: "lane", width: 18 },
          'customsStatus': { header: "Customs status", key: "customsStatus", width: 18 },
          'reasonDate': { header: "Reason date", key: "reasonDate", width: 18 },
          'handover': { header: "Handover", key: "handover", width: 18 },
          'shipmemntValueUSD': { header: "Shipmemnt value (USD)", key: "shipmemntValueUSDshipmemntValueUSD", width: 18 },
          'importTax': { header: "Import tax", key: "importTax", width: 18 },
          'VAT': { header: "VAT", key: "VAT", width: 18 },
          'duty': { header: "Duty", key: "duty", width: 18 },
          'dutyTaxVND': { header: "Duty & tax( VND)", key: "dutyTaxVND", width: 18 },
          'otherFee': { header: "Other fee (if any)", key: "otherFee", width: 18 },
          'notes': { header: "Notes", key: "notes", width: 18 },
          'consignorName': { header: "Consignor_NM", key: "consignorName", width: 18 },
          'consignorAddress': { header: "Consignor_address", key: "consignorAddress", width: 18 },
          'consignorCityName': { header: "Consignor_city_NM", key: "consignorCityName", width: 18 },
          'consignorContactName': { header: "Consignor_contact_NM", key: "consignorContactName", width: 18 },
          'consignorTelephone': { header: "Consignor_tel_no", key: "consignorTelephone", width: 18 },
          'consigneeName': { header: "Consignee_NM", key: "consigneeName", width: 18 },
          'consigneeAddress': { header: "Consignee_address", key: "consigneeAddress", width: 18 },
          'consigneeAddress2': { header: "Consignee_address2", key: "consigneeAddress2", width: 18 },
          'consigneeCityName': { header: "Consignee_city_NM", key: "consigneeCityName", width: 18 },
          'consigneeContactName': { header: "Consignee_contact_NM", key: "consigneeContactName", width: 18 },
          'consigneeTelephone': { header: "Consignee_tel_no", key: "consigneeTelephone", width: 18 },
          'remarks': { header: "Remarks", key: "remarks", width: 18 },
          'taxNumberCode': { header: "Số biên lai", key: "taxNumberCode", width: 18 },
          'taxNumberDate': { header: "Ngày in biên lai", key: "taxNumberDate", width: 18 },
          'externalBoxName': { header: "External box", key: "externalBoxName", width: 18 },
        }

        const colums: any = [{ header: "No.", key: "no", width: 5 }];
        excelFields.split(',').forEach((item: string) => {
          colums.push(mapFields[item])
        });
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet("sheet 1", { properties: { tabColor: { argb: 'FFC0000' } } });

        worksheet.columns = colums;
        worksheet.getRow(1).eachCell((cell: any) => {
          cell.font = { bold: true, name: 'Calibri' };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '96C8FB' },
            bgColor: { argb: '96C8FB' },
          };
          cell.alignment = { vertical: 'middle', horizontal: 'center' },
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };
        });
        worksheet.addRows(manifests);

        let filename = `BAO_CAO_HAI_QUAN_${moment().format(EConfigure.DAY_TIME)}_${moment().format("X")}.xlsx`;
        await workbook.xlsx.writeFile(`${EConfigure.FOLDER_REPORT_CUSTOM}${filename}`);
        createHistory.link = `${new Config().clearanceDomain()}/clearance/report/${filename}`;
      }
      createHistory.isSucess = true;
      createHistory.successDate = moment().format(EConfigure.FULL_TIME);
      createHistory.save();
    } catch (error) {
      Utilities.sendDiscordErr('[service][reportCustomExport][handleExportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async changeExchangeRate(value: any, employeeId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
        }
      }
      const { HAWBs, exchangeRate } = value;
      if (HAWBs && HAWBs.length > 0) {
        const createTransactions: any = [];
        HAWBs.forEach((item: string) => {
          const tractionObj: IExchangeRateTransaction = {
            'HAWB': item,
            'action': ActionKey.UPDATE_EXCHANGE_RATE,
            'employeeId': employeeId,
            'newData': exchangeRate,
          }
          createTransactions.push(tractionObj);
        });
        const queryHAWBs: string = `'${HAWBs.join("','")}'`;
        const [[ida, totalIDA], [mic, totalMIC]] = await Promise.all([
          this._sequelize.query(`
            UPDATE clearance_idas SET "priceVND" = ROUND("totalInvoicePrice" * ${exchangeRate}), "valueClearanceVND" = ${exchangeRate} 
            WHERE "phase" <> 7 AND "HAWB" IN (${queryHAWBs}) AND "invoiceCurrencyCode" <> '${EConfigure.CURRENCY_VND}'`,
            { type: this._sequelize.QueryTypes.UPDATE }),
          this._sequelize.query(`
            UPDATE clearance_mics SET "priceVND" = ROUND("totalInvoicePrice" * ${exchangeRate}), "valueClearanceVND" = ${exchangeRate} 
            WHERE "phase" <> 7 AND "HAWB" IN (${queryHAWBs}) AND "invoiceCurrencyCode" <> '${EConfigure.CURRENCY_VND}'`,
            { type: this._sequelize.QueryTypes.UPDATE }),
        ]);

        if (totalIDA > 0 || totalMIC > 0) {
          await Promise.all([
            this._sequelize.query(`
              UPDATE clearance_import_details SET "priceVND" = ROUND("invoiceValue" * ${exchangeRate})
              WHERE "HAWB" IN (${queryHAWBs})`,
              { type: this._sequelize.QueryTypes.UPDATE }),
            this.importTransactionRepository.createBulk(createTransactions),
          ]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][changeExchangeRate]', (error as Error).message);
      throw new Error(error as any);
    }
  }


  public async getOneMECEDA(HAWB: string, optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      optional.getWhere().push(
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      );
      // ida
      const EDAQuery = this.edaRepository.getOneOptional(optional);

      // mic
      const MECQuery = this.mecRepository.getOneOptional(optional);

      let [EDA, MEC] = await Promise.all([EDAQuery, MECQuery]);
      if (EDA) {
        const EDAData: EDA = EDA;
        EDAData.setDataValue('dataType', EConfigure.EDA);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = EDAData;
      }
      if (MEC) {
        const MECData: MEC = MEC;
        MECData.setDataValue('dataType', EConfigure.MEC);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = MECData;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getOneMECEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async masterLimit(hubId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const [MICs, IDAs] = await Promise.all([
        this.sequelize.query(`
          SELECT "MAWB"
            FROM (
                SELECT "MAWB", MAX("createdAt") AS "createdAt"
                FROM "clearance_mics" AS "MIC"
                WHERE "hubId" = ${hubId} AND "MAWB" IS NOT NULL AND "isDeleted" = false
                GROUP BY "MAWB"
            ) AS "MIC"
            ORDER BY "createdAt" DESC
            LIMIT 200;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB"
            FROM (
                SELECT "MAWB", MAX("createdAt") AS "createdAt"
                FROM "clearance_idas" AS "MIC"
                WHERE "hubId" = ${hubId} AND "MAWB" IS NOT NULL AND "isDeleted" = false
                GROUP BY "MAWB"
            ) AS "MIC"
            ORDER BY "createdAt" DESC
            LIMIT 200;`,
          { type: this.sequelize.QueryTypes.SELECT }),
      ]);
      let masters: string[] = [];
      if (IDAs.length > 0 || MICs.length > 0) {
        masters = [...IDAs, ...MICs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][masterLimit]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateSortLane(HAWB: string): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      let [mic, ida] = await Promise.all([
        this.micRepository.getOneOptional(optional),
        this.idaRepository.getOneOptional(optional)
      ]);
      if (mic || ida) {
        let micObj: MIC = mic;
        let idaObj: IDA = ida;
        micObj["isSortLane"] = true;
        idaObj["isSortLane"] = true;
        await micObj.save();
        await idaObj.save();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = true;
      }
      return reponze;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async generateInvoice(data: Record<string, any>): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': 'Vui lòng kiểm tra lại HAWB hoặc chưa thông quan hoặc đã có số biên lai',
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      const checkAccept: Optional = new Optional();
      const checkInvoice: Optional = new Optional();
      checkAccept.setAttributes(['HAWB']);
      checkAccept.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.NOT_EQUAL, ActionKey.ACCEPT_CLEARANCE),
      ]);
      checkInvoice.setAttributes(['HAWB']);
      checkInvoice.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
      ]);
      optional.setAttributes(['HAWB']);
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.IS, null),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
      ]);
      optional.setOrderby([
        new OrderBy('dateClearanced', EConfigure.ASCENDING)
      ]);
      const [mics, accepts, invoices] = await Promise.all([
        this.micRepository.queryAll(optional),
        this.micRepository.queryAll(checkAccept),
        this.micRepository.queryAll(checkInvoice),
      ]);
      if (mics.length > 0 && accepts.length == 0 && invoices.length == 0) {
        for (const item of mics) {
          const taxURL = process.env.TAX_URL || 'https://devapi.globex.vn/taxcode-sync/api/';
          console.log(taxURL);
          await Utilities.callAPINonAuth(`${taxURL}mic_tax_number_manual?HAWB=${item['HAWB']}`);
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      } else {
        reponze['message']['error'] = {
          "notAccept": accepts,
          "invoiveNumber": invoices,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][generateInvoice]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async swapInvoice(data: Record<string, any>[]): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': 'Vui lòng kiểm tra lại các HAWB cần chuyển số biên lai',
          'data': null,
          'error': []
        }
      }
      if (data.length > 0) {
        const HAWBs: string[] = data.map((item) => item['HAWB']);
        const optional: Optional = new Optional();
        optional.setAttributes(['HAWB', 'phase', 'taxCodeNumber']);
        optional.setWhere([
          new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        const mics: MIC[] = await this.micRepository.queryAll(optional);
        if (mics.length > 0) {
          if (mics.every((item) => item.phase == ActionKey.ACCEPT_CLEARANCE && item.taxCodeNumber != null)) {
            for (const item of data) {
              const updateOptional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, item['HAWB']),
                new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, item['HAWB']),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
              ]);
              await this.micRepository.updateData({ 'taxCodeNumber': item['taxCodeNumber'] }, updateOptional);
            }
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.SUCCESS;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][swapInvoice]', (error as Error).message);
      throw new Error(error as any);
    }
  }
}

export default ImportClearanceService;