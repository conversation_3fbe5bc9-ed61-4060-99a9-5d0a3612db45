'use strict';

import MECClearanceRepository from "./eMECClearance.reporsitory";
import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import { Database } from '../../../database/index';
import { ClassifyName, ClassifyKey, ClassifyValidateName, ClassifyValidateKey } from "../../../emuns/classifyExport";
import { ActionKey } from "../../../emuns/action";
import { IResponze } from "../../../https/responze";
import { WeightName, WeightKey, WeightClearance } from "../../../emuns/weight";
import { IMEC, ICountry, IWarehouse, IExportValidate, IExportDetailValidate, IExportDetailCreate, IEDACreate, IEDA, EDA, IEDAMaster, IExportDetail, IMasterValidate, Country, ExportDetail, TaxCode, User, CustomerBusiness, CustomerPersonal, Hold, eMEC } from "../../../models/index.model";
import Utilities from "../../../util/utilities";
import { Clearance } from "../../../xmlClearance/clearance";
import { ClearanceTypeId } from "../../../xmlClearance/enum";
import moment from "moment";
import HSCodeDetailRepository from "../hscodeDetail/hscodeDetail.reporsitory";
import ExportClearanceDetailService from "../exportClearanceDetail/exportClearanceDetail.service";
import HSCodeRepository from "../hscode/hscode.reporsitory";
import TaxCodeRepository from "../taxCode/taxCode.reporsitory";
import OrderBy from "../../../parser/orderBy";
import { IHoldTransaction, IReturnCargo } from "../../../models/importTransaction.model";
import UserRepository from "../user/user.reporsitory";
import ExportClearanceValidate from "./eMECClearance.validate";
import ErrorValidate from "../../../util/error.validate";
import { ClearanceCreateKey } from "../../../emuns/clearanceCreate";
import ClearanceCreateLogMongo from "../../../models/mongo/clearanceCreateLog.model";
import eMECRepository from "./eMECClearance.reporsitory";
import ExportDetailRepository from "../eExportClearanceDetail/exportClearanceDetail.reporsitory";

interface IStoreWarehouse {
  HAWB?: string;
  MAWB?: string;
  HAWBClearance?: string;
  warehouseAddress?: string;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
}

interface IHold {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IExportDetail[];
  reasonDetails?: Hold[];
}

interface IClearanceCreateLog {
  clearanceCreateLogId?: number;
  data?: any;
  message?: string;
  cause?: any;
  processResult?: any;
  handleData?: any;
  type?: number;
  classify?: any;
  isSuccess?: boolean;
}

interface ICheckout {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  details?: IDetail[];
}

interface IDetail {
  itemNameVN: string;
  quantity: number;
}

interface IExportReport {
  expDate: string;
  trackingNo: string;
  name: string;
  MAWB: string;
  country: string;
  pcs: number;
  grossWeight: number;
  puDate: string;
  cdsDate: string;
  crDate: string;
  confirmUpliftDate: string;
  cdsNo: string;
  importType: any;
  lane: number;
  customsStatus: string;
  customsClearance: any;
  handling: any;
  shipmemntValue: any;
  dutyTaxVND: number;
  otherFee: number;
  notes: string;
  type: string;
  partner: string;
}

interface IManagement {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  bussiness?: CustomerBusiness;
  personal?: CustomerPersonal;
  exportDetails?: ExportDetail[];
  isHold?: boolean;
  reasonHold?: Hold[];
}

interface IGetAll {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IExportDetail[];
  inspectionKindTimes?: any;
  clearanceDeclarationTimes?: any;
  reasonDetails?: Hold[];
  isHold?: any;
  isSortLane: boolean;
  externalBoxName?: string;
  internalBoxName?: string;
  createdAt: string;
}

interface IAccountExportReport {
  trackingNo: any;
  MAWB: any;
  origin: any;
  country: any;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: any;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: any;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: any;
  consignorName: any
  consignorAddress: any;
  consignorCityName: any;
  consignorContactName: any;
  consignorTelephone: any;
  consigneeName: any;
  consigneeAddress: any;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: any;
  consigneeTelephone: any;
  remarks: any;
  status?: any;
  statusId: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;
  customerBusiness?: any;
  customerPersonal?: any;
  dateClearanced?: any;
  orderNumber?: any;
  itemIds?: any;
  itemCount?: any;
  station?: any;
  exporterCode?: any;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
}

class eMECClearanceService extends BaseService {
  private sequelize: any;
  private eMECRepository: eMECRepository;
  private taxCodeRepository: TaxCodeRepository;
  private exportDetailRepository: ExportDetailRepository;
  private hscodeDetailRepository: HSCodeDetailRepository;
  private hscodeRepository: HSCodeRepository;
  private _sequelize: any;
  constructor() {
    super(new MECClearanceRepository());
    this.sequelize = Database.Sequelize;
    this.eMECRepository = new eMECRepository();
    this.exportDetailRepository = new ExportDetailRepository();
    this.hscodeDetailRepository = new HSCodeDetailRepository();
    this.taxCodeRepository = new TaxCodeRepository();
    this.hscodeRepository = new HSCodeRepository();
    this._sequelize = Database.Sequelize;
  }

  public async generateInvoice(data: Record<string, any>): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': 'Vui lòng kiểm tra lại HAWB hoặc chưa thông quan hoặc đã có số biên lai',
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      const checkAccept: Optional = new Optional();
      const checkInvoice: Optional = new Optional();
      checkAccept.setAttributes(['HAWB']);
      checkAccept.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.NOT_EQUAL, ActionKey.ACCEPT_CLEARANCE),
      ]);
      checkInvoice.setAttributes(['HAWB']);
      checkInvoice.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
      ]);
      optional.setAttributes(['HAWB']);
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.IS, null),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
      ]);
      optional.setOrderby([
        new OrderBy('dateClearanced', EConfigure.ASCENDING)
      ]);
      const [mics, accepts, invoices] = await Promise.all([
        this.eMECRepository.queryAll(optional),
        this.eMECRepository.queryAll(checkAccept),
        this.eMECRepository.queryAll(checkInvoice),
      ]);
      if (mics.length > 0 && accepts.length == 0 && invoices.length == 0) {
        for (const item of mics) {
          const taxURL = process.env.TAX_URL || 'https://devapi.globex.vn/taxcode-sync/api/';
          console.log(taxURL);
          await Utilities.callAPINonAuth(`${taxURL}emec_tax_number_sync?HAWB=${item['HAWB']}`);
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      } else {
        reponze['message']['error'] = {
          "notAccept": accepts,
          "invoiveNumber": invoices,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][generateInvoice]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getHoldManifest(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.setRelation(['country', 'exportDetailItems', 'holds']);
      const EDAs = await this.eMECRepository.queryAll(optional);
      if (EDAs.length > 0) {
        let edaData: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: IEDA) => {
            const eda: IHold = {
              "HAWBClearance": EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": EDA[EConfigure.HAWB_FIELD],
              "declarationNo": EDA['declarationNo'],
              "MAWB": EDA['MAWB'],
              "invoiceValue": EDA['totalInvoicePrice'],
              "invoiceCurrency": EDA['invoiceCurrencyCode'],
              "priceVND": EDA['priceVND'],
              "country": EDA['country'],
              "cargoPiece": EDA['cargoPiece'],
              "weight": EDA['cargoWeightGross'],
              "massOfWeight": EDA['weightUnitCodeGross'],
              "inspectionKindClassification": EDA['inspectionKindClassification'],
              "phase": EDA['phase_name'],
              "classify": EDA['classify'],
              "type": String(EConfigure.EDA),
              "details": EDA['exportDetailItems'],
              "reasonDetails": EDA['holds']
            }
            return eda;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': edaData,
          'total': edaData.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getHoldManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneEDA(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.eMECRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getOneEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMEC(optional: Optional): Promise<any> {
    try {
      const objData = await this.eMECRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getAllMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async createExport(manifestExports: any[]): Promise<IResponze> {
    try {
      let HAWBs: string[] = [];
      let HAWBClearances: string[] = [];
      let processingHAWBs: string[] = [];
      let processingHAWBClearances: string[] = [];
      HAWBs = manifestExports.map((manifest: any) => {
        return manifest[EConfigure.HAWB_FIELD];
      });
      const processOptional: Optional = new Optional();
      processOptional.setAttributes([EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD]);
      processOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      const edas: any = await this.eMECRepository.queryAll(processOptional);
      if (edas.length > 0) {
        edas.forEach((eda: IEDA) => {
          processingHAWBs.push(eda[EConfigure.HAWB_FIELD] || '');
          processingHAWBClearances.push(eda[EConfigure.HAWB_CLEARANCE_FIELD] || '');
        });
      }
      HAWBs = [];
      const dataEDAs: IEDACreate[] = [];
      const dataDetails: any[] = [];
      let edaData: any;
      let detailData: any;
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.CREATE_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          }
        }
      }
      const actionCreate: number = ActionKey.CREATE;
      for (const manifest of manifestExports) {
        const cloneManifest: IExportValidate = { ...manifest };
        const HAWB: string = cloneManifest[EConfigure.HAWB_FIELD] || '';
        const HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD] || '';
        if (!processingHAWBs.includes(HAWB)) {
          HAWBs.push(HAWB);
          HAWBClearances.push(HAWBClearance);
          let country: ICountry = {};
          let warehouse: IWarehouse = {};
          let valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
          const totalPrice: number = Number(cloneManifest['totalPrice']);
          const priceVND: number = +(totalPrice * valueClearanceVND).toFixed(0);
          const stationId: number = cloneManifest['stationId'] || 0;
          if (cloneManifest['warehouseId']) {
            warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
          }
          const countryCode: any = cloneManifest['consigneeCountryCode'];
          if (countryCode) {
            country = await Utilities.getCountryByCode(countryCode);
          }
          let declareCargoNameEDA: boolean = false;
          let details: IExportDetailValidate[] = [];
          let classifyDOC: boolean = false;
          if (manifest['items'].length > 0) {
            details = manifest['items'];
            details.forEach((detail: IExportDetailValidate, index: number) => {
              detailData = {};
              const cloneDetail: IExportDetailValidate = { ...detail };
              const itemNameEN: string = cloneDetail['itemName'] || '';
              const checkName: string = itemNameEN;
              if (checkName && checkName.toUpperCase().includes("DOC")) {
                classifyDOC = true;
              }
              const totalPrice: number = Number(cloneDetail['invoiceValue']);
              const priceDetailVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
              const currencyCode: string = cloneDetail['currencyCode'];
              detailData['HSCode'] = cloneDetail['HSCode'] ? String(cloneDetail['HSCode']) : null;
              detailData['itemName'] = itemNameEN;
              detailData['itemNameVN'] = cloneDetail['itemNameVN'];
              detailData['url'] = cloneDetail['url'];
              detailData['quantity1'] = cloneDetail['quantity'];
              detailData['unitPriceCurrencyCode'] = currencyCode;
              detailData['position'] = index;
              if (currencyCode === EConfigure.CURRENCY_VND) {
                detailData['invoiceValue'] = +(totalPrice.toFixed(0));
                detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
              } else {
                detailData['invoiceValue'] = totalPrice;
                detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
              }
              detailData['priceVND'] = priceDetailVND;
              detailData['valueClearanceVND'] = valueClearanceVND;
              detailData['priceQuantityUnit'] = EConfigure.PIECE;
              detailData['quantity1'] = cloneDetail['quantity'];
              detailData['quantity2'] = cloneDetail['quantity'];
              detailData['quantityUnitCode1'] = EConfigure.PIECE;
              detailData['quantityUnitCode2'] = EConfigure.PIECE;
              detailData[EConfigure.HAWB_FIELD] = HAWB;
              detailData['placeOfOriginCode'] = EConfigure.VN;
              if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
                detailData['weightKG'] = Number(cloneManifest['weight']);
              } else {
                detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
              }
              detailData['productId'] = detail['productId'];
              dataDetails.push(detailData);
            });
          }
          if (details.length > 0) {
            declareCargoNameEDA = details.every((detail: IExportDetailValidate) => {
              return detail['itemNameVN'] && detail['HSCode'] && detail['HSCode'].match(/^0+/) === null && detail['HSCode'].match(/^[0-9]+$/) !== null;
            });
          }
          edaData = {
            'HAWB': HAWB,
            'HAWBClearance': HAWBClearance,
            'cargoNo': HAWBClearance,
            'stationId': stationId,
            'phase': actionCreate,
            'serviceId': cloneManifest['serviceId'],
          };
          if (declareCargoNameEDA) {
            edaData['phase'] = ActionKey.UPDATE_CARGO_NAME;
          }
          edaData['MAWB'] = cloneManifest['MAWB'];
          edaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
          if (!cloneManifest['exporterCode']) {
            edaData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
          } else {
            edaData['exporterCode'] = Utilities.subString(cloneManifest['exporterCode'], 0, 15);
          }
          if (!cloneManifest['exporterName']) {
            edaData['exporterName'] = EConfigure.IMPORTER_NAME
          } else {
            edaData['exporterName'] = cloneManifest['exporterName'];
            edaData['exporterFullName'] = cloneManifest['exporterName'];
          }
          edaData['postCode'] = cloneManifest['exporterPostCode'];
          edaData['addressOfExporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['exporterAddress'] as string), 0, 200);
          edaData['telephoneNumber'] = Utilities.subString(cloneManifest['exporterTelephoneNumber'], 0, 20);
          edaData['consigneeCode'] = cloneManifest['consigneeCode'];
          edaData['consigneeName'] = cloneManifest['consigneeName'];
          edaData['consigneeTelephoneNumber'] = cloneManifest['consigneeTelephoneNumber'];
          edaData['postCodeIdentification'] = cloneManifest['consigneePostCode'];
          const addresses = Utilities.handleAddress(cloneManifest['consigneeAddress'] as string);
          if (addresses) {
            for (const [key, value] of Object.entries(addresses)) {
              if (key <= '3') {
                edaData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
              }
            }
          }
          edaData['declarationKindCode'] = EConfigure.H21;
          edaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT as string;
          edaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
          edaData['termOfPayment'] = EConfigure.KHONGTT;
          if (warehouse && Object.keys(warehouse).length > 0) {
            edaData['customsWarehouseCode'] = warehouse['code'];
            edaData['plannedDeclarantCode'] = warehouse['agencyCode'];
            edaData['loadingPortCode'] = warehouse['unloadingPortCode'];
            edaData['loadingPortName'] = warehouse['unloadingPortCode'];
            edaData['customsOffice'] = warehouse['customsOffice'];
            edaData['customsSubSection'] = warehouse['customsSubSection'];
            edaData['terminalName'] = warehouse['id'];
          }
          if (cloneManifest['departureDate'] && cloneManifest['flightNo']) {
            edaData['loadingPlannedVesselName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['departureDate']);
            edaData['departurePlannedDate'] = cloneManifest['departureDate'];
          } else {
            edaData['loadingPlannedVesselName'] = cloneManifest['flightNo'];
          }
          if (country && Object.keys(country).length > 0) {
            edaData['address4'] = country['fullName'];
            edaData['countryCode'] = countryCode;
            edaData['theFinalDestinationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
            edaData['theFinalDestinationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
          }
          if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
            edaData['cargoWeightGross'] = Number(cloneManifest['weight']);
          } else {
            edaData['cargoWeightGross'] = Utilities.convertKG(Number(cloneManifest['weight']), String(cloneManifest['unitOfMass']));
          }
          edaData['weightUnitCodeGross'] = String(WeightClearance.get('kg'));
          edaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
          edaData['currencyCodeOfTaxValue'] = cloneManifest['currencyCode'];
          if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
            edaData['totalOfTaxValue'] = +(totalPrice.toFixed(0));;
            edaData['totalInvoicePrice'] = +(totalPrice.toFixed(0));;
          } else {
            edaData['totalOfTaxValue'] = totalPrice;
            edaData['totalInvoicePrice'] = totalPrice;
          }
          edaData['valueClearanceVND'] = valueClearanceVND;
          edaData['customerBusinessId'] = cloneManifest['customerBusinessId'] ? String(cloneManifest['customerBusinessId']) : null;
          edaData['customerPersonalId'] = cloneManifest['customerPersonalId'] ? String(cloneManifest['customerPersonalId']) : null;
          edaData['notes'] = cloneManifest['note'];
          edaData['classify'] = cloneManifest['classify'];
          edaData['priceVND'] = priceVND;
          edaData['hubId'] = cloneManifest['hubId'];
          edaData['taxPayer'] = EConfigure.TAX_PAYER;
          edaData['invoicePriceKindCode'] = EConfigure.TYPE_A;
          edaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
          edaData['codeOfExtendingDueDate'] = EConfigure.TYPE_A;
          edaData['invoicePriceConditionCode'] = EConfigure.CIF;
          edaData['clientId'] = cloneManifest['clientId'];
          edaData['internalBoxName'] = cloneManifest['internalBoxName'];
          edaData['externalBoxName'] = cloneManifest['externalBoxName'];
          edaData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
          dataEDAs.push(edaData);
        }
      };
      if (dataEDAs.length > 0) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.NOT_IN, processingHAWBs.join())
        ]);
        try {
          await Promise.all([
            this.eMECRepository.destroyData(optional),
            this.exportDetailRepository.destroyData(optional)
          ]);

          const edaCreated = await this.eMECRepository.createBulk(dataEDAs);
          await this.exportDetailRepository.createBulk(dataDetails);
          if (edaCreated.length > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.CREATED_DATA;
            reponze['message']['data'] = {
              'succesfull': HAWBClearances,
              'fail': processingHAWBClearances
            }
          }
        } catch (error) {
          console.log('---- create exports error: %o', error);
          reponze['message']['data']['fail'].push(...HAWBClearances, ...processingHAWBClearances)
        }
      } else {
        reponze['message']['data']['fail'].push(...HAWBClearances, ...processingHAWBClearances)
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][createExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async createImportExcel(manifestImport: any): Promise<any> {
    let reponze: IResponze = {
      'status': false,
      'message': {
        'message': EMessage.CREATE_FAIL,
        'data': {
          'fail': [],
          'succesfull': []
        }
      }
    }
    let { error, value } = ExportClearanceValidate.importExcel.validate(manifestImport, { abortEarly: false });
    try {
      value = Utilities.convertNull(value);
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let messages: string[] = errorValidate.handleError();
        reponze['message']['message'] = EMessage.VALIDATE_ERROR;
        reponze['message']['error'] = messages;
        const clearanceCreateLogId: number = value['clearanceCreateLogId'];
        const mongoLog: IClearanceCreateLog = {
          "clearanceCreateLogId": clearanceCreateLogId,
          "data": value,
          "message": reponze['message']['message'],
          "cause": messages,
          "processResult": reponze,
          "type": ClearanceCreateKey.ONLY_OUTBOUND,
          "isSuccess": reponze['status']
        }
        ClearanceCreateLogMongo.createLog(mongoLog);
        Utilities.updateCreateLog(clearanceCreateLogId, false);
        return reponze;
      }

      const cloneManifest: IExportValidate = { ...value };
      let storeDetail: Map<string, ExportDetail[]> = new Map();
      const HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD] || '';


      const existOptional: Optional = new Optional();
      existOptional.setAttributes([EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'declarationNo']);
      existOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWBClearance),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      existOptional.setOrderby([
        new OrderBy('createdAt', EConfigure.DESCENDING)
      ]);

      const existEDA: any = this.eMECRepository.getOneOptional(existOptional);

      let internalHAWB: string = Utilities.randomString(20);
      const valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
      let totalPrice: number = Number(cloneManifest['totalPrice']);
      const priceVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
      if (existEDA) {
        if ((cloneManifest['classify'] !== ClassifyName.get(ClassifyKey.DOC) && priceVND > 5000000) && !existEDA['declarationNo']) {
          internalHAWB = existEDA[EConfigure.HAWB_FIELD];

        }
      }
      if ((cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND <= 5000000)) {
        if (existEDA) {
          const HAWB: string = existEDA[EConfigure.HAWB_FIELD];
          const destroyOptional: Optional = new Optional();
          destroyOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
            new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null)
          ]);
          const total = await this.eMECRepository.destroyData(destroyOptional);
          if (total) {
            await this.exportDetailRepository.destroyDataObj({ HAWB })
          }

        }
      }
      const detailOptional: Optional = new Optional();
      detailOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, internalHAWB),
      ]);
      const details: ExportDetail[] = await this.exportDetailRepository.queryAll(detailOptional);

      if (details.length > 0) {
        details.forEach((detail: ExportDetail) => {
          const HAWB: string = String(detail[EConfigure.HAWB_FIELD]);
          if (!storeDetail.has(HAWB)) {
            storeDetail.set(HAWB, [detail]);
          } else {
            const store: any = storeDetail.get(HAWB);
            store.push(detail);
            storeDetail.set(HAWB, store);
          }
        });
      }
      const dataDetails: any[] = [];
      let mecData: any;
      let edaData: any;
      let detailData: IExportDetailCreate;

      const actionCreate: number = ActionKey.CREATE;
      let warehouse: IWarehouse = {};

      const countryCode: string = String(cloneManifest['consigneeCountryCode']);
      const stationId: number = cloneManifest['stationId'] || 0;
      if (cloneManifest['warehouseId']) {
        warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
      }
      const country: ICountry = await Utilities.getCountryByCode(countryCode);
      let declareCargoNameMEC: boolean = false;
      let declareCargoNameEDA: boolean = false;
      let cause = null;
      let classifyDOC: boolean = false;
      let createDetails: IExportDetailValidate[] = cloneManifest['items'] || []
      if (createDetails.length > 0) {
        createDetails.forEach((detail: IExportDetailValidate, index: number) => {
          detail = Utilities.convertNull(detail);
          detailData = {};
          const cloneDetail: IExportDetailValidate = { ...detail };
          const itemNameEN: string = cloneDetail['itemName'] || '';
          const checkName: string = itemNameEN;
          if (checkName && checkName.toUpperCase().includes("DOC")) {
            classifyDOC = true;
          }
          const currencyCode: string = cloneDetail['currencyCode'];

          if (cloneDetail['invoiceValue'] && !cloneDetail['invoiceUnitPrice']) {
            const totalPrice: number = Number(cloneDetail['invoiceValue']);
            const priceDetailVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
              detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
            } else {
              detailData['invoiceValue'] = totalPrice;
              detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
            }
            detailData['priceVND'] = priceDetailVND;
          }
          if (!cloneDetail['invoiceValue'] && cloneDetail['invoiceUnitPrice']) {
            const invoiceUnitPrice: number = Number(cloneDetail['invoiceUnitPrice']);
            const totalPrice: number = invoiceUnitPrice * Number(cloneDetail['quantity']);
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceUnitPrice'] = +(invoiceUnitPrice.toFixed(0));
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
            } else {
              detailData['invoiceUnitPrice'] = invoiceUnitPrice;
              detailData['invoiceValue'] = totalPrice;
            }
            detailData['priceVND'] = +(totalPrice * valueClearanceVND).toFixed(0);
          }
          if (cloneDetail['invoiceValue'] && cloneDetail['invoiceUnitPrice']) {
            const totalPrice: number = Number(cloneDetail['invoiceValue']);
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceUnitPrice'] = +(cloneDetail['invoiceUnitPrice'].toFixed(0));
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
            } else {
              detailData['invoiceUnitPrice'] = cloneDetail['invoiceUnitPrice'];
              detailData['invoiceValue'] = totalPrice;
            }
            detailData['priceVND'] = +(totalPrice * valueClearanceVND).toFixed(0);
          }
          detailData['HSCode'] = cloneDetail['HSCode'] ? String(cloneDetail['HSCode']) : '';
          detailData['itemName'] = itemNameEN;
          detailData['itemNameVN'] = cloneDetail['itemNameVN'];
          detailData['url'] = cloneDetail['url'];
          detailData['quantity1'] = cloneDetail['quantity'];
          detailData['unitPriceCurrencyCode'] = currencyCode;
          detailData['position'] = index;
          detailData['valueClearanceVND'] = valueClearanceVND;
          detailData['priceQuantityUnit'] = EConfigure.PIECE;
          detailData['quantity1'] = cloneDetail['quantity'];
          detailData['quantity2'] = cloneDetail['quantity'];
          detailData['quantityUnitCode1'] = EConfigure.PIECE;
          detailData['quantityUnitCode2'] = EConfigure.PIECE;
          detailData[EConfigure.HAWB_FIELD] = internalHAWB;
          detailData['placeOfOriginCode'] = EConfigure.VN;
          if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
            detailData['weightKG'] = Number(cloneManifest['weight']);
          } else {
            detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
          }
          detailData['productId'] = detail['productId'];
          dataDetails.push(detailData);
        });
      }

      if (dataDetails.length > 0) {
        declareCargoNameEDA = dataDetails.every((detail: ExportDetail) => {
          return detail['itemNameVN'] && detail['HSCode'] && detail['HSCode'].match(/^0+/) === null && detail['HSCode'].match(/^[0-9]+$/) !== null;
        });
      }
      edaData = {
        'HAWB': internalHAWB,
        'HAWBClearance': HAWBClearance,
        'cargoNo': HAWBClearance,
        'stationId': stationId,
        'phase': actionCreate,
        'serviceId': cloneManifest['serviceId'],
      };
      if (declareCargoNameEDA) {
        edaData['phase'] = ActionKey.UPDATE_CARGO_NAME;
      }
      edaData['MAWB'] = cloneManifest['MAWB'];
      edaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
      edaData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
      //if(!cloneManifest['exporterCode']) {
      //  edaData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
      //} else {
      //  edaData['exporterCode'] = Utilities.subString(cloneManifest['exporterCode'], 0, 15);
      //}
      if (!cloneManifest['exporterName']) {
        edaData['exporterName'] = EConfigure.IMPORTER_NAME
      } else {
        edaData['exporterName'] = cloneManifest['exporterName'];
        edaData['exporterFullName'] = cloneManifest['exporterName'];
      }
      edaData['postCode'] = cloneManifest['exporterPostCode'];
      edaData['addressOfExporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['exporterAddress'] as string), 0, 200);
      edaData['telephoneNumber'] = Utilities.subString(cloneManifest['exporterTelephoneNumber'], 0, 20);
      edaData['consigneeCode'] = cloneManifest['consigneeCode'];
      edaData['consigneeName'] = cloneManifest['consigneeName'];
      edaData['consigneeTelephoneNumber'] = cloneManifest['consigneeTelephoneNumber'];
      edaData['postCodeIdentification'] = cloneManifest['consigneePostCode'];
      const addresses = Utilities.handleAddress(cloneManifest['consigneeAddress'] as string);
      if (addresses) {
        for (const [key, value] of Object.entries(addresses)) {
          if (key <= '3') {
            edaData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
          }
        }
      }
      edaData['declarationKindCode'] = EConfigure.H21;
      edaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT as string;
      edaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
      edaData['termOfPayment'] = EConfigure.KHONGTT;
      if (warehouse && Object.keys(warehouse).length > 0) {
        edaData['customsWarehouseCode'] = warehouse['code'];
        edaData['plannedDeclarantCode'] = warehouse['agencyCode'];
        edaData['loadingPortCode'] = warehouse['unloadingPortCode'];
        edaData['loadingPortName'] = warehouse['unloadingPortCode'];
        edaData['customsOffice'] = warehouse['customsOffice'];
        edaData['customsSubSection'] = warehouse['customsSubSection'];
        edaData['terminalName'] = warehouse['id'];
      }
      if (cloneManifest['departureDate'] && cloneManifest['flightNo']) {
        edaData['loadingPlannedVesselName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['departureDate']);
        edaData['departurePlannedDate'] = cloneManifest['departureDate'];
      } else {
        edaData['loadingPlannedVesselName'] = cloneManifest['flightNo'];
      }
      if (country && Object.keys(country).length > 0) {
        edaData['address4'] = country['fullName'];
        edaData['countryCode'] = countryCode;
        edaData['theFinalDestinationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        edaData['theFinalDestinationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
      }
      if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
        edaData['cargoWeightGross'] = Number(cloneManifest['weight']);
      } else {
        edaData['cargoWeightGross'] = Utilities.convertKG(Number(cloneManifest['weight']), String(cloneManifest['unitOfMass']));
      }
      edaData['weightUnitCodeGross'] = String(WeightClearance.get('kg'));
      edaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
      edaData['currencyCodeOfTaxValue'] = cloneManifest['currencyCode'];
      if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
        edaData['totalOfTaxValue'] = +(totalPrice.toFixed(0));;
        edaData['totalInvoicePrice'] = +(totalPrice.toFixed(0));;
      } else {
        edaData['totalOfTaxValue'] = totalPrice;
        edaData['totalInvoicePrice'] = totalPrice;
      }
      edaData['valueClearanceVND'] = valueClearanceVND;
      edaData['customerBusinessId'] = cloneManifest['customerBusinessId'] ? String(cloneManifest['customerBusinessId']) : null;
      edaData['customerPersonalId'] = cloneManifest['customerPersonalId'] ? String(cloneManifest['customerPersonalId']) : null;
      edaData['notes'] = cloneManifest['note'];
      edaData['classify'] = cloneManifest['classify'];
      edaData['priceVND'] = priceVND;
      edaData['hubId'] = cloneManifest['hubId'];
      edaData['taxPayer'] = EConfigure.TAX_PAYER;
      edaData['invoicePriceKindCode'] = EConfigure.TYPE_A;
      edaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
      edaData['codeOfExtendingDueDate'] = EConfigure.TYPE_A;
      edaData['invoicePriceConditionCode'] = EConfigure.CIF;
      edaData['clientId'] = cloneManifest['clientId'];
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
      ]);
      try {
        const total = await this.eMECRepository.destroyData(optional);
        if (total > 0) {
          const optional: Optional = new Optional();
          optional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
          ]);
          await this.exportDetailRepository.destroyData(optional);
        }
        const [idaCreated] = await Promise.all([
          this.eMECRepository.createData(edaData),
          this.exportDetailRepository.createBulk(dataDetails),
        ]);
        if (idaCreated) {
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.CREATED_DATA;
          reponze['message']['data'] = {
            'succesfull': null,
            'fail': null
          }
        }
      } catch (error) {
        console.log('---- create imports error: %o', error);
        cause = (error as any).message;
        reponze['message']['data']['fail'].push()
      }
      edaData['details'] = dataDetails;
      const clearanceCreateLogId: number = value['clearanceCreateLogId'];
      const mongoLog: IClearanceCreateLog = {
        "clearanceCreateLogId": clearanceCreateLogId,
        "data": value,
        "message": reponze['message']['message'],
        "cause": cause,
        "handleData": edaData,
        "processResult": reponze,
        "classify": ClassifyValidateName.get(ClassifyValidateKey.EDA) as string,
        "type": ClearanceCreateKey.ONLY_OUTBOUND,
        "isSuccess": reponze['status']
      }
      ClearanceCreateLogMongo.createLog(mongoLog);
      if (!cause && reponze['status']) {
        Utilities.updateCreateLog(clearanceCreateLogId, true);
      } else {
        Utilities.updateCreateLog(clearanceCreateLogId, false);
      }
      return reponze;
    } catch (error) {
      const clearanceCreateLogId: number = value['clearanceCreateLogId'];
      const mongoLog: IClearanceCreateLog = {
        "clearanceCreateLogId": clearanceCreateLogId,
        "data": value,
        "message": reponze['message']['message'],
        "cause": (error as Error).message,
        "handleData": null,
        "processResult": reponze,
        "classify": null,
        "type": ClearanceCreateKey.ONLY_OUTBOUND,
        "isSuccess": reponze['status']
      }
      ClearanceCreateLogMongo.createLog(mongoLog);
      Utilities.updateCreateLog(clearanceCreateLogId, false);
      Utilities.sendDiscordErr('[service][import][createImportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerEDE(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMECRepository.queryAll(serviceOptional);
      const EDAQuery: any = this.eMECRepository.queryAll(optional);
      const [EDAs, sameService] = await Promise.all([EDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'EDE';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (EDAs.length > 0) {
        EDAs.forEach((eda: IEDA, index: number) => {
          foundHAWB[String(eda[EConfigure.HAWB_FIELD])] = String(eda[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(eda[EConfigure.HAWB_CLEARANCE_FIELD]));
          let cause: any = null;
          if (eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
            }
          }
          if (eda['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && eda['isEditProcessing'] === true) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang chờ thông điệp ECUS`
            }
          }
          if (eda['isIEDAed'] === false) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': 'Chưa khai báo thời khai EDA0x'
            }
          }
          if (cause) {
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push(cause);
          }

          // const register: IRegister = {
          //   'HAWB': String(ida['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(ida['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.XacNhanChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'typeAction': typeAction,
                'isIEDAed': false,
                'isEditProcessing': true,
                'dateAction': now,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.eMECRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerEDE]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIEDA(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMECRepository.queryAll(serviceOptional);
      const EDAQuery: any = this.eMECRepository.queryAll(optional);
      const [EDAs, sameService] = await Promise.all([EDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      let typeAction: string = 'EDA0';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (EDAs.length > 0) {
        EDAs.forEach((eda: IEDA, index: number) => {
          foundHAWB[String(eda[EConfigure.HAWB_FIELD])] = String(eda[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(eda[EConfigure.HAWB_CLEARANCE_FIELD]));
          let cause: any = null;
          if (!eda['isEDCed'] || eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
            }
          }
          if (eda['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && eda['isEditProcessing'] === true) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang chờ thông điệp ECUS`
            }
          }
          if (eda['times'] === 9) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Sỗ lần gửi cập nhật vượt quá quy định 9 lần`
            }
          }
          if (cause) {
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push(cause);
          }
          typeAction = `${typeAction}${(eda['times']) ? Number(eda['times']) + 1 : 1}`;
          // const register: IRegister = {
          //   'HAWB': String(ida['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(ida['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.ChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              await Promise.all(EDAs.map(async (eda: EDA) => {
                eda['phase'] = action;
                eda['dateAction'] = now;
                eda['typeAction'] = `${typeAction}${(eda['times']) ? Number(eda['times']) + 1 : 1}`;
                eda['isEditProcessing'] = true;
                eda['isIEDAed'] = true;
                if (isPrioritize != undefined) {
                  eda['isPrioritize'] = isPrioritize;
                }
                await eda.save()
              }));
              // await this.importTransactionRepository.createBulk(transactions);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerIEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerEDC(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMECRepository.queryAll(serviceOptional);
      const EDAQuery: any = this.eMECRepository.queryAll(optional);
      const [EDAs, sameService] = await Promise.all([EDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'EDC';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (EDAs.length > 0) {
        EDAs.forEach((eda: IEDA, index: number) => {
          foundHAWB[String(eda[EConfigure.HAWB_FIELD])] = String(eda[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(eda[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (eda['phase'] === ActionKey.CREATE
            || eda['phase'] === ActionKey.UPDATE_CARGO_NAME
            || eda['phase'] === ActionKey.SUBMIT_INFOMATION
            || (eda['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE && eda['isError'] === false)
            || eda['phase'] === ActionKey.INSPECTION_KIND
            || eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
            });
          }
          // const register: IRegister = {
          //   'HAWB': String(ida['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(ida['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.ChinhThuc, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isEDCed': true,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);

              await Promise.all([
                this.eMECRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerEDC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerEDA(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMECRepository.queryAll(serviceOptional);
      const EDAQuery: any = this.eMECRepository.queryAll(optional);
      const [EDAs, sameService] = await Promise.all([EDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_INFOMATION;
      const typeAction: string = 'EDA';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (EDAs.length > 0) {
        EDAs.forEach((eda: IEDA, index: number) => {
          foundHAWB[String(eda[EConfigure.HAWB_FIELD])] = String(eda[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(eda[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (eda['phase'] === ActionKey.CREATE
            || eda['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE
            || eda['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE
            || eda['phase'] === ActionKey.INSPECTION_KIND
            || eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
            });
          }
          // const register: IRegister = {
          //   'HAWB': String(ida['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(ida['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.Tam, HAWBs, isPrioritize);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isError': false,
                'isECUSError': false,
                'isPrioritize': isPrioritize
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);

              await Promise.all([
                this.eMECRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
                Utilities.updateDeclaration(HAWBs)
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerMEE(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMECRepository.queryAll(serviceOptional);
      const MECQuery: any = this.eMECRepository.queryAll(optional);
      const [MECs, sameService] = await Promise.all([MECQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'MEE';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (MECs.length > 0) {
        MECs.forEach((mec: IMEC, index: number) => {
          foundHAWB[String(mec[EConfigure.HAWB_FIELD])] = String(mec[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(mec[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (mec['phase'] === ActionKey.CREATE
            || mec['phase'] === ActionKey.UPDATE_CARGO_NAME
            || mec['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE
            || (mec['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && mec['isError'] === false)
            || mec['phase'] === ActionKey.ACCEPT_CLEARANCE
          ) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mec[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(mec['phase_name']['vi']).toLowerCase()}`
            });
          }
          // const register: IRegister = {
          //   'HAWB': String(mic['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(mic['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, true, ClearanceTypeId.ChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.eMECRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerMEE]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerMEC(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMECRepository.queryAll(serviceOptional);
      const MECQuery: any = this.eMECRepository.queryAll(optional);
      const [MECs, sameService] = await Promise.all([MECQuery, sameServiceQuery]);
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      const typeAction: string = EConfigure.MEC;
      const action: number = ActionKey.SUBMIT_CUSTOM_CLEARANCE;
      // let transactions: IRegister[] = [];

      if (MECs.length > 0) {
        MECs.forEach((mec: IMEC, index: number) => {
          foundHAWB[String(mec[EConfigure.HAWB_FIELD])] = String(mec[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(mec[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (mec['phase'] === ActionKey.CREATE
            || (mec['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE && mec['isError'] === false)
            || mec['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE
            || mec['phase'] === ActionKey.ACCEPT_CLEARANCE
            || mec['phase'] === ActionKey.INSPECTION_KIND
          ) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mec[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(mec['phase_name']['vi']).toLowerCase()}`
            });
          }
          // const register: IRegister = {
          //   'HAWB': String(mic['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(mic['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, true, ClearanceTypeId.ChinhThuc, HAWBs, isPrioritize);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isError': false,
                'isECUSError': false,
                'isPrioritize': isPrioritize
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.eMECRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
                Utilities.updateDeclaration(HAWBs)
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateMaster(manifestMasters: any[]): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      let failHAWBs: string[] = [];
      let successHAWBs: string[] = [];
      // let transactions: IMaster[] = [];
      transaction = await this._sequelize.transaction();
      await Promise.all(manifestMasters.map(async (manifestMaster: any) => {
        let warehouse: IWarehouse = {};
        const cloneMaster: IMasterValidate = { ...manifestMaster };
        const HAWB: string = manifestMaster[EConfigure.HAWB_FIELD];
        const HAWBClearance: string = manifestMaster[EConfigure.HAWB_CLEARANCE_FIELD];
        const MAWB: any = cloneMaster['MAWB'] ? String(cloneMaster['MAWB']) : null;
        const departureDate: any = cloneMaster['departureDate'] ? String(cloneMaster['departureDate']) : null;
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWBClearance),
          new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.IN, `${ActionKey.CREATE}, ${ActionKey.UPDATE_CARGO_NAME}`),
        ]);
        let flightCode: any = null;
        if (cloneMaster['departureDate']) {
          flightCode = Utilities.flightCode(String(cloneMaster['flightNo']), String(cloneMaster['departureDate']));
        }
        if (cloneMaster['warehouseId']) {
          warehouse = await Utilities.getWarehouse(cloneMaster['warehouseId']);
        }
        const edaUpdate: IEDAMaster = {
          'MAWB': MAWB,
          'departurePlannedDate': departureDate,
          'loadingPlannedVesselName': flightCode,
          'hubId': cloneMaster['hubId']
        }
        edaUpdate['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT;
        if (cloneMaster['meanOfTransportationCode']) {
          edaUpdate['meansOfTransportationCode'] = cloneMaster['meanOfTransportationCode'];
        }
        if (warehouse && Object.keys(warehouse).length > 0) {
          edaUpdate['customsWarehouseCode'] = warehouse['code'];
          edaUpdate['plannedDeclarantCode'] = warehouse['agencyCode'];
          edaUpdate['loadingPortCode'] = warehouse['unloadingPortCode'];
          edaUpdate['loadingPortName'] = warehouse['unloadingPortCode'];
          edaUpdate['customsOffice'] = warehouse['customsOffice'];
          edaUpdate['customsSubSection'] = warehouse['customsSubSection'];
          edaUpdate['terminalName'] = String(warehouse['id']);
        }

        // if(cloneMaster['departureCountry']) {
        //   let country: ICountry = null || {};
        //   const countryCode: any = cloneMaster['departureCountry'];
        //   if(countryCode){
        //     country = await Utilities.getCountryByCode(countryCode);
        //     if(country && Object.keys(country).length > 0) {
        //       // MEC
        //       mecUpdate['address4'] = country['fullName'];
        //       mecUpdate['countryCode'] = countryCode;
        //       mecUpdate['theFinalDestination'] = `${countryCode}${EConfigure.OTHER_CODE}`;

        //       // EDA
        //       edaUpdate['address4'] = country['fullName'];
        //       edaUpdate['countryCode'] = countryCode;
        //       edaUpdate['theFinalDestinationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        //       edaUpdate['theFinalDestinationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        //     }
        //   }
        // }
        const [edatotal] = await this.eMECRepository.updateDataTrx(edaUpdate, optional, transaction);
        if (edatotal > 0) {
          // const master: IMaster = {
          //   'HAWB': HAWB,
          //   'MAWB': MAWB,
          //   'action': ActionKey.UPDATE_MASTER
          // }
          // await this.importTransactionRepository.createDataTrx(master, transaction);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
          successHAWBs.push(HAWBClearance);
        } else {
          reponze['status'] = false;
          failHAWBs.push(HAWBClearance);
        }
      }));
      await transaction.commit();

      reponze['message']['data'] = {
        'succesfull': successHAWBs,
        'fail': failHAWBs
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][updateMaster]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async updateEDA(HAWB: string, value: any, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const eda: IEDA = await this.eMECRepository.getOneOptional(optional);
      if (eda) {
        if (eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
            'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
          });
        } else {
          transaction = await this._sequelize.transaction();
          const edaUpdate: IEDA = value;
          const clearanceVND: any = value['valueClearanceVND'] ? (Number(value['valueClearanceVND'])) : 0;
          const edaDetailUpdates: IExportDetailCreate[] = value['exportDetails'];
          const currencyCode: string = String(edaUpdate['invoiceCurrencyCode']);

          edaUpdate['valueClearanceVND'] = clearanceVND;
          if (edaUpdate['totalInvoicePrice']) {
            edaUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(edaUpdate['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(edaUpdate['totalInvoicePrice'])).toFixed(0);
          } else {
            edaUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(eda['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(eda['totalInvoicePrice'])).toFixed(0);
          }
          if (currencyCode === EConfigure.CURRENCY_VND) {
            edaUpdate['totalInvoicePrice'] = +(Number(edaUpdate['totalInvoicePrice']).toFixed(0));
          } else {
            edaUpdate['totalInvoicePrice'] = edaUpdate['totalInvoicePrice'];
          }
          edaUpdate['exporterFullName'] = edaUpdate['exporterName'];


          if (eda['classify'] === ClassifyName.get(ClassifyKey.COM)) {
            if (!edaUpdate['dateClearanced']) {
              edaUpdate['dateClearanced'] = moment().format(EConfigure.FULL_TIME);
            }
            edaUpdate['declarationNo'] = value['declarationNoCustomer'];
            edaUpdate['phase'] = ActionKey.ACCEPT_CLEARANCE;
          }
          const edaOptional: Optional = new Optional();
          edaOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          ]),
            await this.eMECRepository.updateDataTrx(edaUpdate, edaOptional, transaction);
          if (edaDetailUpdates.length > 0) {
            await Promise.all(edaDetailUpdates.map(async (edaDetailUpdate: any) => {
              if (edaDetailUpdate['HSCode'] && (edaDetailUpdate['HSCode'].substring(0, 2) == 85 || edaDetailUpdate['HSCode'].substring(0, 2) == 84)) {
                edaDetailUpdate['otherLawCode'] = EConfigure.MO;
              }
              let itemNameVN: string = Utilities.removeSpeialCharacter(edaDetailUpdate['itemNameVN']);
              itemNameVN = Utilities.removeGeneralToken(itemNameVN);
              edaDetailUpdate['itemNameVN'] = itemNameVN;
              edaDetailUpdate['placeOfOrigin'] = edaUpdate['countryCode'];
              edaDetailUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(edaDetailUpdate['invoiceValue']) * clearanceVND).toFixed(0) : +(Number(edaDetailUpdate['invoiceValue'])).toFixed(0);
              edaDetailUpdate['unitPriceCurrencyCode'] = currencyCode;

              if (currencyCode === EConfigure.CURRENCY_VND) {
                edaDetailUpdate['invoiceValue'] = +(edaDetailUpdate['invoiceValue'].toFixed(0));
                edaDetailUpdate['invoiceUnitPrice'] = +(Number(edaDetailUpdate['invoiceValue']) / Number(edaDetailUpdate['quantity1'])).toFixed(0);
              } else {
                edaDetailUpdate['invoiceValue'] = edaDetailUpdate['invoiceValue'];
                edaDetailUpdate['invoiceUnitPrice'] = Number(edaDetailUpdate['invoiceValue']) / Number(edaDetailUpdate['quantity1']);
              }
              edaDetailUpdate['quantityUnitCode1'] = edaDetailUpdate['priceQuantityUnit'];

              const checkHSCodeDetail: Optional = new Optional();
              checkHSCodeDetail.setWhere([
                new Where(EConfigure.AND, 'name', EConfigure.EQUAL, edaDetailUpdate['itemName']),
                new Where(EConfigure.AND, 'nameVN', EConfigure.EQUAL, itemNameVN),
                new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, edaDetailUpdate['HSCode']),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);

              const checkHSCode: Optional = new Optional();
              checkHSCode.setWhere([
                new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, edaDetailUpdate['HSCode']),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);
              const hscodeDetailQuery: any = this.hscodeDetailRepository.getOneOptional(checkHSCodeDetail);
              const hscodeQuery: any = this.hscodeRepository.getOneOptional(checkHSCode);
              let [hscodeDetail, hscode] = await Promise.all([hscodeDetailQuery, hscodeQuery]);
              if (!hscode) {
                const newHSCode: any = {
                  'hsCode': edaDetailUpdate['HSCode'],
                  'importTaxCode': edaDetailUpdate['exportTaxCode'],
                  'importTaxValue': edaDetailUpdate['exportTax'],
                  'VATCode': edaDetailUpdate['VATTaxCode'],
                  'VATValue': edaDetailUpdate['VATTax'],
                  'specialConsumptionTaxCode': edaDetailUpdate['specialConsumptionTaxCode'],
                  'specialConsumptionTaxValue': edaDetailUpdate['specialConsumptionTax'],
                  'environmentTaxCode': edaDetailUpdate['environmentTaxCode'],
                  'environmentTaxPrice': edaDetailUpdate['environmentTax'],
                }
                hscode = await this.hscodeRepository.createDataTrx(newHSCode, transaction);
              } else {
                hscode['importTaxCode'] = edaDetailUpdate['exportTaxCode'],
                  hscode['importTaxValue'] = edaDetailUpdate['exportTax'];
                hscode['VATCode'] = edaDetailUpdate['VATTaxCode'],
                  hscode['VATValue'] = edaDetailUpdate['VATTax'];
                hscode['specialConsumptionTaxCode'] = edaDetailUpdate['specialConsumptionTaxCode'],
                  hscode['specialConsumptionTaxValue'] = edaDetailUpdate['specialConsumptionTax'];
                hscode['environmentTaxCode'] = edaDetailUpdate['environmentTaxCode'],
                  hscode['environmentTaxPrice'] = edaDetailUpdate['environmentTax'];
                await hscode.save();
              }
              if (!hscodeDetail) {
                const newHSCodeDetail: any = {
                  'name': edaDetailUpdate['itemName'],
                  'nameVN': edaDetailUpdate['itemNameVN'],
                  'quantityUnitCode1': edaDetailUpdate['priceQuantityUnit'],
                  'priceQuantityUnit': edaDetailUpdate['priceQuantityUnit'],
                  'hsCode': edaDetailUpdate['HSCode'],
                }
                await this.hscodeDetailRepository.createDataTrx(newHSCodeDetail, transaction);
              } else {
                hscodeDetail['quantityUnitCode1'] = edaDetailUpdate['priceQuantityUnit'];
                hscodeDetail['priceQuantityUnit'] = edaDetailUpdate['priceQuantityUnit'];
                hscodeDetail.save();
              }

              const optional: Optional = new Optional();
              optional.setWhere([
                new Where(EConfigure.AND, 'id', EConfigure.EQUAL, edaDetailUpdate['id']),
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, edaDetailUpdate[EConfigure.HAWB_FIELD])
              ]);
              edaDetailUpdate = Utilities.taxAndCollection(edaDetailUpdate, hscode);
              await this.exportDetailRepository.updateDataTrx(edaDetailUpdate, optional, transaction);
            }));
          }
          await transaction.commit();
          // const updateTransaction: IUpdateAction = {
          //   'HAWB': HAWB,
          //   'employeeId': employeeId,
          //   'action': ActionKey.UPDATE_IDA,
          //   'classify': ida['classify'] as string,
          //   'data': ida,
          //   'newData': value,
          // }
          // await this.importTransactionRepository.createData(updateTransaction);
          const exportDetailService = new ExportClearanceDetailService();
          await exportDetailService.updateTotalTax([HAWB]);
          await exportDetailService.checkUpdateItemNameVN([HAWB]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][updateEDA]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async filterEDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalRegisterTemp': 0,
            'totalSentTemp': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalRegisterTemp: Optional = new Optional();
      optionalRegisterTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSentTemp: Optional = new Optional();
      optionalSentTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SENT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      // eda
      const EDAQuery = this.eMECRepository.getAll(optional);
      const EDATotalCreateQuery = this.eMECRepository.countData(optionalCreate);
      const EDATotalUpdateCarageNameQuery = this.eMECRepository.countData(optionalUpdateCarageName);
      const EDATotalRegisterTempQuery = this.eMECRepository.countData(optionalRegisterTemp);
      const EDATotalSentTempQuery = this.eMECRepository.countData(optionalSentTemp);
      const EDATotalSendClearanceQuery = this.eMECRepository.countData(optionalSendClearance);
      const EDATotalEditClearanceQuery = this.eMECRepository.countData(optionalEditClearance);
      const EDATotalInspectionKindQuery = this.eMECRepository.countData(optionalInspectionKind);
      const EDATotalAcceptClearanceQuery = this.eMECRepository.countData(optionalAcceptClearance);
      const EDAErrorQuery = this.eMECRepository.countData(optionalError);

      const [[EDAs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError, registerTemp, sentTemp] =
        await Promise.all([EDAQuery, EDATotalCreateQuery, EDATotalUpdateCarageNameQuery, EDATotalSendClearanceQuery, EDATotalEditClearanceQuery, EDATotalInspectionKindQuery,
          EDATotalAcceptClearanceQuery, EDAErrorQuery, EDATotalRegisterTempQuery, EDATotalSentTempQuery]);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': EDAs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalRegisterTemp': registerTemp,
          'totalSentTemp': sentTemp,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][filterEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async master(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const masterOptional: Optional = new Optional();
      masterOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'MAWB', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      masterOptional.setAttributes(['MAWB']);
      masterOptional.setGroup(['MAWB', 'createdAt']);
      masterOptional.setOrderby([
        new OrderBy('createdAt', EConfigure.DESCENDING)
      ]);
      const EDAs = await this.eMECRepository.queryAll(masterOptional);
      let masters = [];
      if (EDAs.length > 0) {
        masters = [...EDAs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][master]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
          }
        }
      }
      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      const EDAs = await this.eMECRepository.queryAll(optionalTotal);
      if (EDAs.length > 0) {
        let edaData: ICheckout[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: IEDA) => {
            const eda: ICheckout = {
              'HAWB': EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': EDA['MAWB'],
              'declarationNo': EDA['declarationNo'],
              'clearanceInspection': EDA['inspectionKindClassification'],
              'dateCheckout': EDA['dateCheckout'],
              'dateCheckin': EDA['dateCheckin'],
              'dateClearanced': EDA['dateClearanced'],
              'cargoPiece': EDA['cargoPiece'],
              'weight': EDA['cargoWeightGross'],
            }
            return eda;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': edaData.length,
          'manifests': edaData,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][clearanceReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportExport(optional: Optional): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      optionalQuery.setRelation(['exportDetails', 'customerPersonal', 'customerBusiness']);
      const EDAs = await this.eMECRepository.queryAll(optionalQuery);
      if (EDAs.length > 0) {
        let manifests: any[] = [];
        const customReleased: string = 'custom released';
        if (EDAs.length > 0) {
          EDAs.forEach((EDA: IEDA) => {
            const importTax: number = EDA['totalExportTax'] ? Number(EDA['totalExportTax']) : 0;
            const VATTax: number = EDA['totalVATTax'] ? Number(EDA['totalVATTax']) : 0;

            let itemname: string[] = [];
            let partner: any = null;
            if (EDA['exportDetails'].length > 0) {
              itemname = EDA['exportDetails'].map((exportDetail: ExportDetail) => {
                return String(exportDetail['itemNameVN']);
              });
            }
            if (EDA['customerBusiness']) {
              partner = EDA['customerBusiness']['customerShortName'];
            }
            if (EDA['customerPersonal']) {
              partner = EDA['customerPersonal']['customerLastName'];
            }
            const eda: IExportReport = {
              'expDate': moment(String(EDA['departurePlannedDate'])).format(EConfigure.EXPORT_DATE),
              'trackingNo': String(EDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'name': itemname.join(','),
              'MAWB': String(EDA['MAWB']),
              'country': String(EDA['countryCode']),
              'pcs': Number(EDA['cargoPiece']),
              'grossWeight': Number(EDA['cargoWeightGross']),
              'puDate': moment(String(EDA['createdAt'])).format(EConfigure.EXPORT_DATE),
              'cdsDate': EDA['declarationPlannedDate'] ? moment(String(EDA['declarationPlannedDate'])).format(EConfigure.EXPORT_DATE) : '',
              'crDate': EDA['dateCheckout'] ? moment(String(EDA['dateCheckout'])).format(EConfigure.EXPORT_DATE) : '',
              'confirmUpliftDate': moment(String(EDA['departurePlannedDate'])).format(EConfigure.EXPORT_DATE),
              'cdsNo': String(EDA['declarationNo']),
              'importType': null,
              'lane': Number(EDA['inspectionKindClassification']),
              'customsStatus': customReleased,
              'customsClearance': null,
              'handling': null,
              'shipmemntValue': Number(EDA['totalOfTaxValue']),
              'dutyTaxVND': importTax + VATTax,
              'otherFee': Number(EDA['totalEnvironmentTax']) + Number(EDA['totalSpecialConsumptionTax']),
              'notes': 'Bảng kê EDA',
              'type': 'EDA',
              'partner': partner
            }
            manifests.push(eda);
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][clearanceReportExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async taxCodeBussiness(HAWBs: string[]): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'successful': null,
            'fail': null
          }
        }
      }
      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      optionalQuery.setAttributes(['exporterFullName', 'HAWB']);
      const EDAs = await this.eMECRepository.queryAll(optionalQuery);
      if (EDAs.length > 0) {
        let success: any[] = [];
        let fail: any[] = [];
        if (EDAs.length > 0) {
          await Promise.all(EDAs.map(async (EDA: EDA) => {
            const optionalEDA: Optional = new Optional();
            optionalEDA.setAttributes(['code']);
            optionalEDA.setWhere([
              new Where(EConfigure.AND, 'name_vn', EConfigure.EQUAL, EDA['exporterFullName']),
              new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
            ]);
            const taxCode: TaxCode = await this.taxCodeRepository.getOneOptional(optionalEDA);
            if (taxCode) {
              EDA['exporterCode'] = taxCode['code'];
              await EDA.save();
              success.push(EDA[EConfigure.HAWB_FIELD]);
            } else {
              fail.push(EDA[EConfigure.HAWB_FIELD]);
            }
          }));
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data']['successful'] = success;
        reponze['message']['data']['fail'] = fail;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][taxCodeBussiness]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async mecCheckin(HAWBs: string[], dateCheckin: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'successful': null,
            'fail': null
          },
          'error': []
        }
      }
      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
      ]);
      if (hubs) {
        optionalQuery.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optionalQuery.setAttributes(['HAWB', 'isDeleted']);
      // eda
      const EDAs = await this.eMECRepository.queryAll(optionalQuery);
      if (EDAs.length > 0) {
        let success: any[] = [];
        let fail: any[] = [];
        if (EDAs.length > 0) {
          EDAs.forEach((eda: EDA) => {
            if (eda[EConfigure.DELETED_FIELD]) {
              reponze['message']['error'].push({
                'HAWB': eda[EConfigure.HAWB_FIELD],
                'cause': `${EConfigure.HAWB_FIELD} đã bị xóa`
              });
              reponze['message']['message'] = EMessage.FAIL;
              fail.push(eda[EConfigure.HAWB_FIELD]);
            } else {
              success.push(eda[EConfigure.HAWB_FIELD]);
            }
          });
          if (fail.length === 0) {
            const updateData: any = {
              'dateCheckin': dateCheckin ? dateCheckin : moment().format(EConfigure.FULL_TIME),
            }
            const optionalUpdate: Optional = new Optional();
            optionalUpdate.setWhere([
              new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, success.join())
            ])
            const [total] = await this.eMECRepository.updateData(updateData, optionalUpdate);
            if (total > 0) {
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.SUCCESS;
              reponze['message']['data']['successful'] = success;
            } else {
              reponze['message']['data']['fail'] = success;
            }
          } else {
            reponze['message']['data']['fail'] = fail;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][edaCheckin]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async edaAssignCargoName(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'success': null,
            'assigned': [],
          },
          'error': []
        }
      }

      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'HAWBClearance', 'employeeUpdateCargoName', 'phase']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      optional.setRelation(['employeeUpdateCargo']);
      const EDAs: EDA[] = await this.eMECRepository.queryAll(optional);
      if (EDAs.length > 0) {
        const HAWBCreates: string[] = [];
        let transactions: any[] = [];
        EDAs.forEach((eda: EDA) => {
          const HAWBClearance: string = eda[EConfigure.HAWB_CLEARANCE_FIELD] || '';
          if (eda['phase'] === ActionKey.ASSIGN_UPDATE_CARGO_NAME && eda['employeeUpdateCargoName']) {
            reponze['message']['data']['assigned'].push({
              'HAWB': HAWBClearance,
              'employee': eda['employeeUpdateCargo'],
            });
          } else {
            HAWBCreates.push(eda[EConfigure.HAWB_FIELD] || '');
            let transactionUpdate: IReturnCargo = {
              'HAWB': HAWBClearance,
              'action': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
              'employeeId': employeeId
            }
            transactions.push(transactionUpdate);
          }
        });
        if (HAWBCreates.length > 0) {
          const updateCargo: any = {
            'phase': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
            'employeeUpdateCargoName': employeeId
          }
          const updateOptional: Optional = new Optional();
          updateOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBCreates.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
          ])
          const [total] = await Promise.all([
            this.eMECRepository.updateData(updateCargo, updateOptional),
            // this.importTransactionRepository.createBulk(transactions)
          ]);
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          if (total > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
            reponze['message']['data']['success'] = HAWBCreates;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][edaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getEdaAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.eMECRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getEdaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMECEDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': [{
            'manifests': [],
            'total': 0,
          }]
        }
      }
      optional.setRelation(['country', 'exportDetailItems']);
      const [EDAs, totalEDA] = await this.eMECRepository.getAll(optional);
      if (EDAs.length > 0) {
        let edaData: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: EDA) => {
            const eda: IGetAll = {
              "HAWBClearance": EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": EDA[EConfigure.HAWB_FIELD],
              "declarationNo": EDA['declarationNo'],
              "MAWB": EDA['MAWB'],
              "invoiceValue": EDA['totalInvoicePrice'],
              "invoiceCurrency": EDA['invoiceCurrencyCode'],
              "priceVND": EDA['priceVND'],
              "country": EDA['country'],
              "cargoPiece": EDA['cargoPiece'],
              "weight": EDA['cargoWeightGross'],
              "massOfWeight": EDA['weightUnitCodeGross'],
              "inspectionKindClassification": EDA['inspectionKindClassification'],
              "phase": EDA['phase_name'],
              "classify": EDA['classify'],
              "type": String(EConfigure.EDA),
              "details": EDA['exportDetailItems'],
              "reasonDetails": [],
              "inspectionKindTimes": EDA['inspectionKindTimes'],
              "clearanceDeclarationTimes": EDA['clearanceDeclarationTimes'],
              "isHold": EDA['isHold'],
              "isSortLane": EDA['isSortLane'],
              "externalBoxName": EDA['externalBoxName'],
              "internalBoxName": EDA['internalBoxName'],
              "createdAt": EDA['createdAt']
            }
            return eda;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': edaData,
          'total': totalEDA
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getAllMECEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async exportReport(optional: Optional): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      optional.setRelation(['manifest', 'exportDetailItems', 'station', 'customerBusiness', 'customerPersonal']);
      const EDAs = await this.eMECRepository.queryAll(optional);
      if (EDAs.length > 0) {
        let edaData: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: EDA) => {
            const importTax: number = EDA['totalExportTax'] ? Number(EDA['totalExportTax']) : 0;
            const VATTax: number = EDA['totalVATTax'] ? Number(EDA['totalVATTax']) : 0;
            const fee: number = 0;
            let productId: any = [];
            let details: ExportDetail[] = EDA['exportDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ExportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (EDA['manifest']) {
              orderNumber = EDA['manifest']['orderNumber']
              if (EDA['manifest']['originalOrderNumberClient']) {
                orderNumber = EDA['manifest']['originalOrderNumberClient']
              }
            }
            const eda: IAccountExportReport = {
              'MAWB': EDA['MAWB'],
              'trackingNo': EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': EDA['declarationNo'],
              'lane': EDA['inspectionKindClassification'] ? String(EDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': EDA['countryCode'] ? `${EDA['countryCode']}${EConfigure.G}` : null,
              'country': EDA['countryCode'],
              'pcs': Number(EDA['cargoPiece']),
              'trueWeight': Number(EDA['cargoWeightGross']),
              'volWeight': Number(EDA['cargoWeightGross']),
              'manisfestImported': EDA['createdAt'],
              'arDate': EDA['dateCheckin'],
              'cdsDate': EDA['declarationPlannedDate'],
              'crDate': EDA['dateCheckout'],
              'importType': EDA['isHold'] ? EConfigure.HOLD : (EDA['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.H11 : EConfigure.COM),
              'customsStatus': null,
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(EDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': EDA['totalSpecialConsumptionTax'],
              'environmentTax': EDA['totalEnvironmentTax'],
              'duty': fee,
              'priceVND': EDA['priceVND'],
              'totalTax': EDA['totalTax'],
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(EDA['totalEnvironmentTax']) + Number(EDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H21,
              'consignorName': EDA['exporterFullName'],
              'consignorAddress': EDA['addressOfExporter'],
              'consignorCityName': null,
              'consignorContactName': EDA['exporterFullName'],
              'consignorTelephone': EDA['telephoneNumber'],
              'consigneeName': EDA['consigneeName'],
              'consigneeAddress': `${EDA['address1']}${EDA['address2'] ? EDA['address2'] : ''}${EDA['address3'] ? EDA['address3'] : ''}`,
              'consigneeAddress2': `${EDA['address4'] ? EDA['address4'] : null}`,
              'consigneeCityName': null,
              'consigneeContactName': EDA['consigneeName'],
              'consigneeTelephone': EDA['consigneeTelephoneNumber'],
              'remarks': null,
              'status': EDA['phase_name']['vi'],
              'cdsNoCustomer': null,
              'currency': EDA['invoiceCurrencyCode'],
              'customerBusiness': EDA['customerBusiness'],
              'customerPersonal': EDA['customerPersonal'],
              'dateClearanced': EDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': EDA['station'] ? EDA['station'] : null,
              'exporterCode': EDA['exporterCode'],
              'statusId': EDA['phase'],
              'warehouseCheckin': null,
              'warehouseCheckout': null
            }
            return eda;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = edaData;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][exportReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async holdManifest(data: any[], isRemoveMAWB: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL
        }
      }
      const HAWBs: string[] = [];
      await Promise.all(data.map(async (element: any) => {
        const HAWB: string = element[EConfigure.HAWB_FIELD];
        HAWBs.push(HAWB)
        const isHold: boolean = element['isHold'];
        const dataUpdate: any = {
          'isHold': isHold,
          'reasonIds': element['reasonIds'] ? String(element['reasonIds']).split(',') : null,
        }
        const transactionHold: IHoldTransaction = {
          'HAWB': HAWB,
          'action': isHold ? ActionKey.HOLD : ActionKey.UN_HOLD,
          'holdId': element['reasonIds'],
          'employeeId': employeeId,
        }
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        if (hubs) {
          optional.getWhere().push(
            new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
          )
        }
        await this.eMECRepository.updateData(dataUpdate, optional);
        // await this.exportTransactionRepository.createData(transactionHold);
        Utilities.updatePartnerStatusHAWB([HAWB], 112);
        return true;
      }));
      if (isRemoveMAWB) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        let updateClearance: any = {
          "MAWB": null,
        }
        await Promise.all([
          Utilities.callTMMApi('manifests/removeMAWB', EConfigure.PUT_METHOD, { HAWBs }),
          this.eMECRepository.updateData(updateClearance, optional),
        ]);
      }
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.SUCCESS;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][holdManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async EDAGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      const exportOptional: Optional = new Optional();
      exportOptional.setRelation(["exportDetailItems"]);
      exportOptional.setAttributes(value['select'].split(','));
      exportOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);
      if (value['classify']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['dateCheckout']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      const EDAs = await this.eMECRepository.queryAll(exportOptional);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { 'none': EDAs };
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][EDAGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async checkClearanced(MAWB: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
          'data': null
        }
      }
      const totalOptional: Optional = new Optional();
      totalOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const cleanrancedOptional: Optional = new Optional();
      cleanrancedOptional.setAttributes([this._sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
      cleanrancedOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, 'dateClearanced', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, 7),
      ]);
      const notClearancedOptional: Optional = new Optional();
      notClearancedOptional.setAttributes([this._sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
      notClearancedOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.EQUAL, null),
        new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, null),
        new Where(EConfigure.AND, 'dateClearanced', EConfigure.EQUAL, null),
      ]);
      const [totalEDAs, EDAClearanced, EDANotClearance] = await Promise.all([
        this.eMECRepository.countData(totalOptional),
        this.eMECRepository.queryOneRaw(cleanrancedOptional),
        this.eMECRepository.queryOneRaw(notClearancedOptional),
      ]);

      const HAWBClearanced: any = [];
      const HAWBNotClearanced: any = [];
      if (EDAClearanced && EDAClearanced['HAWBs'] && EDAClearanced['HAWBs'].length > 0) {
        HAWBClearanced.push(...EDAClearanced['HAWBs'].split(','))
      }
      if (EDANotClearance && EDANotClearance['HAWBs'] && EDANotClearance['HAWBs'].length > 0) {
        HAWBNotClearanced.push(...EDANotClearance['HAWBs'].split(','))
      }
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.SUCCESS;
      reponze['message']['data'] = {
        'total': Number(totalEDAs),
        'HAWBClearanced': HAWBClearanced,
        'HAWBNotClearanced': HAWBNotClearanced,
      };
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][checkClearanced]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async saveWarehouse(HAWB: string, address: string, employeeId: number, hubs: any, date: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const EDA = await this.eMECRepository.getOneOptional(optional);
      const updateWarehouse: any = {
        'warehouseAddress': address,
        'warehouseCheckout': null
      }
      const now: string = moment().format(EConfigure.TIME);
      if (EDA) {
        const eda: EDA = EDA;
        updateWarehouse['warehouseCheckin'] = `${date} ${now}`;
        const [total] = await this.eMECRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][storeWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getStoreWarehouse(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      )
      const EDAs = await this.eMECRepository.queryAll(optional);
      if (EDAs.length > 0) {
        let edaData: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: EDA) => {
            const eda: IStoreWarehouse = {
              'HAWBClearance': EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'HAWB': EDA[EConfigure.HAWB_FIELD],
              'MAWB': EDA['MAWB'],
              'warehouseAddress': EDA['warehouseAddress'],
              'warehouseCheckin': EDA['warehouseCheckin'],
              'warehouseCheckout': EDA['warehouseCheckout'],
            }
            return eda;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': edaData,
          'total': edaData.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getStoreWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async deleteWarehouse(HAWB: string, address: string, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const EDA = await this.eMECRepository.getOneOptional(optional);
      const updateWarehouse: any = {
        'warehouseCheckout': moment().format(EConfigure.FULL_TIME)
      }
      if (EDA) {
        const eda: EDA = EDA;
        if (!eda['warehouseCheckin']) {
          reponze['message'] = {
            'message': EMessage.MANIFEST_NOT_CHECKIN_WAREHOUSE,
            'data': HAWB
          }
          return reponze;
        }
        const [total] = await this.eMECRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][deleteWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async masterLimit(hubId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const [MICs, IDAs] = await Promise.all([
        this.sequelize.query(`
          SELECT "MAWB"
            FROM (
                SELECT "MAWB", MAX("createdAt") AS "createdAt"
                FROM "clearance_mecs" AS "MIC"
                WHERE "hubId" = ${hubId} AND "MAWB" IS NOT NULL AND "isDeleted" = false
                GROUP BY "MAWB"
            ) AS "MIC"
            ORDER BY "createdAt" DESC
            LIMIT 200;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB"
            FROM (
                SELECT "MAWB", MAX("createdAt") AS "createdAt"
                FROM "clearance_edas" AS "MIC"
                WHERE "hubId" = ${hubId} AND "MAWB" IS NOT NULL AND "isDeleted" = false
                GROUP BY "MAWB"
            ) AS "MIC"
            ORDER BY "createdAt" DESC
            LIMIT 200;`,
          { type: this.sequelize.QueryTypes.SELECT }),
      ]);
      let masters: string[] = [];
      if (IDAs.length > 0 || MICs.length > 0) {
        masters = [...IDAs, ...MICs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][masterLimit]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async boxMAWB(externalBoxName: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const [MECs, EDAs, MICs, IDAs] = await Promise.all([
        this.sequelize.query(`
          SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "countryCode", "isHold"  
          FROM "clearance_mecs"
          WHERE "externalBoxName" = '${externalBoxName}' AND "isDeleted" = false;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "countryCode", "isHold" 
          FROM "clearance_edas"
          WHERE "externalBoxName" = '${externalBoxName}' AND "isDeleted" = false;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "countryCode", "isHold" 
          FROM "clearance_mics"
          WHERE "externalBoxName" = '${externalBoxName}' AND "isDeleted" = false;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "countryCode", "isHold" 
          FROM "clearance_idas"
          WHERE "externalBoxName" = '${externalBoxName}' AND "isDeleted" = false;`,
          { type: this.sequelize.QueryTypes.SELECT }),
      ]);
      let data: any[] = [];
      if (EDAs.length > 0) {
        data = [...data, ...EDAs];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = data;
      }
      if (IDAs.length > 0) {
        data = [...data, ...IDAs];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = data;
      }
      if (MICs.length > 0) {
        data = [...data, ...MICs];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = data;
      }
      if (MECs.length > 0) {
        data = [...data, ...MECs];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = data;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][boxMAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateSortLane(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      let [eda] = await Promise.all([
        this.eMECRepository.getOneOptional(optional)
      ]);
      if (eda) {
        let edaObj: eMEC = eda;
        edaObj["isSortLane"] = true;
        await edaObj.save();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = true;
      }
      return reponze;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async exportLane(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      if (HAWB != "") {
        let [mec, eda] = await Promise.all([
          this.sequelize.query(`
            SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "phase", "externalBoxName", "isHold" 
            FROM "clearance_mecs" WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}') AND "isDeleted" = false LIMIT 1;`,
            { type: this.sequelize.QueryTypes.SELECT }),
          this.sequelize.query(`
            SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "phase", "externalBoxName", "isHold" 
            FROM "clearance_edas" WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}') AND "isDeleted" = false LIMIT 1;`,
            { type: this.sequelize.QueryTypes.SELECT }),
        ]);
        if (mec.length > 0) {
          mec = mec[0];
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = mec;
        }
        if (eda.length > 0) {
          eda = eda[0];
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = eda;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][printAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async printAWB(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      if (HAWB != "") {
        let [mec, eda] = await Promise.all([
          this.sequelize.query(`
            SELECT "inspectionKindClassification", "phase", "originalOrderNumberClient" 
            FROM "clearance_mecs" WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}' OR "originalOrderNumberClient" = '${HAWB}') AND "isDeleted" = false 
            ORDER BY "createdAt" DESC;`,
            { type: this.sequelize.QueryTypes.SELECT }),
          this.sequelize.query(`
            SELECT "inspectionKindClassification", "phase", "originalOrderNumberClient" 
            FROM "clearance_edas" WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}' OR "originalOrderNumberClient" = '${HAWB}') AND "isDeleted" = false 
            ORDER BY "createdAt" DESC;`,
            { type: this.sequelize.QueryTypes.SELECT }),
        ]);
        if (mec.length > 0) {
          mec = mec[0];
          await this.sequelize.query(`
            UPDATE clearance_mecs SET "isSortLane" = true, "updatedAt" = '${moment.utc()}'
            WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}' OR "originalOrderNumberClient" = '${HAWB}') AND "isDeleted" = false;`,
            { type: this.sequelize.QueryTypes.UPDATE });
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = mec;
        }
        if (eda.length > 0) {
          eda = eda[0];
          await this.sequelize.query(`
            UPDATE clearance_edas SET "isSortLane" = true, "updatedAt" = '${moment.utc()}'
            WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}' OR "originalOrderNumberClient" = '${HAWB}') AND "isDeleted" = false;`,
            { type: this.sequelize.QueryTypes.UPDATE });
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = eda;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][printAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async boxCheckSort(value: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const MAWB = value['MAWB'];
      const boxName = value['externalBoxName'];
      const type = value['type'];
      if (MAWB != "") {
        let addBoxEda = "";
        if (boxName != undefined && boxName != "") {
          addBoxEda = `AND ce."externalBoxName" = '${boxName}'`;
        }
        let fullSort = "";
        switch (type) {
          case 1:
            fullSort = 'HAVING CAST(SUM(CASE WHEN "isSortLane" = false THEN 1 ELSE 0 END) AS int) <> 0';
            break;
          case 2:
            fullSort = 'HAVING CAST(SUM(CASE WHEN "isSortLane" = false THEN 1 ELSE 0 END) AS int) = 0';
            break;
        }
        const hawbs = await this.sequelize.query(`
          WITH outbound_tables AS (
            SELECT ce."externalBoxName", ce."MAWB", ce."isSortLane" FROM public.clearance_eedas ce WHERE ce."MAWB"  = '${MAWB}' ${addBoxEda} 
          )
          SELECT 
            "MAWB", "externalBoxName",
            CAST(SUM(CASE WHEN "isSortLane" = false THEN 1 ELSE 0 END) AS int) AS "unSort",
            CAST(SUM(CASE WHEN "isSortLane" = true THEN 1 ELSE 0 END)AS int) AS "sort"
          FROM outbound_tables 
          GROUP BY "externalBoxName", "MAWB"
          ${fullSort};`,
          { type: this.sequelize.QueryTypes.SELECT });
        const counter = await this.sequelize.query(`
          WITH outbound_hawbs AS (
            SELECT ce."HAWBClearance", ce."externalBoxName", ce."MAWB", ce."isSortLane"
            FROM public.clearance_eedas ce WHERE ce."MAWB" = '${MAWB}' ${addBoxEda}
          ),
          box_sort_status AS (
            SELECT 
              "externalBoxName",
              COUNT(1) AS "totalItems",
              SUM(CASE WHEN "isSortLane" = true THEN 1 ELSE 0 END) AS "sortedItems",
              COUNT(1) FILTER (WHERE "isSortLane" = false) AS "unSortItems",
              COUNT(1) FILTER (WHERE "isSortLane" = true) AS "sortItems"
            FROM outbound_hawbs
            GROUP BY "externalBoxName"
          )
          SELECT 
            CAST(COUNT(DISTINCT "externalBoxName") AS int) AS "totalBox",
            CAST(SUM(CASE WHEN "totalItems" = "sortedItems" THEN 1 ELSE 0 END) AS int) AS "sortBox",
            CAST(SUM(CASE WHEN "totalItems" != "sortedItems" THEN 1 ELSE 0 END) AS int) AS "unSortBox",
            CAST(SUM("totalItems") AS int) AS "totalHAWB",
            CAST(SUM("unSortItems") AS int) AS "unSort",
            CAST(SUM("sortItems") AS int) AS "sort"
          FROM box_sort_status;
          `, { type: this.sequelize.QueryTypes.SELECT });
        if (hawbs.length > 0) {
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = { counter, hawbs };
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][boxCheckSort]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async counterBoxAll(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const data: Record<string, number> = {
        unSort: 0,
        sort: 0,
        boxName: 0,
        unBoxName: 0,
        unDeclaration: 0,
        blue: 0,
        yellow: 0,
        red: 0,
      }

      optional.setAttributes([
        this.sequelize.literal('CAST(SUM(case when "isSortLane" = false then 1 else 0 end) as int) as "unSort"'),
        this.sequelize.literal('CAST(SUM(case when "isSortLane" = true then 1 else 0 end)as int) as "sort"'),
        this.sequelize.literal('CAST(COUNT(DISTINCT "externalBoxName") as int) as "boxName"'),
        this.sequelize.literal('CAST(SUM(case when "externalBoxName" is null then 1 else 0 end)as int) as "unBoxName"'),
        this.sequelize.literal('CAST(SUM(case when "inspectionKindClassification" is null then 1 else 0 end)as int) as "unDeclaration"'),
        this.sequelize.literal('CAST(SUM(case when "inspectionKindClassification" = \'1\' then 1 else 0 end)as int) as "blue"'),
        this.sequelize.literal('CAST(SUM(case when "inspectionKindClassification" = \'2\' then 1 else 0 end)as int) as "yellow"'),
        this.sequelize.literal('CAST(SUM(case when "inspectionKindClassification" = \'3\' then 1 else 0 end)as int) as "red"'),
      ]);
      const eda = await this.eMECRepository.queryOneRaw(optional);
      if (eda) {
        Object.keys(data).forEach(key => {
          if (eda.hasOwnProperty(key)) {
            data[key] = data[key];
          }
        });
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = data;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][counterBoxAll]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async externalBoxes(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const masterExternalOptional: Optional = new Optional();
      masterExternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterExternalOptional.setAttributes(['externalBoxName']);
      masterExternalOptional.setGroup(['externalBoxName']);
      const masterInternalOptional: Optional = new Optional();
      masterInternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterInternalOptional.setAttributes(['internalBoxName']);
      masterInternalOptional.setGroup(['internalBoxName']);
      const EDAExternalQuery = this.eMECRepository.queryAll(masterExternalOptional);
      const EDAInternalQuery = this.eMECRepository.queryAll(masterInternalOptional);
      const [EDAExs, EDAIns] = await Promise.all([EDAExternalQuery, EDAInternalQuery]);
      if (EDAExs.length > 0 || EDAIns.length > 0) {
        let externalBoxes = [];
        externalBoxes = [...EDAExs];
        if (externalBoxes.length > 0) {
          externalBoxes = [...new Set(externalBoxes.map((item: any) => item['externalBoxName']))];
        }
        let internalBoxes = [];
        internalBoxes = [...EDAIns];
        if (externalBoxes.length > 0) {
          internalBoxes = [...new Set(internalBoxes.map((item: any) => item['internalBoxName']))];
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { externalBoxes, internalBoxes };
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][externalBoxes]', (error as Error).message);
      throw new Error(error as any);
    }
  }
}
export default eMECClearanceService;