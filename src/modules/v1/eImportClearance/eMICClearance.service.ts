'use strict';

import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import * as pdf from 'html-pdf';
import { Database } from '../../../database/index';
import { ClassifyName, ClassifyKey, ClassifyValidateName, ClassifyValidateKey } from "../../../emuns/classify";
import { ActionKey } from "../../../emuns/action";
import { IResponze } from "../../../https/responze";
import { WeightName, WeightKey, WeightClearance } from "../../../emuns/weight";
import Utilities from "../../../util/utilities";
import { ICountry, Country, CustomerPersonal, CustomerBusiness, IWarehouse, IMICMasterValidate, Hold, User, Order, IExportDetail, MEC, EDA, HistoryInvoice, eIMIC, eMIC, eIImportDetail, eImportDetail, eIDetailValidate, eIImportDetailCreate, eIMICMaster, eIMICCreate } from "../../../models/index.model";
import eMICRepository from './eMICClearance.repository';
import HSCodeDetailRepository from '../hscodeDetail/hscodeDetail.reporsitory';
import { Clearance } from '../../../xmlClearance/clearance';
import { ClearanceTypeId } from '../../../xmlClearance/enum';
import ExchangeRateRepository from '../exchangeRate/exchangeRate.reporsitory';
import { IExchangeRate } from '../../../models/exchangeRate.model';
import HSCodeRepository from '../hscodeDetail/hscodeDetail.reporsitory';
import ImportClearanceDetailService from '../importClearanceDetail/importClearanceDetail.service';
import moment from 'moment';
import { ICreateAction, IUpdateAction, IChangeClassify, IMessageTax, IMaster, IReset, IRegister, IHoldTransaction, IWarehouseTransaction, INoteHold, IReturnCargo, IExchangeRateTransaction } from '../../../models/importTransaction.model';
import ImportTransactionRepository from '../importTransaction/importTransaction.reporsitory';
import OrderBy from '../../../parser/orderBy';
import _ from 'lodash';
import UserRepository from '../user/user.reporsitory';
import ImportClearanceValidate from './eMICClearance.validate';
import ErrorValidate from '../../../util/error.validate';
import httpStatus from 'http-status';
import { RabbitSendInternal } from '../../../util/rabbit';
import HoldRepository from '../hold/hold.reporsitory';
import ShipmentRepository from '../shipment/shipment.reporsitory';
import MECRepository from '../exportClearance/MECClearance.reporsitory';
import EDARepository from '../exportClearance/EDAClearance.reporsitory';
import HistoryMonitorRepository from '../historyMonitor/historyMonitor.reporsitory';
import ejs from 'ejs';
import * as path from 'path';
import { PrintKey } from '../../../emuns/print';
import Config from '../../../util/configure';
import HistoryInvoiceService from '../historyInvoice/historyInvoice.service';
import eMICDetailRepository from '../eImportClearanceDetail/eMICClearanceDetail.reporsitory';
import { IImportValidate } from '../../../models/mic.model';
const ExcelJS = require('exceljs');


interface IStoreWarehouse {
  HAWB?: string;
  MAWB?: string;
  HAWBClearance?: string;
  warehouseAddress?: string;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
}

interface IManagement {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: number;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  bussiness?: CustomerBusiness;
  personal?: CustomerPersonal;
  importDetails?: eImportDetail[];
  isHold?: boolean;
  reasonHold?: Hold[];
  clientName?: string;
  dateGate?: string;
  weightRound?: number;
}

interface ICheckout {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  type?: string;
  isHold?: any;
  externalBoxName?: string;
  details?: IDetail[];
}

interface IDetail {
  itemNameVN: string;
  quantity: number;
}

interface IImportReport {
  trackingNo: any;
  MAWB: any;
  origin: any;
  country: any;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: any;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: any;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: any;
  consignorName: any
  consignorAddress: any;
  consignorCityName: any;
  consignorContactName: any;
  consignorTelephone: any;
  consigneeName: any;
  consigneeAddress: any;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: any;
  consigneeTelephone: any;
  remarks: any;
  status?: any;
  statusId: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;

  customerBusiness?: any;
  customerPersonal?: any;
  dateClearanced?: any;
  orderNumber?: any;
  itemIds?: any;
  itemCount?: any;
  station?: any;
  importerCode?: any;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
  order?: Order;
  taxNumberCode?: any;
  taxNumberDate?: any;

  details?: any;
  externalBoxName?: any;
}

interface IExportReport {
  trackingNo: string;
  MAWB: string;
  origin: string;
  country: string;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: string;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: number;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: string;
  consignorName: string
  consignorAddress: string;
  consignorCityName: any;
  consignorContactName: string;
  consignorTelephone: any;
  consigneeName: string;
  consigneeAddress: string;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: string;
  consigneeTelephone: string;
  remarks: any;
  status?: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;
  taxNumberCode?: any;
  taxNumberDate?: any;
  externalBoxName?: any;
}

interface IHold {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: eIImportDetail[];
  reasonDetails?: Hold[];
}

interface IGetAll {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: eIImportDetail[];
  inspectionKindTimes?: any;
  clearanceDeclarationTimes?: any;
  reasonDetails?: Hold[];
  isHold?: any;
  externalBoxName?: any;
  internalBoxName?: any;
  warehouseAddress?: any
}

interface IWebHookRequest {
  'orderIds': string[],
  'action': number,
  'clientId': number,
  'reasonDetailId': number | null,
  'stationOriginId': number | null,
  'stationDestinationId': number | null,
  'holdClearance': any | null
}

interface IMAWBReport {
  HAWB: string,
  HAWBClearance?: string,
  piece: number,
  weight: number,
  invoicePrice: number,
  invoiceCurrency: string,
  consignorFullName: string,
  consignorAddress: string,
  consigneeName: string,
  consigneeAddress: string,
  details: eIImportDetail[],
}

class eMICClearanceService extends BaseService {
  private sequelize: any;
  private eMICRepository: eMICRepository;
  private eMICDetailRepository: eMICDetailRepository;
  private hscodeDetailRepository: HSCodeDetailRepository;
  private hscodeRepository: HSCodeRepository;
  private importTransactionRepository: ImportTransactionRepository;
  private holdRepository: HoldRepository;
  private shipmentRepository: ShipmentRepository;
  private historyMonitorRepository: HistoryMonitorRepository;
  private historyInvoiceService: HistoryInvoiceService;
  private _sequelize: any;
  constructor() {
    super(new eMICRepository());
    this.sequelize = Database.Sequelize;
    this.eMICRepository = new eMICRepository();
    this.eMICDetailRepository = new eMICDetailRepository();
    this.hscodeDetailRepository = new HSCodeDetailRepository();
    this.hscodeRepository = new HSCodeRepository();
    this.importTransactionRepository = new ImportTransactionRepository();
    this.holdRepository = new HoldRepository();
    this.shipmentRepository = new ShipmentRepository();
    this.historyMonitorRepository = new HistoryMonitorRepository();
    this.historyInvoiceService = new HistoryInvoiceService();
    this._sequelize = Database.Sequelize;
  }

  public async deleMIC(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.IN, [ActionKey.CREATE, ActionKey.UPDATE_CARGO_NAME].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      optional.setRelation(['importDetails']);
      const mics = await this.eMICRepository.queryAll(optional);
      if (mics.length > 0) {
        let transactions: any[] = [];
        let destroyHAWBs: string[] = [];
        if (mics.length > 0) {
          mics.forEach((MIC: eIMIC) => {
            const HAWB: string = String(MIC[EConfigure.HAWB_FIELD]);
            const transaction: any = {
              'HAWB': HAWB,
              'employeeId': employeeId,
              'action': ActionKey.DELETE_HAWB,
              'data': MIC,
              'newData': MIC,
              'classify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
              'currentClassify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
            }
            transactions.push(transaction);
            destroyHAWBs.push(HAWB);
          });
        }
        if (destroyHAWBs.length > 0) {
          const destroyOptional: Optional = new Optional();
          destroyOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, destroyHAWBs.join()),
          ]);
          await Promise.all([
            this.importTransactionRepository.createBulk(transactions),
            this.eMICRepository.destroyData(destroyOptional),
            this.eMICDetailRepository.destroyData(destroyOptional),
          ]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][deleMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async deleteWarehouse(HAWB: string, address: string, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const MIC: any = this.eMICRepository.getOneOptional(optional);
      const updateWarehouse: any = {
        'warehouseCheckout': moment().format(EConfigure.FULL_TIME)
      }
      if (MIC) {
        const ida: eMIC = MIC;
        if (!ida['warehouseCheckin']) {
          reponze['message'] = {
            'message': EMessage.MANIFEST_NOT_CHECKIN_WAREHOUSE,
            'data': HAWB
          }
          return reponze;
        }
        const [total] = await this.eMICRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          const transactionWarehouse: IWarehouseTransaction = {
            'HAWB': HAWB,
            'action': ActionKey.DELETE_WAREHOUSE,
            'employeeId': employeeId,
            'warehouseAddress': address,
          }
          await this.importTransactionRepository.createData(transactionWarehouse);
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][deleteWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async MICGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const importOptional: Optional = new Optional();
      importOptional.setRelation(["importDetailItems"]);
      importOptional.setAttributes(value['select'].split(','));
      importOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);
      if (value['classify']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        importOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (value['dateCheckout']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      const MICs = await this.eMICRepository.queryAll(importOptional);
      if (MICs.length > 0) {
        const obj: any = {};
        if (value['boxes'] && value['boxes'].length > 0) {
          const idaExternalBoxes = _.groupBy(MICs, IDA => IDA['externalBoxName']);
          const idaInternalBoxes = _.groupBy(MICs, IDA => IDA['internalBoxName']);
          value['boxes'].forEach((item: any) => {
            obj[item] = [
              ...idaExternalBoxes[item] ? idaExternalBoxes[item] : [],
              ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []
            ];
          })
        } else {
          MICs.forEach((ida: eMIC) => {
            let key: string = 'none';
            if (ida['externalBoxName']) {
              key = ida['externalBoxName'];
            } else if (ida['internalBoxName']) {
              key = ida['internalBoxName'];
            }
            if (obj[key]) {
              obj[key].push(ida);
            } else {
              obj[key] = [ida];
            }
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = obj;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][IDAGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async swapInvoice(data: Record<string, any>[]): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': 'Vui lòng kiểm tra lại các HAWB cần chuyển số biên lai',
          'data': null,
          'error': []
        }
      }
      if (data.length > 0) {
        const HAWBs: string[] = data.map((item) => item['HAWB']);
        const optional: Optional = new Optional();
        optional.setAttributes(['HAWB', 'phase', 'taxCodeNumber']);
        optional.setWhere([
          new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        const mics: eMIC[] = await this.eMICRepository.queryAll(optional);
        if (mics.length > 0) {
          if (mics.every((item) => item.phase == ActionKey.ACCEPT_CLEARANCE && item.taxCodeNumber != null)) {
            for (const item of data) {
              const updateOptional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, item['HAWB']),
                new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, item['HAWB']),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
              ]);
              await this.eMICRepository.updateData({ 'taxCodeNumber': item['taxCodeNumber'] }, updateOptional);
            }
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.SUCCESS;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][swapInvoice]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async generateInvoice(data: Record<string, any>): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': 'Vui lòng kiểm tra lại HAWB hoặc chưa thông quan hoặc đã có số biên lai',
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      const checkAccept: Optional = new Optional();
      const checkInvoice: Optional = new Optional();
      checkAccept.setAttributes(['HAWB']);
      checkAccept.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.NOT_EQUAL, ActionKey.ACCEPT_CLEARANCE),
      ]);
      checkInvoice.setAttributes(['HAWB']);
      checkInvoice.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
      ]);
      optional.setAttributes(['HAWB']);
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.IS, null),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
      ]);
      optional.setOrderby([
        new OrderBy('dateClearanced', EConfigure.ASCENDING)
      ]);
      const [mics, accepts, invoices] = await Promise.all([
        this.eMICRepository.queryAll(optional),
        this.eMICRepository.queryAll(checkAccept),
        this.eMICRepository.queryAll(checkInvoice),
      ]);
      if (mics.length > 0 && accepts.length == 0 && invoices.length == 0) {
        for (const item of mics) {
          const taxURL = process.env.TAX_URL || 'https://devapi.globex.vn/taxcode-sync/api/';
          console.log(taxURL);
          await Utilities.callAPINonAuth(`${taxURL}emic_tax_number_sync?HAWB=${item['HAWB']}`);
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      } else {
        reponze['message']['error'] = {
          "notAccept": accepts,
          "invoiveNumber": invoices,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][generateInvoice]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async saveWarehouse(HAWB: string, address: string, employeeId: number, hubs: any, date: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const MIC: any = await this.eMICRepository.getOneOptional(optional);
      const updateWarehouse: any = {
        'warehouseAddress': address,
        'warehouseCheckout': null
      }
      const now: string = moment().format(EConfigure.TIME);
      if (MIC) {
        updateWarehouse['warehouseCheckin'] = `${date} ${now}`;
        const [total] = await this.eMICRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          const transactionWarehouse: IWarehouseTransaction = {
            'HAWB': HAWB,
            'action': ActionKey.SAVE_WAREHOUSE,
            'employeeId': employeeId,
            'warehouseAddress': address,
          }
          await this.importTransactionRepository.createData(transactionWarehouse);
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][storeWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filtereMIC(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalRegisterTemp': 0,
            'totalSentTemp': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const optionalRegisterTemp: Optional = new Optional();
      optionalRegisterTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const optionalSentTemp: Optional = new Optional();
      optionalSentTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SENT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      const IDAQuery = this.eMICRepository.getAll(optional);
      const IDATotalCreateQuery = this.eMICRepository.countData(optionalCreate);
      const IDATotalUpdateCarageNameQuery = this.eMICRepository.countData(optionalUpdateCarageName);
      const IDATotalRegisterTempQuery = this.eMICRepository.countData(optionalRegisterTemp);
      const IDATotalSentTempQuery = this.eMICRepository.countData(optionalSentTemp);
      const IDATotalSendClearanceQuery = this.eMICRepository.countData(optionalSendClearance);
      const IDATotalEditClearanceQuery = this.eMICRepository.countData(optionalEditClearance);
      const IDATotalInspectionKindQuery = this.eMICRepository.countData(optionalInspectionKind);
      const IDATotalAcceptClearanceQuery = this.eMICRepository.countData(optionalAcceptClearance);
      const IDAErrorQuery = this.eMICRepository.countData(optionalError);
      const [[EDAs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError, registerTemp, sentTemp] =
        await Promise.all([IDAQuery, IDATotalCreateQuery, IDATotalUpdateCarageNameQuery, IDATotalSendClearanceQuery, IDATotalEditClearanceQuery, IDATotalInspectionKindQuery,
          IDATotalAcceptClearanceQuery, IDAErrorQuery, IDATotalRegisterTempQuery, IDATotalSentTempQuery]);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': EDAs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalRegisterTemp': registerTemp,
          'totalSentTemp': sentTemp,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][filtereMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAlleMIC(optional: Optional): Promise<any> {
    try {
      const objData = await this.eMICRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][getAlleMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async monitorGate(HAWB: string, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.OR, 'declarationNo', EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optional.setRelation(['importDetails', 'country']);
      const IDA: any = await this.eMICRepository.getOneOptional(optional);
      let checkoutData: ICheckout = {};
      if (IDA) {
        const ida: eIMIC = IDA;
        const details: any = ida['importDetails'];
        checkoutData = {
          'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
          'MAWB': ida['MAWB'],
          'declarationType': 'IDA',
          'declarationNo': ida['declarationNo'],
          'clearanceInspection': ida['inspectionKindClassification'],
          'dateCheckout': ida['dateCheckout'],
          'dateCheckin': ida['dateCheckin'],
          'dateClearanced': ida['dateClearanced'],
          'cargoPiece': ida['cargoPiece'],
          'weight': ida['cargoWeightGross'],
          'classify': ida['classify'] as string,
          'country': ida['country'],
          'isHold': ida['isHold'],
        }
        if (details.length > 0) {
          checkoutData['details'] = details.map((detail: any) => {
            const importDetail: IDetail = {
              'itemNameVN': detail['itemNameVN'],
              'quantity': detail['quantity1']
            }
            return importDetail
          });
        }
        reponze['status'] = true;
        reponze['message'] = {
          'message': EMessage.FOUND,
          'data': checkoutData
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][monitorGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async createImport(manifestImports: IImportValidate[]): Promise<any> {
    let transaction: any;
    let reponze: IResponze = {
      'status': false,
      'statusCode': httpStatus.OK,
      'message': {
        'message': EMessage.CREATE_FAIL,
        'data': {
          'fail': [],
          'succesfull': [],
        }
      }
    }
    let { error, value } = ImportClearanceValidate.arrCreate.validate(manifestImports, { abortEarly: false, stripUnknown: true });
    if (error) {
      let errorValidate: ErrorValidate = new ErrorValidate(error);
      let messages: string[] = errorValidate.handleError();
      reponze['statusCode'] = httpStatus.BAD_REQUEST;
      reponze['message']['error'] = messages;
      return reponze;
    }
    try {
      value = Utilities.convertNull(value);
      let HAWBs: string[] = [];
      let HAWBClearances: string[] = [];
      let processingHAWBs: string[] = [];
      let processingHAWBClearances: string[] = [];
      let storeDetail: Map<string, eImportDetail[]> = new Map()
      HAWBs = value.map((manifest: any) => {
        return manifest[EConfigure.HAWB_FIELD];
      });
      const processOptional: Optional = new Optional();
      processOptional.setAttributes([EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD]);
      processOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      const detailOptional: Optional = new Optional();
      detailOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
      ]);
      detailOptional.setOrderby([
        new OrderBy('position', EConfigure.ASCENDING)
      ])
      const idaQuery: any = this.eMICRepository.queryAll(processOptional);
      const detailQuery: any = this.eMICDetailRepository.queryAll(detailOptional);
      const [idas, details] = await Promise.all([idaQuery, detailQuery]);
      if (idas.length > 0) {
        idas.forEach((ida: eIMIC) => {
          processingHAWBs.push(ida[EConfigure.HAWB_FIELD] || '');
          processingHAWBClearances.push(ida[EConfigure.HAWB_CLEARANCE_FIELD] || '');
        });
      }
      if (details.length > 0) {
        details.forEach((detail: eImportDetail) => {
          const HAWB: string = detail[EConfigure.HAWB_FIELD];
          if (!storeDetail.has(HAWB)) {
            storeDetail.set(HAWB, [detail]);
          } else {
            const store: any = storeDetail.get(HAWB);
            store.push(detail);
            storeDetail.set(HAWB, store);
          }
        });
      }
      HAWBs = [];
      const dataIDAs: eIMICCreate[] = [];
      const dataDetails: any[] = [];
      let idaData: any;
      let detailData: eIImportDetailCreate;
      const actionCreate: number = ActionKey.CREATE;
      let createTransactions: ICreateAction[] = [];
      for (const manifest of manifestImports) {
        const cloneManifest: IImportValidate = { ...manifest };
        const orderTypeId: number = (!cloneManifest['orderTypeId'] || (cloneManifest['orderTypeId'] && cloneManifest['orderTypeId'] < EConfigure.INDEX_4)) ? EConfigure.INDEX_0 : cloneManifest['orderTypeId'];
        const HAWB: string = cloneManifest[EConfigure.HAWB_FIELD] || '';
        const HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD];
        if (!processingHAWBs.includes(HAWB)) {
          HAWBs.push(HAWB);
          HAWBClearances.push(HAWBClearance);
          let warehouse: IWarehouse = {};
          const valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
          let totalPrice: number = Number(cloneManifest['totalPrice']);
          const priceVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
          const countryCode: string = cloneManifest['consignorCountryCode'];
          const stationId: number = cloneManifest['stationId'];
          if (cloneManifest['warehouseId'] && orderTypeId < EConfigure.INDEX_4) {
            warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
          }
          if (!cloneManifest['warehouseId'] && orderTypeId >= EConfigure.INDEX_4 && cloneManifest['hubId']) {
            warehouse = await Utilities.getWarehouseByHub(cloneManifest['hubId'], orderTypeId);
          }
          const country: ICountry = await Utilities.getCountryByCode(countryCode);
          let details: eIDetailValidate[] = [];
          let declareCargoNameIDA: boolean = false;
          let hasMO: boolean = false;
          if (manifest['items'].length > 0) {
            details = manifest['items'];
            details.forEach((detail: eIDetailValidate, index: number) => {
              detailData = {};
              if (detail['HSCode'] && (+(detail['HSCode'].substring(0, 2)) == 85 || +(detail['HSCode'].substring(0, 2)) == 84)) {
                hasMO = true;
              }
              const cloneDetail: eIDetailValidate = { ...detail };
              const itemNameEN: string = cloneDetail['itemName'] || '';
              const totalPrice: number = Number(cloneDetail['invoiceValue']);
              const priceDetailVND: number = +(totalPrice * valueClearanceVND).toFixed(0);
              const currencyCode: string = cloneDetail['currencyCode'];
              detailData['HSCode'] = cloneDetail['HSCode'];
              detailData[EConfigure.HAWB_FIELD] = HAWB;
              detailData['itemName'] = itemNameEN;
              detailData['itemNameVN'] = detail['itemNameVN'];
              if (storeDetail.has(HAWB)) {
                const currentDetail: any = storeDetail.get(HAWB);
                if (currentDetail[index]) {
                  detailData['HSCode'] = currentDetail[index]['HSCode'];
                  detailData['itemNameVN'] = currentDetail[index]['itemNameVN'];
                  detailData['quantity2'] = currentDetail[index]['quantity2'];
                  detailData['quantityUnitCode2'] = currentDetail[index]['quantityUnitCode2'];
                }
              }
              if (detail['placeOfOrigin']) {
                detailData['placeOfOrigin'] = detail['placeOfOrigin'];
              } else {
                detailData['placeOfOrigin'] = countryCode;
                if (country) {
                  detailData['originalPlaceName'] = country['shortName'];
                }
              }
              if (currencyCode === EConfigure.CURRENCY_VND) {
                detailData['invoiceValue'] = +(totalPrice.toFixed(0));
                detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
              } else {
                detailData['invoiceValue'] = totalPrice;
                detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
              }
              detailData['priceVND'] = priceDetailVND;
              detailData['unitPriceCurrencyCode'] = currencyCode;
              if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
                detailData['weightKG'] = cloneManifest['weight'];
              } else {
                detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
              }
              detailData['url'] = cloneDetail['url'];
              detailData['originalUrl'] = cloneDetail['originalUrl'];
              detailData['originalProductName'] = cloneDetail['originalProductName'] ? Utilities.removeSpeialCharacter(cloneDetail['originalProductName']) : null;
              detailData['productId'] = detail['productId'];
              detailData['quantity1'] = cloneDetail['quantity'];
              detailData['quantity2'] = cloneDetail['quantity'];
              detailData['quantityUnitCode1'] = EConfigure.PIECE;
              detailData['quantityUnitCode2'] = EConfigure.PIECE;
              detailData['priceQuantityUnit'] = EConfigure.PIECE;
              detailData['quantityUnitPrice'] = EConfigure.PIECE;
              detailData['importTaxCode'] = EConfigure.IMPORT_TAX_INCENTIVES;
              detailData['VATTaxCode'] = EConfigure.VAT_TAX_INCENTIVES_VB215;
              detailData['position'] = index;
              dataDetails.push(detailData);
            });
          }
          if (dataDetails.length > 0) {
            declareCargoNameIDA = dataDetails.every((detail: eIDetailValidate) => {
              return detail['itemNameVN'] && detail['HSCode'] && detail['HSCode'].match(/^0+/) === null && detail['HSCode'].match(/^[0-9]+$/) !== null;
            });
          }
          idaData = {
            'HAWB': HAWB,
            'HAWBClearance': HAWBClearance,
            'cargoNo': HAWBClearance,
            'stationId': stationId,
            'phase': actionCreate,
            'serviceId': cloneManifest['serviceId'],
            'identity': cloneManifest['identity'],
            'threadCode': cloneManifest['threadCode'],
            'threadName': cloneManifest['threadName'],
            'threadColor': cloneManifest['threadColor'],
            'threadUrl': cloneManifest['threadUrl'],
            'orderTypeId': orderTypeId
          };
          idaData['hubId'] = cloneManifest['hubId'];
          if (warehouse != null && warehouse['isGenerateInvoice']) {
            const taxURL = process.env.TAX_URL || 'https://devapi.globex.vn/taxcode-sync/api/';
            const no = await Utilities.callAPINonAuth(`${taxURL}invoice_number_sync`);
            idaData['invoiceNo'] = Utilities.padWithZeroes(Number(no), EConfigure.INDEX_7);
            idaData['invoiceDate'] = cloneManifest['invoiceDate'];
          }
          if (declareCargoNameIDA) {
            idaData['phase'] = ActionKey.UPDATE_CARGO_NAME;
          }
          idaData['MAWB'] = cloneManifest['MAWB'];
          idaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
          if (!cloneManifest['importerCode']) {
            idaData['importerCode'] = EConfigure.IMPORTER_CODE as string;
          } else {
            idaData['importerCode'] = cloneManifest['importerCode'];
          }
          if (!cloneManifest['importerName']) {
            idaData['importerName'] = EConfigure.IMPORTER_NAME;
          } else {
            idaData['importerName'] = cloneManifest['importerName'];
            idaData['importerFullName'] = cloneManifest['importerName'];
          }
          idaData['postCode'] = cloneManifest['importerPostCode'];
          idaData['addressOfImporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['importerAddress'] as string), 0, 200);
          if (cloneManifest['importerTelephoneNumber']) {
            idaData['telephoneNumberOfImporter'] = Utilities.subString(cloneManifest['importerTelephoneNumber'], 0, 20);
          }
          idaData['consignorCode'] = cloneManifest['consignorCode'];
          idaData['consignorName'] = cloneManifest['consignorName'];
          const addresses = Utilities.handleAddress(cloneManifest['consignorAddress'] as string);
          if (addresses) {
            for (const [key, value] of Object.entries(addresses)) {
              if (key <= '3') {
                idaData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
              }
            }
          }
          if (country) {
            idaData['address4'] = country['fullName'];
          }
          idaData['countryCode'] = countryCode;
          idaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT;
          if (cloneManifest['meanOfTransportationCode']) {
            idaData['meansOfTransportationCode'] = cloneManifest['meanOfTransportationCode'];
          }
          idaData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
          idaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
          idaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
          idaData['codeOfExtendingDueDateForPayment'] = EConfigure.TYPE_D;
          idaData['clientId'] = cloneManifest['clientId'];
          idaData['termOfPayment'] = EConfigure.KHONGTT;
          idaData['invoicePriceConditionCode'] = EConfigure.DDP;
          if (idaData['clientId'] == EConfigure.INDEX_1 || idaData['clientId'] == EConfigure.INDEX_2) {
            idaData['termOfPayment'] = EConfigure.KC;
            idaData['invoicePriceConditionCode'] = EConfigure.CIF;
          }
          idaData['declarationKindCode'] = EConfigure.H11;
          idaData['arrivalDate'] = cloneManifest['arrivalDate'];
          if (warehouse && Object.keys(warehouse).length > 0) {
            if (orderTypeId >= 4) {
              idaData['declarationKindCode'] = warehouse['kindleCode'];
              idaData['arrivalDate'] = moment().format(EConfigure.DAY_TIME);
            }
            idaData['customsWarehouseCode'] = warehouse['code'];
            idaData['plannedDeclarantCode'] = warehouse['agencyCode'];
            idaData['unloadingPortCode'] = warehouse['unloadingPortCode'];
            idaData['unloadingPortName'] = warehouse['unloadingPortCode'];
            idaData['customsOffice'] = warehouse['customsOffice'];
            idaData['customsSubSection'] = warehouse['customsSubSection'];
            idaData['feeClearance'] = warehouse['feePrice'];
            idaData['terminalName'] = warehouse['id'];
            idaData['classificationOfIndividualOrganization'] = warehouse['individualOrganization'];
          }
          if (cloneManifest['arrivalDate'] && cloneManifest['flightNo']) {
            idaData['loadingVesselAircraftName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['arrivalDate']);
          } else {
            idaData['loadingVesselAircraftName'] = cloneManifest['flightNo'];
          }
          idaData['loadingLocationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
          idaData['loadingLocationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
          if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
            idaData['cargoWeightGross'] = cloneManifest['weight'];
          } else {
            idaData['cargoWeightGross'] = Utilities.convertKG(cloneManifest['weight'], cloneManifest['unitOfMass']);
          }
          idaData['weightUnitCodeGross'] = WeightClearance.get('kg');
          idaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
          if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
            totalPrice = +(totalPrice.toFixed(0));
            idaData['totalInvoicePrice'] = totalPrice;
            idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
            idaData['totalOfTaxValue'] = totalPrice;
            idaData['totalOfProportional'] = totalPrice;
          } else {
            idaData['totalInvoicePrice'] = totalPrice;
            idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
            idaData['totalOfTaxValue'] = totalPrice;
            idaData['totalOfProportional'] = totalPrice;
          }
          idaData['valueClearanceVND'] = valueClearanceVND;
          idaData['customerBusinessId'] = cloneManifest['customerBusinessId'];
          idaData['customerPersonalId'] = cloneManifest['customerPersonalId'];
          idaData['notes'] = cloneManifest['note'];
          idaData['classify'] = cloneManifest['classify'];
          idaData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
          idaData['priceVND'] = priceVND;
          idaData['taxPayer'] = EConfigure.TAX_PAYER;
          idaData['invoicePriceKindCode'] = EConfigure.TYPE_A;
          idaData['orderId'] = cloneManifest['orderId'];
          idaData['originalPrice'] = cloneManifest['originalPrice'];
          idaData['currencyOriginalPrice'] = cloneManifest['currencyOriginalPrice'];
          idaData['internalBoxName'] = cloneManifest['internalBoxName'];
          idaData['externalBoxName'] = cloneManifest['externalBoxName'];
          idaData['childOrderNumber'] = cloneManifest['childOrderNumber'];
          idaData['orderNumber'] = cloneManifest['orderNumber'];
          idaData['labelCustomer'] = cloneManifest['labelCustomer'];
          idaData['invoiceCustomer'] = cloneManifest['invoiceCustomer'];
          if (hasMO) {
            idaData['otherLawCode'] = EConfigure.MO;
          }
          dataIDAs.push(idaData);
          const idaTraction: ICreateAction = {
            'HAWB': HAWB,
            'action': actionCreate,
            'classify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
            'currentClassify': idaData['classify'],
            'data': idaData,
            'newData': idaData,
            'stationId': stationId
          }
          createTransactions.push(idaTraction);
        }
      }
      if (dataIDAs.length > 0) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.NOT_IN, processingHAWBs.join())
        ]);
        try {
          transaction = await this._sequelize.transaction();
          await Promise.all([
            this.eMICRepository.destroyDataTrx(optional, transaction),
            this.eMICDetailRepository.destroyDataTrx(optional, transaction)
          ]);
          const micCreated = await this.eMICRepository.createBulkTrx(dataIDAs, transaction);
          await this.eMICDetailRepository.createBulkTrx(dataDetails, transaction);
          await this.importTransactionRepository.createBulkTrx(createTransactions, transaction);
          await transaction.commit();
          if (micCreated.length > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.CREATED_DATA;
            reponze['message']['data'] = {
              'succesfull': HAWBClearances,
              'fail': processingHAWBClearances
            }
          }
        } catch (error) {
          await transaction.rollback();
          console.log('---- create imports error: %o', error);
          reponze['message']['data']['fail'].push(...HAWBClearances, ...processingHAWBClearances)
        }
      } else {
        reponze['message']['data']['fail'].push(...HAWBClearances, ...processingHAWBClearances)
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][createImport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async changeExchangeRate(value: any, employeeId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
        }
      }
      const { HAWBs, exchangeRate } = value;
      if (HAWBs && HAWBs.length > 0) {
        const createTransactions: any = [];
        HAWBs.forEach((item: string) => {
          const tractionObj: IExchangeRateTransaction = {
            'HAWB': item,
            'action': ActionKey.UPDATE_EXCHANGE_RATE,
            'employeeId': employeeId,
            'newData': exchangeRate,
          }
          createTransactions.push(tractionObj);
        });
        const queryHAWBs: string = `'${HAWBs.join("','")}'`;
        const [_, totalIDA] = await this._sequelize.query(`
          UPDATE clearance_emics SET "priceVND" = ROUND("totalInvoicePrice" * ${exchangeRate}), "valueClearanceVND" = ${exchangeRate} 
          WHERE "phase" <> 7 AND "HAWB" IN (${queryHAWBs}) AND "invoiceCurrencyCode" <> '${EConfigure.CURRENCY_VND}'`,
          { type: this._sequelize.QueryTypes.UPDATE });
        if (totalIDA > 0) {
          await Promise.all([
            this._sequelize.query(`
              UPDATE clearance_eimport_details SET "priceVND" = ROUND("invoiceValue" * ${exchangeRate})
              WHERE "HAWB" IN (${queryHAWBs})`,
              { type: this._sequelize.QueryTypes.UPDATE }),
            this.importTransactionRepository.createBulk(createTransactions),
          ]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][changeExchangeRate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateeMIC(HAWB: string, value: any, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const ida: eMIC = await this.eMICRepository.getOneOptional(optional);
      if (ida) {
        const HAWBClearance: string = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
        if (ida['phase'] === ActionKey.ACCEPT_CLEARANCE && ida['classify'] === ClassifyName.get(ClassifyKey.PAR)) {
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': HAWBClearance,
            'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
          });
        } else {
          const idaUpdate: eIMIC = value;
          const clearanceVND: any = value['valueClearanceVND'] ? (Number(value['valueClearanceVND'])) : 0;
          const idaDetailUpdates: eIImportDetailCreate[] = value['importDetails'];
          const freight: number = Number(idaUpdate['freight']);
          const currencyCode: string = String(idaUpdate['invoiceCurrencyCode']);
          const importTransaction: any = [];
          idaUpdate['valueClearanceVND'] = clearanceVND;
          if (idaUpdate['totalInvoicePrice']) {
            idaUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(idaUpdate['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(idaUpdate['totalInvoicePrice'])).toFixed(0);
          } else {
            idaUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(ida['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(ida['totalInvoicePrice'])).toFixed(0);
          }
          if (idaUpdate['feeClearance']) {
            idaUpdate['feeClearance'] = +(Number(idaUpdate['feeClearance']).toFixed(0));
          }
          if (currencyCode === EConfigure.CURRENCY_VND) {
            idaUpdate['totalInvoicePrice'] = +(Number(idaUpdate['totalInvoicePrice']).toFixed(0));
            idaUpdate['totalOfProportionalDistributionOnTaxValue'] = +(Number(idaUpdate['totalInvoicePrice']).toFixed(0));
            idaUpdate['totalOfTaxValue'] = +(Number(idaUpdate['totalInvoicePrice']).toFixed(0));
            idaUpdate['totalOfProportional'] = +(Number(idaUpdate['totalInvoicePrice']).toFixed(0));
          } else {
            idaUpdate['totalInvoicePrice'] = idaUpdate['totalInvoicePrice'];
            idaUpdate['totalOfProportionalDistributionOnTaxValue'] = idaUpdate['totalInvoicePrice'];
            idaUpdate['totalOfTaxValue'] = idaUpdate['totalInvoicePrice'];
            idaUpdate['totalOfProportional'] = idaUpdate['totalInvoicePrice'];
          }
          if ((ida['phase'] === ActionKey.CREATE || ida['phase'] === ActionKey.UPDATE_CARGO_NAME) && value['importerName']) {
            idaUpdate['importerFullName'] = value['importerName'];
          }
          if (freight && freight > 0) {
            if (idaUpdate['freightCurrencyCode'] && idaUpdate['freightCurrencyCode'] !== currencyCode) {
              const exchangeRateOptional: Optional = new Optional();
              exchangeRateOptional.setWhere([
                new Where(EConfigure.AND, 'currency', EConfigure.EQUAL, idaUpdate['freightCurrencyCode']),
              ])
              const exchangeRate: IExchangeRate = await new ExchangeRateRepository().queryOneRaw(exchangeRateOptional);
              if (exchangeRate) {
                idaUpdate['freightExchangeRate'] = exchangeRate['valueClearance'];
              }
            } else {
              idaUpdate['freightExchangeRate'] = clearanceVND;
              idaUpdate['freightCurrencyCode'] = currencyCode;
            }
          } else {
            idaUpdate['freight'] = null;
            idaUpdate['freightExchangeRate'] = null;
            idaUpdate['freightCurrencyCode'] = null;
          }
          if (ida['noteHold'] !== idaUpdate['noteHold']) {
            const noteHold: INoteHold = {
              'HAWB': HAWBClearance,
              'employeeId': employeeId,
              'action': ActionKey.NOTE_HOLD,
              'noteHold': idaUpdate['noteHold']
            }
            importTransaction.push(noteHold);
          }
          if (_.isEqual(ida['reasonIds'], idaUpdate['reasonIds'] ? String(idaUpdate['reasonIds']).split(',').map(Number) : null) === false) {
            const transactionHold: IHoldTransaction = {
              'HAWB': HAWBClearance,
              'action': ActionKey.HOLD,
              'holdId': idaUpdate['reasonIds'],
              'employeeId': employeeId,
            }
            importTransaction.push(transactionHold);
            Utilities.updatePartnerStatusHAWB([HAWB], 112);
          }
          idaUpdate['reasonIds'] = idaUpdate['reasonIds'] ? String(idaUpdate['reasonIds']).split(',').map(Number) : null;
          if (ida['classify'] === ClassifyName.get(ClassifyKey.COM)) {
            if (!idaUpdate['dateClearanced']) {
              idaUpdate['dateClearanced'] = moment().format(EConfigure.FULL_TIME);
            }
            idaUpdate['phase'] = ActionKey.ACCEPT_CLEARANCE;
          }
          idaUpdate['cargoWeightGross'] = Number(idaUpdate['cargoWeightGross']);

          const idaOptional: Optional = new Optional();
          idaOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          ]);
          transaction = await this._sequelize.transaction();
          await this.eMICRepository.updateDataTrx(idaUpdate, idaOptional, transaction);
          if (idaDetailUpdates.length > 0) {
            await this.eMICDetailRepository.destroyData(idaOptional);
            await Promise.all(idaDetailUpdates.map(async (idaDetailUpdate: any, index: number) => {
              if (idaDetailUpdate['HSCode'] && (idaDetailUpdate['HSCode'].substring(0, 2) == 85 || idaDetailUpdate['HSCode'].substring(0, 2) == 84)) {
                idaUpdate['otherLawCode'] = EConfigure.MO;
              }
              let itemNameVN: string = Utilities.removeSpeialCharacter(idaDetailUpdate['itemNameVN']);
              itemNameVN = Utilities.removeGeneralToken(itemNameVN);
              idaDetailUpdate['itemNameVN'] = itemNameVN;
              idaDetailUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(idaDetailUpdate['invoiceValue']) * clearanceVND).toFixed(0) : +(Number(idaDetailUpdate['invoiceValue'])).toFixed(0);
              if (currencyCode === EConfigure.CURRENCY_VND) {
                idaDetailUpdate['invoiceValue'] = +(Number(idaDetailUpdate['invoiceValue']).toFixed(0));
              } else {
                idaDetailUpdate['invoiceValue'] = idaDetailUpdate['invoiceValue'];
              }
              idaDetailUpdate['priceQuantityUnit'] = idaDetailUpdate['quantityUnitCode1'];
              idaDetailUpdate['quantityUnitPrice'] = idaDetailUpdate['quantityUnitCode1'];
              idaDetailUpdate['priceQuantityUnit'] = idaDetailUpdate['quantityUnitCode1'];
              idaDetailUpdate['position'] = index;
              if (!idaDetailUpdate['importTaxCode']) {
                idaDetailUpdate['importTaxCode'] = EConfigure.IMPORT_TAX_INCENTIVES;
              }
              if (!idaDetailUpdate['VATTaxCode']) {
                idaDetailUpdate['VATTaxCode'] = EConfigure.VAT_TAX_INCENTIVES_VB215;
              }
              if (!idaDetailUpdate['quantity2']) {
                idaDetailUpdate['quantity2'] = idaDetailUpdate['quantity1'];
              }
              if (!idaDetailUpdate['quantityUnitCode2']) {
                idaDetailUpdate['quantityUnitCode2'] = idaDetailUpdate['quantityUnitCode1'];
              }
              idaDetailUpdate['weightKG'] = idaDetailUpdate['weightKG'] ? idaDetailUpdate['weightKG'] : null;
              idaDetailUpdate['url'] = idaDetailUpdate['url'] ? idaDetailUpdate['url'] : null;
              if (freight && freight > 0) {
                const rateFreight: number = freight / (Number(idaUpdate['totalInvoicePrice']) / Number(idaDetailUpdate['invoiceValue']));
                const priceFreight = (Number(idaUpdate['freightExchangeRate']) > 0) ? +(rateFreight * Number(idaUpdate['freightExchangeRate'])).toFixed(0) : +rateFreight.toFixed(0);
                idaDetailUpdate['priceVND'] = +(Number(idaDetailUpdate['priceVND']) + priceFreight).toFixed(0);
              }
              const checkHSCodeDetail: Optional = new Optional();
              checkHSCodeDetail.setWhere([
                new Where(EConfigure.AND, 'name', EConfigure.EQUAL, idaDetailUpdate['itemName']),
                new Where(EConfigure.AND, 'nameVN', EConfigure.EQUAL, itemNameVN),
                new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, idaDetailUpdate['HSCode']),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);
              const checkHSCode: Optional = new Optional();
              checkHSCode.setWhere([
                new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, idaDetailUpdate['HSCode']),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);
              const hscodeDetailQuery: any = this.hscodeDetailRepository.getOneOptional(checkHSCodeDetail);
              const hscodeQuery: any = this.hscodeRepository.getOneOptional(checkHSCode);
              let [hscodeDetail, hscode] = await Promise.all([hscodeDetailQuery, hscodeQuery]);
              if (!hscode) {
                const newHSCode: any = {
                  'hsCode': idaDetailUpdate['HSCode'],
                  'importTaxCode': idaDetailUpdate['importTaxCode'],
                  'importTaxValue': idaDetailUpdate['importTax'],
                  'VATCode': idaDetailUpdate['VATTaxCode'],
                  'VATValue': idaDetailUpdate['VATTax'],
                  'specialConsumptionTaxCode': idaDetailUpdate['specialConsumptionTaxCode'],
                  'specialConsumptionTaxValue': idaDetailUpdate['specialConsumptionTax'],
                  'environmentTaxCode': idaDetailUpdate['environmentTaxCode'],
                  'environmentTaxPrice': idaDetailUpdate['environmentTax'],
                }
                hscode = await this.hscodeRepository.createDataTrx(newHSCode, transaction);
              } else {
                hscode['importTaxCode'] = idaDetailUpdate['importTaxCode'],
                  hscode['importTaxValue'] = idaDetailUpdate['importTax'];
                hscode['VATCode'] = idaDetailUpdate['VATTaxCode'],
                  hscode['VATValue'] = idaDetailUpdate['VATTax'];
                hscode['specialConsumptionTaxCode'] = idaDetailUpdate['specialConsumptionTaxCode'],
                  hscode['specialConsumptionTaxValue'] = idaDetailUpdate['specialConsumptionTax'];
                hscode['environmentTaxCode'] = idaDetailUpdate['environmentTaxCode'],
                  hscode['environmentTaxPrice'] = idaDetailUpdate['environmentTax'];
                await hscode.save();
              }
              if (!hscodeDetail) {
                const newHSCodeDetail: any = {
                  'name': idaDetailUpdate['itemName'],
                  'nameVN': idaDetailUpdate['itemNameVN'],
                  'quantityUnitCode1': idaDetailUpdate['priceQuantityUnit'],
                  'priceQuantityUnit': idaDetailUpdate['priceQuantityUnit'],
                  'hsCode': idaDetailUpdate['HSCode'],
                  'importTaxCode': idaDetailUpdate['importTaxCode'],
                  'importTaxValue': idaDetailUpdate['importTax'],
                  'VATCode': idaDetailUpdate['VATTaxCode'],
                  'VATValue': idaDetailUpdate['VATTax'],
                  'specialConsumptionTaxCode': idaDetailUpdate['specialConsumptionTaxCode'],
                  'specialConsumptionTaxValue': idaDetailUpdate['specialConsumptionTax'],
                  'environmentTaxCode': idaDetailUpdate['environmentTaxCode'],
                  'environmentTaxPrice': idaDetailUpdate['environmentTax'],
                }
                await this.hscodeDetailRepository.createDataTrx(newHSCodeDetail, transaction);
              } else {
                hscodeDetail['quantityUnitCode1'] = idaDetailUpdate['priceQuantityUnit'];
                hscodeDetail['priceQuantityUnit'] = idaDetailUpdate['priceQuantityUnit'];
                hscodeDetail['importTaxCode'] = idaDetailUpdate['importTaxCode'];
                hscodeDetail['importTaxValue'] = idaDetailUpdate['importTax'];
                hscodeDetail['VATCode'] = idaDetailUpdate['VATTaxCode'];
                hscodeDetail['VATValue'] = idaDetailUpdate['VATTax'];
                hscodeDetail['specialConsumptionTaxCode'] = idaDetailUpdate['specialConsumptionTaxCode'];
                hscodeDetail['specialConsumptionTaxValue'] = idaDetailUpdate['specialConsumptionTax'];
                hscodeDetail['environmentTaxCode'] = idaDetailUpdate['environmentTaxCode'];
                hscodeDetail['environmentTaxPrice'] = idaDetailUpdate['environmentTax'];
                hscodeDetail.save();
              }
              idaDetailUpdate = Utilities.taxAndCollection(idaDetailUpdate, hscode);
              await this.eMICDetailRepository.createDataTrx(idaDetailUpdate, transaction);
            }));
          }
          await transaction.commit();
          const updateTransaction: IUpdateAction = {
            'HAWB': HAWB,
            'employeeId': employeeId,
            'action': ActionKey.UPDATE_IDA,
            'classify': EConfigure.IDA as string,
            'currentClassify': ida['classify'] as string,
            'data': ida,
            'newData': value,
          }
          importTransaction.push(updateTransaction);
          await this.importTransactionRepository.createBulk(importTransaction);
          const importDetailService = new ImportClearanceDetailService()
          await importDetailService.updateTotalTax([HAWB]);
          await importDetailService.checkUpdateItemNameVN([HAWB]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][updateeMIC]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async updateSortLane(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      let mic = await this.eMICRepository.getOneOptional(optional);
      if (mic) {
        let micObj: eMIC = mic;
        micObj["isSortLane"] = true;
        await micObj.save();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = true;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][updateSortLane]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateImport(HAWB: string, manifestImport: IImportValidate): Promise<any> {
    let transaction: any;
    try {
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.EQUAL, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const detailOptional: Optional = new Optional();
      detailOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
      ]);
      detailOptional.setOrderby([new OrderBy('position', EConfigure.ASCENDING)]);
      const micQuery: any = this.eMICRepository.getOneOptional(optional);
      const detailQuery: any = this.eMICDetailRepository.queryAll(detailOptional);
      const [ida, details] = await Promise.all([micQuery, detailQuery]);
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': {
            'fail': null,
            'succesfull': null,
          }
        }
      }
      if (ida) {
        let idaData: any;
        let importTraction: ICreateAction;
        const dataDetails: any[] = [];
        let detailData: eIImportDetailCreate;
        const cloneManifest: IImportValidate = { ...manifestImport };
        const orderTypeId: number = (!cloneManifest['orderTypeId'] || (cloneManifest['orderTypeId'] && cloneManifest['orderTypeId'] < EConfigure.INDEX_4)) ? EConfigure.INDEX_0 : cloneManifest['orderTypeId'];
        const cloneDetails = cloneManifest['items'];
        const totalCloneDetails: number = cloneDetails.length;
        const HAWB: string = cloneManifest[EConfigure.HAWB_FIELD];
        const HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD];
        let warehouse: IWarehouse = {};
        const valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
        let totalPrice: number = Number(cloneManifest['totalPrice']);
        const priceVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
        const countryCode: string = cloneManifest['consignorCountryCode'];
        const stationId: number = cloneManifest['stationId'];

        if (cloneManifest['warehouseId'] && orderTypeId < EConfigure.INDEX_4) {
          warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
        }
        if (!cloneManifest['warehouseId'] && orderTypeId >= EConfigure.INDEX_4 && cloneManifest['hubId']) {
          warehouse = await Utilities.getWarehouseByHub(cloneManifest['hubId'], orderTypeId);
        }
        const country: ICountry = await Utilities.getCountryByCode(countryCode);
        idaData = {
          'HAWB': HAWB,
          'HAWBClearance': HAWBClearance,
          'cargoNo': HAWBClearance,
          'stationId': stationId,
          'phase': ida['phase'],
          'serviceId': cloneManifest['serviceId'],
          'identity': cloneManifest['identity'],
          'threadCode': cloneManifest['threadCode'],
          'threadName': cloneManifest['threadName'],
          'threadColor': cloneManifest['threadColor'],
          'threadUrl': cloneManifest['threadUrl'],
          'orderTypeId': orderTypeId
        };
        idaData['MAWB'] = cloneManifest['MAWB'];
        idaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
        if (!cloneManifest['importerCode']) {
          idaData['importerCode'] = EConfigure.IMPORTER_CODE as string;
        } else {
          idaData['importerCode'] = cloneManifest['importerCode'];
        }
        if (!cloneManifest['importerName']) {
          idaData['importerName'] = EConfigure.IMPORTER_NAME;
        } else {
          idaData['importerName'] = cloneManifest['importerName'];
          idaData['importerFullName'] = cloneManifest['importerName'];
        }
        idaData['postCode'] = cloneManifest['importerPostCode'];
        idaData['addressOfImporter'] = Utilities.handleRemoveSpaceComma(cloneManifest['importerAddress'] as string);
        idaData['telephoneNumberOfImporter'] = Utilities.subString(cloneManifest['importerTelephoneNumber'], 0, 20);
        idaData['consignorCode'] = cloneManifest['consignorCode'];
        idaData['consignorName'] = cloneManifest['consignorName'];
        const addresses = Utilities.handleAddress(cloneManifest['consignorAddress'] as string);
        if (addresses) {
          for (const [key, value] of Object.entries(addresses)) {
            if (key <= '3') {
              idaData[`address${key}`] = Utilities.removeSpeialCharacter(String(value));
            }
          }
        }
        if (country) {
          idaData['address4'] = country['fullName'];
        }
        idaData['countryCode'] = countryCode;
        idaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT;
        if (cloneManifest['meanOfTransportationCode']) {
          idaData['meansOfTransportationCode'] = cloneManifest['meanOfTransportationCode'];
        }
        idaData['classificationOfIndividualOrganization'] = EConfigure.INDIVIDUAL_ORGANIZATION;
        idaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
        idaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
        idaData['codeOfExtendingDueDateForPayment'] = EConfigure.TYPE_D;
        idaData['clientId'] = cloneManifest['clientId'];
        idaData['termOfPayment'] = EConfigure.KHONGTT;
        idaData['invoicePriceConditionCode'] = EConfigure.DDP;
        if (idaData['clientId'] == EConfigure.INDEX_1 || idaData['clientId'] == EConfigure.INDEX_2) {
          idaData['termOfPayment'] = EConfigure.KC;
          idaData['invoicePriceConditionCode'] = EConfigure.CIF;
        }
        idaData['declarationKindCode'] = EConfigure.H11;
        idaData['arrivalDate'] = cloneManifest['arrivalDate'];
        if (warehouse && Object.keys(warehouse).length > 0) {
          if (orderTypeId >= EConfigure.INDEX_4) {
            idaData['declarationKindCode'] = warehouse['kindleCode'];
            idaData['arrivalDate'] = moment().format(EConfigure.DAY_TIME);
          }
          idaData['customsWarehouseCode'] = warehouse['code'];
          idaData['plannedDeclarantCode'] = warehouse['agencyCode'];
          idaData['unloadingPortCode'] = warehouse['unloadingPortCode'];
          idaData['unloadingPortName'] = warehouse['unloadingPortCode'];
          idaData['customsOffice'] = warehouse['customsOffice'];
          idaData['customsSubSection'] = warehouse['customsSubSection'];
          idaData['feeClearance'] = warehouse['feePrice'];
          idaData['terminalName'] = warehouse['id'];
          idaData['classificationOfIndividualOrganization'] = warehouse['individualOrganization'];
        }
        if (cloneManifest['arrivalDate'] && cloneManifest['flightNo']) {
          idaData['loadingVesselAircraftName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['arrivalDate']);
        } else {
          idaData['loadingVesselAircraftName'] = cloneManifest['flightNo'];
        }
        idaData['loadingLocationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        idaData['loadingLocationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
          idaData['cargoWeightGross'] = cloneManifest['weight'];
        } else {
          idaData['cargoWeightGross'] = Utilities.convertKG(cloneManifest['weight'], cloneManifest['unitOfMass']);
        }
        idaData['weightUnitCodeGross'] = WeightClearance.get('kg');
        idaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
        if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
          totalPrice = +(totalPrice.toFixed(0));
          idaData['totalInvoicePrice'] = totalPrice;
          idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
          idaData['totalOfTaxValue'] = totalPrice;
          idaData['totalOfProportional'] = totalPrice;
        } else {
          idaData['totalInvoicePrice'] = totalPrice;
          idaData['totalOfProportionalDistributionOnTaxValue'] = totalPrice;
          idaData['totalOfTaxValue'] = totalPrice;
          idaData['totalOfProportional'] = totalPrice;
        }
        idaData['valueClearanceVND'] = valueClearanceVND;
        idaData['customerBusinessId'] = cloneManifest['customerBusinessId'];
        idaData['customerPersonalId'] = cloneManifest['customerPersonalId'];
        idaData['notes'] = cloneManifest['note'];
        idaData['classify'] = cloneManifest['classify'];
        idaData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
        idaData['hubId'] = cloneManifest['hubId'];
        idaData['priceVND'] = priceVND;
        idaData['taxPayer'] = EConfigure.TAX_PAYER;
        idaData['invoicePriceKindCode'] = EConfigure.TYPE_A;
        idaData['totalImportTax'] = ida['totalImportTax'];
        idaData['subjectVATCode'] = ida['subjectVATCode'];
        idaData['numberVAT'] = ida['numberVAT'];
        idaData['totalVATTax'] = ida['totalVATTax'];
        idaData['subjectEnvironmentCode'] = ida['subjectEnvironmentCode'];
        idaData['numberEnvironment'] = ida['numberEnvironment'];
        idaData['totalEnvironmentTax'] = ida['totalEnvironmentTax'];
        idaData['subjectSpecialConsumptionCode'] = ida['subjectSpecialConsumptionCode'];
        idaData['numberSpecialConsumption'] = ida['numberSpecialConsumption'];
        idaData['totalSpecialConsumptionTax'] = ida['totalSpecialConsumptionTax'];
        idaData['totalTax'] = ida['totalTax'];
        idaData['termOfPayment'] = ida['termOfPayment'];
        idaData['dateCheckin'] = ida['dateCheckin'];
        idaData['invoiceNo'] = ida['invoiceNo'];
        idaData['invoiceDate'] = cloneManifest['invoiceDate'];
        idaData['orderId'] = cloneManifest['orderId'];
        idaData['originalPrice'] = cloneManifest['originalPrice'];
        idaData['currencyOriginalPrice'] = cloneManifest['currencyOriginalPrice'];
        idaData['internalBoxName'] = cloneManifest['internalBoxName'];
        idaData['externalBoxName'] = cloneManifest['externalBoxName'];
        idaData['childOrderNumber'] = cloneManifest['childOrderNumber'];
        idaData['orderNumber'] = cloneManifest['orderNumber'];
        idaData['labelCustomer'] = cloneManifest['labelCustomer'];
        idaData['invoiceCustomer'] = cloneManifest['invoiceCustomer'];
        importTraction = {
          'HAWB': HAWB,
          'action': ActionKey.RE_CREATE,
          'classify': ClassifyValidateName.get(ClassifyValidateKey.IDA) as string,
          'currentClassify': idaData['classify'],
          'data': idaData,
          'newData': idaData,
          'stationId': stationId
        }
        if (totalCloneDetails > 0) {
          cloneDetails.forEach((detail: eIDetailValidate, index: number) => {
            if (detail['HSCode'] && (+(detail['HSCode'].substring(0, 2)) == 85 || +(detail['HSCode'].substring(0, 2)) == 84)) {
              idaData['otherLawCode'] = EConfigure.MO;
            }
            detailData = {};
            const cloneDetail: eIDetailValidate = { ...detail };
            const totalPrice: number = Number(cloneDetail['invoiceValue']);
            const priceDetailVND: number = +(totalPrice * valueClearanceVND).toFixed(0);
            const currencyCode: string = cloneDetail['currencyCode'];
            detailData[EConfigure.HAWB_FIELD] = HAWB;
            detailData['HSCode'] = cloneDetail['HSCode'];
            detailData['itemNameVN'] = cloneDetail['itemNameVN'];
            if (details[index]) {
              detailData['HSCode'] = details[index]['HSCode'];
              detailData['itemNameVN'] = details[index]['itemNameVN'];
              detailData['quantity2'] = details[index]['quantity2'];
              detailData['quantityUnitCode2'] = details[index]['quantityUnitCode2'];
            }
            detailData['itemName'] = cloneDetail['itemName'];
            if (detail['placeOfOrigin']) {
              detailData['placeOfOrigin'] = detail['placeOfOrigin'];
            } else {
              detailData['placeOfOrigin'] = countryCode;
              if (country) {
                detailData['originalPlaceName'] = country['shortName'];
              }
            }
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
              detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
            } else {
              detailData['invoiceValue'] = totalPrice;
              detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
            }
            detailData['priceVND'] = priceDetailVND;
            detailData['unitPriceCurrencyCode'] = currencyCode;
            if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
              detailData['weightKG'] = cloneManifest['weight'];
            } else {
              detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
            }
            detailData['url'] = cloneDetail['url'];
            detailData['originalUrl'] = cloneDetail['originalUrl'];
            detailData['originalProductName'] = cloneDetail['originalProductName'] ? Utilities.removeSpeialCharacter(cloneDetail['originalProductName']) : null;;
            detailData['productId'] = detail['productId'];
            detailData['quantity1'] = cloneDetail['quantity'];
            detailData['quantity2'] = cloneDetail['quantity'];
            detailData['quantityUnitCode1'] = EConfigure.PIECE;
            detailData['quantityUnitCode2'] = EConfigure.PIECE;
            detailData['priceQuantityUnit'] = EConfigure.PIECE;
            detailData['quantityUnitPrice'] = EConfigure.PIECE;
            detailData['importTaxCode'] = EConfigure.IMPORT_TAX_INCENTIVES;
            detailData['VATTaxCode'] = EConfigure.VAT_TAX_INCENTIVES_VB215;
            detailData['position'] = index;
            dataDetails.push(detailData);
          });
        }
        if (totalCloneDetails < details.length) {
          details.forEach((detail: eImportDetail, index: number) => {
            if (index + 1 > totalCloneDetails) {
              let importDetail: any = detail.toJSON();
              importDetail['position'] = index;
              dataDetails.push(importDetail);
            }
          });
        }
        if (idaData) {
          try {
            let micCreated, idaCreated, detailCreated = null;
            transaction = await this._sequelize.transaction();
            await Promise.all([
              this.eMICRepository.destroyDataTrx(optional, transaction),
              this.eMICDetailRepository.destroyDataTrx(detailOptional, transaction)
            ]);
            [idaCreated, detailCreated] = await Promise.all([
              this.eMICRepository.createDataTrx(idaData, transaction),
              this.eMICDetailRepository.createBulkTrx(dataDetails, transaction),
              this.importTransactionRepository.createDataTrx(importTraction, transaction),
            ]);
            await transaction.commit();
            if (micCreated || idaCreated) {
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.CREATED_DATA;
              reponze['message']['data']['succesfull'] = HAWBClearance;
            }
          } catch (error) {
            await transaction.rollback();
            console.log('---- Recreate imports error: %o', error);
            reponze['message']['data']['fail'] = HAWBClearance
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][recreateImport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async micTaxPercent(data: Record<string, any>[]): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      if (data.length > 0) {
        await Promise.all(data.map(async (item) => {
          const optional: Optional = new Optional();
          optional.setWhere([
            new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, item['HAWB']),
            new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, item['HAWB']),
            new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          ]);
          await this.eMICRepository.updateData({ 'percentTaxPrint': item['percentValue'] }, optional);
        }));
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][micTaxPercent]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async micCountPrint(data: Record<string, any>): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'totalPrint']);
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, data['HAWBs'].join()),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const mics: eMIC[] = await this.eMICRepository.queryAll(optional);
      if (mics.length > 0) {
        await Promise.all(mics.map(async (item: eMIC) => {
          item['totalPrint'] = ++item['totalPrint']!;
          await item.save();
        }));
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][micCountPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async countPrint(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'totalPrint']);
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, "taxCodeNumber", EConfigure.NOT, null),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const ida: eMIC = await this.eMICRepository.getOneOptional(optional);
      if (ida) {
        ida['totalPrint'] = ++ida['totalPrint'];
        await ida.save();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][countPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async idaAssignCargoName(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'success': null,
            'assigned': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'HAWBClearance', 'employeeUpdateCargoName', 'employeeUpdateCargo', 'phase']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      optional.setRelation(['employeeUpdateCargo']);
      const IDAs: eMIC[] = await this.eMICRepository.queryAll(optional);
      if (IDAs.length > 0) {
        const HAWBCreates: string[] = [];
        let transactions: any[] = [];
        IDAs.forEach((ida: eMIC) => {
          const HAWBClearance: string = ida[EConfigure.HAWB_CLEARANCE_FIELD] || '';
          if (ida['phase'] === ActionKey.ASSIGN_UPDATE_CARGO_NAME && ida['employeeUpdateCargoName']) {
            reponze['message']['data']['assigned'].push({
              'HAWB': HAWBClearance,
              'employee': ida['employeeUpdateCargo'],
            });
          } else {
            HAWBCreates.push(ida[EConfigure.HAWB_FIELD]);
            let transactionUpdate: IReturnCargo = {
              'HAWB': HAWBClearance,
              'action': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
              'employeeId': employeeId
            }
            transactions.push(transactionUpdate);
          }
        });
        if (HAWBCreates.length > 0) {
          const updateCargo: any = {
            'phase': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
            'employeeUpdateCargoName': employeeId
          }
          const updateOptional: Optional = new Optional();
          updateOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBCreates.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
          ])
          const [total, _] = await Promise.all([
            this.eMICRepository.updateData(updateCargo, updateOptional),
            this.importTransactionRepository.createBulk(transactions)
          ]);
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          if (total > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
            reponze['message']['data']['success'] = HAWBCreates;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][idaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async returnCargo(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'isHold', 'dateCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'dateCheckout', EConfigure.EQUAL, null),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const IDAs: any = this.eMICRepository.queryAll(optional);
      let now: string = moment().format(EConfigure.FULL_TIME);
      let transactions: any[] = [];
      let updateData: any = {
        'isHold': false,
        'isDeleted': true,
        'phase': ActionKey.RETURN_CARGO_DELIVERY,
        'warehouseCheckout': now,
      }
      if (IDAs.length > 0) {
        await Promise.all(IDAs.map(async (ida: eMIC) => {
          const HAWB: string = ida['HAWB'];
          const optionalUpdate: Optional = new Optional();
          optionalUpdate.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          ]);
          if (ida['isHold']) {
            const transactionHold: IHoldTransaction = {
              'HAWB': HAWB,
              'action': ActionKey.UN_HOLD,
              'holdId': null,
              'employeeId': employeeId,
            }
            transactions.push(transactionHold);
          }
          updateData['warehouseCheckin'] = now;
          if (ida['dateCheckin']) {
            updateData['warehouseCheckin'] = ida['dateCheckin'];
          }
          const transactionReturn: IReturnCargo = {
            'HAWB': HAWB,
            'action': ActionKey.RETURN_CARGO_DELIVERY,
            'employeeId': employeeId,
          }
          transactions.push(transactionReturn);
          await this.eMICRepository.updateData(updateData, optionalUpdate);
          await this.eMICDetailRepository.createBulk(transactions);
        }));
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][returnCargo]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async holdManifest(data: any[], isRemoveMAWB: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL
        }
      }
      const HAWBs: string[] = [];
      await Promise.all(data.map(async (element: any) => {
        const HAWB: string = element[EConfigure.HAWB_FIELD];
        const reasonIds: any = element['reasonIds'] ? String(element['reasonIds']).split(',') : null
        HAWBs.push(HAWB)
        const isHold: boolean = element['isHold'];
        const dataUpdate: any = {
          'isHold': isHold,
          'reasonIds': reasonIds,
        }
        const transactionHold: IHoldTransaction = {
          'HAWB': HAWB,
          'action': isHold ? ActionKey.HOLD : ActionKey.UN_HOLD,
          'holdId': element['reasonIds'],
          'employeeId': employeeId,
        }
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        if (hubs) {
          optional.getWhere().push(
            new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
          )
        }
        await this.eMICRepository.updateData(dataUpdate, optional);
        await this.importTransactionRepository.createData(transactionHold);
        Utilities.updatePartnerStatusHAWB([HAWB], 112);
        this.handlePushWebHook([HAWB], isHold ? EConfigure.HOLD_CLEANCE : EConfigure.UNHOLD_CLEANCE, reasonIds)
        return true;
      }));
      if (isRemoveMAWB) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        let updateClearance: any = {
          "MAWB": null,
          "employeeHandle": employeeId
        }
        await Promise.all([
          Utilities.callTMMApi('manifests/removeMAWB', EConfigure.PUT_METHOD, { HAWBs }),
          this.eMICRepository.updateData(updateClearance, optional),
        ]);
      }
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.SUCCESS;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][holdManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIDE(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMICRepository.queryAll(serviceOptional);
      const IDAQuery: any = this.eMICRepository.queryAll(optional);
      const [IDAs, sameService] = await Promise.all([IDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'IDE';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (IDAs.length > 0) {
        IDAs.forEach((ida: eIMIC, _: number) => {
          foundHAWB[String(ida[EConfigure.HAWB_FIELD])] = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(ida[EConfigure.HAWB_CLEARANCE_FIELD]))
          let cause: any = null;
          if (ida['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
            }
          }
          if (ida['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && ida['isEditProcessing'] === true) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang chờ thông điệp ECUS`
            }
          }
          if (ida['isIIDAed'] === false) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': 'Chưa khai báo thời khai IDA0x'
            }
          }
          if (ida['isHold']) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            }
          }
          if (cause) {
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push(cause);
          }
          const register: IRegister = {
            'HAWB': String(ida[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.IDA as string,
            'currentClassify': String(ida['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.XacNhanChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'typeAction': typeAction,
                'isIIDAed': false,
                'isEditProcessing': true,
                'dateAction': now,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.eMICRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][registerIDE]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIIDA(HAWBs: string[], isPrioritize: any, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMICRepository.queryAll(serviceOptional);
      const IDAQuery: any = this.eMICRepository.queryAll(optional);
      const [IDAs, sameService] = await Promise.all([IDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      let typeAction: string = 'IDA0';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (IDAs.length > 0) {
        IDAs.forEach((ida: eIMIC, _: number) => {
          foundHAWB[String(ida[EConfigure.HAWB_FIELD])] = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(ida[EConfigure.HAWB_CLEARANCE_FIELD]));
          let cause: any = null;
          if (!ida['isIDCed'] || ida['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
            }
          }
          if (ida['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && ida['isEditProcessing'] === true) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang chờ thông điệp ECUS`
            }
          }
          if (ida['times'] === 9) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Sỗ lần gửi cập nhật vượt quá quy định 9 lần`
            }
          }
          if (ida['isHold']) {
            reponze['status'] = false;
            cause = {
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            }
          }
          if (cause) {
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push(cause);
          }
          typeAction = `${typeAction}${(ida['times']) ? Number(ida['times']) + 1 : 1}`;
          const register: IRegister = {
            'HAWB': String(ida[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.IDA as string,
            'currentClassify': String(ida['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.ChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              typeAction = 'IDA0';
              const now: string = moment().format(EConfigure.FULL_TIME);
              await Promise.all(IDAs.map(async (ida: eMIC) => {
                ida['phase'] = action;
                ida['dateAction'] = now;
                ida['typeAction'] = `${typeAction}${(ida['times']) ? Number(ida['times']) + 1 : 1}`;
                ida['isEditProcessing'] = true;
                ida['isIIDAed'] = true;
                await ida.save()
              }));
              await this.importTransactionRepository.createBulk(transactions);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][registerIIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIDC(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMICRepository.queryAll(serviceOptional);
      const IDAQuery: any = this.eMICRepository.queryAll(optional);
      const [IDAs, sameService] = await Promise.all([IDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'IDC';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (IDAs.length > 0) {
        IDAs.forEach((ida: eIMIC, _: number) => {
          foundHAWB[String(ida[EConfigure.HAWB_FIELD])] = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
          if (ida['phase'] === ActionKey.CREATE
            || ida['phase'] === ActionKey.UPDATE_CARGO_NAME
            || ida['phase'] === ActionKey.SUBMIT_INFOMATION
            || (ida['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE && ida['isError'] === false)
            || ida['phase'] === ActionKey.INSPECTION_KIND
            || ida['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
            });
          }
          if (ida['isHold']) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            });
          }
          const register: IRegister = {
            'HAWB': String(ida[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.IDA as string,
            'currentClassify': String(ida['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.ChinhThuc, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isIDCed': true,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.eMICRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][registerIDC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIDA(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMICRepository.queryAll(serviceOptional);
      const IDAQuery: any = this.eMICRepository.queryAll(optional);
      const [IDAs, sameService] = await Promise.all([IDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_INFOMATION;
      const typeAction: string = 'IDA';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (IDAs.length > 0) {
        IDAs.forEach((ida: eIMIC, _: number) => {
          foundHAWB[String(ida[EConfigure.HAWB_FIELD])] = String(ida[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(ida[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (ida['phase'] === ActionKey.CREATE
            || ida['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE
            || ida['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE
            || ida['phase'] === ActionKey.INSPECTION_KIND
            || ida['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(ida['phase_name']['vi']).toLowerCase()}`
            });
          }
          if (ida['isHold']) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            });
          }
          const register: IRegister = {
            'HAWB': String(ida[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.IDA as string,
            'currentClassify': String(ida['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.Tam, HAWBs, isPrioritize);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isError': false,
                'isECUSError': false
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);

              await Promise.all([
                this.eMICRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
                Utilities.updateDeclaration(HAWBs)
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][registerIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerMIE(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMICRepository.queryAll(serviceOptional);
      const MICQuery: any = this.eMICRepository.queryAll(optional);
      const [MICs, sameService] = await Promise.all([MICQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'MIE';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      let transactions: IRegister[] = [];
      if (MICs.length > 0) {
        MICs.forEach((mic: eIMIC, _: number) => {
          foundHAWB[String(mic[EConfigure.HAWB_FIELD])] = String(mic[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(mic[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (mic['phase'] === ActionKey.CREATE
            || mic['phase'] === ActionKey.UPDATE_CARGO_NAME
            || mic['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE
            || (mic['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && mic['isError'] === false)
            || mic['phase'] === ActionKey.ACCEPT_CLEARANCE
          ) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(mic['phase_name']['vi']).toLowerCase()}`
            });
          }
          if (mic['isHold']) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            });
          }
          const register: IRegister = {
            'HAWB': String(mic[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.MIC as string,
            'currentClassify': String(mic['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, true, ClearanceTypeId.ChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.eMICRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][registerMIE]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerMIC(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.eMICRepository.queryAll(serviceOptional);
      const MICQuery: any = this.eMICRepository.queryAll(optional);
      const [MICs, sameService] = await Promise.all([MICQuery, sameServiceQuery]);
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      const typeAction: string = EConfigure.MIC;
      const action: number = ActionKey.SUBMIT_CUSTOM_CLEARANCE;
      let transactions: IRegister[] = [];
      if (MICs.length > 0) {
        MICs.forEach((mic: eIMIC, _: number) => {
          foundHAWB[String(mic[EConfigure.HAWB_FIELD])] = String(mic[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(mic[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (mic['phase'] === ActionKey.CREATE
            || (mic['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE && mic['isError'] === false)
            || mic['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE
            || mic['phase'] === ActionKey.ACCEPT_CLEARANCE
            || mic['phase'] === ActionKey.INSPECTION_KIND
          ) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(mic['phase_name']['vi']).toLowerCase()}`
            });
          }
          if (mic['isHold']) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.WAIT_CUSTOMER_CONFIRM,
            });
          }
          if (mic['priceVND'] && mic['priceVND'] > EConfigure.INDEX_1000000) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': EMessage.PRICE_THAN_1M,
            });
          }
          const register: IRegister = {
            'HAWB': String(mic[EConfigure.HAWB_CLEARANCE_FIELD]),
            'action': action,
            'employeeId': employeeId,
            'classify': EConfigure.MIC as string,
            'currentClassify': String(mic['classify']),
            'typeAction': typeAction,
          }
          transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, true, ClearanceTypeId.ChinhThuc, HAWBs, isPrioritize);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isError': false,
                'isECUSError': false
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.eMICRepository.updateData(update, updateOptional),
                this.importTransactionRepository.createBulk(transactions),
                Utilities.updateDeclaration(HAWBs)
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][registerMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async resetMIC(HAWBs: string[], employeeId: number, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optional.setRelation(['importDetails']);
      const dataUpdate: any = {
        'phase': ActionKey.UPDATE_CARGO_NAME,
        'declarationNo': null,
        'inspectionKindClassification': null,
        'dateClearanced': null
      }
      const IDAs: eMIC[] = await this.eMICRepository.queryAll(optional);
      const [total]: any = await this.eMICRepository.updateData(dataUpdate, optional);
      if (total > 0) {
        let transactions: IReset[] = [];
        if (IDAs.length > 0) {
          transactions = IDAs.map((IDA: eMIC) => {
            const reset: IReset = {
              'HAWB': String(IDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'employeeId': employeeId,
              'action': ActionKey.RESET_IDA,
              'data': IDA,
              'newData': dataUpdate,
              'classify': String(IDA['classify']),
              'currentClassify': String(IDA['classify'])
            }
            return reset;
          });
          await this.importTransactionRepository.createBulk(transactions);
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][resetMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateMaster(manifestMasters: any[]): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      let failHAWBs: string[] = [];
      let successHAWBs: string[] = [];
      transaction = await this._sequelize.transaction();
      await Promise.all(manifestMasters.map(async (manifestMaster: any) => {
        const cloneMaster: IMICMasterValidate = { ...manifestMaster };
        const HAWB: string = manifestMaster[EConfigure.HAWB_FIELD];
        const HAWBClearance: string = manifestMaster[EConfigure.HAWB_CLEARANCE_FIELD];
        const MAWB: any = cloneMaster['MAWB'] ? String(cloneMaster['MAWB']) : null;
        let warehouse: IWarehouse = {};
        const arrivalDate: any = cloneMaster['arrivalDate'] ? String(cloneMaster['arrivalDate']) : null;
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWBClearance),
          new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.IN, `${ActionKey.CREATE}, ${ActionKey.UPDATE_CARGO_NAME}`),
          new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ]);
        let flightCode: any = null;
        if (cloneMaster['arrivalDate']) {
          flightCode = Utilities.flightCode(String(cloneMaster['flightNo']), String(cloneMaster['arrivalDate']));
        }
        if (cloneMaster['warehouseId']) {
          warehouse = await Utilities.getWarehouse(Number(cloneMaster['warehouseId']));
        }
        const hubId: any = cloneMaster['hubId'];
        const idaUpdate: eIMICMaster = {
          'MAWB': MAWB,
          'arrivalDate': arrivalDate,
          'loadingVesselAircraftName': flightCode,
          'hubId': hubId
        }
        if (cloneMaster['meanOfTransportationCode']) {
          idaUpdate['meansOfTransportationCode'] = cloneMaster['meanOfTransportationCode'];
        }
        if (warehouse && Object.keys(warehouse).length > 0) {
          idaUpdate['customsWarehouseCode'] = String(warehouse['code']);
          idaUpdate['plannedDeclarantCode'] = String(warehouse['agencyCode']);
          idaUpdate['unloadingPortCode'] = String(warehouse['unloadingPortCode']);
          idaUpdate['unloadingPortName'] = String(warehouse['unloadingPortCode']);
          idaUpdate['customsOffice'] = String(warehouse['customsOffice']);
          idaUpdate['customsSubSection'] = String(warehouse['customsSubSection']);
          idaUpdate['feeClearance'] = Number(warehouse['feePrice']);
          idaUpdate['terminalName'] = String(warehouse['id']);
          idaUpdate['classificationOfIndividualOrganization'] = Number(warehouse['individualOrganization']);
        }
        const [idatotal] = await this.eMICRepository.updateDataTrx(idaUpdate, optional, transaction);
        if (idatotal > 0) {
          const master: IMaster = {
            'HAWB': HAWBClearance,
            'MAWB': MAWB,
            'action': ActionKey.UPDATE_MASTER,
            'hubId': hubId,
          }
          await this.importTransactionRepository.createDataTrx(master, transaction);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
          successHAWBs.push(HAWBClearance);
        } else {
          reponze['status'] = false;
          failHAWBs.push(HAWBClearance);
        }
      }));
      await transaction.commit();
      reponze['message']['data'] = {
        'succesfull': successHAWBs,
        'fail': failHAWBs
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][updateMaster]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async messageTax(HAWBs: string[], status: boolean, employeeId: number, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const dataUpdate: any = {
        'isMessageTax': status
      }
      const [total] = await this.eMICRepository.updateData(dataUpdate, optional);
      if (total > 0) {
        let transactions = HAWBs.map((HAWB: string) => {
          const messageTax: IMessageTax = {
            'HAWB': HAWB,
            'action': ActionKey.MESSAGE_TAX,
            'isMessageTax': true,
            'employeeId': employeeId,
          }
          return messageTax;
        });
        await this.importTransactionRepository.createBulk(transactions);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][messageTax]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async reportTax(optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(
        new Where(EConfigure.AND, 'taxCodeNumber', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'dateTaxCodeNumber', EConfigure.NOT, null),
      );
      const objData = await this.eMICRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][reportTax]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMAWB(MAWB: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
          'data': {
            'shipment': null,
            'HAWBClearances': [],
            'totalHAWBClearance': 0
          }
        }
      }
      const idaOptional: Optional = new Optional();
      idaOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeightGross', 'totalInvoicePrice', 'invoiceCurrencyCode', 'importerFullName',
        'addressOfImporter', 'consignorName', 'address1', 'address2', 'address3', 'address4'
      ]);
      idaOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      idaOptional.setRelation(['importDetailItems']);
      const shipmentOptional: Optional = new Optional();
      shipmentOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const [shipment, idas] = await Promise.all([
        this.shipmentRepository.getOneOptional(shipmentOptional),
        this.eMICRepository.queryAll(idaOptional)

      ]);
      if (shipment && idas.length > 0) {
        const HAWBClearances: IMAWBReport[] = [];

        if (idas.length > 0) {
          idas.forEach((element: eMIC) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeightGross'],
              'invoicePrice': element['totalInvoicePrice'],
              'invoiceCurrency': element['invoiceCurrencyCode'],
              'consignorFullName': element['importerFullName'],
              'consignorAddress': element['addressOfImporter'],
              'consigneeName': element['consignorName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['importDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        reponze['status'] = true;
        reponze['message'] = {
          'message': EMessage.SUCCESS,
          'data': {
            'shipment': shipment,
            'HAWBClearances': HAWBClearances,
            'totalHAWBClearance': HAWBClearances.length
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][getMAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneeMIC(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.eMICRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][getOneeMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMAWBs(optional: Optional): Promise<any> {
    try {
      const [data, total] = await this.shipmentRepository.getAll(optional);
      if (data.length > 0) {
        await Promise.all(data.map(async (element: any) => {
          const optinalMAWB: Optional = new Optional();
          optinalMAWB.setWhere([
            new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, element['MAWB']),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          ]);
          const emic = await this.eMICRepository.countData(optinalMAWB);
          element.setDataValue('totalHAWB', emic);
        }))
      }
      return [data, total];
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][getMAWBs]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getIdaAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.eMICRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][getIdaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async reportCustomExport(optional: Optional, parseUrl: any, hubIds: string, employeeId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      let excelFields: string = '';
      let MAWB: string = '';
      let hubId: number = 0;
      if (hubIds.split(',').length == 1) {
        hubId = Number(hubIds);
      }
      optional.getWhere().forEach((where: Where, index: number) => {
        if (where.getKey() === 'excelFields') {
          excelFields = String(where.getValue());
          optional.getWhere().splice(index, EConfigure.INDEX_1);
        }
        if (where.getKey() === "MAWB") {
          MAWB = String(where.getValue());
        }
      });
      const createHistory = await this.historyInvoiceService.create({
        "MAWB": MAWB ? MAWB : employeeId,
        "employeeId": employeeId,
        "printType": PrintKey.REPORT_CUSTOM_IMPORT,
        "hubId": hubId,
        "isSucess": false,
        "dataSearch": parseUrl['params'],
        "note": null,
      });

      this.handleExportExcel(optional, createHistory, excelFields);
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.SUCCESS;
      reponze['message']['data'] = createHistory;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][reportCustomExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async reportCustom(optional: Optional): Promise<any> {
    try {
      let responze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
          }
        }
      }
      const optionalInOut: Optional = new Optional();
      optional.getWhere().forEach((where: Where) => {
        if ((where.getKey() !== 'dateCheckin' || where.getKey() !== 'dateCheckout') && (where.getValue() !== 'null')) {
          optionalInOut.getWhere().push(where);
        }
      });
      const idaWeigthOptional: Optional = new Optional();
      idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
      idaWeigthOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
      ]);
      const idaTotalOtptional: Optional = new Optional();
      idaTotalOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
      ]);
      const idaHoldOtptional: Optional = new Optional();
      idaHoldOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
      ]);
      const idaCOMOtptional: Optional = new Optional();
      idaCOMOtptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
      ]);
      const idaCheckInOtptional: Optional = new Optional();
      idaCheckInOtptional.setWhere([
        ...optionalInOut.getWhere(),
      ]);
      const idaCheckOutOtptional: Optional = new Optional();
      idaCheckOutOtptional.setWhere([
        ...optionalInOut.getWhere(),
      ]);
      const [idaWeight, idaTotal, idaHoldTotal, COMTotal, idaCheckin, idaCheckout] = await Promise.all([
        this.eMICRepository.queryOneRaw(idaWeigthOptional),
        this.eMICRepository.countData(idaTotalOtptional),
        this.eMICRepository.countData(idaHoldOtptional),
        this.eMICRepository.countData(idaCOMOtptional),
        this.eMICRepository.countData(idaCheckInOtptional),
        this.eMICRepository.countData(idaCheckOutOtptional),
      ]);
      let totalWeight: number = 0, totalHold: number = 0;
      if (idaTotal > 0) {
        responze['status'] = true;
        responze['message']['message'] = EMessage.FOUND;
      }
      totalWeight += Number(idaWeight['totalIDAWeigth']);
      totalHold += idaHoldTotal;
      responze['message']['data'] = {
        "totalWeight": +(totalWeight.toFixed(2)),
        "totalIDA": idaTotal,
        "totalHold": totalHold,
        "totalCOM": COMTotal,
        "totalHAWB": idaTotal,
        "totalCheckin": idaCheckin,
        "totalCheckout": idaCheckout,
      }
      return responze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][reportCustom]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getStoreWarehouse(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      )
      const IDAs = await this.eMICRepository.queryAll(optional);
      if (IDAs.length > 0) {
        let idaData: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: eMIC) => {
            const ida: IStoreWarehouse = {
              'HAWBClearance': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'HAWB': IDA[EConfigure.HAWB_FIELD],
              'MAWB': IDA['MAWB'],
              'warehouseAddress': IDA['warehouseAddress'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
            }
            return ida;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': idaData,
          'total': idaData.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getStoreWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async externalBoxes(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const masterExternalOptional: Optional = new Optional();
      masterExternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterExternalOptional.setAttributes(['externalBoxName']);
      masterExternalOptional.setGroup(['externalBoxName']);

      const masterInternalOptional: Optional = new Optional();
      masterInternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterInternalOptional.setAttributes(['internalBoxName']);
      masterInternalOptional.setGroup(['internalBoxName']);
      const IDAExternalQuery = this.eMICRepository.queryAll(masterExternalOptional);
      const IDAInternalQuery = this.eMICRepository.queryAll(masterInternalOptional);
      const [IDAExs, IDAIns] = await Promise.all([IDAExternalQuery, IDAInternalQuery]);
      if (IDAExs.length > 0 || IDAIns.length > 0) {
        let externalBoxes: string[] = [];
        externalBoxes = IDAExs;
        if (externalBoxes.length > 0) {
          externalBoxes = [...new Set(externalBoxes.map((item: any) => item['externalBoxName']))];
        }
        let internalBoxes: string[] = [];
        internalBoxes = IDAIns;
        if (externalBoxes.length > 0) {
          internalBoxes = [...new Set(internalBoxes.map((item: any) => item['internalBoxName']))];
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { externalBoxes, internalBoxes };
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][externalBoxes]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getHoldManifest(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.setRelation(['country', 'importDetailItems', 'holds']);
      const IDAs = await this.eMICRepository.queryAll(optional);
      if (IDAs.length > 0) {
        let idaData: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: eIMIC) => {
            const ida: IHold = {
              "HAWBClearance": IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": IDA[EConfigure.HAWB_FIELD],
              "declarationNo": IDA['declarationNo'],
              "MAWB": IDA['MAWB'],
              "invoiceValue": IDA['totalInvoicePrice'],
              "invoiceCurrency": IDA['invoiceCurrencyCode'],
              "priceVND": IDA['priceVND'],
              "country": IDA['country'],
              "cargoPiece": IDA['cargoPiece'],
              "weight": IDA['cargoWeightGross'],
              "massOfWeight": IDA['weightUnitCodeGross'],
              "inspectionKindClassification": IDA['inspectionKindClassification'],
              "phase": IDA['phase_name'],
              "classify": IDA['classify'],
              "type": String(EConfigure.IDA),
              "details": IDA['importDetailItems'],
              "reasonDetails": IDA['holds']
            }
            return ida;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': idaData,
          'total': idaData.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][getHoldManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async importReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      optional.setRelation(['holds', 'manifest', 'importDetailItems', 'station', 'customerBusiness', 'customerPersonal', 'order']);
      const IDAs = await this.eMICRepository.queryAll(optional);
      if (IDAs.length > 0) {
        let idaData: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: eMIC) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: eImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: eImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }
            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': IDA['isHold'] ? EConfigure.HOLD : (IDA['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.H11 : EConfigure.COM),
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'priceVND': IDA['priceVND'],
              'totalTax': IDA['totalTax'],
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H11,
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? EConfigure.HOLD : IDA['phase_name']['vi'],
              'cdsNoCustomer': null,
              'currency': IDA['invoiceCurrencyCode'],
              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'order': IDA['order'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
              'details': details,
            }
            return ida;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = idaData;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][importReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getHoldReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      const optionalHoldQuery: Optional = new Optional();
      optionalHoldQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      optionalHoldQuery.setRelation(['holds', 'manifest', 'importDetailItems', 'station', 'customerBusiness', 'customerPersonal']);
      const optionalCOMQuery: Optional = new Optional();
      optionalCOMQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      const IDAHoldQuery = this.eMICRepository.queryAll(optionalHoldQuery);
      const IDACOMQuery = this.eMICRepository.queryAll(optionalCOMQuery);
      const [holdIDAs, comIDAs] = await Promise.all([IDAHoldQuery, IDACOMQuery]);
      if (holdIDAs.length > 0 || comIDAs.length > 0) {
        let manifests: any[] = [];
        if (comIDAs.length > 0) {
          comIDAs.forEach((IDA: eMIC) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: eImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: eImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }
            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'cdsNoCustomer': IDA['declarationNoCustomer'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': EConfigure.COM,
              'customsStatus': null,
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'totalTax': IDA['totalTax'],
              'priceVND': IDA['priceVND'],
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': 'Khác',
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': IDA['phase_name']['vi'],
              'currency': IDA['invoiceCurrencyCode'],
              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        if (holdIDAs.length > 0) {
          holdIDAs.forEach((IDA: eMIC) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: eImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: eImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }
            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': EConfigure.HOLD,
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'totalTax': IDA['totalTax'],
              'priceVND': IDA['priceVND'],
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H11,
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': EConfigure.HOLD,
              'cdsNoCustomer': null,
              'currency': IDA['invoiceCurrencyCode'],
              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][getHoldReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportExport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ])
      optionalQuery.setRelation(['holds', 'manifest']);
      const IDAs = await this.eMICRepository.queryAll(optionalQuery);
      if (IDAs.length > 0) {
        let manifests: any[] = [];
        IDAs.forEach((IDA: eMIC) => {
          const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
          const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
          const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
          const ida: IExportReport = {
            'trackingNo': String(IDA[EConfigure.HAWB_CLEARANCE_FIELD]),
            'MAWB': String(IDA['MAWB']),
            'origin': String(IDA['countryCode']),
            'country': String(IDA['countryCode']),
            'pcs': Number(IDA['cargoPiece']),
            'trueWeight': Number(IDA['cargoWeightGross']),
            'volWeight': IDA['manifest'] ? IDA['manifest']['weightChargeKG'] : null,
            'manisfestImported': String(IDA['createdAt']),
            'arDate': String(IDA['dateCheckin']),
            'cdsDate': String(IDA['declarationPlannedDate']),
            'crDate': String(IDA['dateCheckout']),
            'cdsNo': String(IDA['declarationNo']),
            'importType': (IDA['classify'] === ClassifyName.get(ClassifyKey.PAR)) ? String(EConfigure.H11) : 'MẬU DỊCH',
            'lane': Number(IDA['inspectionKindClassification']),
            'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : IDA['phase_name']['vi'],
            'reasonDate': null,
            'handover': null,
            'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
            'importTax': importTax,
            'VAT': VATTax,
            'duty': fee,
            'dutyTaxVND': importTax + VATTax + fee,
            'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
            'notes': String(EConfigure._H11),
            'consignorName': String(IDA['consignorName']),
            'consignorAddress': String(IDA['address1']),
            'consignorCityName': null,
            'consignorContactName': String(IDA['consignorName']),
            'consignorTelephone': null,
            'consigneeName': String(IDA['importerFullName']),
            'consigneeAddress': String(IDA['addressOfImporter']),
            'consigneeAddress2': null,
            'consigneeCityName': null,
            'consigneeContactName': String(IDA['importerFullName']),
            'consigneeTelephone': String(IDA['telephoneNumberOfImporter']),
            'remarks': null,
            'taxNumberCode': IDA['taxCodeNumber'],
            'taxNumberDate': IDA['dateTaxCodeNumber'],
            'externalBoxName': IDA['externalBoxName'],
          }
          manifests.push(ida);
        });
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][clearanceReportExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportCount(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'total': 0,
            'totalCheckin': 0,
            'totalCheckout': 0,
            'totalWarehouse': 0
          }
        }
      }
      const optionalCheckin: Optional = new Optional();
      optionalCheckin.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'dateCheckin', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalCheckout: Optional = new Optional();
      optionalCheckout.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'dateCheckout', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      const IDATotalCheckinQuery = this.eMICRepository.countData(optionalCheckin);
      const IDATotalCheckoutQuery = this.eMICRepository.countData(optionalCheckout);
      const IDATotalQuery = this.eMICRepository.countData(optionalTotal);

      const [IDATotal, IDATotalCheckin, IDATotalCheckout] =
        await Promise.all([IDATotalQuery, IDATotalCheckinQuery, IDATotalCheckoutQuery]);
      const totalCheckin: number = Number(IDATotalCheckin);
      const totalCheckout: number = Number(IDATotalCheckout);
      const totalWarehouse: number = totalCheckin - totalCheckout
      const total: number = Number(IDATotal);
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.FOUND;
      reponze['message']['data'] = {
        'totalCheckin': totalCheckin,
        'totalCheckout': totalCheckout,
        'totalWarehouse': (totalWarehouse > 0) ? totalWarehouse : 0,
        'total': total,
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][clearanceReportCount]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
          }
        }
      }
      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      const IDAs = await this.eMICRepository.queryAll(optionalTotal);
      if (IDAs.length > 0) {
        let idaData: ICheckout[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: eIMIC) => {
            const ida: ICheckout = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': IDA['MAWB'],
              'declarationNo': IDA['declarationNo'],
              'clearanceInspection': IDA['inspectionKindClassification'],
              'dateCheckout': IDA['dateCheckout'],
              'dateCheckin': IDA['dateCheckin'],
              'dateClearanced': IDA['dateClearanced'],
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'classify': IDA['classify'],
              'type': String(EConfigure.IDA),
              'externalBoxName': IDA['externalBoxName'],
            }
            return ida;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': idaData.length,
          'manifests': idaData,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][clearanceReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async exportManagementGate(optional: Optional, employeeId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      let queryAll: number = 0;
      let title: string = '';
      let IDAs: eMIC[] = [];
      let classify: any = null;
      let startCheckout: any = null;
      let endCheckout: any = null;
      let removeIndex: number[] = [];
      let printType: number = 0;
      let contentTitle: string = '';
      optional.getWhere().forEach((where: Where, index: number) => {
        if (where.getKey() === 'type') {
          queryAll = Number(where.getValue());
          removeIndex.push(index);
        }
        if (where.getKey() === 'statusName') {
          title = where.getValue() ? String(where.getValue()) : '';
          if (title !== 'Tất cả') {
            title = `DANH SÁCH ${title.toUpperCase()}`;
          } else {
            title = `DANH SÁCH MANIFEST`;
          }
          removeIndex.push(index);
        }
        if (where.getKey() === 'startCheckout') {
          startCheckout = where.getValue();
          removeIndex.push(index);
        }
        if (where.getKey() === 'endCheckout') {
          endCheckout = where.getValue();
          removeIndex.push(index);
        }
        if (where.getKey() === 'printType') {
          printType = where.getValue();
          removeIndex.push(index);
        }
      });
      const query: Where[] = optional.getWhere().filter((value, index) => {
        return removeIndex.indexOf(index) == -1;
      });
      const idaOptional: Optional = new Optional();
      idaOptional.setAttributes([`${EConfigure.HAWB_CLEARANCE_FIELD}`, "dateCheckin", "dateClearanced", "MAWB", "declarationNo", "cargoPiece", "cargoWeightGross", "classify", "dateCheckout", "hubId", "inspectionKindClassification"]);
      idaOptional.setWhere(query);
      idaOptional.setRelation(['client']);
      if (queryAll === 0) {
        contentTitle = `${title.toLocaleLowerCase()} tất cả`;

        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        );
        IDAs = await this.eMICRepository.queryAll(idaOptional);
      }
      if (queryAll === 1) {
        contentTitle = `${title.toLocaleLowerCase()} ${EConfigure.MIC}`;
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        IDAs = await this.eMICRepository.queryAll(idaOptional);
      }
      if (queryAll === 4) {
        contentTitle = `${title.toLocaleLowerCase()} ${String(EConfigure.COM).toLocaleLowerCase()}`;
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        IDAs = await this.eMICRepository.queryAll(idaOptional);
      }
      if (queryAll === 5) {
        contentTitle = `${title.toLocaleLowerCase()} ${String(EConfigure.HOLD).toLocaleLowerCase()}`;
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        );
        IDAs = await this.eMICRepository.queryAll(idaOptional);
      }
      if (IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        let hubId: number = 0;
        if (IDAs.length > 0) {
          hubId = IDAs[0]['hubId'] ? IDAs[0]['hubId'] : 0;
          let classify: string = "";
          micData = IDAs.map((IDA: eIMIC) => {
            if (IDA['isHold'] == true) {
              classify = 'HOLD';
            } else {
              if (IDA['classify'] == "PAR") {
                classify = "MIC";
              } else {
                classify = "DOC";
              }
            }
            const ida: IManagement = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'declarationNo': IDA['declarationNo'],
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'weightRound': Number(IDA['cargoWeightGross']),
              'MAWB': IDA['MAWB'],
              'classify': classify,
              'dateCheckout': IDA['dateCheckout'] ? moment(IDA['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateGate': IDA['dateCheckout'] ? moment(IDA['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateCheckin': IDA['dateCheckin'] ? moment(IDA['dateCheckin']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateClearanced': IDA['dateClearanced'] ? moment(IDA['dateClearanced']).format("DD-MM-YYYY HH:mm:ss") : '',
              'clearanceInspection': Number(IDA['inspectionKindClassification']),
              'clientName': IDA['client'] ? IDA['client']['code'] : '',
            }
            return ida;
          });
        }
        manifests = [...micData, ...idaData];
        if (manifests.length > 0 && (printType == PrintKey.MANAGEMENT_PDF || printType == PrintKey.MANAGEMENT_EXCEL)) {
          const createHistory = await this.historyMonitorRepository.createData({
            title: `${contentTitle}_${moment().format("YYMMDD_HHmmssSS")}`,
            employeeId: employeeId,
            printType: printType,
            hubId: hubId,
            isSucess: false,
          });
          if (printType == PrintKey.MANAGEMENT_PDF) {
            this.exportPDF(manifests, createHistory, classify, startCheckout, endCheckout, title);
          } else {
            this.exportExcel(manifests, createHistory);
          }
          reponze['message']['data'] = createHistory;
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][exportManagementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async totalManagementGate(optional: Optional): Promise<any> {
    try {
      let responze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      let queryAll: number = 0;
      optional.getWhere().forEach((where: Where, index: number) => {
        if (where.getKey() === 'type') {
          queryAll = Number(where.getValue());
          optional.getWhere().splice(index, EConfigure.INDEX_1);
        }
      });
      let totalWeight: number = 0, totalMIC = 0, totalIDA = 0, totalHold = 0, totalDOC = 0, totalCOM = 0, totalHAWB = 0;
      if (queryAll == 1 || queryAll == 0) {
        const micWeigthOptional: Optional = new Optional();
        micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
        micWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);
        const micTotalOtptional: Optional = new Optional();
        micTotalOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);
        const micHoldOtptional: Optional = new Optional();
        micHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);
        const micDOCOtptional: Optional = new Optional();
        micDOCOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);
        const [micWeight, micTotal, micHoldTotal, DOCTotal] = await Promise.all([
          this.eMICRepository.queryOneRaw(micWeigthOptional),
          this.eMICRepository.countData(micTotalOtptional),
          this.eMICRepository.countData(micHoldOtptional),
          this.eMICRepository.countData(micDOCOtptional)
        ]);
        if (micTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(micWeight['totalMICWeigth']);
        totalMIC = micTotal;
        totalHold += micHoldTotal;
        totalDOC += DOCTotal;
        totalHAWB += micTotal;
      }
      if (queryAll == 2 || queryAll == 0) {
        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);
        const idaTotalOtptional: Optional = new Optional();
        idaTotalOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);
        const idaHoldOtptional: Optional = new Optional();
        idaHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);
        const idaCOMOtptional: Optional = new Optional();
        idaCOMOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);
        const [idaWeight, idaTotal, idaHoldTotal, COMTotal] = await Promise.all([
          this.eMICRepository.queryOneRaw(idaWeigthOptional),
          this.eMICRepository.countData(idaTotalOtptional),
          this.eMICRepository.countData(idaHoldOtptional),
          this.eMICRepository.countData(idaCOMOtptional)
        ]);
        if (idaTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(idaWeight['totalIDAWeigth']);
        totalIDA = idaTotal;
        totalHold += idaHoldTotal;
        totalCOM += COMTotal;
        totalHAWB += idaTotal;
      }
      if (queryAll == 3) {
        const micWeigthOptional: Optional = new Optional();
        micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
        micWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
        ]);
        const micDOCOtptional: Optional = new Optional();
        micDOCOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);
        const [micWeight, DOCTotal] = await Promise.all([
          this.eMICRepository.queryOneRaw(micWeigthOptional),
          this.eMICRepository.countData(micDOCOtptional)
        ]);
        if (DOCTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(micWeight['totalMICWeigth']);
        totalDOC += DOCTotal;
      }
      if (queryAll == 4) {
        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
        ]);
        const idaCOMOtptional: Optional = new Optional();
        idaCOMOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);
        const [idaWeight, COMTotal] = await Promise.all([
          this.eMICRepository.queryOneRaw(idaWeigthOptional),
          this.eMICRepository.countData(idaCOMOtptional)
        ]);
        if (COMTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(idaWeight['totalIDAWeigth']);
        totalCOM += COMTotal;
      }
      if (queryAll == 5) {
        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);
        const idaHoldOtptional: Optional = new Optional();
        idaHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);
        const [idaWeight, idaHoldTotal] = await Promise.all([
          this.eMICRepository.queryOneRaw(idaWeigthOptional),
          this.eMICRepository.countData(idaHoldOtptional),
        ]);
        if (idaHoldTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(idaWeight['totalIDAWeigth']);
        totalHold += idaHoldTotal;
      }
      responze['message']['data'] = {
        totalWeight: +(totalWeight.toFixed(2)),
        totalMIC,
        totalIDA,
        totalHold,
        totalDOC,
        totalCOM,
        totalHAWB
      }
      return responze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][managementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async managementGate(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      let queryAll: number = 0;
      optional.setRelation(['customerBusiness', 'customerPersonal', 'importDetails', 'holds']);
      let IDAs: eMIC[] = [];
      if (queryAll === 2) {
        IDAs = await this.eMICRepository.getAll(optional);
      }
      if (IDAs.length > 0) {
        let idaData: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: eIMIC) => {
            const ida: IManagement = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': IDA['MAWB'],
              'declarationNo': IDA['declarationNo'],
              'clearanceInspection': Number(IDA['inspectionKindClassification']),
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'dateCheckout': IDA['dateCheckout'],
              'dateCheckin': IDA['dateCheckin'],
              'dateClearanced': IDA['dateClearanced'],
              'bussiness': IDA['customerBusiness'],
              'personal': IDA['customerPersonal'],
              'declarationType': ClassifyValidateName.get(ClassifyValidateKey.MIC),
              'classify': IDA['classify'],
              'importDetails': IDA['importDetails'],
              'isHold': IDA['isHold'],
              'reasonHold': IDA['holds']
            }
            return ida;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': idaData,
          'total': idaData.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][managementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async master(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const masterOptional: Optional = new Optional();
      masterOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'MAWB', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterOptional.setAttributes(['MAWB']);
      masterOptional.setGroup(['MAWB']);
      const IDAs = await this.eMICRepository.queryAll(masterOptional);
      let masters: string[] = [];
      if (IDAs.length > 0) {
        masters = [...IDAs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][master]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAll(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': [{
            'manifests': [],
            'total': 0,
          }]
        }
      }
      optional.setRelation(['country', 'importDetailItems', 'holds']);
      const [IDAs, totalIDA] = await this.eMICRepository.getAll(optional);
      if (IDAs.length > 0) {
        let idaData: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: eIMIC) => {
            const ida: IGetAll = {
              "HAWBClearance": IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": IDA[EConfigure.HAWB_FIELD],
              "declarationNo": IDA['declarationNo'],
              "MAWB": IDA['MAWB'],
              "invoiceValue": IDA['totalInvoicePrice'],
              "invoiceCurrency": IDA['invoiceCurrencyCode'],
              "priceVND": IDA['priceVND'],
              "country": IDA['country'],
              "cargoPiece": IDA['cargoPiece'],
              "weight": IDA['cargoWeightGross'],
              "massOfWeight": IDA['weightUnitCodeGross'],
              "inspectionKindClassification": IDA['inspectionKindClassification'],
              "phase": IDA['phase_name'],
              "classify": IDA['classify'],
              "type": String(EConfigure.IDA),
              "details": IDA['importDetailItems'],
              "reasonDetails": IDA['holds'],
              "inspectionKindTimes": IDA['inspectionKindTimes'],
              "clearanceDeclarationTimes": IDA['clearanceDeclarationTimes'],
              "isHold": IDA['isHold'],
              "externalBoxName": IDA['externalBoxName'],
              "internalBoxName": IDA['internalBoxName'],
              "warehouseAddress": IDA['warehouseAddress'],
            }
            return ida;
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': idaData,
          'total': totalIDA
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][getAll]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async handlePushWebHook(HAWBs: string[], action: number, reasonIds?: any): Promise<any> {
    try {
      if (HAWBs.length > 0) {
        const optional: Optional = new Optional();
        const optionalHold: Optional = new Optional();
        let hold: any = null;
        optional.setAttributes(['orderId', 'clientId'])
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);
        optional.setGroup(['orderId', 'clientId']);
        if (reasonIds) {
          optionalHold.setAttributes(['id', 'name']);
          optionalHold.setWhere([
            new Where(EConfigure.AND, 'id', EConfigure.IN, reasonIds.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
          ]);
          hold = await this.holdRepository.getOneOptional(optionalHold);
        }
        const idas = await this.eMICRepository.queryAll(optional);
        if (idas.length > 0) {
          const orders: Map<number, string[]> = new Map();
          const orderLazadas: Set<string> = new Set();
          if (idas.length > 0) {
            idas.forEach((ida: eMIC) => {
              const clientId: number = Number(ida[EConfigure.CLIENT_ID_FIELD]);
              const orderId: string = ida[EConfigure.ORDER_ID_FIELD];
              orderLazadas.add(orderId);
              if (orders.has(clientId)) {
                const orderIds: string[] = orders.get(clientId) || [];
                orderIds.push(orderId);
                orders.set(clientId, orderIds)
              } else {
                orders.set(clientId, [orderId])
              }
            });
          }
          let holdDetail: any = null;
          if (hold) {
            holdDetail = {
              "code": hold['id'],
              "name": hold['name']
            }
          }
          if (orders.size > 0) {
            for (const [key, value] of orders) {
              const data: IWebHookRequest = {
                'action': action,
                'clientId': key,
                'orderIds': [...new Set(value)],
                'reasonDetailId': null,
                'holdClearance': holdDetail,
                'stationOriginId': null,
                'stationDestinationId': null
              }
              await new RabbitSendInternal().send(EConfigure.PUSH_WEBHOOK_PARTNER, [data], { durable: true, autoDelete: false });
            }
          }
          if (orderLazadas.size > 0) {
            for (let item of orderLazadas) {
              const data: any = {
                'orderId': item,
                'isClearance': true,
                'statusId': EConfigure.HOLD_CLEANCE,
                'now': moment().format(EConfigure.FULL_TIME),
                'hold': holdDetail,
              }
              await new RabbitSendInternal().send(EConfigure.UPDATE_LASTEST_MANIFEST_STATUS, [data], { durable: true, autoDelete: false });
            }
          }
        }
      }
    } catch (error) {
      Utilities.sendDiscordErr('[service][eimport][handlePushWebHook]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async exportExcel(manifests: any, createHistory: any): Promise<void> {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("sheet 1", { properties: { tabColor: { argb: 'FFC0000' } } });
      worksheet.columns = [
        { header: "Mã vận đơn", key: "HAWB", width: 18 },
        { header: "Số tờ khai", key: "declarationNo", width: 16 },
        { header: "MAWB", key: "MAWB", width: 16 },
        { header: "Loại hình", key: "classify", width: 10 },
        { header: "Kiện", key: "cargoPiece", width: 5 },
        { header: "Trọng lượng", key: "weightRound", width: 15 },
        { header: "Ra cổng", key: "dateGate", width: 20 },
        { header: "Thông quan", key: "dateClearanced", width: 20 },
        { header: "VNS luồng", key: "clearanceInspection", width: 14 },
        { header: "Checkin", key: "dateCheckin", width: 20 },
        { header: "Checkout", key: "dateCheckout", width: 20 },
        { header: "Khách hàng", key: "clientName", width: 14 },
        { header: "Lý do", key: "note", width: 10 },
      ];
      worksheet.getRow(1).eachCell((cell: any) => {
        cell.font = { bold: true, name: 'Calibri' };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '96C8FB' },
          bgColor: { argb: '96C8FB' },
        };
        cell.alignment = { vertical: 'middle', horizontal: 'center' },
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
      });
      worksheet.addRows(manifests);
      let filename = `DS_MANIFEST_RA_CONG_${moment().format(EConfigure.DAY_TIME)}_${moment().format("X")}.xlsx`;
      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}/${filename}`)
      createHistory.isSucess = true;
      createHistory.successDate = moment().format(EConfigure.FULL_TIME);
      createHistory.link = `${new Config().clearanceDomain()}/clearance/invoice/${filename}`;
      createHistory.save();
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][exportManagementGate][exportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async exportPDF(manifests: any, createHistory: any, classify: any, startCheckout: any, endCheckout: any, title: any): Promise<void> {
    try {
      const config: pdf.CreateOptions = {
        "format": "A4",
        "orientation": "portrait",
        "border": {
          "top": "8px",
          "right": "10px",
          "bottom": "20px",
          "left": "10px"
        },
        timeout: 300000
      }
      const now: string = moment().format('DD-MM-YYYY');
      let date: string = '';
      let filename = `bangkeracong${classify ? '_' + classify : ''}_${moment().format("X")}.pdf`;
      let pathTemplate = `../../../../../template/bangke/bangkeracong.ejs`;
      if (startCheckout && startCheckout) {
        if (moment(startCheckout).isSame(endCheckout, 'date')) {
          date = `Ngày ${moment(startCheckout).format('DD-MM-YYYY')}`;
        } else {
          date = `Từ ngày ${moment(startCheckout).format('DD-MM-YYYY')} đến ngày ${moment(endCheckout).format('DD-MM-YYYY')}`;
        }
      }
      config['footer'] = {
        "height": "10mm",
        "contents": {
          default: `<table width="100%;" style="font-size: 8px;">
            <tr>
              <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
              <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
            </tr>
          </table>`,
        }
      }
      let template = await ejs.renderFile(path.join(__dirname, pathTemplate), { manifests, title, date }, { async: true });
      pdf.create(template, config).toFile(`${EConfigure.FOLDER_INVOICE}/${filename}`, function (err, res) {
        if (res) {
          createHistory.isSucess = true;
          createHistory.successDate = moment().format(EConfigure.FULL_TIME);
          createHistory.link = `${new Config().clearanceDomain()}/clearance/invoice/${filename}`;
          createHistory.save();
        }
        if (err) {
          Utilities.sendDiscordErr('[service][PdfGenerate][create][pdfPrint]', err.toString());
        }
      });
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][exportManagementGate][exportPDF]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async handleExportExcel(optional: Optional, createHistory: HistoryInvoice, excelFields: string): Promise<void> {
    try {
      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ])
      optionalQuery.setRelation(['holds', 'manifest']);
      const IDAs = await this.eMICRepository.queryAll(optionalQuery);
      if (IDAs.length > 0) {
        let manifests: any[] = [];
        if (IDAs.length > 0) {
          IDAs.forEach((IDA: eIMIC) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            const ida: IExportReport = {
              'trackingNo': String(IDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'MAWB': String(IDA['MAWB']),
              'origin': String(IDA['countryCode']),
              'country': String(IDA['countryCode']),
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': IDA['manifest'] ? IDA['manifest']['weightChargeKG'] : null,
              'manisfestImported': String(IDA['createdAt']),
              'arDate': String(IDA['dateCheckin']),
              'cdsDate': String(IDA['declarationPlannedDate']),
              'crDate': String(IDA['dateCheckout']),
              'cdsNo': String(IDA['declarationNo']),
              'importType': (IDA['classify'] === ClassifyName.get(ClassifyKey.PAR)) ? String(EConfigure.H11) : 'MẬU DỊCH',
              'lane': Number(IDA['inspectionKindClassification']),
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : IDA['phase_name']['vi'],
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'duty': fee,
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': String(EConfigure._H11),
              'consignorName': String(IDA['consignorName']),
              'consignorAddress': String(IDA['address1']),
              'consignorCityName': null,
              'consignorContactName': String(IDA['consignorName']),
              'consignorTelephone': null,
              'consigneeName': String(IDA['importerFullName']),
              'consigneeAddress': String(IDA['addressOfImporter']),
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': String(IDA['importerFullName']),
              'consigneeTelephone': String(IDA['telephoneNumberOfImporter']),
              'remarks': null,
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        manifests.forEach((manifest: any, index: number) => {
          manifest['no'] = index + EConfigure.INDEX_1;
        });
        const mapFields: any = {
          'trackingNo': { header: "Tracking no.", key: "trackingNo", width: 18 },
          'MAWB': { header: "MAWB", key: "MAWB", width: 18 },
          'origin': { header: "Origin", key: "origin", width: 18 },
          'country': { header: "Country", key: "country", width: 18 },
          'pcs': { header: "Pcs", key: "pcs", width: 18 },
          'trueWeight': { header: "True weight", key: "trueWeight", width: 18 },
          'volWeight': { header: "Vol weight", key: "volWeight", width: 18 },
          'manisfestImported': { header: "Manisfest imported", key: "manisfestImported", width: 18 },
          'arDate': { header: "AR date", key: "arDate", width: 18 },
          'cdsDate': { header: "CDS date", key: "cdsDate", width: 18 },
          'crDate': { header: "CR date", key: "crDate", width: 18 },
          'cdsNo': { header: "CDS no.", key: "cdsNo", width: 18 },
          'importType': { header: "Import type", key: "importType", width: 18 },
          'lane': { header: "Lane", key: "lane", width: 18 },
          'customsStatus': { header: "Customs status", key: "customsStatus", width: 18 },
          'reasonDate': { header: "Reason date", key: "reasonDate", width: 18 },
          'handover': { header: "Handover", key: "handover", width: 18 },
          'shipmemntValueUSD': { header: "Shipmemnt value (USD)", key: "shipmemntValueUSDshipmemntValueUSD", width: 18 },
          'importTax': { header: "Import tax", key: "importTax", width: 18 },
          'VAT': { header: "VAT", key: "VAT", width: 18 },
          'duty': { header: "Duty", key: "duty", width: 18 },
          'dutyTaxVND': { header: "Duty & tax( VND)", key: "dutyTaxVND", width: 18 },
          'otherFee': { header: "Other fee (if any)", key: "otherFee", width: 18 },
          'notes': { header: "Notes", key: "notes", width: 18 },
          'consignorName': { header: "Consignor_NM", key: "consignorName", width: 18 },
          'consignorAddress': { header: "Consignor_address", key: "consignorAddress", width: 18 },
          'consignorCityName': { header: "Consignor_city_NM", key: "consignorCityName", width: 18 },
          'consignorContactName': { header: "Consignor_contact_NM", key: "consignorContactName", width: 18 },
          'consignorTelephone': { header: "Consignor_tel_no", key: "consignorTelephone", width: 18 },
          'consigneeName': { header: "Consignee_NM", key: "consigneeName", width: 18 },
          'consigneeAddress': { header: "Consignee_address", key: "consigneeAddress", width: 18 },
          'consigneeAddress2': { header: "Consignee_address2", key: "consigneeAddress2", width: 18 },
          'consigneeCityName': { header: "Consignee_city_NM", key: "consigneeCityName", width: 18 },
          'consigneeContactName': { header: "Consignee_contact_NM", key: "consigneeContactName", width: 18 },
          'consigneeTelephone': { header: "Consignee_tel_no", key: "consigneeTelephone", width: 18 },
          'remarks': { header: "Remarks", key: "remarks", width: 18 },
          'taxNumberCode': { header: "Số biên lai", key: "taxNumberCode", width: 18 },
          'taxNumberDate': { header: "Ngày in biên lai", key: "taxNumberDate", width: 18 },
          'externalBoxName': { header: "External box", key: "externalBoxName", width: 18 },
        }

        const colums: any = [{ header: "No.", key: "no", width: 5 }];
        excelFields.split(',').forEach((item: string) => {
          colums.push(mapFields[item])
        });
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet("sheet 1", { properties: { tabColor: { argb: 'FFC0000' } } });

        worksheet.columns = colums;
        worksheet.getRow(1).eachCell((cell: any) => {
          cell.font = { bold: true, name: 'Calibri' };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '96C8FB' },
            bgColor: { argb: '96C8FB' },
          };
          cell.alignment = { vertical: 'middle', horizontal: 'center' },
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };
        });
        worksheet.addRows(manifests);

        let filename = `BAO_CAO_HAI_QUAN_${moment().format(EConfigure.DAY_TIME)}_${moment().format("X")}.xlsx`;
        await workbook.xlsx.writeFile(`${EConfigure.FOLDER_REPORT_CUSTOM}${filename}`);
        createHistory.link = `${new Config().clearanceDomain()}/clearance/report/${filename}`;
      }
      createHistory.isSucess = true;
      createHistory.successDate = moment().format(EConfigure.FULL_TIME);
      createHistory.save();
    } catch (error) {
      Utilities.sendDiscordErr('[service][reportCustomExport][handleExportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }
}

export default eMICClearanceService;