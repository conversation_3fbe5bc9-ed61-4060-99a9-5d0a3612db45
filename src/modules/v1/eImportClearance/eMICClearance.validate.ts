'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';
import Utilities from '../../../util/utilities';
import { ClassifyName, ClassifyValidateName, } from '../../../emuns/classify';
import ImportClearanceDetailValidate from '../importClearanceDetail/importClearanceDetail.validate';
import { WeightName } from '../../../emuns/weight';

class eMICClearanceValidate {
  static create: any = Joi.object({
    items: Joi.array().min(1).items(ImportClearanceDetailValidate.create).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    HAWBClearance: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWBClearance ${EMessage.REQUIRED}`,
      "string.empty": `HAWBClearance ${EMessage.EMPTYED}`,
      "string.max": `HAWBClearance ${EMessage.MAX_50}`,
    }),
    MAWB: Joi.string().trim().max(50).allow(null).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    flightNo: Joi.string().max(50).trim().allow(null).messages({
      "string.empty": `flightCode ${EMessage.EMPTYED}`,
      "string.max": `flightCode ${EMessage.MAX_50}`,
    }),
    arrivalDate: Joi.string().max(20).messages({
      "string.empty": `arrivalDate ${EMessage.EMPTYED}`,
      "string.max": `arrivalDate ${EMessage.MAX_20}`,
    }),
    importerCode: Joi.string().trim().max(15).allow(null).messages({
      "string.empty": `importerCode ${EMessage.EMPTYED}`,
      "string.max": `importerCode ${EMessage.MAX_15}`,
    }),
    importerName: Joi.string().trim().max(255).allow(null).messages({
      "string.empty": `importerName ${EMessage.EMPTYED}`,
      "string.max": `importerName ${EMessage.MAX_255}`,
    }),
    importerPostCode: Joi.string().trim().max(15).allow(null).messages({
      "string.empty": `importerPostCode ${EMessage.EMPTYED}`,
      "string.max": `importerPostCode ${EMessage.MAX_15}`,
    }),
    importerAddress: Joi.string().trim().required().messages({
      "any.required": `importerAddress ${EMessage.REQUIRED}`,
      "string.empty": `importerAddress ${EMessage.EMPTYED}`,
    }),
    importerTelephoneNumber: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `telephoneNumberOfImporter ${EMessage.EMPTYED}`,
    }),
    consignorCode: Joi.string().trim().max(15).allow(null).messages({
      "string.empty": `consignorCode ${EMessage.EMPTYED}`,
      "string.max": `consignorCode ${EMessage.MAX_15}`,
    }),
    consignorName: Joi.string().trim().max(255).messages({
      "string.empty": `consignorName ${EMessage.EMPTYED}`,
      "string.max": `consignorName ${EMessage.MAX_255}`,
    }),
    consignorPostCode: {
      "string.empty": `consignorPostCode ${EMessage.EMPTYED}`,
      "string.max": `consignorPostCode ${EMessage.MAX_15}`,
    },
    consignorAddress: Joi.string().messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    consignorCountryCode: Joi.string().trim().max(2).required().messages({
      "any.required": `consignorCountryCode ${EMessage.REQUIRED}`,
      "string.empty": `consignorCountryCode ${EMessage.EMPTYED}`,
      "string.max": `consignorCountryCode ${EMessage.MAX_2}`,
    }),
    weight: Joi.number().required().messages({
      "any.required": `weight ${EMessage.REQUIRED}`,
      "number.base": `weight ${EMessage.NUMBER}`,
    }),
    originalOrderNumberClient: Joi.string().trim().max(255).allow(null).messages({
      "string.empty": `originalOrderNumberClient ${EMessage.EMPTYED}`,
      "string.max": `originalOrderNumberClient ${EMessage.MAX_255}`,
    }),
    currencyCode: Joi.string().trim().max(3).required().messages({
      "any.required": `currencyCode ${EMessage.REQUIRED}`,
      "string.empty": `currencyCode ${EMessage.EMPTYED}`,
      "string.max": `currencyCode ${EMessage.MAX_3}`,
    }),
    totalPrice: Joi.number().required().messages({
      "any.required": `totalPrice ${EMessage.REQUIRED}`,
      "number.base": `totalPrice ${EMessage.NUMBER}`,
    }),
    valueClearanceVND: Joi.number().min(0).required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
      "number.min": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).required().max(3).trim().messages({
      "any.required": `unitOfMass ${EMessage.REQUIRED}`,
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    stationId: Joi.number().min(0).greater(0).required().messages({
      "any.required": `stationId ${EMessage.REQUIRED}`,
      "number.base": `stationId ${EMessage.NUMBER}`,
      "number.min": `stationId ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `stationId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    hubId: Joi.number().min(0).allow(null).messages({
      "number.base": `hubId ${EMessage.NUMBER}`,
      "number.min": `hubId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    warehouseId: Joi.number().min(0).allow(null).messages({
      "number.base": `warehouseId ${EMessage.NUMBER}`,
      "number.min": `warehouseId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    meanOfTransportationCode: Joi.number().allow(null).messages({
      "number.base": `meanOfTransportationCode ${EMessage.NUMBER}`,
    }),
    piece: Joi.number().allow(null).messages({
      "number.base": `piece ${EMessage.NUMBER}`,
    }),
    //
    note: Joi.string().allow(null).allow('').messages({
      "string.empty": `note ${EMessage.EMPTYED}`,
    }),
    serviceId: Joi.number().integer().required().min(1).messages({
      "any.required": `serviceId ${EMessage.REQUIRED}`,
      "number.base": `serviceId ${EMessage.NUMBER}`,
      "number.integer": `serviceId ${EMessage.INTEGER}`,
      "number.min": `serviceId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    classify: Utilities.stringValid(ClassifyName).required().trim().max(3).messages({
      "any.required": `classify ${EMessage.REQUIRED}`,
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_3}`,
    }),
    customerBusinessId: Joi.string().trim().allow(null).messages({
      "string.empty": `customerBusinessId ${EMessage.EMPTYED}`,
    }),
    customerPersonalId: Joi.string().trim().allow(null).messages({
      "string.empty": `customerPersonalId ${EMessage.EMPTYED}`,
    }),
    clientId: Joi.number().integer().required().min(1).messages({
      "any.required": `clientId ${EMessage.REQUIRED}`,
      "number.base": `clientId ${EMessage.NUMBER}`,
      "number.integer": `clientId ${EMessage.INTEGER}`,
      "number.min": `clientId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    orderId: Joi.string().trim().allow(null).messages({
      "string.empty": `orderId ${EMessage.EMPTYED}`,
    }),
    originalPrice: Joi.number().allow(null),
    currencyOriginalPrice: Joi.string().trim().max(5).allow(null).messages({
      "string.max": `externalBoxName ${EMessage.MAX_5}`,
    }),
    internalBoxName: Joi.string().trim().max(33).allow(null).messages({
      "string.max": `internalBoxName ${EMessage.MAX_33}`,
    }),
    externalBoxName: Joi.string().trim().max(50).allow(null).messages({
      "string.max": `externalBoxName ${EMessage.MAX_50}`,
    }),
    orderNumber: Joi.string().trim().max(50).allow(null).messages({
      "string.max": `orderNumber ${EMessage.MAX_50}`,
    }),
    childOrderNumber: Joi.string().trim().max(30).allow(null).messages({
      "string.max": `childOrderNumber ${EMessage.MAX_30}`,
    }),
    labelCustomer: Joi.string().trim().max(5000).allow(null).allow('').messages({
      "string.max": `labelCustomer ${EMessage.MAX_5000}`,
    }),
    invoiceCustomer: Joi.string().trim().max(5000).allow(null).allow('').messages({
      "string.max": `invoiceCustomer ${EMessage.MAX_5000}`,
    }),
    orderTypeId: Joi.number().allow(null).allow(''),
    invoiceDate: Joi.string().allow(null).allow('').max(20).messages({
      "string.max": `invoiceDate ${EMessage.MAX_20}`,
    }),
    identity: Joi.string().allow(null).allow('').max(1000).messages({
      "string.max": `identity ${EMessage.MAX_1000}`,
    }),
    threadCode: Joi.string().trim().allow(null).allow('').max(50).messages({
      "string.max": `threadCode ${EMessage.MAX_50}`,
    }),
    threadName: Joi.string().trim().allow(null).allow('').max(200).messages({
      "string.max": `threadName ${EMessage.MAX_200}`,
    }),
    threadColor: Joi.string().trim().allow(null).allow('').max(50).messages({
      "string.max": `threadColor ${EMessage.MAX_50}`,
    }),
    threadUrl: Joi.string().trim().allow(null).allow('').max(255).messages({
      "string.max": `threadUrl ${EMessage.MAX_255}`,
    }),
    detailsOfValuation: Joi.string().trim().allow(null).allow('').max(200).messages({
      "string.max": `detailsOfValuation ${EMessage.MAX_200}`,
    }),
  });

  static arrCreate = Joi.array().min(1).items(eMICClearanceValidate.create).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`,
  });

  static updateMIC: any = Joi.object({
    importDetails: Joi.array().min(1).items(ImportClearanceDetailValidate.updateMIC).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    classificationOfIndividualOrganization: Joi.number().messages({
      "number.base": `classificationOfIndividualOrganization ${EMessage.NUMBER}`,
    }),
    // inspectionKindClassification: Joi.number().messages({
    //   "number.base": `inspectionKindClassification ${EMessage.NUMBER}`,
    // }),
    customsOffice: Joi.string().trim().max(8).allow(null).allow('').messages({
      "string.empty": `customsOffice ${EMessage.EMPTYED}`,
      "string.max": `customsOffice ${EMessage.MAX_8}`,
    }),
    customsSubSection: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `customsSubSection ${EMessage.EMPTYED}`,
      "string.max": `customsSubSection ${EMessage.MAX_4}`,
    }),
    cargoPiece: Joi.number().messages({
      "number.base": `cargoPiece ${EMessage.NUMBER}`,
    }),
    cargoWeight: Joi.number().messages({
      "number.base": `cargoWeight ${EMessage.NUMBER}`,
    }),
    customsWarehouseCode: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `customsWarehouseCode ${EMessage.EMPTYED}`,
      "string.max": `customsWarehouseCode ${EMessage.MAX_10}`,
    }),
    arrivalDate: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `arrivalDate ${EMessage.EMPTYED}`,
      "string.max": `arrivalDate ${EMessage.MAX_20}`,
    }),
    unloadingPort: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `unloadingPort ${EMessage.EMPTYED}`,
      "string.max": `unloadingPort ${EMessage.MAX_10}`,
    }),
    loadingLocationCode: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `loadingLocationCode ${EMessage.EMPTYED}`,
      "string.max": `loadingLocationCode ${EMessage.MAX_10}`,
    }),
    totalInvoicePrice: Joi.number().messages({
      "number.base": `totalInvoicePrice ${EMessage.NUMBER}`,
    }),
    invoiceCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `invoiceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceCurrencyCode ${EMessage.MAX_3}`,
    }),
    valueClearanceVND: Joi.number().min(0).required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
      "number.min": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),

    insuranceDemarcation: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `insuranceDemarcation ${EMessage.EMPTYED}`,
      "string.max": `insuranceDemarcation ${EMessage.MAX_10}`,
    }),
    insuranceCurrency: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `insuranceCurrency ${EMessage.EMPTYED}`,
      "string.max": `insuranceCurrency ${EMessage.MAX_3}`,
    }),
    insuranceAmount: Joi.number().messages({
      "number.base": `insuranceAmount ${EMessage.NUMBER}`,
    }),

    invoicePriceCondition: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `invoicePriceCondition ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceCondition ${EMessage.MAX_4}`,
    }),
    invoicePriceKind: Joi.string().max(2).allow(null).allow('').messages({
      "string.empty": `invoicePriceKind ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceKind ${EMessage.MAX_2}`,
    }),

    importerCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `importerCode ${EMessage.EMPTYED}`,
      "string.max": `importerCode ${EMessage.MAX_15}`,
    }),
    importerName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `importerName ${EMessage.EMPTYED}`,
      "string.max": `importerName ${EMessage.MAX_255}`,
    }),
    postCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `postCode ${EMessage.EMPTYED}`,
      "string.max": `postCode ${EMessage.MAX_15}`,
    }),
    addressOfImporter: Joi.string().trim().messages({
      "string.empty": `addressOfImporter ${EMessage.EMPTYED}`,
    }),
    telephoneNumberOfImporter: Joi.string().trim().max(20).allow(null).allow('').messages({
      "string.empty": `telephoneNumberOfImporter ${EMessage.EMPTYED}`,
      "string.max": `telephoneNumberOfImporter ${EMessage.MAX_20}`,
    }),

    consignorCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `consignorCode ${EMessage.EMPTYED}`,
      "string.max": `consignorCode ${EMessage.MAX_15}`,
    }),
    consignorName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `consignorName ${EMessage.EMPTYED}`,
      "string.max": `consignorName ${EMessage.MAX_255}`,
    }),
    postCodeIdentification: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `postCodeIdentification ${EMessage.EMPTYED}`,
      "string.max": `postCodeIdentification ${EMessage.MAX_15}`,
    }),
    address1: Joi.string().allow(null).allow('').messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    address2: Joi.string().allow(null).allow('').messages({
      "string.empty": `address2 ${EMessage.EMPTYED}`,
    }),
    address3: Joi.string().allow(null).allow('').messages({
      "string.empty": `address3 ${EMessage.EMPTYED}`,
    }),
    address4: Joi.string().allow(null).allow('').messages({
      "string.empty": `address4 ${EMessage.EMPTYED}`,
    }),
    countryCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `countryCode ${EMessage.EMPTYED}`,
      "string.max": `countryCode ${EMessage.MAX_2}`,
    }),
    flightNo: Joi.string().max(50).trim().required().messages({
      "any.required": `flightCode ${EMessage.REQUIRED}`,
      "string.empty": `flightCode ${EMessage.EMPTYED}`,
      "string.max": `flightCode ${EMessage.MAX_50}`,
    }),

    freightCurrency: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `freightCurrency ${EMessage.EMPTYED}`,
      "string.max": `freightCurrency ${EMessage.MAX_3}`,
    }),
    freight: Joi.number().messages({
      "number.base": `freight ${EMessage.NUMBER}`,
    }),
    freightDemarcation: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `freightDemarcation ${EMessage.EMPTYED}`,
      "string.max": `freightDemarcation ${EMessage.MAX_10}`,
    }),
    notes: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `notes ${EMessage.EMPTYED}`,
    }),
    reasonIds: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `reasonIds ${EMessage.EMPTYED}`,
    }),
    noteHold: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `notes ${EMessage.EMPTYED}`,
    }),
    identity: Joi.string().allow(null).allow('').max(1000).messages({
      "string.max": `identity ${EMessage.MAX_1000}`,
    }),
  });

  static arrUpdate = Joi.array().min(1).items(eMICClearanceValidate.updateMIC).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`,
  });

  static master = Joi.object({
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    HAWBClearance: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWBClearance ${EMessage.REQUIRED}`,
      "string.empty": `HAWBClearance ${EMessage.EMPTYED}`,
      "string.max": `HAWBClearance ${EMessage.MAX_50}`,
    }),
    MAWB: Joi.string().trim().max(50).allow(null).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    arrivalDate: Joi.string().max(20).trim().allow(null).messages({
      "string.empty": `arrivalDate ${EMessage.EMPTYED}`,
      "string.max": `arrivalDate ${EMessage.MAX_20}`,
    }),
    flightNo: Joi.string().max(50).trim().allow(null).messages({
      "string.empty": `flightCode ${EMessage.EMPTYED}`,
      "string.max": `flightCode ${EMessage.MAX_50}`,
    }),
    hubId: Joi.number().min(0).allow(null).messages({
      "number.base": `hubId ${EMessage.NUMBER}`,
      "number.min": `hubId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    warehouseId: Joi.number().min(0).allow(null).messages({
      "number.base": `warehouseId ${EMessage.NUMBER}`,
      "number.min": `warehouseId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    meanOfTransportationCode: Joi.number().allow(null).messages({
      "number.base": `meanOfTransportationCode ${EMessage.NUMBER}`,
    }),
  })

  static arrMaster = Joi.array().min(1).items(eMICClearanceValidate.master).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static HAWBs: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `items ${EMessage.REQUIRED}`,
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
  });

  static messageTax: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `items ${EMessage.REQUIRED}`,
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    status: Joi.boolean().required().messages({
      "any.required": `status ${EMessage.REQUIRED}`,
      "boolean.base": `status ${EMessage.BOOLEAN}`,
    }),
  });

  static updateIDA: any = Joi.object({
    importDetails: Joi.array().min(1).items(ImportClearanceDetailValidate.updateIDA).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    declarationNo: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `declarationNo ${EMessage.EMPTYED}`,
      "string.max": `declarationNo ${EMessage.MAX_50}`,
    }),
    HAWB: Joi.string().max(30).allow(null).allow('').messages({
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    MAWB: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    loadingVesselAircraftName: Joi.string().max(100).allow(null).allow('').messages({
      "string.empty": `loadingVesselAircraftName ${EMessage.EMPTYED}`,
      "string.max": `loadingVesselAircraftName ${EMessage.MAX_100}`,
    }),
    declarationKindCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `declarationKindCode ${EMessage.EMPTYED}`,
      "string.max": `declarationKindCode ${EMessage.MAX_4}`,
    }),
    cargoClassificationCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `cargoClassificationCode ${EMessage.EMPTYED}`,
      "string.max": `cargoClassificationCode ${EMessage.MAX_4}`,
    }),
    meansOfTransportationCode: Joi.number().messages({
      "number.base": `meansOfTransportationCode ${EMessage.NUMBER}`,
    }),
    classificationOfIndividualOrganization: Joi.number().messages({
      "number.base": `classificationOfIndividualOrganization ${EMessage.NUMBER}`,
    }),
    // inspectionKindClassification: Joi.number().messages({
    //   "number.base": `inspectionKindClassification ${EMessage.NUMBER}`,
    // }),
    customsOffice: Joi.string().trim().max(8).allow(null).allow('').messages({
      "string.empty": `customsOffice ${EMessage.EMPTYED}`,
      "string.max": `customsOffice ${EMessage.MAX_8}`,
    }),
    plannedDeclarantCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `plannedDeclarantCode ${EMessage.EMPTYED}`,
      "string.max": `plannedDeclarantCode ${EMessage.MAX_10}`,
    }),
    customsSubSection: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `customsSubSection ${EMessage.EMPTYED}`,
      "string.max": `customsSubSection ${EMessage.MAX_4}`,
    }),
    declarationPlannedDate: Joi.string().trim().max(20).allow(null).allow('').messages({
      "string.empty": `declarationPlannedDate ${EMessage.EMPTYED}`,
      "string.max": `declarationPlannedDate ${EMessage.MAX_20}`,
    }),
    pieceUnitCode: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `pieceUnitCode ${EMessage.EMPTYED}`,
      "string.max": `pieceUnitCode ${EMessage.MAX_4}`,
    }),
    cargoWeightGross: Joi.number().messages({
      "number.base": `cargoWeightGross ${EMessage.NUMBER}`,
    }),
    weightUnitCodeGross: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `weightUnitCodeGross ${EMessage.EMPTYED}`,
      "string.max": `weightUnitCodeGross ${EMessage.MAX_4}`,
    }),
    customsWarehouseCode: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `customsWarehouseCode ${EMessage.EMPTYED}`,
      "string.max": `customsWarehouseCode ${EMessage.MAX_10}`,
    }),
    arrivalDate: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `arrivalDate ${EMessage.EMPTYED}`,
      "string.max": `arrivalDate ${EMessage.MAX_20}`,
    }),
    unloadingPortCode: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `unloadingPortCode ${EMessage.EMPTYED}`,
      "string.max": `unloadingPortCode ${EMessage.MAX_10}`,
    }),
    unloadingPortName: Joi.string().max(100).allow(null).allow('').messages({
      "string.empty": `unloadingPortName ${EMessage.EMPTYED}`,
      "string.max": `unloadingPortName ${EMessage.MAX_100}`,
    }),
    loadingLocationCode: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `loadingLocationCode ${EMessage.EMPTYED}`,
      "string.max": `loadingLocationCode ${EMessage.MAX_10}`,
    }),
    loadingLocationName: Joi.string().max(100).allow(null).allow('').messages({
      "string.empty": `loadingLocationName ${EMessage.EMPTYED}`,
      "string.max": `loadingLocationName ${EMessage.MAX_100}`,
    }),
    invoiceClassificationCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `invoiceClassificationCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceClassificationCode ${EMessage.MAX_2}`,
    }),
    totalInvoicePrice: Joi.number().messages({
      "number.base": `totalInvoicePrice ${EMessage.NUMBER}`,
    }),
    invoiceCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `invoiceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceCurrencyCode ${EMessage.MAX_3}`,
    }),
    valueClearanceVND: Joi.number().messages({
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
      "number.min": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    invoicePriceConditionCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `invoicePriceConditionCode ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceConditionCode ${EMessage.MAX_4}`,
    }),
    invoicePriceKindCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `invoicePriceKindCode ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceKindCode ${EMessage.MAX_2}`,
    }),
    insuranceDemarcationCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `insuranceDemarcationCode ${EMessage.EMPTYED}`,
      "string.max": `insuranceDemarcationCode ${EMessage.MAX_2}`,
    }),
    freightDemarcationCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `freightDemarcationCode ${EMessage.EMPTYED}`,
      "string.max": `freightDemarcationCode ${EMessage.MAX_2}`,
    }),
    freight: Joi.number().messages({
      "number.base": `freight ${EMessage.NUMBER}`,
    }),
    freightExchangeRate: Joi.number().messages({
      "number.base": `freightExchangeRate ${EMessage.NUMBER}`,
    }),
    freightDemarcation: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `freightDemarcation ${EMessage.EMPTYED}`,
      "string.max": `freightDemarcation ${EMessage.MAX_10}`,
    }),
    freightCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `freightCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `freightCurrencyCode ${EMessage.MAX_3}`,
    }),
    taxPayer: Joi.number().messages({
      "number.base": `taxPayer ${EMessage.NUMBER}`,
    }),
    paymentClassification: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `paymentClassification ${EMessage.EMPTYED}`,
      "string.max": `paymentClassification ${EMessage.MAX_3}`,
    }),
    valuationDeclarationDemarcationCode: Joi.number().messages({
      "number.base": `valuationDeclarationDemarcationCode ${EMessage.NUMBER}`,
    }),
    codeOfExtendingDueDateForPayment: Joi.string().trim().max(3).messages({
      "string.empty": `codeOfExtendingDueDateForPayment ${EMessage.EMPTYED}`,
      "string.max": `codeOfExtendingDueDateForPayment ${EMessage.MAX_3}`,
    }),
    insuranceDemarcation: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `insuranceDemarcation ${EMessage.EMPTYED}`,
      "string.max": `insuranceDemarcation ${EMessage.MAX_10}`,
    }),
    insuranceCurrency: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `insuranceCurrency ${EMessage.EMPTYED}`,
      "string.max": `insuranceCurrency ${EMessage.MAX_3}`,
    }),
    insuranceAmount: Joi.number().messages({
      "number.base": `insuranceAmount ${EMessage.NUMBER}`,
    }),
    notes: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `notes ${EMessage.EMPTYED}`,
    }),
    termOfPayment: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `termOfPayment ${EMessage.EMPTYED}`,
      "string.max": `termOfPayment ${EMessage.MAX_50}`,
    }),
    ////
    importerCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `importerCode ${EMessage.EMPTYED}`,
      "string.max": `importerCode ${EMessage.MAX_15}`,
    }),
    importerName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `importerName ${EMessage.EMPTYED}`,
      "string.max": `importerName ${EMessage.MAX_255}`,
    }),
    postCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `importerCode ${EMessage.EMPTYED}`,
      "string.max": `importerCode ${EMessage.MAX_15}`,
    }),
    addressOfImporter: Joi.string().trim().messages({
      "string.empty": `addressOfImporter ${EMessage.EMPTYED}`,
    }),
    telephoneNumberOfImporter: Joi.string().trim().max(20).allow(null).allow('').messages({
      "string.empty": `telephoneNumberOfImporter ${EMessage.EMPTYED}`,
      "string.max": `telephoneNumberOfImporter ${EMessage.MAX_20}`,
    }),
    importContractorCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `importContractorCode ${EMessage.EMPTYED}`,
      "string.max": `importContractorCode ${EMessage.MAX_15}`,
    }),
    importContractorName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `importContractorName ${EMessage.EMPTYED}`,
      "string.max": `importContractorName ${EMessage.MAX_255}`,
    }),
    consignorCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `consignorCode ${EMessage.EMPTYED}`,
      "string.max": `consignorCode ${EMessage.MAX_15}`,
    }),
    consignorName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `consignorName ${EMessage.EMPTYED}`,
      "string.max": `consignorName ${EMessage.MAX_255}`,
    }),
    postCodeIdentification: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `postCodeIdentification ${EMessage.EMPTYED}`,
      "string.max": `postCodeIdentification ${EMessage.MAX_15}`,
    }),
    address1: Joi.string().allow(null).allow('').messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    address2: Joi.string().allow(null).allow('').messages({
      "string.empty": `address2 ${EMessage.EMPTYED}`,
    }),
    address3: Joi.string().allow(null).allow('').messages({
      "string.empty": `address3 ${EMessage.EMPTYED}`,
    }),
    address4: Joi.string().allow(null).allow('').messages({
      "string.empty": `address4 ${EMessage.EMPTYED}`,
    }),
    countryCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `countryCode ${EMessage.EMPTYED}`,
      "string.max": `countryCode ${EMessage.MAX_2}`,
    }),
    exportConsignerName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `exportConsignerName ${EMessage.EMPTYED}`,
      "string.max": `exportConsignerName ${EMessage.MAX_255}`,
    }),
    otherLawCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `otherLawCode ${EMessage.EMPTYED}`,
      "string.max": `otherLawCode ${EMessage.MAX_10}`,
    }),
    electronicInvoiceReceiptNo: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `electronicInvoiceReceiptNo ${EMessage.EMPTYED}`,
      "string.max": `electronicInvoiceReceiptNo ${EMessage.MAX_50}`,
    }),
    releaseBeforePermitRequestReasonCode: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `releaseBeforePermitRequestReasonCode ${EMessage.EMPTYED}`,
      "string.max": `releaseBeforePermitRequestReasonCode ${EMessage.MAX_50}`,
    }),
    invoiceNo: Joi.string().max(255).allow(null).allow('').messages({
      "string.empty": `invoiceNo ${EMessage.EMPTYED}`,
      "string.max": `invoiceNo ${EMessage.MAX_255}`,
    }),
    invoiceDate: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `invoiceDate ${EMessage.EMPTYED}`,
      "string.max": `invoiceDate ${EMessage.MAX_10}`,
    }),
    permitLicenses: Joi.array().items(
      Joi.object({
        permitType: Joi.string().trim().allow(null).allow('').messages({
          "string.empty": `permitType ${EMessage.EMPTYED}`,
        }),
        permitLicenseNo: Joi.string().trim().allow(null).allow('').messages({
          "string.empty": `permitLicenseNo ${EMessage.EMPTYED}`,
        }),
      })
    ).allow(null).allow('').messages({
      "array.base": `data ${EMessage.ARRAY}`,
    }),
    reasonIds: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `reasonIds ${EMessage.EMPTYED}`,
    }),
    noteHold: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `notes ${EMessage.EMPTYED}`,
    }),
    feeClearance: Joi.number().messages({
      "number.base": `feeClearance ${EMessage.NUMBER}`,
    }),
    cargoPiece: Joi.number().messages({
      "number.base": `cargoPiece ${EMessage.NUMBER}`,
    }),
    identity: Joi.string().allow(null).allow('').max(1000).messages({
      "string.max": `identity ${EMessage.MAX_1000}`,
    }),
    detailsOfValuation: Joi.string().allow(null).allow('').max(250).messages({
      "string.max": `detailsOfValuation ${EMessage.MAX_250}`,
    }),
  });

  static checkin: any = Joi.object({
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    checkin: Joi.string().trim().max(30).required().messages({
      "any.required": `checkin ${EMessage.REQUIRED}`,
      "string.empty": `checkin ${EMessage.EMPTYED}`,
      "string.max": `checkin ${EMessage.MAX_30}`,
    }),
  });

  static checkout: any = Joi.object({
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    checkout: Joi.string().trim().max(30).required().messages({
      "any.required": `checkout ${EMessage.REQUIRED}`,
      "string.empty": `checkout ${EMessage.EMPTYED}`,
      "string.max": `checkout ${EMessage.MAX_30}`,
    }),
  });

  static HAWB: any = Joi.object({
    HAWB: Joi.string().trim().uppercase().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
  });

  static changeClassify: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `items ${EMessage.REQUIRED}`,
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    classify: Utilities.stringValid(ClassifyValidateName).max(4).trim().messages({
      "string.empty": `unitOfLength ${EMessage.EMPTYED}`,
      "string.max": `unitOfLength ${EMessage.MAX_4}`,
    }),
  });

  static holdManifest: any = Joi.object({
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    isHold: Joi.boolean().allow(null).allow('').messages({
      "boolean.base": `isHold ${EMessage.BOOLEAN}`,
    }),
    reasonIds: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `reasonIds ${EMessage.EMPTYED}`,
    }),
  });

  static arrHoldManifest = Joi.object({
    holdData: Joi.array().min(1).items(eMICClearanceValidate.holdManifest).required().messages({
      "any.required": `data ${EMessage.REQUIRED}`,
      "array.base": `data ${EMessage.ARRAY}`,
      "array.min": `data ${EMessage.LEAST_ITEM_1}`,
    }),
    isRemoveMAWB: Joi.boolean().required().messages({
      "any.required": `isRemoveMAWB ${EMessage.REQUIRED}`,
      "boolean.base": `isRemoveMAWB ${EMessage.BOOLEAN}`,
    }),
  });

  static updateCOM: any = Joi.object({
    importDetails: Joi.array().items(ImportClearanceDetailValidate.updateCOM).messages({
      "array.base": `items ${EMessage.ARRAY}`,
    }),
    declarationNoCustomer: Joi.string().max(50).required().messages({
      "any.required": `declarationNoCustomer ${EMessage.REQUIRED}`,
      "string.empty": `declarationNoCustomer ${EMessage.EMPTYED}`,
      "string.max": `declarationNoCustomer ${EMessage.MAX_50}`,
    }),
    HAWB: Joi.string().max(30).allow(null).allow('').messages({
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    MAWB: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    loadingVesselAircraftName: Joi.string().max(100).allow(null).allow('').messages({
      "string.empty": `loadingVesselAircraftName ${EMessage.EMPTYED}`,
      "string.max": `loadingVesselAircraftName ${EMessage.MAX_100}`,
    }),
    declarationKindCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `declarationKindCode ${EMessage.EMPTYED}`,
      "string.max": `declarationKindCode ${EMessage.MAX_4}`,
    }),
    cargoClassificationCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `cargoClassificationCode ${EMessage.EMPTYED}`,
      "string.max": `cargoClassificationCode ${EMessage.MAX_4}`,
    }),
    meansOfTransportationCode: Joi.number().messages({
      "number.base": `meansOfTransportationCode ${EMessage.NUMBER}`,
    }),
    classificationOfIndividualOrganization: Joi.number().messages({
      "number.base": `classificationOfIndividualOrganization ${EMessage.NUMBER}`,
    }),
    inspectionKindClassification: Joi.number().required().messages({
      "any.required": `inspectionKindClassification ${EMessage.REQUIRED}`,
      "number.base": `inspectionKindClassification ${EMessage.NUMBER}`,
    }),
    customsOffice: Joi.string().trim().max(8).allow(null).allow('').messages({
      "string.empty": `customsOffice ${EMessage.EMPTYED}`,
      "string.max": `customsOffice ${EMessage.MAX_8}`,
    }),
    plannedDeclarantCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `plannedDeclarantCode ${EMessage.EMPTYED}`,
      "string.max": `plannedDeclarantCode ${EMessage.MAX_10}`,
    }),
    customsSubSection: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `customsSubSection ${EMessage.EMPTYED}`,
      "string.max": `customsSubSection ${EMessage.MAX_4}`,
    }),
    declarationPlannedDate: Joi.string().trim().max(20).allow(null).allow('').messages({
      "string.empty": `declarationPlannedDate ${EMessage.EMPTYED}`,
      "string.max": `declarationPlannedDate ${EMessage.MAX_20}`,
    }),
    pieceUnitCode: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `pieceUnitCode ${EMessage.EMPTYED}`,
      "string.max": `pieceUnitCode ${EMessage.MAX_4}`,
    }),
    cargoWeightGross: Joi.number().messages({
      "number.base": `cargoWeightGross ${EMessage.NUMBER}`,
    }),
    weightUnitCodeGross: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `weightUnitCodeGross ${EMessage.EMPTYED}`,
      "string.max": `weightUnitCodeGross ${EMessage.MAX_4}`,
    }),
    customsWarehouseCode: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `customsWarehouseCode ${EMessage.EMPTYED}`,
      "string.max": `customsWarehouseCode ${EMessage.MAX_10}`,
    }),
    arrivalDate: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `arrivalDate ${EMessage.EMPTYED}`,
      "string.max": `arrivalDate ${EMessage.MAX_20}`,
    }),
    unloadingPortCode: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `unloadingPortCode ${EMessage.EMPTYED}`,
      "string.max": `unloadingPortCode ${EMessage.MAX_10}`,
    }),
    unloadingPortName: Joi.string().max(100).allow(null).allow('').messages({
      "string.empty": `unloadingPortName ${EMessage.EMPTYED}`,
      "string.max": `unloadingPortName ${EMessage.MAX_100}`,
    }),
    loadingLocationCode: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `loadingLocationCode ${EMessage.EMPTYED}`,
      "string.max": `loadingLocationCode ${EMessage.MAX_10}`,
    }),
    loadingLocationName: Joi.string().max(100).allow(null).allow('').messages({
      "string.empty": `loadingLocationName ${EMessage.EMPTYED}`,
      "string.max": `loadingLocationName ${EMessage.MAX_100}`,
    }),
    invoiceClassificationCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `invoiceClassificationCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceClassificationCode ${EMessage.MAX_2}`,
    }),
    totalInvoicePrice: Joi.number().messages({
      "number.base": `totalInvoicePrice ${EMessage.NUMBER}`,
    }),
    invoiceCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `invoiceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceCurrencyCode ${EMessage.MAX_3}`,
    }),
    valueClearanceVND: Joi.number().messages({
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
      "number.min": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    invoicePriceConditionCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `invoicePriceConditionCode ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceConditionCode ${EMessage.MAX_4}`,
    }),
    invoicePriceKindCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `invoicePriceKindCode ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceKindCode ${EMessage.MAX_2}`,
    }),
    insuranceDemarcationCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `insuranceDemarcationCode ${EMessage.EMPTYED}`,
      "string.max": `insuranceDemarcationCode ${EMessage.MAX_2}`,
    }),
    freightDemarcationCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `freightDemarcationCode ${EMessage.EMPTYED}`,
      "string.max": `freightDemarcationCode ${EMessage.MAX_2}`,
    }),
    freight: Joi.number().messages({
      "number.base": `freight ${EMessage.NUMBER}`,
    }),
    freightExchangeRate: Joi.number().messages({
      "number.base": `freightExchangeRate ${EMessage.NUMBER}`,
    }),
    freightDemarcation: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `freightDemarcation ${EMessage.EMPTYED}`,
      "string.max": `freightDemarcation ${EMessage.MAX_10}`,
    }),
    freightCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `freightCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `freightCurrencyCode ${EMessage.MAX_3}`,
    }),
    taxPayer: Joi.number().messages({
      "number.base": `taxPayer ${EMessage.NUMBER}`,
    }),
    paymentClassification: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `paymentClassification ${EMessage.EMPTYED}`,
      "string.max": `paymentClassification ${EMessage.MAX_3}`,
    }),
    valuationDeclarationDemarcationCode: Joi.number().messages({
      "number.base": `valuationDeclarationDemarcationCode ${EMessage.NUMBER}`,
    }),
    codeOfExtendingDueDateForPayment: Joi.string().trim().max(3).messages({
      "string.empty": `codeOfExtendingDueDateForPayment ${EMessage.EMPTYED}`,
      "string.max": `codeOfExtendingDueDateForPayment ${EMessage.MAX_3}`,
    }),
    insuranceDemarcation: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `insuranceDemarcation ${EMessage.EMPTYED}`,
      "string.max": `insuranceDemarcation ${EMessage.MAX_10}`,
    }),
    insuranceCurrency: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `insuranceCurrency ${EMessage.EMPTYED}`,
      "string.max": `insuranceCurrency ${EMessage.MAX_3}`,
    }),
    insuranceAmount: Joi.number().messages({
      "number.base": `insuranceAmount ${EMessage.NUMBER}`,
    }),
    notes: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `notes ${EMessage.EMPTYED}`,
    }),
    termOfPayment: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `termOfPayment ${EMessage.EMPTYED}`,
      "string.max": `termOfPayment ${EMessage.MAX_50}`,
    }),
    ////
    importerCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `importerCode ${EMessage.EMPTYED}`,
      "string.max": `importerCode ${EMessage.MAX_15}`,
    }),
    importerName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `importerName ${EMessage.EMPTYED}`,
      "string.max": `importerName ${EMessage.MAX_255}`,
    }),
    postCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `importerCode ${EMessage.EMPTYED}`,
      "string.max": `importerCode ${EMessage.MAX_15}`,
    }),
    addressOfImporter: Joi.string().trim().messages({
      "string.empty": `addressOfImporter ${EMessage.EMPTYED}`,
    }),
    telephoneNumberOfImporter: Joi.string().trim().max(20).allow(null).allow('').messages({
      "string.empty": `telephoneNumberOfImporter ${EMessage.EMPTYED}`,
      "string.max": `telephoneNumberOfImporter ${EMessage.MAX_20}`,
    }),
    importContractorCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `importContractorCode ${EMessage.EMPTYED}`,
      "string.max": `importContractorCode ${EMessage.MAX_15}`,
    }),
    importContractorName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `importContractorName ${EMessage.EMPTYED}`,
      "string.max": `importContractorName ${EMessage.MAX_255}`,
    }),
    consignorCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `consignorCode ${EMessage.EMPTYED}`,
      "string.max": `consignorCode ${EMessage.MAX_15}`,
    }),
    consignorName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `consignorName ${EMessage.EMPTYED}`,
      "string.max": `consignorName ${EMessage.MAX_255}`,
    }),
    postCodeIdentification: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `postCodeIdentification ${EMessage.EMPTYED}`,
      "string.max": `postCodeIdentification ${EMessage.MAX_15}`,
    }),
    address1: Joi.string().allow(null).allow('').messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    address2: Joi.string().allow(null).allow('').messages({
      "string.empty": `address2 ${EMessage.EMPTYED}`,
    }),
    address3: Joi.string().allow(null).allow('').messages({
      "string.empty": `address3 ${EMessage.EMPTYED}`,
    }),
    address4: Joi.string().allow(null).allow('').messages({
      "string.empty": `address4 ${EMessage.EMPTYED}`,
    }),
    countryCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `countryCode ${EMessage.EMPTYED}`,
      "string.max": `countryCode ${EMessage.MAX_2}`,
    }),
    exportConsignerName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `exportConsignerName ${EMessage.EMPTYED}`,
      "string.max": `exportConsignerName ${EMessage.MAX_255}`,
    }),
    otherLawCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `otherLawCode ${EMessage.EMPTYED}`,
      "string.max": `otherLawCode ${EMessage.MAX_10}`,
    }),
    electronicInvoiceReceiptNo: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `electronicInvoiceReceiptNo ${EMessage.EMPTYED}`,
      "string.max": `electronicInvoiceReceiptNo ${EMessage.MAX_50}`,
    }),
    releaseBeforePermitRequestReasonCode: Joi.string().max(50).allow(null).allow('').messages({
      "string.empty": `releaseBeforePermitRequestReasonCode ${EMessage.EMPTYED}`,
      "string.max": `releaseBeforePermitRequestReasonCode ${EMessage.MAX_50}`,
    }),
    dateClearanced: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateClearanced ${EMessage.EMPTYED}`,
      "string.max": `dateClearanced ${EMessage.MAX_20}`,
    }),
    permitLicenses: Joi.array().items(
      Joi.object({
        permitType: Joi.string().trim().allow(null).allow('').messages({
          "string.empty": `permitType ${EMessage.EMPTYED}`,
        }),
        permitLicenseNo: Joi.string().trim().allow(null).allow('').messages({
          "string.empty": `permitLicenseNo ${EMessage.EMPTYED}`,
        }),
      })
    ).allow(null).allow('').messages({
      "array.base": `data ${EMessage.ARRAY}`,
    }),
    feeClearance: Joi.number().messages({
      "number.base": `feeClearance ${EMessage.NUMBER}`,
    }),
    identity: Joi.string().allow(null).allow('').max(1000).messages({
      "string.max": `identity ${EMessage.MAX_1000}`,
    }),
  });

  static storeWarehouse: any = Joi.object({
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    address: Joi.string().trim().max(200).required().messages({
      "any.required": `address ${EMessage.REQUIRED}`,
      "string.empty": `address ${EMessage.EMPTYED}`,
      "string.max": `address ${EMessage.MAX_200}`,
    }),
    date: Joi.string().trim().max(10).required().messages({
      "any.required": `date ${EMessage.REQUIRED}`,
      "string.empty": `date ${EMessage.EMPTYED}`,
      "string.max": `date ${EMessage.MAX_10}`,
    }),
  });

  static returnCargo: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `items ${EMessage.REQUIRED}`,
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    employeeId: Joi.number().messages({
      "number.base": `employeeId ${EMessage.NUMBER}`,
    }),
  });

  static taxCodeNumber: any = Joi.object({
    HAWB: Joi.string().trim().required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
    })
  });


  static importExcel = Joi.object({
    items: Joi.array().min(1).items(ImportClearanceDetailValidate.createImportExcel).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    HAWBClearance: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWBClearance ${EMessage.REQUIRED}`,
      "string.empty": `HAWBClearance ${EMessage.EMPTYED}`,
      "string.max": `HAWBClearance ${EMessage.MAX_50}`,
    }),
    MAWB: Joi.string().trim().max(50).allow(null).allow('').messages({
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    flightNo: Joi.string().max(50).trim().allow(null).messages({
      "string.empty": `flightCode ${EMessage.EMPTYED}`,
      "string.max": `flightCode ${EMessage.MAX_50}`,
    }),
    arrivalDate: Joi.string().max(20).messages({
      "string.empty": `arrivalDate ${EMessage.EMPTYED}`,
      "string.max": `arrivalDate ${EMessage.MAX_20}`,
    }),
    importerCode: Joi.string().trim().max(15).allow(null).allow("").messages({
      "string.empty": `importerCode ${EMessage.EMPTYED}`,
      "string.max": `importerCode ${EMessage.MAX_15}`,
    }),
    importerName: Joi.string().trim().max(255).allow(null).allow("").messages({
      "string.empty": `importerName ${EMessage.EMPTYED}`,
      "string.max": `importerName ${EMessage.MAX_255}`,
    }),
    importerPostCode: Joi.string().trim().max(15).allow(null).allow("").messages({
      "string.empty": `importerPostCode ${EMessage.EMPTYED}`,
      "string.max": `importerPostCode ${EMessage.MAX_15}`,
    }),
    importerAddress: Joi.string().trim().required().messages({
      "any.required": `importerAddress ${EMessage.REQUIRED}`,
      "string.empty": `importerAddress ${EMessage.EMPTYED}`,
    }),
    importerTelephoneNumber: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `telephoneNumberOfImporter ${EMessage.EMPTYED}`,
    }),
    consignorCode: Joi.string().trim().max(15).allow(null).allow("").messages({
      "string.empty": `consignorCode ${EMessage.EMPTYED}`,
      "string.max": `consignorCode ${EMessage.MAX_15}`,
    }),
    consignorName: Joi.string().trim().max(255).messages({
      "string.empty": `consignorName ${EMessage.EMPTYED}`,
      "string.max": `consignorName ${EMessage.MAX_255}`,
    }),
    consignorPostCode: Joi.string().trim().max(15).allow(null).allow("").messages({
      "string.empty": `consignorPostCode ${EMessage.EMPTYED}`,
      "string.max": `consignorPostCode ${EMessage.MAX_15}`,
    }),
    consignorAddress: Joi.string().messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    consignorCountryCode: Joi.string().trim().max(2).required().messages({
      "any.required": `consignorCountryCode ${EMessage.REQUIRED}`,
      "string.empty": `consignorCountryCode ${EMessage.EMPTYED}`,
      "string.max": `consignorCountryCode ${EMessage.MAX_2}`,
    }),
    weight: Joi.number().required().messages({
      "any.required": `weight ${EMessage.REQUIRED}`,
      "number.base": `weight ${EMessage.NUMBER}`,
    }),
    originalOrderNumberClient: Joi.string().trim().max(255).allow(null).allow("").messages({
      "string.empty": `originalOrderNumberClient ${EMessage.EMPTYED}`,
      "string.max": `originalOrderNumberClient ${EMessage.MAX_255}`,
    }),
    currencyCode: Joi.string().trim().max(3).required().messages({
      "any.required": `currencyCode ${EMessage.REQUIRED}`,
      "string.empty": `currencyCode ${EMessage.EMPTYED}`,
      "string.max": `currencyCode ${EMessage.MAX_3}`,
    }),
    totalPrice: Joi.number().required().messages({
      "any.required": `totalPrice ${EMessage.REQUIRED}`,
      "number.base": `totalPrice ${EMessage.NUMBER}`,
    }),
    valueClearanceVND: Joi.number().min(0).required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
      "number.min": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).required().max(3).trim().messages({
      "any.only": `unitOfMass ${EMessage.ONLY}`,
      "any.required": `unitOfMass ${EMessage.REQUIRED}`,
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    stationId: Joi.number().min(0).greater(0).required().messages({
      "any.required": `stationId ${EMessage.REQUIRED}`,
      "number.base": `stationId ${EMessage.NUMBER}`,
      "number.min": `stationId ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `stationId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    hubId: Joi.number().min(0).allow(null).messages({
      "number.base": `hubId ${EMessage.NUMBER}`,
      "number.min": `hubId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    warehouseId: Joi.number().min(0).allow(null).messages({
      "number.base": `warehouseId ${EMessage.NUMBER}`,
      "number.min": `warehouseId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    meanOfTransportationCode: Joi.number().allow(null).messages({
      "number.base": `meanOfTransportationCode ${EMessage.NUMBER}`,
    }),
    piece: Joi.number().allow(null).messages({
      "number.base": `piece ${EMessage.NUMBER}`,
    }),
    note: Joi.string().trim().allow('').allow(null).messages({
      "string.empty": `note ${EMessage.EMPTYED}`,
    }),
    serviceId: Joi.number().integer().required().min(1).messages({
      "any.required": `serviceId ${EMessage.REQUIRED}`,
      "number.base": `serviceId ${EMessage.NUMBER}`,
      "number.integer": `serviceId ${EMessage.INTEGER}`,
      "number.min": `serviceId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    classify: Utilities.stringValid(ClassifyName).required().trim().max(3).messages({
      "any.required": `classify ${EMessage.REQUIRED}`,
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_3}`,
    }),
    customerBusinessId: Joi.string().trim().allow(null).allow("").messages({
      "string.empty": `customerBusinessId ${EMessage.EMPTYED}`,
    }),
    customerPersonalId: Joi.string().trim().allow(null).allow("").messages({
      "string.empty": `customerPersonalId ${EMessage.EMPTYED}`,
    }),
    clientId: Joi.number().integer().required().min(1).messages({
      "any.required": `clientId ${EMessage.REQUIRED}`,
      "number.base": `clientId ${EMessage.NUMBER}`,
      "number.integer": `clientId ${EMessage.INTEGER}`,
      "number.min": `clientId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    orderId: Joi.string().trim().allow(null).messages({
      "string.empty": `orderId ${EMessage.EMPTYED}`,
    }),
    originalPrice: Joi.number().allow(null),
    currencyOriginalPrice: Joi.string().trim().max(5).allow(null).allow("").messages({
      "string.max": `externalBoxName ${EMessage.MAX_5}`,
    }),
    internalBoxName: Joi.string().trim().max(33).allow(null).allow("").messages({
      "string.max": `internalBoxName ${EMessage.MAX_33}`,
    }),
    externalBoxName: Joi.string().trim().max(50).allow(null).allow("").messages({
      "string.max": `externalBoxName ${EMessage.MAX_50}`,
    }),
    orderNumber: Joi.string().trim().max(50).allow(null).allow("").messages({
      "string.max": `orderNumber ${EMessage.MAX_50}`,
    }),
    childOrderNumber: Joi.string().trim().max(30).allow(null).allow("").messages({
      "string.max": `childOrderNumber ${EMessage.MAX_30}`,
    }),
    clearanceCreateLogId: Joi.number().allow(null)
  });

  static register: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `HAWBs ${EMessage.REQUIRED}`,
      "array.base": `HAWBs ${EMessage.ARRAY}`,
      "array.min": `HAWBs ${EMessage.LEAST_ITEM_1}`,
    }),
    isPrioritize: Joi.allow(null).allow("").messages({
      "boolean.base": `isPrioritize ${EMessage.BOOLEAN}`,
    }),
  });

  static groupBox: any = Joi.object({
    MAWB: Joi.string().trim().max(50).allow('').allow(null).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    boxes: Joi.array().min(1).items(Joi.string().trim()).unique().allow('').allow(null).messages({
      "any.required": `boxes ${EMessage.REQUIRED}`,
      "array.base": `boxes ${EMessage.ARRAY}`,
      "array.min": `boxes ${EMessage.LEAST_ITEM_1}`,
    }),
    dateCheckout: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateCheckout ${EMessage.EMPTYED}`,
      "string.max": `dateCheckout ${EMessage.MAX_20}`,
    }),
    dateCreate: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateCreate ${EMessage.EMPTYED}`,
      "string.max": `dateCreate ${EMessage.MAX_20}`,
    }),
    classify: Joi.string().max(3).allow(null).allow('').messages({
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_20}`,
    }),
    phase: Joi.number().required().messages({
      "any.required": `phase ${EMessage.REQUIRED}`,
      "number.base": `phase ${EMessage.NUMBER}`,
    }),
    select: Joi.string().trim().required().messages({
      "string.empty": `select ${EMessage.EMPTYED}`,
    }),
  });


  static historyInvoice: any = Joi.object({
    MAWB: Joi.string().trim().max(50).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    phase: Joi.allow('').allow(null),
    printType: Joi.number().required().messages({
      "any.required": `printType ${EMessage.REQUIRED}`,
      "number.base": `printType ${EMessage.NUMBER}`,
    }),
    boxes: Joi.array().min(1).items(Joi.string().trim().uppercase()).allow('').allow(null).messages({
      "array.base": `boxes ${EMessage.ARRAY}`,
      "array.min": `boxes ${EMessage.LEAST_ITEM_1}`,
    }),
    dateCheckout: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateCheckout ${EMessage.EMPTYED}`,
      "string.max": `dateCheckout ${EMessage.MAX_20}`,
    }),
    dateCreate: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateCreate ${EMessage.EMPTYED}`,
      "string.max": `dateCreate ${EMessage.MAX_20}`,
    }),
    classify: Joi.string().max(3).allow(null).allow('').messages({
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_20}`,
    }),
    note: Joi.string().allow(null).allow(''),
    isHasFooter: Joi.boolean().allow(null).allow('').messages({
      "boolean.base": `isHasFooter ${EMessage.BOOLEAN}`,
    }),
    itemLanguage: Joi.string().max(2).allow(null).allow(''),
    inspectionKindClassification: Joi.number().allow(null).allow('').messages({
      "boolean.base": `inspectionKindClassification ${EMessage.BOOLEAN}`,
    }),
  });

  static pickupPrint: any = Joi.object({
    MAWB: Joi.string().trim().max(50).required().messages({
      "any.required": `printType ${EMessage.REQUIRED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    phase: Joi.allow('').allow(null),
    printType: Joi.number().required().messages({
      "any.required": `printType ${EMessage.REQUIRED}`,
      "number.base": `printType ${EMessage.NUMBER}`,
    }),
    no: Joi.string().max(100).allow(null).allow('').messages({
      "string.max": `no ${EMessage.MAX_100}`,
    }),
    ola: Joi.string().max(200).allow(null).allow('').messages({
      "string.max": `ola ${EMessage.MAX_200}`,
    }),
    date: Joi.string().max(30).allow(null).allow('').messages({
      "string.max": `date ${EMessage.MAX_100}`,
    }),
    clearanceName: Joi.string().max(400).allow(null).allow('').messages({
      "string.max": `clearanceName ${EMessage.MAX_400}`,
    }),
    boxes: Joi.array().min(1).items(Joi.string().trim().uppercase()).allow('').allow(null).messages({
      "array.base": `boxes ${EMessage.ARRAY}`,
      "array.min": `boxes ${EMessage.LEAST_ITEM_1}`,
    }),
  });

  static changeExchangeRate: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim().max(50)).required().messages({
      "any.required": `HAWBs ${EMessage.REQUIRED}`,
      "array.base": `HAWBs ${EMessage.ARRAY}`,
      "array.min": `HAWBs ${EMessage.LEAST_ITEM_1}`,
    }),
    exchangeRate: Joi.number().required().messages({
      "any.required": `exchangeRate ${EMessage.REQUIRED}`,
      "number.base": `exchangeRate ${EMessage.NUMBER}`,
    }),
  });

  static masterLimit: any = Joi.object({
    hubId: Joi.number().required().messages({
      "any.required": `hubId ${EMessage.REQUIRED}`,
      "number.base": `hubId ${EMessage.NUMBER}`,
    }),
  });

  static boxMAWB: any = Joi.object({
    externalBoxName: Joi.string().trim().required().messages({
      "any.required": `externalBoxName ${EMessage.REQUIRED}`,
    }),
  });

  static micTaxPercent: any = Joi.array().min(1).items(Joi.object({
    HAWB: Joi.string().trim().max(50).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    percentValue: Joi.number().required().messages({
      "any.required": `percentValue ${EMessage.REQUIRED}`,
      "number.base": `percentValue ${EMessage.NUMBER}`,
    })
  }));

  static micTaxPint: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `items ${EMessage.REQUIRED}`,
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
  });

  static generateInvoice: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `items ${EMessage.REQUIRED}`,
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
  });

  static swapInvoice: any = Joi.array().items(
    Joi.object({
      HAWB: Joi.string().trim().max(50).required().messages({
        "any.required": `HAWB ${EMessage.REQUIRED}`,
        "string.max": `HAWB ${EMessage.MAX_50}`,
      }),
      taxCodeNumber: Joi.string().trim().max(7).min(7).required().messages({
        "any.required": `taxCodeNumber ${EMessage.REQUIRED}`,
        "string.max": `taxCodeNumber ${EMessage.MAX_7}`,
        "string.min": `taxCodeNumber ${EMessage.MIN_7}`,
      }),
    })
  ).allow(null).allow('').messages({
    "array.base": `data ${EMessage.ARRAY}`,
  });

}

export default eMICClearanceValidate;
