'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import Middlewares from '../../../middleware/middlewares';
import ParserUrl from '../../../parser/url';
import EMessage from '../../../emuns/messages';
import EConfigure from '../../../emuns/configures';
import Optional from '../../../parser/optional';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import HttpData from '../../../https/data';
import ErrorValidate from "../../../util/error.validate";
import Utilities from "../../../util/utilities";
import Meta from "../../../https/meta";
import HSCodeValidate from './invoiceCondition.validate';
import HSCodeService from './invoiceCondition.service';

class InvoiceConditionController {
  public path = '/invoice_conditions';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get(this.path, this.getAll);
    this.router.get(`${this.path}/:id`, this.getOne);
    this.router.put(this.path, this.update);
    this.router.patch(this.path, this.create);
    this.router.delete(`${this.path}/:id`, this.deleteOne);
  }

  private async getAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const hscodeService = new HSCodeService();
      const [data, total] = await hscodeService.getAll(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][hscode][get_all]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOne(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const id: number = Number(req.params.id);
      const optional: Optional = parseUrl.handleParameter();
      const areaService = new HSCodeService();
      const data = await areaService.getOneOptional(id, optional);
      if(data) {
        return HttpResponse.sendData(res, httpStatus.FOUND, new HttpData(true, data));
      } else {
        return HttpResponse.sendMessage(res, httpStatus.NOT_FOUND, new HttpException(true, EMessage.NOT_FOUND));
      }
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][hscode][get_one]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async update(req: Request, res: Response, next: NextFunction) {
    // try {
    //   const data: any = req.body;
    //   const { error } = AreaValidate.update.validate(data, { abortEarly: false });
    //   if(error) {
    //     let errorValidate: ErrorValidate = new ErrorValidate(error);
    //     let message: string  = errorValidate.handleError();
    //     return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
    //   }
    //   const parseUrl: ParserUrl = new ParserUrl(req.query);
    //   const optional: Optional = parseUrl.handleParameter();
    //   const fields: Array<string> = ['name', 'provinceId', 'isActivated'];
    //   const areaService =  new HSCodeService();
    //   const [total] = await areaService.update(objUpdate, optional);
    //   if(total > 0) {
    //     return HttpResponse.sendMessage(res, httpStatus.OK, new HttpException(false, EMessage.UPDATE_SUCCESS));
    //   } else {
    //     return HttpResponse.sendMessage(res, httpStatus.EXPECTATION_FAILED, new HttpException(false, EMessage.UPDATE_FAIL));
    //   }
    // } catch (error) {
    //   console.log(' \t --- [%s] [error][controller][area][update]: %o', moment().format(EConfigure.FULL_TIME), error);
    //   return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    // }
  }

  private async create(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const data: any = req.body;
      const { error, value } =  HSCodeValidate.arrCreate.validate(data, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const hscodeService =  new HSCodeService();
      const hscodes = await hscodeService.createBulk(value);
      return HttpResponse.sendData(res, httpStatus.CREATED, new HttpData(true, hscodes));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][area][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async deleteOne(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const id: number = Number(req.params.id);
      const hscodeService =  new HSCodeService();
      const [total] = await hscodeService.deleteOne(id);
      if(total > 0) {
        return HttpResponse.sendMessage(res, httpStatus.OK, new HttpException(true, EMessage.DELETE_SUCCESS));
      } else {
        return HttpResponse.sendMessage(res, httpStatus.EXPECTATION_FAILED, new HttpException(true, EMessage.DELETE_FAIL));
      }
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][area][delete_one]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default InvoiceConditionController;