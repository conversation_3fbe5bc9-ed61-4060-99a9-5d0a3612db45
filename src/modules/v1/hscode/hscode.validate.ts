'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';

class HSCodeValidate {
  static create: any = Joi.object({
    hsCode: Joi.string().trim().required().max(50).messages({
      "any.required": `hsCode ${EMessage.REQUIRED}`,
      "string.empty": `hsCode ${EMessage.EMPTYED}`,
      "string.max": `hsCode ${EMessage.MAX_50}`,
    }),
    name: Joi.string().trim().messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
    }),
    importTaxValue: Joi.number().min(0).messages({
      "number.base": `importTaxValue ${EMessage.NUMBER}`,
      "number.min": `importTaxValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    importTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `invoiceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceCurrencyCode ${EMessage.MAX_10}`,
    }),
    VATValue: Joi.number().min(0).required().messages({
      "any.required": `VATValue ${EMessage.REQUIRED}`,
      "number.base": `VATValue ${EMessage.NUMBER}`,
      "number.min": `VATValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    VATCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATCode ${EMessage.EMPTYED}`,
      "string.max": `VATCode ${EMessage.MAX_10}`,
    }),
    specialConsumptionTaxValue: Joi.number().min(0).messages({
      "number.base": `specialConsumptionTaxValue ${EMessage.NUMBER}`,
      "number.min": `specialConsumptionTaxValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    specialConsumptionTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `specialConsumptionTaxCode ${EMessage.EMPTYED}`,
      "string.max": `specialConsumptionTaxCode ${EMessage.MAX_10}`,
    }),
    environmentTaxPrice: Joi.number().min(0).messages({
      "number.base": `VATValue ${EMessage.NUMBER}`,
      "number.min": `VATValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    environmentTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATCode ${EMessage.EMPTYED}`,
      "string.max": `VATCode ${EMessage.MAX_10}`,
    }),
  });

  static arrCreate: any = Joi.array().items(HSCodeValidate.create).unique().required();
}

export default HSCodeValidate;
