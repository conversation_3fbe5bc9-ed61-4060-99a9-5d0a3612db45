export enum DocumentTypeId {
  GiayPhep = 1,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>on,
  CO,
  <PERSON>ha<PERSON>
}

export const DocumentFolderName = new Map<number, string[]>([
  [DocumentTypeId.<PERSON><PERSON>yPhep, ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>']],
  [DocumentTypeId.HopDong, ['HopDong', 'HopDong']],
  [DocumentTypeId.HoaDon, ['HoaDon', 'INV']],
  [DocumentTypeId.VanDon, ['VanDon', 'HAWB']],
  [DocumentTypeId.CO, ['CO', 'CO']],
  [DocumentTypeId.Khac, ['Khac', 'Khac']]
]);

export enum DocumentStatusId {
  Moi = 1,
  UpFile,
  DaGui,
  ThanhCong1Phan,
  ThanhCong
}

export const DocumentStatusName = new Map<number, string>([
  [DocumentStatusId.Moi, 'Moi'],
  [DocumentStatusId.UpFile, 'Đã xuất file'],
  [DocumentStatusId.DaGui, 'Đã gửi V5'],
  [DocumentStatusId.ThanhCong1Phan, 'Thành công 1 phần'],
  [DocumentStatusId.ThanhCong, 'Thành công']
]);
