import Axios from 'axios';
import moment from 'moment';
import { json2xml, xml2json } from 'xml-js';
import Configure from '../../../emuns/configures';
import { IResponze } from '../../../https/responze';
import { IDA } from '../../../models/ida.model';
import { Shipment } from '../../../models/shipment.model';
import { Terminal } from '../../../models/terminal.model';
import { Ftp } from '../../../util/ftp';
import { DocumentFolderName, DocumentStatusId, DocumentTypeId } from './autoV5.enum';
import fs from 'fs';
import _ from 'lodash';
import Utilities from '../../../util/utilities';
import { Hub } from '../../../models/hub.model';
import { RabbitSend } from '../../../util/rabbit';
import { Documents } from '../../../models/document.model';
import { Op, Sequelize } from 'sequelize';

export class AutoV5 {
  private folderV5 = Configure.FOLDER_V5_SEND as string;

  async send(data: any) {
    let { HAWB, documentTypeId, url, id } = data;

    try {
      let clearanceDocument = await Documents.findByPk(id);
      if (!clearanceDocument) {
        Utilities.sendTelegramNoti('AUTO_V5_SEND', `Không tìm thấy document ${HAWB}`);
        return false;
      }

      try {
        let ida: any = await IDA.findOne({ where: { HAWB, isDeleted: false }, include: [{ model: Hub, as: 'hub', attributes: ['name'] }], raw: true });
        if (!ida) {
          throw 'Không tìm thấy dữ liệu ' + HAWB;
        }

        let file = await Axios.get(url, { responseType: 'arraybuffer' });
        let fileSize = file.headers['content-length'];

        let fileSizeMB = fileSize / 1024 / 1024;

        if (fileSizeMB < 0.95) {
          let base64 = Buffer.from(file.data, 'binary').toString('base64');

          let generateXml = await this.generateXml(documentTypeId, ida, base64, data);
          if (generateXml.status == false) {
            throw generateXml.message.error;
          }

          let saveXml = await this.saveXml(documentTypeId, ida.HAWBClearance.substring(ida.HAWBClearance.length - 20), generateXml.message.data, 0);
          if (saveXml.status == false) {
            throw saveXml.message.error;
          }

          let sendV5 = await this.sendV5(ida.ftpId, saveXml.message.data, ida['hub.name']);
          if (sendV5.status == false) {
            throw sendV5.message.error;
          }

          fs.unlinkSync(`${this.folderV5}/${saveXml.message.data}`);
        }
      } catch (error) {
        clearanceDocument.errorTimes = clearanceDocument.errorTimes + 1;
        clearanceDocument.messageError = 'Gửi file thất bại: ' + error;

        await clearanceDocument.save();
      }
    } catch (error) {
      Utilities.sendTelegramNoti('AUTO_V5_SEND', error.message);
      return false;
    }
  }

  private async generateXml(documentTypeId: DocumentTypeId, ida: any, base64: string, data: any): Promise<IResponze> {
    try {
      let hawbClearance = ida.HAWBClearance.substring(ida.HAWBClearance.length - 20);
      let fileType = data.url.substr(_.lastIndexOf(data.url, '.'), data.length);

      let documentFolderName = DocumentFolderName.get(documentTypeId) || [];
      if (documentFolderName.length <= 0) {
        return { status: false, message: { error: 'Không tìm thấy loại chứng từ' } };
      }

      let jsonXml: any = {
        ShipmentID: hawbClearance,
        AdditionalDocuments: {
          AdditionalDocument: {
            Reference: '',
            Description: '',
            Issue: '',
            IssueLocation: '',
            Category: '',
            Expire: '',
            Issuer: '',
            Stype: '',
            TotalValue: '',
            Representative: '',
            TransitLocation: '',
            Content: '',
            AttachedFile: {
              FileName: `${hawbClearance}_${data.sendTimes}_${documentFolderName[1].toUpperCase()}${fileType}`,
              Content: base64
            }
          }
        }
      };

      let invoiceData = data.invoiceData;

      if (!invoiceData) {
        let shipment = await Shipment.findOne({ where: { MAWB: ida.MAWB, isDeleted: false }, attributes: ['dateFrom'] });
        if (!shipment) {
          return { status: false, message: { error: 'Không tìm thấy shipment' } };
        }

        invoiceData = shipment.dateFrom;
      }

      let invoiceDataFormat = moment(invoiceData).format('DD/MM/YYYY');
      jsonXml.AdditionalDocuments.AdditionalDocument.Issue = invoiceDataFormat;

      if (documentTypeId == DocumentTypeId.HoaDon) {
        jsonXml.AdditionalDocuments.AdditionalDocument.Reference = ida.invoiceNo;
      } else if (documentTypeId == DocumentTypeId.VanDon) {
        jsonXml.AdditionalDocuments.AdditionalDocument.Reference = ida.HAWBClearance;
        jsonXml.AdditionalDocuments.AdditionalDocument.IssueLocation = ida.countryCode;

        let meansOfTransportationCode = 9;
        if (ida.meansOfTransportationCode == 1) {
          meansOfTransportationCode = 2;
        } else if (ida.meansOfTransportationCode == 2 || ida.meansOfTransportationCode == 3) {
          meansOfTransportationCode = 1;
        } else if (ida.meansOfTransportationCode == 4) {
          meansOfTransportationCode = 3;
        } else if (ida.meansOfTransportationCode == 5) {
          meansOfTransportationCode = 4;
        }

        jsonXml.AdditionalDocuments.AdditionalDocument.Category = meansOfTransportationCode;
      }

      let xml = `<?xml version="1.0" encoding="utf-8"?><Root>${json2xml(JSON.stringify(jsonXml), { compact: true, ignoreComment: true })}</Root>`;

      return { status: true, message: { data: xml } };
    } catch (error) {
      Utilities.sendTelegramNoti('AUTO_V5_GENERATER', error.message);
      return { status: false, message: { error: error.message } };
    }
  }

  private async saveXml(documentTypeId: DocumentTypeId, hawbClearance: string, data: string, fileNo: number): Promise<IResponze> {
    try {
      if (!fs.existsSync(this.folderV5)) {
        fs.mkdirSync(this.folderV5, { recursive: true });
      }

      let documentFolderName = DocumentFolderName.get(documentTypeId) || [];
      if (documentFolderName.length <= 0) {
        return { status: false, message: { error: 'Không tìm thấy loại chứng từ' } };
      }

      let fileName = fileNo == 0 ? hawbClearance : hawbClearance + `_${_.padStart(fileNo.toString(), 3)}`;
      fileName += `_${documentFolderName[0].toUpperCase()}`;
      fileName += '.xml';

      fs.writeFileSync(`${this.folderV5}/${fileName}`, data);

      return { status: true, message: { data: `${fileName}` } };
    } catch (error) {
      Utilities.sendTelegramNoti('AUTO_V5_SAVE_XML', error.message);
      return { status: false, message: { error: error.message } };
    }
  }

  // V5: ECUS_AUTO_V5/HAN(SGN)/V5_CPN_ECUS
  private async sendV5(ftpId: number, file: string, hub: string): Promise<IResponze> {
    try {
      // let ftpUsername = process.env.FTP_USERNAME || '';
      // let ftpPassword = process.env.FTP_PASSWORD || '';

      let pathHost = 'ECUS_AUTO_V5';

      if (process.env.NODE_ENV != 'production') {
        pathHost = `ECUS_AUTO_V5_TEST`;
      }

      let terminal = await Terminal.findOne({ where: { id: ftpId }, attributes: { exclude: ['createdAt', 'updatedAt', 'deletedAt', 'isDeleted'] }, raw: true });
      if (!terminal) {
        return { status: false, message: { error: `Không tìm thấy terminal: ${ftpId}` } };
      }

      let ftp = new Ftp(terminal.host, terminal.username, terminal.password, terminal.port, terminal.isTLS);
      await ftp.connect();

      await ftp.uploadFile(pathHost, this.folderV5, file, `${hub}/V5_CPN_ECUS`);

      ftp.close();

      return { status: true, message: {} };
    } catch (error) {
      Utilities.sendTelegramNoti('AUTO_V5_SEND_FTP', error.message);
      return { status: false, message: { error: error.message } };
    }
  }

  async dowload() {
    if (!fs.existsSync(Configure.FOLDER_V5_RECEIVE)) {
      fs.mkdirSync(Configure.FOLDER_V5_RECEIVE, { recursive: true });
    }

    let pathHost = 'ECUS_AUTO_V5';

    if (process.env.NODE_ENV != 'production') {
      pathHost = `ECUS_AUTO_V5_TEST`;
    }

    let terminals = await Terminal.findAll({ attributes: ['id', 'username', 'password', 'host', 'port', 'isTLS'], where: { isDeleted: false } });

    let terminalGroup = _.chain(terminals)
      .groupBy((x) => `${x.host};${x.port};${x.username};${x.password};${x.isTLS}`)
      .value();

    // let ftpUsername = process.env.FTP_USERNAME || '';
    // let ftpPassword = process.env.FTP_PASSWORD || '';

    let subFolers = ['HAN', 'SGN'];

    for (const key in terminalGroup) {
      try {
        let connectString = key.split(';');
        let ftp = new Ftp(connectString[0], connectString[2], connectString[3], parseInt(connectString[1]), connectString[4] == 'true' ? true : false);
        await ftp.connect();

        for (let idx = 0; idx < subFolers.length; idx++) {
          let subFoler = subFolers[idx];

          let pathHostFile = `${pathHost}/${subFoler}`;

          let listFileFTP = await ftp.listFile(pathHostFile, 'V5_ECUS_CPN');

          if (listFileFTP.length > 0) {
            await ftp.downloadToDir(`${pathHostFile}`, Configure.FOLDER_V5_RECEIVE, 'V5_ECUS_CPN');

            for (let idx = 0; idx < listFileFTP.length; idx++) {
              const fileName = listFileFTP[idx].name;

              try {
                let file = await ftp.checkFile(`${pathHostFile}/V5_ECUS_CPN`, fileName);

                if (file) {
                  let folderBackup = `BACKUP_TMS/${moment().format('YYYY/MM/DD')}`;

                  try {
                    await ftp.createFolder(`${pathHostFile}/${folderBackup}`);
                  } catch (error) {}

                  await ftp.move(`${pathHostFile}/V5_ECUS_CPN`, `${pathHostFile}/${folderBackup}`, fileName);
                }
              } catch (error) {
                console.log('BACKUP_FILE: ', error);
              }
            }
          }
        }

        ftp.close();
      } catch (error) {
        console.log('AUTO_V5_DOWLOAD: ', error);
      }
    }

    let list = fs.readdirSync(Configure.FOLDER_V5_RECEIVE);

    if (list.length > 0) {
      let queueName = `${Configure.RABBIT_RECEIVE_V5}`;
      if (process.env.NODE_ENV === 'local') {
        queueName = `${Configure.RABBIT_RECEIVE_V5}_local`;
      }

      await new RabbitSend().send(queueName, _.chunk(list, 10), { durable: false, autoDelete: true });
    }
  }

  async save(fileNames: string[]) {
    try {
      let hawbClearances = [];

      for (let idx = 0; idx < fileNames.length; idx++) {
        let fileName = fileNames[idx];

        let filePath = Configure.FOLDER_V5_RECEIVE + fileName;

        let hawbClearance = fileName.substring(0, fileName.indexOf('_'));
        let documentFolderName = fileName.substring(fileName.indexOf('_', hawbClearance.length + 1) + 1, fileName.indexOf('.'));
        let documentTypeId = DocumentTypeId.Khac;

        for (let [key, value] of DocumentFolderName) {
          if (value[0].toUpperCase() == documentFolderName.toUpperCase()) {
            documentTypeId = key;
            break;
          }
        }

        let dataXml = fs.readFileSync(filePath, { encoding: 'utf8' });

        let dataJson: any = JSON.parse(xml2json(dataXml, { compact: true, ignoreAttributes: true }));

        hawbClearances.push(hawbClearance);

        await Documents.update(
          {
            successTimes: Sequelize.literal(`"successTimes" + 1`),
            statusId: DocumentStatusId.ThanhCong,
            SoTN: dataJson.Root.SoTN._text,
            NgayTN: moment(_.trim(dataJson.Root.NgayTN._text), ['DD/MM/YYYY', 'D/M/YYYY']).format(Configure.DAY_TIME)
          },
          { where: { HAWBClearance: { [Op.like]: `%${hawbClearance}` }, documentTypeId, statusId: { [Op.ne]: DocumentStatusId.ThanhCong } } }
        );

        try {
          fs.unlinkSync(`${filePath}`);
        } catch (error) {}
      }

      let documentPending = await Documents.findAll({
        attributes: ['HAWBClearance', [Sequelize.literal('COUNT(*)'), 'count']],
        where: { statusId: DocumentStatusId.DaGui },
        group: ['HAWBClearance'],
        raw: true
      });

      let hawbClearancePending = _.map(documentPending, 'HAWBClearance');
      let hawbSuccess = _.difference(hawbClearances, hawbClearancePending);

      for (let idx = 0; idx < documentPending.length; idx++) {
        let document: any = documentPending[idx];
        if (document.count == 0) {
          await IDA.update({ actionSendV5: DocumentStatusId.ThanhCong }, { where: { HAWBClearance: document.HAWBClearance }, fields: ['actionSendV5'] });
        }
      }

      await IDA.update({ actionSendV5: DocumentStatusId.ThanhCong }, { where: { HAWBClearance: hawbSuccess }, fields: ['actionSendV5'] });
    } catch (error) {
      Utilities.sendTelegramNoti('AUTO_V5_DOWLOAD_SAVE_FTP', error.message);
      return false;
    }
  }
}
