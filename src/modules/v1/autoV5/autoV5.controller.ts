'use strict';

import * as express from 'express';
import { Request, Response } from 'express';
import EConfigure from '../../../emuns/configures';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import HttpData from '../../../https/data';
import { IDA } from '../../../models/ida.model';
import multer from 'multer';
import _ from 'lodash';
import { Documents } from '../../../models/document.model';
import { DocumentFolderName, DocumentStatusId } from './autoV5.enum';
import { MiniOUpload } from '../../../util/upload.minio';
import Configure from '../../../emuns/configures';
import { Hub } from '../../../models/hub.model';
import Meta from '../../../https/meta';
import Optional from '../../../parser/optional';
import ParserUrl from '../../../parser/url';
import AutoV5Service from './autoV5.service';
import { Op, Sequelize } from 'sequelize';
import { RabbitSend } from '../../../util/rabbit';

class AutoV5Controller {
  public path = '/autoV5';
  public router = express.Router();
  public bucketName = process.env.MINIO_BUCKET_ECUS_V5 || 'ecusv5';

  constructor() {
    const upload = multer();

    this.router.get(`${this.path}/document`, this.listDocument);
    this.router.post(`${this.path}/upload`, upload.any(), this.uploadDocument);
    this.router.post(`${this.path}/send`, this.sendDocument);
  }

  private async listDocument(req: Request, res: Response): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();

      const autoV5Service = new AutoV5Service();
      const [data, total] = await autoV5Service.getAll(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());

      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_all_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async uploadDocument(req: Request, res: Response): Promise<any> {
    try {
      let { documentTypeId, HAWBClearances } = req.body;
      let files: any = req.files;

      let documentFolderName = DocumentFolderName.get(parseInt(documentTypeId)) || [];
      if (documentFolderName.length <= 0) {
        return HttpResponse.sendData(res, httpStatus.BAD_REQUEST, new HttpData(true, `Sai loại chứng từ`));
      }

      let idas = await IDA.findAll({
        where: { HAWBClearance: HAWBClearances, isDeleted: false },
        attributes: ['HAWB', 'HAWBClearance', 'hubId'],
        include: [{ model: Hub, as: 'hub', attributes: ['name'] }]
      });
      if (idas.length <= 0) {
        return HttpResponse.sendData(res, httpStatus.BAD_REQUEST, new HttpData(true, 'Không tìm thấy IDA'));
      }

      let minioUpload = new MiniOUpload();
      let makeBucket = await minioUpload.makeBucket(new AutoV5Controller().bucketName);
      if (makeBucket.status == false) {
        return HttpResponse.sendData(res, httpStatus.BAD_REQUEST, new HttpData(true, makeBucket.message.error));
      }

      let HawbFiles = [];
      let errorNotFound = [];
      let errorUploadFail = [];

      for (let idx = 0; idx < files.length; idx++) {
        let file = files[idx];

        let HAWBClearance = (file.originalname as string).substr(0, file.originalname.indexOf('_'));

        let ida = _.find(idas, (x) => x.HAWBClearance == HAWBClearance);
        if (!ida) {
          errorNotFound.push(HAWBClearance);
          continue;
        }

        let pathFileName = `${ida.hub.name}/${documentFolderName[1]}/${moment().format(Configure.DAY_TIME)}/${file.originalname}`;

        let upload = await minioUpload.uploadBuffer(new AutoV5Controller().bucketName, pathFileName, file);
        if (upload.status == false) {
          errorUploadFail.push(HAWBClearance);
          continue;
        }

        let documents = await Documents.findOrBuild({ where: { HAWB: ida.HAWB, documentTypeId }, defaults: { HAWB: ida.HAWB, documentTypeId, HAWBClearance: ida.HAWBClearance } });

        let document = documents[0];
        document.url = `${process.env.MINIO_STATICDOMAIN || ''}/${new AutoV5Controller().bucketName}/${pathFileName}`;
        document.statusId = DocumentStatusId.UpFile;

        await document.save();

        HawbFiles.push(ida.HAWB);
      }

      await IDA.update({ actionSendV5: DocumentStatusId.UpFile }, { where: { HAWB: HawbFiles } });

      let error = '';
      if (errorNotFound.length > 0) {
        error += `Không tìm thấy manifest: ${errorNotFound.join(', ')}. `;
      }
      if (errorUploadFail.length > 0) {
        error += `Xuất file thất bại: ${errorUploadFail.join(', ')}.`;
      }

      if (error) {
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, error));
      }

      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(true, error));
    } catch (error) {
      console.log(' --- [%s] [SEND_AUTO_V5]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async sendDocument(req: Request, res: Response): Promise<any> {
    try {
      let { hawbs, documentTypeIds } = req.body;

      let ida = await IDA.findAll({ attributes: ['HAWB'], where: { HAWB: hawbs, declarationNo: { [Op.not]: null } } });
      if (ida.length != hawbs.length) {
        return HttpResponse.sendMessage(res, httpStatus.NOT_FOUND, new HttpException(false, 'Không tìm thấy IDA hoặc IDA chưa có số tờ khai'));
      }

      let documents = await Documents.findAll({ attributes: ['id', 'HAWB', 'documentTypeId', 'url', 'statusId', 'sendTimes'], where: { HAWB: hawbs, documentTypeId: documentTypeIds }, raw: true });

      if (documents.length <= 0) {
        return HttpResponse.sendMessage(res, httpStatus.NOT_FOUND, new HttpException(false, 'Không tìm thấy chứng từ'));
      }

      await Documents.update({ statusId: DocumentStatusId.DaGui, sendTimes: Sequelize.literal(`"sendTimes" + 1`), messageError: null }, { where: { HAWB: hawbs, documentTypeId: documentTypeIds } });
      await IDA.update({ actionSendV5: DocumentStatusId.DaGui }, { where: { HAWB: hawbs } });

      let queueName = Configure.RABBIT_SEND_V5 as string;

      if (process.env.NODE_ENV === 'local') {
        queueName = `${Configure.RABBIT_SEND_V5}_local`;
      }

      await new RabbitSend().send(queueName, documents, { durable: false, autoDelete: true });

      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(true, true));
    } catch (error) {
      console.log(' --- [%s] [SEND_AUTO_V5]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default AutoV5Controller;
