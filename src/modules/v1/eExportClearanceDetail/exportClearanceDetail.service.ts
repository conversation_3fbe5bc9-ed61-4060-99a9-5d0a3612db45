'use strict';


import BaseService from '../../index.service';
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import { Database } from "../../../database";
import { IResponze } from "../../../https/responze";
import { ActionKey } from "../../../emuns/action";
import { eIExportDetail, eMEC, IHSCodeDetail } from "../../../models/index.model";
import Utilities from "../../../util/utilities";
import HSCodeDetailRepository from "../hscodeDetail/hscodeDetail.reporsitory";
import HSCodeRepository from "../hscode/hscode.reporsitory";
import { QueryTypes } from 'sequelize';
import eMECDetailRepository from './exportClearanceDetail.reporsitory';
import eMECRepository from '../eExportClearance/eMECClearance.reporsitory';

class ExportClearanceDetailService extends BaseService {
  private eMECDetailRepository: eMECDetailRepository;
  private hsCodeDetailRepository: HSCodeDetailRepository;
  private eMECRepository: eMECRepository;
  private hsCodeRepository: HSCodeRepository;
  private _sequelize: any;
  constructor() {
    super(new eMECDetailRepository());
    this.eMECDetailRepository = new eMECDetailRepository();
    this.hsCodeDetailRepository = new HSCodeDetailRepository();
    this.eMECRepository = new eMECRepository();
    this.hsCodeRepository = new HSCodeRepository();
    this._sequelize = Database.Sequelize;
  }

  public async nameExisted(nameExistes: any[]): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      const checked: Map<string, any> = new Map();
      await Promise.all(nameExistes.map(async (nameExist: any) => {
        let checkName: any = { ...nameExist };
        let nameItemCheck: string = checkName['itemName'];
        if (!checked.has(nameItemCheck)) {
          checkName['itemNameVN'] = null;
          checkName['hsCode'] = null;
          const hsCodeDetailOptional: Optional = new Optional();
          hsCodeDetailOptional.setWhere([
            new Where(EConfigure.AND, 'name', EConfigure.EQUAL, nameItemCheck)
          ]);
          const hsCodeDetail: IHSCodeDetail = await this.hsCodeDetailRepository.getOneOptional(hsCodeDetailOptional);
          if (hsCodeDetail) {
            checkName['itemNameVN'] = hsCodeDetail['nameVN'];
            checkName['hsCode'] = hsCodeDetail['hsCode'];
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.FOUND;
            checked.set(nameItemCheck, hsCodeDetail);
          } else {
            checked.set(nameItemCheck, null);
          }
        } else {
          const cached: any = checkName.get(nameItemCheck);
          if (cached) {
            checkName['itemNameVN'] = cached['nameVN'];
            checkName['hsCode'] = cached['hsCode'];
          }
        }
        reponze['message']['data'].push(checkName);
      }));
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][exportDetail][nameExisted]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async edaItemNameVN(exportManifestDetails: eIExportDetail[], employeeId: number): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      transaction = await this._sequelize.transaction();
      const HAWBs: Set<string> = new Set();
      await Promise.all(exportManifestDetails.map(async (exportManifestDetail: eIExportDetail) => {
        let cloneImportDetail: any = { ...exportManifestDetail };
        const HAWB: string = cloneImportDetail['HAWB'];
        const detailId: number = cloneImportDetail['id'];
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
        ]);
        const eda: eMEC = await this.eMECRepository.getOneOptional(optional);
        if (!eda['declarationNo'] || !eda['inspectionKindClassification'] || eda['phase'] !== ActionKey.ACCEPT_CLEARANCE) {
          HAWBs.add(cloneImportDetail[EConfigure.HAWB_FIELD]);
          let itemNameVN: string = Utilities.removeSpeialCharacter(cloneImportDetail['itemNameVN']);
          itemNameVN = Utilities.removeGeneralToken(itemNameVN);

          const checkHSCodeDetail: Optional = new Optional();
          checkHSCodeDetail.setWhere([
            new Where(EConfigure.AND, 'nameVN', EConfigure.EQUAL, itemNameVN),
            new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, cloneImportDetail['HSCode']),
            new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
          ]);
          const checkHSCode: Optional = new Optional();
          checkHSCode.setWhere([
            new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, cloneImportDetail['HSCode']),
            new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
          ]);
          const detailQuery: any = this.eMECDetailRepository.getOne(detailId);
          const hsCodeDetailQuery: any = await this.hsCodeDetailRepository.getOneOptional(checkHSCodeDetail);
          const hsCodeQuery: any = await this.hsCodeRepository.getOneOptional(checkHSCode);
          let [detail, hsCodeDetail, hsCode] = await Promise.all([detailQuery, hsCodeDetailQuery, hsCodeQuery])
          if (detail) {
            cloneImportDetail['itemNameVN'] = itemNameVN;
            cloneImportDetail['quantity1'] = detail['quantity1'];
            cloneImportDetail['priceVND'] = detail['priceVND'];
            if (hsCodeDetail) {
              cloneImportDetail['quantityUnitCode1'] = hsCodeDetail['unitCode'];
              cloneImportDetail['priceQuantityUnit'] = hsCodeDetail['unitCode'];
              const hsCodeDetailOptional: Optional = new Optional();
              hsCodeDetailOptional.setWhere([
                new Where(EConfigure.AND, 'id', EConfigure.EQUAL, hsCodeDetail['id'])
              ]);
              const updateHSCode: any = {
                'name': cloneImportDetail['itemName']
              }
              await this.hsCodeDetailRepository.updateDataTrx(updateHSCode, hsCodeDetailOptional, transaction);
            }
            const importDetailOptional: Optional = new Optional();
            importDetailOptional.setWhere([
              new Where(EConfigure.AND, 'id', EConfigure.EQUAL, cloneImportDetail['id'])
            ]);
            cloneImportDetail = Utilities.taxAndCollection(cloneImportDetail, hsCode);
            if (cloneImportDetail['HSCode'] && (cloneImportDetail['HSCode'].substring(0, 2) == 85 || cloneImportDetail['HSCode'].substring(0, 2) == 84)) {
              cloneImportDetail['otherLawCode'] = EConfigure.MO;
            }
            await this.eMECDetailRepository.updateDataTrx(cloneImportDetail, importDetailOptional, transaction);
            // const nameVNTransaction: IUpdateNameVN = {
            //   'HAWB': HAWB,
            //   'action': ActionKey.TRANSLATE_PRODUCT_NAME,
            //   'detailId': detailId,
            //   'employeeId': employeeId,
            //   'newData': cloneImportDetail
            // }
            // await this.importTransactionRepositry.createDataTrx(nameVNTransaction, transaction);
          }
        }
      }));
      await transaction.commit();
      let upatedHAWBs: string[] = Array.from(HAWBs.values());
      this.checkUpdateItemNameVN(upatedHAWBs);
      this.updateTotalTax(upatedHAWBs);
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][exportDetail][edaItemNameVN]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async updateTotalTax(HAWBs: string[]): Promise<any> {
    await Promise.all(HAWBs.map((HAWB: string) => {
      this._sequelize.query(`
        update "clearance_emecs" 
        set
          "totalExportTax" = Round("sumTax"."exportTax"),
          "totalVATTax" = Round("sumTax"."VATTax"),
          "totalEnvironmentTax" = Round("sumTax"."environmentTax"),
          "totalSpecialConsumptionTax" = Round("sumTax"."specialConsumptionTax"),
          "totalTax" = Round("sumTax"."totalTax")
        from
          (
            select
              sum("exportPrice") as "exportTax" ,
              sum("VATPrice") as "VATTax",
              sum("environmentPrice") as "environmentTax",
              sum("specialConsumptionPrice") as "specialConsumptionTax",
              sum("exportPrice" + "VATPrice" + "environmentPrice" + "specialConsumptionPrice") as "totalTax"
            from
              public.clearance_eexport_details
            where
              "HAWB" in ('${HAWB}')
          ) AS "sumTax"
        where
          "HAWB" in ('${HAWB}')
        `, { 'type': QueryTypes.UPDATE });
    }));
  }

  public async checkUpdateItemNameVN(upatedHAWBs: string[]) {
    if (upatedHAWBs.length > 0) {
      let itemNameVNNull: string[] = [];
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, upatedHAWBs.join()),
        new Where(EConfigure.OR, 'itemNameVN', EConfigure.IS, null),
        new Where(EConfigure.OR, 'itemNameVN', EConfigure.EQUAL, '')
      ]);
      optional.setAttributes([EConfigure.HAWB_FIELD]);
      const updatedItemVNs: any[] = await this.eMECDetailRepository.queryAll(optional);
      if (updatedItemVNs.length > 0) {
        itemNameVNNull = updatedItemVNs.map((updatedItemVN: any) => {
          return updatedItemVN[EConfigure.HAWB_FIELD];
        });
        itemNameVNNull = [... new Set(itemNameVNNull)];
      }
      const updateImport: any = {
        'phase': ActionKey.UPDATE_CARGO_NAME
      }
      const exportOptional: Optional = new Optional();
      exportOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, upatedHAWBs.join()),
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.NOT_EQUAL, itemNameVNNull.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.OR, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.OR, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
      ]);
      await this.eMECRepository.updateData(updateImport, exportOptional);
    }
  }

  public async updateAutoItemNameVN(autoItemNames: eIExportDetail[], employeeId: number): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }

      const HAWBs: Set<string> = new Set();
      // let transactions: IUpdateNameVN[] = [];
      transaction = await this._sequelize.transaction();
      await Promise.all(autoItemNames.map(async (autoItemName: eIExportDetail) => {
        const cloneDetail: eIExportDetail = { ...autoItemName };
        const hscode: IHSCodeDetail = await Utilities.getHSCode(String(cloneDetail['itemName']));
        if (hscode) {
          HAWBs.add(String(cloneDetail[EConfigure.HAWB_FIELD]));
          const detailId: number = Number(cloneDetail['id']);
          const detailOptional: Optional = new Optional();
          detailOptional.setWhere([
            new Where(EConfigure.AND, 'id', EConfigure.EQUAL, detailId)
          ]);
          let detailUpdate: eIExportDetail = {
            'id': detailId,
            'HSCode': hscode['hsCode'],
            'itemNameVN': hscode['nameVN'],
            'quantityUnitCode1': hscode['unitCode'],
            'quantityUnitCode2': hscode['unitCode'],
          }
          detailUpdate = Utilities.taxAndCollection(detailUpdate, hscode);
          await this.eMECDetailRepository.updateDataTrx(detailUpdate, detailOptional, transaction);
          // const nameVNTransaction: IUpdateNameVN = {
          //   'HAWB': cloneDetail['HAWB'],
          //   'action': ActionKey.TRANSLATE_PRODUCT_NAME,
          //   'detailId': detailId,
          //   'employeeId': employeeId,
          //   'newData': cloneDetail
          // }
          // transactions.push(nameVNTransaction);
        }
      }));
      if (HAWBs.size > 0) {
        const updateImport: any = {
          'phase': ActionKey.UPDATE_CARGO_NAME
        }
        const importOptional: Optional = new Optional();
        importOptional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, Array.from(HAWBs.values()).join()),
          new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ]);
        await Promise.all([
          this.eMECRepository.updateDataTrx(updateImport, importOptional, transaction),
          // this.importClearanceDetailRepository.createBulkTrx(transactions, transaction),
        ]);
      }
      await transaction.commit();
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][exportDetail][updateAutoItemNameVN]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }
}

export default ExportClearanceDetailService;