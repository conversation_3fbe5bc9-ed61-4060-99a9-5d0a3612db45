'use strict';

import { Clearance } from '../../../xmlClearance/clearance';
import { Request, Response, NextFunction } from 'express';
import Middlewares from '../../../middleware/middlewares';
import HttpException from '../../../https/exception';
import EConfigure from '../../../emuns/configures';
import HttpResponse from '../../../https/response';
import HttpData from '../../../https/data';
import httpStatus from 'http-status';
import * as express from 'express';
import moment from 'moment';
import _ from 'lodash';

class xmlClearanceController {
  public path = '/xml/clearance';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();

    this.router.get(`${this.path}/file/pending`, this.filePending);
    this.router.get(`${this.path}/file/backup`, this.fileBackup);
    this.router.get(`${this.path}/file/read`, this.fileRead);

    this.router.put(`${this.path}/file/move/backup`, this.fileMoveBackup);
    this.router.put(`${this.path}/file/remove`, this.fileRemove);

    this.router.post(`${this.path}/test`, this.test);
  }

  private async test(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      await new Clearance().getTerminal();
      // let { serviceId, isLowValue, clearanceTypeId, hawbs } = req.body;
      // await new Clearance().push(serviceId, isLowValue, clearanceTypeId, hawbs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(true, null));
    } catch (error) {
      console.log(' --- [%s] [XML_CLEARANCE_TEST]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filePending(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let clearance = await new Clearance().getPendingFile();
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(clearance.isSuccess, clearance.data));
    } catch (error) {
      console.log(' --- [%s] [XML_CLEARANCE_PENDING_FILE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async fileBackup(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let clearance = await new Clearance().getBackupFile();
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(clearance.isSuccess, clearance.data));
    } catch (error) {
      console.log(' --- [%s] [XML_CLEARANCE_BACKUP_FILE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async fileRead(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { file } = req.query;

      let data = await new Clearance().readFile(file as string);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data.isSuccess, data.data));
    } catch (error) {
      console.log(' --- [%s] [XML_CLEARANCE_READ_FILE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async fileRemove(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { file } = req.body;

      await new Clearance().removeFile(file);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(true, true));
    } catch (error) {
      console.log(' --- [%s] [XML_CLEARANCE_REMOVE_FILE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async fileMoveBackup(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { file } = req.body;

      await new Clearance().moveBackupFile(file);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(true, true));
    } catch (error) {
      console.log(' --- [%s] [XML_CLEARANCE_REMOVE_FILE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default xmlClearanceController;
