'use strict';

import * as express from 'express';
import { Request, Response, NextFunction } from 'express';
import Middlewares from '../../../middleware/middlewares';
import ParserUrl from '../../../parser/url';
import ImportClearanceService from './importClearanceArchive.service';
import EConfigure from '../../../emuns/configures';
import Optional from '../../../parser/optional';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import Meta from '../../../https/meta';
import HttpData from '../../../https/data';
import ErrorValidate from '../../../util/error.validate';
import ImportClearanceValidate from '../importClearance/importClearance.validate';
import { IResponze } from '../../../https/responze';
import { IImportValidate } from '../../../models/mic.model';
import Utilities from '../../../util/utilities';
import RedisPromise from '../../../util/redis.promise';
import UserService from '../user/user.service';
import Where from '../../../parser/where';
import { Clearance } from '../../../xmlClearance/clearance';
import ClearanceCreateLogMongo from '../../../models/mongo/clearanceCreateLog.model';
import PdfGenerate from '../pdfGenerate/pdfGenerate.service';


class ImportClearanceArchiveController {
  public path = '/import_archives';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get(`${this.path}`, this.nonTaxCodeNumber);
    this.router.get(`${this.path}/mic`, this.getAllMIC);
    this.router.get(`${this.path}/ida`, this.getAllIDA);
    this.router.get(`${this.path}/getAll`, this.getAll);
    this.router.get(`${this.path}/master`, this.master);
    this.router.get(`${this.path}/managementGate`, this.managementGate);
    this.router.get(`${this.path}/totalManagementGate`, this.totalManagementGate);
    this.router.get(`${this.path}/exportManagementGate`, this.exportManagementGate);
    this.router.get(`${this.path}/clearanceReport`, this.clearanceReport);
    this.router.get(`${this.path}/clearanceReportCount`, this.clearanceReportCount);
    this.router.get(`${this.path}/clearanceReportExport`, this.clearanceReportExport);
    this.router.get(`${this.path}/holdReport`, this.getHoldReport);
    this.router.get(`${this.path}/importReport`, this.importReport);
    this.router.get(`${this.path}/holdManifest`, this.getHoldManifest);
    this.router.get(`${this.path}/externalBoxes`, this.externalBoxes);
    this.router.get(`${this.path}/storeWarehouse`, this.getStoreWarehouse);


    this.router.get(`${this.path}/mic/assignCargoName`, this.getMicAssignCargoName);
    this.router.get(`${this.path}/ida/assignCargoName`, this.getIdaAssignCargoName);
    this.router.get(`${this.path}/MAWBs`, this.getMAWBs);
    this.router.get(`${this.path}/mic/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneMIC);
    this.router.get(`${this.path}/ida/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneIDA);
    this.router.get(`${this.path}/MAWB/:MAWB`, this.getMAWB);

    this.router.get(`${this.path}/createLog`, this.createLog);
    this.router.get(`${this.path}/reportTax`, this.reportTax);

    this.router.post(`${this.path}/monitorGate`, this.monitorGate);
    this.router.post(`${this.path}/mic`, this.allMIC);
    this.router.post(`${this.path}/ida`, this.allIDA);
    this.router.post(`${this.path}/filterMIC`, this.filterMIC);
    this.router.post(`${this.path}/filterIDA`, this.filterIDA);
    this.router.post(`${this.path}/storeWarehouse`, this.saveWarehouse);
    this.router.post(`${this.path}/retryMIC`, middlewares.CheckPermission('put:v1:clearance.retry'), this.retryMIC);
    this.router.post(`${this.path}/retryIDA`, middlewares.CheckPermission('put:v1:clearance.retry'), this.retryIDA);

    this.router.post(`${this.path}/pdfPrint`, this.pdfPrint);

    this.router.post(`${this.path}/MICGroupBox`, this.MICGroupBox);
    this.router.post(`${this.path}/IDAGroupBox`, this.IDAGroupBox);
  }

  private async exportManagementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
        if(profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
        if(userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.exportManagementGate(optional, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][management_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async totalManagementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.totalManagementGate(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][management_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async pdfPrint(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ImportClearanceValidate.historyInvoice.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);

        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
        if(profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];

        }
        if(userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const pdfGenerate = new PdfGenerate();
      value = Utilities.convertNull(value);
      console.log(employeeId)
      const message: IResponze = await pdfGenerate.pdfPrint(value, hubs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][pdfPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async IDAGroupBox(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ImportClearanceValidate.groupBox.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      value = Utilities.convertNull(value);
      const data: IResponze = await importClearanceService.IDAGroupBox(value, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][IDAGroupBox]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async MICGroupBox(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ImportClearanceValidate.groupBox.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      value = Utilities.convertNull(value);
      const data: IResponze = await importClearanceService.MICGroupBox(value, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][MICGroupBox]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async externalBoxes(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.externalBoxes(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][externalBoxes]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMAWBs(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubTo', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubTo', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getMAWBs(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getMAWBs]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMAWB(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { MAWB } = req.params;
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getMAWB(MAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getMAWB]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async nonTaxCodeNumber(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const importClearanceService = new ImportClearanceService();
      importClearanceService.updateTaxCodeNumber(req.body.HAWB);
      return HttpResponse.sendData(res, httpStatus.OK,  new HttpData(true, true));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][nonTaxCodeNumber]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async reportTax(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.reportTax(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][reportTax]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async createLog(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const data: any = await ClearanceCreateLogMongo.getAll(req.query);
      const status: boolean = data.length > 0?true:false;
      return HttpResponse.sendData(res, httpStatus.OK,  new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getAllMICIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async retryIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const message: any = await new Clearance().getHawbData(value['HAWBs'], 'IDA');
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['isSuccess'], message['hawbSuccess'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][retryIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async retryMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const message: any = await new Clearance().getHawbData(value['HAWBs'], 'MIC');
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['isSuccess'], message['hawbSuccess'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][retryMIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getIdaAssignCargoName(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getIdaAssignCargoName(optional, employeeId);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getIdaAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);

      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMicAssignCargoName(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getMicAssignCargoName(optional, employeeId);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK,  data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getMicAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const data: IResponze = await importClearanceService.getAllMICIDA(optional);
      const meta = new Meta(limit, optional.getOffset(), data['message']['data']['total'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK,  data['message']['data']['manifests'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][getAllMICIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async importReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.importReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getStoreWarehouse(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getStoreWarehouse(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][store_warehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async saveWarehouse(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.storeWarehouse.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWB, address } = value;
      const data: IResponze = await importClearanceService.saveWarehouse(HAWB, address, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][storeWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getHoldReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getHoldReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getHoldManifest(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getHoldManifest(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReportExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReportExport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filterIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.filterIDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), data['message']['data']['totalManifest'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][filer_ida]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filterMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.filterMIC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), data['message']['data']['totalManifest'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][filer_mic]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async allMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getAllMIC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][all_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async allIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getAllIDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][all_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async monitorGate(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.HAWB.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWB } = value;
      const data: IResponze = await importClearanceService.monitorGate(HAWB, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][monitor_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAllMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const [data, total] = await importClearanceService.getAllMIC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_all_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAllIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getAllIDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_all_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: any = await importClearanceService.getOneMIC(HAWB, optional);
      let status: boolean = false;
      if(data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_one_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: any = await importClearanceService.getOneIDA(HAWB, optional);
      let status: boolean = false;
      if(data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_one_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReportCount(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReportCount(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report_count]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async master(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.master(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async managementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.managementGate(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][management_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default ImportClearanceArchiveController;
