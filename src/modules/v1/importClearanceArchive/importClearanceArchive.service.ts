'use strict';

import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import * as pdf from 'html-pdf';
import { Database } from '../../../database/index';
import ImportDetailRepository from "../importClearanceDetail/importClearanceDetail.reporsitory";
import { ClassifyName, ClassifyKey, ClassifyValidateName, ClassifyValidateKey } from "../../../emuns/classify";
import { IMIC, MIC } from "../../../models/mic.model";
import { ActionKey } from "../../../emuns/action";
import { IResponze } from "../../../https/responze";
import { IImportDetail, ImportDetail } from "../../../models/importDetail.model";
import { WeightClearance } from "../../../emuns/weight";
import Utilities from "../../../util/utilities";
import { Country, CustomerPersonal, CustomerBusiness, Hold, User, Order, IExportDetail, MEC, EDA } from "../../../models/index.model";
import MICRepository from "./MICClearanceArchive.reporsitory";
import IDARepository from './IDAClearanceArchive.repository';
import { IDA, IIDA } from '../../../models/ida.model';
import HSCodeDetailRepository from '../hscodeDetail/hscodeDetail.reporsitory';
import HSCodeRepository from '../hscodeDetail/hscodeDetail.reporsitory';
import moment from 'moment';
import { IWarehouseTransaction } from '../../../models/importTransaction.model';
import ImportTransactionRepository from '../importTransaction/importTransaction.reporsitory';
import _ from 'lodash';
import UserRepository from '../user/user.reporsitory';
import HubRepository from '../hub/hub.reporsitory';
import ClearanceCreateLogRepository from '../clearanceCreateLog/clearanceCreateLog.reporsitory';
import { RabbitSendInternal } from '../../../util/rabbit';
import HoldRepository from '../hold/hold.reporsitory';
import WarehouseRepository from '../warehouse/warehouse.reporsitory';
import ShipmentRepository from '../shipment/shipment.reporsitory';
import MECRepository from '../exportClearance/MECClearance.reporsitory';
import EDARepository from '../exportClearance/EDAClearance.reporsitory';
import HistoryMonitorRepository from '../historyMonitor/historyMonitor.reporsitory';
import ejs from 'ejs';
import * as path from 'path';
import { PrintKey } from '../../../emuns/print';
import Config from '../../../util/configure';
const ExcelJS = require('exceljs');

interface IClearanceCreateLog {
  clearanceCreateLogId?: number;
  data?: any;
  message?: string;
  cause?: any;
  processResult?: any;
  handleData?: any;
  type?: number;
  classify?: any;
  isSuccess?: boolean;
}

interface IStoreWarehouse {
  HAWB?: string;
  MAWB?: string;
  HAWBClearance?: string;
  warehouseAddress?: string;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
}

interface IManagement {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: number;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  bussiness?: CustomerBusiness;
  personal?: CustomerPersonal;
  importDetails?: ImportDetail[];
  isHold?: boolean;
  reasonHold?: Hold[];
  clientName?: string;
  dateGate?: string;
  weightRound?: number;
}

interface ICheckout {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  type?: string;
  isHold?: any;
  externalBoxName?: string;
  details?: IDetail[];
}

interface IDetail {
  itemNameVN: string;
  quantity: number;
}

interface IImportReport {
  trackingNo: any;
  MAWB: any;
  origin: any;
  country: any;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: any;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: any;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: any;
  consignorName: any
  consignorAddress: any;
  consignorCityName: any;
  consignorContactName: any;
  consignorTelephone: any;
  consigneeName: any;
  consigneeAddress: any;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: any;
  consigneeTelephone: any;
  remarks: any;
  status?: any;
  statusId: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;

  customerBusiness?: any;
  customerPersonal?: any;
  dateClearanced?: any;
  orderNumber?: any;
  itemIds?: any;
  itemCount?: any;
  station?: any;
  importerCode?: any;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
  order?: Order;
  taxNumberCode?: any;
  taxNumberDate?: any;

  details?: any;
  externalBoxName?: any;
}

interface IExportReport {
  trackingNo: string;
  MAWB: string;
  origin: string;
  country: string;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: string;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: number;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: string;
  consignorName: string
  consignorAddress: string;
  consignorCityName: any;
  consignorContactName: string;
  consignorTelephone: any;
  consigneeName: string;
  consigneeAddress: string;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: string;
  consigneeTelephone: string;
  remarks: any;
  status?: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;
  taxNumberCode?: any;
  taxNumberDate?: any;
  externalBoxName?: any;
}

interface IHold {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IImportDetail[];
  reasonDetails?: Hold[];
}

interface IGetAll {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IImportDetail[];
  inspectionKindTimes?: any;
  clearanceDeclarationTimes?: any;
  reasonDetails?: Hold[];
  isHold?: any;
  externalBoxName?: any;
  internalBoxName?: any;
  warehouseAddress?: any
}

interface IWebHookRequest {
  'orderIds': string[],
  'action': number,
  'clientId': number,
  'reasonDetailId': number | null,
  'stationOriginId': number | null,
  'stationDestinationId': number | null,
  'holdClearance': any | null
}

interface IMAWBReport {
  HAWB: string,
  HAWBClearance?: string,
  piece: number,
  weight: number,
  invoicePrice: number,
  invoiceCurrency: string,
  consignorFullName: string,
  consignorAddress: string,
  consigneeName: string,
  consigneeAddress: string,
  details: IImportDetail[] | IExportDetail[],
}

class ImportClearanceService extends BaseService {
  private micRepository: MICRepository;
  private idaRepository: IDARepository;
  private mecRepository: MECRepository;
  private edaRepository: EDARepository;
  private importDetailRepository: ImportDetailRepository;
  private hscodeDetailRepository: HSCodeDetailRepository;
  private hscodeRepository: HSCodeRepository;
  private importTransactionRepository: ImportTransactionRepository;
  private clearanceCreateLogRepository: ClearanceCreateLogRepository;
  private warehouseRepository: WarehouseRepository;
  private holdRepository: HoldRepository;
  private hubRepository: HubRepository;
  private shipmentRepository: ShipmentRepository;
  private historyMonitorRepository: HistoryMonitorRepository;
  private _sequelize: any;
  constructor() {
    super(new MICRepository());
    this.micRepository = new MICRepository();
    this.idaRepository = new IDARepository();
    this.mecRepository = new MECRepository();
    this.edaRepository = new EDARepository();
    this.importDetailRepository = new ImportDetailRepository();
    this.hscodeDetailRepository = new HSCodeDetailRepository();
    this.hscodeRepository = new HSCodeRepository();
    this.importTransactionRepository = new ImportTransactionRepository();
    this.clearanceCreateLogRepository = new ClearanceCreateLogRepository();
    this.warehouseRepository = new WarehouseRepository();
    this.holdRepository = new HoldRepository();
    this.hubRepository = new HubRepository();
    this.shipmentRepository = new ShipmentRepository();
    this.historyMonitorRepository = new HistoryMonitorRepository();
    this._sequelize = Database.Sequelize;
  }

  public async externalBoxes(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const masterExternalOptional: Optional = new Optional();
      masterExternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterExternalOptional.setAttributes(['externalBoxName']);
      masterExternalOptional.setGroup(['externalBoxName']);

      const masterInternalOptional: Optional = new Optional();
      masterInternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterInternalOptional.setAttributes(['internalBoxName']);
      masterInternalOptional.setGroup(['internalBoxName']);
      // ida
      const IDAExternalQuery = this.idaRepository.queryAll(masterExternalOptional);
      // mic
      const MICExternalQuery = this.micRepository.queryAll(masterExternalOptional);
      // ida
      const IDAInternalQuery = this.idaRepository.queryAll(masterInternalOptional);
      // mic
      const MICInternalQuery = this.micRepository.queryAll(masterInternalOptional);

      const [IDAExs, MICExs, IDAIns, MICIns] = await Promise.all([IDAExternalQuery, MICExternalQuery, IDAInternalQuery, MICInternalQuery]);
      if (IDAExs.length > 0 || MICExs.length > 0 || IDAIns.length > 0 || MICIns.length > 0) {
        let externalBoxes: string[] = [];
        externalBoxes = [...IDAExs, ...MICExs];
        if (externalBoxes.length > 0) {
          externalBoxes = [...new Set(externalBoxes.map((item: any) => item['externalBoxName']))];
        }

        let internalBoxes: string[] = [];
        internalBoxes = [...IDAExs, ...MICExs];
        if (externalBoxes.length > 0) {
          internalBoxes = [...new Set(internalBoxes.map((item: any) => item['internalBoxName']))];
        }

        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { externalBoxes, internalBoxes };
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][externalBoxes]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async reportTax(optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(
        new Where(EConfigure.AND, 'taxCodeNumber', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'dateTaxCodeNumber', EConfigure.NOT, null),
      );
      const objData = await this.idaRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][reportTax]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMIC(optional: Optional): Promise<any> {
    try {
      const objData = await this.micRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getAllMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllIDA(optional: Optional): Promise<any> {
    try {
      const objData = await this.idaRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getAllIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneMIC(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.micRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getOneMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneIDA(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.idaRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getOneIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async monitorGate(HAWB: string, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.OR, 'declarationNo', EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optional.setRelation(['importDetails', 'country']);
      const IDAQuery: any = this.idaRepository.getOneOptional(optional);
      const MICQuery: any = this.micRepository.getOneOptional(optional);
      const [IDA, MIC] = await Promise.all([IDAQuery, MICQuery]);
      let checkoutData: ICheckout = {};
      if (MIC) {
        const mic: IMIC = MIC;
        const details: any = mic['importDetails'];
        checkoutData = {
          'HAWB': mic[EConfigure.HAWB_CLEARANCE_FIELD],
          'MAWB': mic['MAWB'],
          'declarationType': String(EConfigure.MIC),
          'declarationNo': mic['declarationNo'],
          'clearanceInspection': String(mic['inspectionKindClassification']),
          'dateCheckout': mic['dateCheckout'],
          'dateCheckin': mic['dateCheckin'],
          'dateClearanced': mic['dateClearanced'],
          'cargoPiece': mic['cargoPiece'],
          'weight': mic['cargoWeight'],
          'classify': mic['classify'] as string,
          'country': mic['country'],
          'isHold': mic['isHold'],
        }
        if (details.length > 0) {
          checkoutData['details'] = details.map((detail: any) => {
            const importDetail: IDetail = {
              'itemNameVN': detail['itemNameVN'],
              'quantity': detail['quantity1']
            }
            return importDetail
          });
        }
        reponze['status'] = true;
        reponze['message'] = {
          'message': EMessage.FOUND,
          'data': checkoutData
        }
      }
      if (IDA) {
        const ida: IIDA = IDA;
        const details: any = ida['importDetails'];
        checkoutData = {
          'HAWB': ida[EConfigure.HAWB_CLEARANCE_FIELD],
          'MAWB': ida['MAWB'],
          'declarationType': 'IDA',
          'declarationNo': ida['declarationNo'],
          'clearanceInspection': ida['inspectionKindClassification'],
          'dateCheckout': ida['dateCheckout'],
          'dateCheckin': ida['dateCheckin'],
          'dateClearanced': ida['dateClearanced'],
          'cargoPiece': ida['cargoPiece'],
          'weight': ida['cargoWeightGross'],
          'classify': ida['classify'] as string,
          'country': ida['country'],
          'isHold': ida['isHold'],
        }
        if (details.length > 0) {
          checkoutData['details'] = details.map((detail: any) => {
            const importDetail: IDetail = {
              'itemNameVN': detail['itemNameVN'],
              'quantity': detail['quantity1']
            }
            return importDetail
          });
        }
        reponze['status'] = true;
        reponze['message'] = {
          'message': EMessage.FOUND,
          'data': checkoutData
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][monitorGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportCount(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'total': 0,
            'totalCheckin': 0,
            'totalCheckout': 0,
            'totalWarehouse': 0
          }
        }
      }
      const optionalCheckin: Optional = new Optional();
      optionalCheckin.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'dateCheckin', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalCheckout: Optional = new Optional();
      optionalCheckout.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'dateCheckout', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        // new Where(EConfigure.AND, 'dateCheckin', EConfigure.NOT, null),
      ]);

      // ida
      const IDATotalCheckinQuery = this.idaRepository.countData(optionalCheckin);
      const IDATotalCheckoutQuery = this.idaRepository.countData(optionalCheckout);
      const IDATotalQuery = this.idaRepository.countData(optionalTotal);
      // mic
      const MICTotalCheckinQuery = this.micRepository.countData(optionalCheckin);
      const MICTotalCheckoutQuery = this.micRepository.countData(optionalCheckout);
      const MICTotalQuery = this.micRepository.countData(optionalTotal);

      const [IDATotal, MICTotal, IDATotalCheckin, IDATotalCheckout, MICTotalCheckin, MICTotalCheckout] =
        await Promise.all([IDATotalQuery, MICTotalQuery, IDATotalCheckinQuery, IDATotalCheckoutQuery, MICTotalCheckinQuery, MICTotalCheckoutQuery]);
      const totalCheckin: number = Number(IDATotalCheckin) + Number(MICTotalCheckin);
      const totalCheckout: number = Number(IDATotalCheckout) + Number(MICTotalCheckout);
      const totalWarehouse: number = totalCheckin - totalCheckout
      const total: number = Number(IDATotal) + Number(MICTotal);

      reponze['status'] = true;
      reponze['message']['message'] = EMessage.FOUND;
      reponze['message']['data'] = {
        'totalCheckin': totalCheckin,
        'totalCheckout': totalCheckout,
        'totalWarehouse': (totalWarehouse > 0) ? totalWarehouse : 0,
        'total': total,
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][clearanceReportCount]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
          }
        }
      }

      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        // new Where(EConfigure.AND, 'dateCheckout', EConfigure.NOT, null),
        // new Where(EConfigure.AND, 'dateCheckin', EConfigure.NOT, null),
      ])

      // ida
      const IDAQuery = this.idaRepository.queryAll(optionalTotal);

      // mic
      const MICQuery = this.micRepository.queryAll(optionalTotal);

      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (IDAs.length > 0 || MICs.length > 0) {
        let micData: ICheckout[] = [];
        let idaData: ICheckout[] = [];
        let manifests: ICheckout[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IIDA) => {
            const ida: ICheckout = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': IDA['MAWB'],
              'declarationNo': IDA['declarationNo'],
              'clearanceInspection': IDA['inspectionKindClassification'],
              'dateCheckout': IDA['dateCheckout'],
              'dateCheckin': IDA['dateCheckin'],
              'dateClearanced': IDA['dateClearanced'],
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'classify': IDA['classify'],
              'type': String(EConfigure.IDA),
              'externalBoxName': IDA['externalBoxName'],
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: IMIC) => {
            const mic: ICheckout = {
              'HAWB': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': MIC['MAWB'],
              'declarationNo': MIC['declarationNo'],
              'clearanceInspection': String(MIC['inspectionKindClassification']),
              'dateCheckout': MIC['dateCheckout'],
              'dateCheckin': MIC['dateCheckin'],
              'dateClearanced': MIC['dateClearanced'],
              'cargoPiece': MIC['cargoPiece'],
              'weight': MIC['cargoWeight'],
              'classify': MIC['classify'],
              'type': String(EConfigure.MIC),
              'externalBoxName': MIC['externalBoxName'],
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': manifests.length,
          'manifests': manifests,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][clearanceReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async master(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const masterOptional: Optional = new Optional();
      masterOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'MAWB', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterOptional.setAttributes(['MAWB']);
      masterOptional.setGroup(['MAWB']);
      // ida
      const IDAQuery = this.idaRepository.queryAll(masterOptional);
      // mic
      const MICQuery = this.micRepository.queryAll(masterOptional);
      let masters: string[] = [];
      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (IDAs.length > 0 || MICs.length > 0) {
        masters = [...IDAs, ...MICs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][master]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async managementGate(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      let queryAll: number = 0;
      optional.getWhere().forEach((where: Where) => {
        if (where.getKey() === 'classify') {
          if (where.getValue() === ClassifyValidateName.get(ClassifyValidateKey.DOC)) {
            queryAll = 1;
          }
          if (where.getValue() === ClassifyValidateName.get(ClassifyValidateKey.MIC)) {
            where.setValue(ClassifyName.get(ClassifyKey.PAR));
            queryAll = 1;
          }
          if (where.getValue() === ClassifyValidateName.get(ClassifyValidateKey.COM)) {
            queryAll = 2;
          }
          if (where.getValue() === ClassifyValidateName.get(ClassifyValidateKey.IDA)) {
            where.setValue(ClassifyName.get(ClassifyKey.PAR));
            queryAll = 2;
          }
        }
      });
      optional.setRelation(['customerBusiness', 'customerPersonal', 'importDetails', 'holds']);
      let MICs: MIC[] = [];
      let IDAs: IDA[] = [];

      if (queryAll === 2) {
        IDAs = await this.idaRepository.getAll(optional);
      }
      if (queryAll === 1) {
        MICs = await this.micRepository.getAll(optional);
      }
      if (queryAll === 0) {
        const IDAQuery = this.idaRepository.queryAll(optional);
        const MICQuery = this.micRepository.queryAll(optional);
        [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      }
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IIDA) => {
            const ida: IManagement = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': IDA['MAWB'],
              'declarationNo': IDA['declarationNo'],
              'clearanceInspection': Number(IDA['inspectionKindClassification']),
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'dateCheckout': IDA['dateCheckout'],
              'dateCheckin': IDA['dateCheckin'],
              'dateClearanced': IDA['dateClearanced'],
              'bussiness': IDA['customerBusiness'],
              'personal': IDA['customerPersonal'],
              'declarationType': ClassifyValidateName.get(ClassifyValidateKey.IDA),
              'classify': IDA['classify'],
              'importDetails': IDA['importDetails'],
              'isHold': IDA['isHold'],
              'reasonHold': IDA['holds']
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: IMIC) => {
            const mic: IManagement = {
              'HAWB': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': MIC['MAWB'],
              'declarationNo': MIC['declarationNo'],
              'clearanceInspection': Number(MIC['inspectionKindClassification']),
              'dateCheckout': MIC['dateCheckout'],
              'dateCheckin': MIC['dateCheckin'],
              'dateClearanced': MIC['dateClearanced'],
              'cargoPiece': MIC['cargoPiece'],
              'weight': MIC['cargoWeight'],
              'bussiness': MIC['customerBusiness'],
              'personal': MIC['customerPersonal'],
              'declarationType': ClassifyValidateName.get(ClassifyValidateKey.MIC),
              'classify': MIC['classify'],
              'importDetails': MIC['importDetails'],
              'isHold': MIC['isHold'],
              'reasonHold': MIC['holds']
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][managementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filterMIC(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      // mic
      const MICQuery = this.micRepository.getAll(optional);
      const MICTotalCreateQuery = this.micRepository.countData(optionalCreate);
      const MICTotalUpdateCarageNameQuery = this.micRepository.countData(optionalUpdateCarageName);
      const MICTotalSendClearanceQuery = this.micRepository.countData(optionalSendClearance);
      const MICTotalEditClearanceQuery = this.micRepository.countData(optionalEditClearance);
      const MICTotalInspectionKindQuery = this.micRepository.countData(optionalInspectionKind);
      const MICTotalAcceptClearanceQuery = this.micRepository.countData(optionalAcceptClearance);
      const MICErrorQuery = this.micRepository.countData(optionalError);

      const [[MICs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError] =
        await Promise.all([MICQuery, MICTotalCreateQuery, MICTotalUpdateCarageNameQuery, MICTotalSendClearanceQuery, MICTotalEditClearanceQuery, MICTotalInspectionKindQuery,
          MICTotalAcceptClearanceQuery, MICErrorQuery]);
      if (MICs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': MICs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][filterMIC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filterIDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalRegisterTemp': 0,
            'totalSentTemp': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalRegisterTemp: Optional = new Optional();
      optionalRegisterTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalSentTemp: Optional = new Optional();
      optionalSentTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SENT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        ...optional.getWhere(),
      ]);
      // ida
      const IDAQuery = this.idaRepository.getAll(optional);
      const IDATotalCreateQuery = this.idaRepository.countData(optionalCreate);
      const IDATotalUpdateCarageNameQuery = this.idaRepository.countData(optionalUpdateCarageName);
      const IDATotalRegisterTempQuery = this.idaRepository.countData(optionalRegisterTemp);
      const IDATotalSentTempQuery = this.idaRepository.countData(optionalSentTemp);
      const IDATotalSendClearanceQuery = this.idaRepository.countData(optionalSendClearance);
      const IDATotalEditClearanceQuery = this.idaRepository.countData(optionalEditClearance);
      const IDATotalInspectionKindQuery = this.idaRepository.countData(optionalInspectionKind);
      const IDATotalAcceptClearanceQuery = this.idaRepository.countData(optionalAcceptClearance);
      const IDAErrorQuery = this.idaRepository.countData(optionalError);

      const [[EDAs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError, registerTemp, sentTemp] =
        await Promise.all([IDAQuery, IDATotalCreateQuery, IDATotalUpdateCarageNameQuery, IDATotalSendClearanceQuery, IDATotalEditClearanceQuery, IDATotalInspectionKindQuery,
          IDATotalAcceptClearanceQuery, IDAErrorQuery, IDATotalRegisterTempQuery, IDATotalSentTempQuery]);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': EDAs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalRegisterTemp': registerTemp,
          'totalSentTemp': sentTemp,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][filterIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportExport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }

      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        // new Where(EConfigure.AND, 'dateCheckin', EConfigure.NOT, null),
      ])
      optionalQuery.setRelation(['holds', 'manifest'])

      // ida
      const IDAQuery = this.idaRepository.queryAll(optionalQuery);
      // mic
      const MICQuery = this.micRepository.queryAll(optionalQuery);

      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (IDAs.length > 0 || MICs.length > 0) {
        let manifests: any[] = [];
        if (IDAs.length > 0) {
          IDAs.forEach((IDA: IIDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            const ida: IExportReport = {
              'trackingNo': String(IDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'MAWB': String(IDA['MAWB']),
              'origin': String(IDA['countryCode']),
              'country': String(IDA['countryCode']),
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': IDA['manifest'] ? IDA['manifest']['weightChargeKG'] : null,
              'manisfestImported': String(IDA['createdAt']),
              'arDate': String(IDA['dateCheckin']),
              'cdsDate': String(IDA['declarationPlannedDate']),
              'crDate': String(IDA['dateCheckout']),
              'cdsNo': String(IDA['declarationNo']),
              'importType': (IDA['classify'] === ClassifyName.get(ClassifyKey.PAR)) ? String(EConfigure.H11) : 'MẬU DỊCH',
              'lane': Number(IDA['inspectionKindClassification']),
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : IDA['phase_name']['vi'],
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'duty': fee,
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': String(EConfigure._H11),
              'consignorName': String(IDA['consignorName']),
              'consignorAddress': String(IDA['address1']),
              'consignorCityName': null,
              'consignorContactName': String(IDA['consignorName']),
              'consignorTelephone': null,
              'consigneeName': String(IDA['importerFullName']),
              'consigneeAddress': String(IDA['addressOfImporter']),
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': String(IDA['importerFullName']),
              'consigneeTelephone': String(IDA['telephoneNumberOfImporter']),
              'remarks': null,
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        if (MICs.length > 0) {
          MICs.forEach((MIC: IMIC) => {
            const mic: IExportReport = {
              'trackingNo': String(MIC[EConfigure.HAWB_CLEARANCE_FIELD]),
              'MAWB': String(MIC['MAWB']),
              'origin': String(MIC['countryCode']),
              'country': String(MIC['countryCode']),
              'pcs': Number(MIC['cargoPiece']),
              'trueWeight': Number(MIC['cargoWeight']),
              'volWeight': MIC['manifest'] ? MIC['manifest']['weightChargeKG'] : null,
              'manisfestImported': String(MIC['createdAt']),
              'arDate': String(MIC['dateCheckin']),
              'cdsDate': String(MIC['declarationPlannedDate']),
              'crDate': String(MIC['dateCheckout']),
              'cdsNo': String(MIC['declarationNo']),
              'importType': (MIC['classify'] === ClassifyName.get(ClassifyKey.PAR)) ? EConfigure.MIC : MIC['classify'],
              'lane': Number(MIC['inspectionKindClassification']),
              'customsStatus': (MIC['isHold'] && MIC['holds'] && MIC['holds'].length > 0) ? MIC['holds'][0]['name'] : MIC['phase_name']['vi'],
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MIC['totalInvoicePrice']),
              'importTax': 0,
              'VAT': 0,
              'duty': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': String(EConfigure._MIC),
              'consignorName': String(MIC['consignorName']),
              'consignorAddress': String(MIC['address1']),
              'consignorCityName': null,
              'consignorContactName': String(MIC['consignorName']),
              'consignorTelephone': null,
              'consigneeName': String(MIC['importerFullName']),
              'consigneeAddress': String(MIC['addressOfImporter']),
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': String(MIC['importerFullName']),
              'consigneeTelephone': String(MIC['telephoneNumberOfImporter']),
              'remarks': null,
              'taxNumberCode': null,
              'taxNumberDate': null,
              'externalBoxName': MIC['externalBoxName'],
            }
            manifests.push(mic);
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][clearanceReportExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }



  public async getHoldManifest(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.setRelation(['country', 'importDetailItems', 'holds']);
      const IDAQuery = this.idaRepository.queryAll(optional);
      const MICQuery = this.micRepository.queryAll(optional);

      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);


      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IIDA) => {
            const ida: IHold = {
              "HAWBClearance": IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": IDA[EConfigure.HAWB_FIELD],
              "declarationNo": IDA['declarationNo'],
              "MAWB": IDA['MAWB'],
              "invoiceValue": IDA['totalInvoicePrice'],
              "invoiceCurrency": IDA['invoiceCurrencyCode'],
              "priceVND": IDA['priceVND'],
              "country": IDA['country'],
              "cargoPiece": IDA['cargoPiece'],
              "weight": IDA['cargoWeightGross'],
              "massOfWeight": IDA['weightUnitCodeGross'],
              "inspectionKindClassification": IDA['inspectionKindClassification'],
              "phase": IDA['phase_name'],
              "classify": IDA['classify'],
              "type": String(EConfigure.IDA),
              "details": IDA['importDetailItems'],
              "reasonDetails": IDA['holds']
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: IMIC) => {
            const mic: IHold = {
              "HAWBClearance": MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": MIC[EConfigure.HAWB_FIELD],
              "declarationNo": MIC['declarationNo'],
              "MAWB": MIC['MAWB'],
              "invoiceValue": MIC['totalInvoicePrice'],
              "invoiceCurrency": MIC['invoiceCurrencyCode'],
              "priceVND": MIC['priceVND'],
              "country": MIC['country'],
              "cargoPiece": MIC['cargoPiece'],
              "weight": MIC['cargoWeight'],
              "massOfWeight": WeightClearance.get('kg'),
              "inspectionKindClassification": MIC['inspectionKindClassification'],
              "phase": MIC['phase_name'],
              "classify": MIC['classify'],
              "type": String(EConfigure.MIC),
              "details": MIC['importDetailItems'],
              "reasonDetails": MIC['holds']
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getHoldManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getHoldReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }

      const optionalHoldQuery: Optional = new Optional();
      optionalHoldQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      optionalHoldQuery.setRelation(['holds', 'manifest', 'importDetailItems', 'station', 'customerBusiness', 'customerPersonal']);

      const optionalCOMQuery: Optional = new Optional();
      optionalCOMQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      // ida
      const IDAHoldQuery = this.idaRepository.queryAll(optionalHoldQuery);
      const IDACOMQuery = this.idaRepository.queryAll(optionalCOMQuery);
      // mic
      const MICHoldQuery = this.micRepository.queryAll(optionalHoldQuery);

      const [holdIDAs, holdMICs, comIDAs] = await Promise.all([IDAHoldQuery, MICHoldQuery, IDACOMQuery]);
      if (holdIDAs.length > 0 || holdMICs.length > 0 || comIDAs.length > 0) {

        let manifests: any[] = [];
        if (comIDAs.length > 0) {
          comIDAs.forEach((IDA: IDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: ImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }
            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'cdsNoCustomer': IDA['declarationNoCustomer'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': EConfigure.COM,
              'customsStatus': null,
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),

              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'totalTax': IDA['totalTax'],
              'priceVND': IDA['priceVND'],

              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': 'Khác',
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': IDA['phase_name']['vi'],
              'currency': IDA['invoiceCurrencyCode'],

              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        if (holdIDAs.length > 0) {
          holdIDAs.forEach((IDA: IDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: ImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }
            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': EConfigure.HOLD,
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),

              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'totalTax': IDA['totalTax'],
              'priceVND': IDA['priceVND'],

              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H11,
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': EConfigure.HOLD,
              'cdsNoCustomer': null,
              'currency': IDA['invoiceCurrencyCode'],

              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
            }
            manifests.push(ida);
          });
        }
        if (holdMICs.length > 0) {
          holdMICs.forEach((MIC: MIC) => {
            let productId: any = [];
            let details: ImportDetail[] = MIC['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (MIC['manifest']) {
              orderNumber = MIC['manifest']['orderNumber']
              if (MIC['manifest']['originalOrderNumberClient']) {
                orderNumber = MIC['manifest']['originalOrderNumberClient']
              }
            }
            const mic: IImportReport = {
              'MAWB': MIC['MAWB'],
              'trackingNo': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': MIC['declarationNo'],
              'cdsNoCustomer': null,
              'lane': MIC['inspectionKindClassification'] ? String(MIC['inspectionKindClassification']).substring(0, 1) : null,
              'origin': MIC['countryCode'] ? `${MIC['countryCode']}${EConfigure.G}` : null,
              'country': MIC['countryCode'],
              'pcs': Number(MIC['cargoPiece']),
              'trueWeight': Number(MIC['cargoWeight']),
              'volWeight': Number(MIC['cargoWeight']),
              'manisfestImported': MIC['createdAt'],
              'arDate': MIC['dateCheckin'],
              'cdsDate': MIC['declarationPlannedDate'],
              'crDate': MIC['dateCheckout'],
              'importType': EConfigure.HOLD,
              'customsStatus': (MIC['holds'] && MIC['holds'].length > 0) ? MIC['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MIC['totalInvoicePrice']),
              'importTax': 0,
              'VAT': 0,
              'specialConsumptionTax': 0,
              'environmentTax': 0,
              'duty': 0,
              'totalTax': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'priceVND': MIC['priceVND'],
              'notes': EConfigure._MIC,
              'consignorName': MIC['consignorName'],
              'consignorAddress': `${MIC['address1']}${MIC['address2'] ? MIC['address2'] : ''}${MIC['address3'] ? MIC['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': MIC['consignorName'],
              'consignorTelephone': null,
              'consigneeName': MIC['importerFullName'],
              'consigneeAddress': MIC['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': MIC['importerFullName'],
              'consigneeTelephone': MIC['telephoneNumberOfImporter'],
              'remarks': null,
              'status': EConfigure.HOLD,
              'currency': MIC['invoiceCurrencyCode'],

              'customerBusiness': MIC['customerBusiness'],
              'customerPersonal': MIC['customerPersonal'],
              'dateClearanced': MIC['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': MIC['station'] ? MIC['station'] : null,
              'importerCode': MIC['importerCode'],
              'statusId': MIC['phase'],
              'warehouseCheckin': MIC['warehouseCheckin'],
              'warehouseCheckout': MIC['warehouseCheckout'],
              'taxNumberCode': null,
              'taxNumberDate': null,
              'externalBoxName': MIC['externalBoxName'],
            }
            manifests.push(mic);
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getHoldReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async saveWarehouse(HAWB: string, address: string, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const IDAQuery: any = this.idaRepository.getOneOptional(optional);
      const MICQuery: any = this.micRepository.getOneOptional(optional);
      const [IDA, MIC] = await Promise.all([IDAQuery, MICQuery]);
      const updateWarehouse: any = {
        'warehouseAddress': address,
        'warehouseCheckout': null
      }
      const now: string = moment().format(EConfigure.FULL_TIME);
      if (MIC) {
        const mic: MIC = MIC;
        // if(!mic['warehouseCheckin']) {
        updateWarehouse['warehouseCheckin'] = now;
        // }

        const [total] = await this.micRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          const transactionWarehouse: IWarehouseTransaction = {
            'HAWB': HAWB,
            'action': ActionKey.SAVE_WAREHOUSE,
            'employeeId': employeeId,
            'warehouseAddress': address,
          }
          await this.importTransactionRepository.createData(transactionWarehouse);
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }

      }
      if (IDA) {
        const ida: IDA = IDA;
        // if(!ida['warehouseCheckin']) {
        updateWarehouse['warehouseCheckin'] = now;
        // }
        const [total] = await this.idaRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          const transactionWarehouse: IWarehouseTransaction = {
            'HAWB': HAWB,
            'action': ActionKey.SAVE_WAREHOUSE,
            'employeeId': employeeId,
            'warehouseAddress': address,
          }
          await this.importTransactionRepository.createData(transactionWarehouse);
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][storeWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async importReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      optional.setRelation(['holds', 'manifest', 'importDetailItems', 'station', 'customerBusiness', 'customerPersonal', 'order']);
      const IDAQuery = this.idaRepository.queryAll(optional);
      const MICQuery = this.micRepository.queryAll(optional);
      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IDA) => {
            const importTax: number = IDA['totalImportTax'] ? Number(IDA['totalImportTax']) : 0;
            const VATTax: number = IDA['totalVATTax'] ? Number(IDA['totalVATTax']) : 0;
            const fee: number = IDA['feeClearance'] ? Number(IDA['feeClearance']) : 0;
            let productId: any = [];
            let details: ImportDetail[] = IDA['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (IDA['manifest']) {
              orderNumber = IDA['manifest']['orderNumber']
              if (IDA['manifest']['originalOrderNumberClient']) {
                orderNumber = IDA['manifest']['originalOrderNumberClient']
              }
            }

            const ida: IImportReport = {
              'MAWB': IDA['MAWB'],
              'trackingNo': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': IDA['declarationNo'],
              'lane': IDA['inspectionKindClassification'] ? String(IDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': IDA['countryCode'] ? `${IDA['countryCode']}${EConfigure.G}` : null,
              'country': IDA['countryCode'],
              'pcs': Number(IDA['cargoPiece']),
              'trueWeight': Number(IDA['cargoWeightGross']),
              'volWeight': Number(IDA['cargoWeightGross']),
              'manisfestImported': IDA['createdAt'],
              'arDate': IDA['dateCheckin'],
              'cdsDate': IDA['declarationPlannedDate'],
              'crDate': IDA['dateCheckout'],
              'importType': IDA['isHold'] ? EConfigure.HOLD : (IDA['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.H11 : EConfigure.COM),
              'customsStatus': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? IDA['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(IDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': IDA['totalSpecialConsumptionTax'],
              'environmentTax': IDA['totalEnvironmentTax'],
              'duty': fee,
              'priceVND': IDA['priceVND'],
              'totalTax': IDA['totalTax'],
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(IDA['totalEnvironmentTax']) + Number(IDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H11,
              'consignorName': IDA['consignorName'],
              'consignorAddress': `${IDA['address1']}${IDA['address2'] ? IDA['address2'] : ''}${IDA['address3'] ? IDA['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': IDA['consignorName'],
              'consignorTelephone': null,
              'consigneeName': IDA['importerFullName'],
              'consigneeAddress': IDA['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': IDA['importerFullName'],
              'consigneeTelephone': IDA['telephoneNumberOfImporter'],
              'remarks': null,
              'status': (IDA['isHold'] && IDA['holds'] && IDA['holds'].length > 0) ? EConfigure.HOLD : IDA['phase_name']['vi'],
              'cdsNoCustomer': null,
              'currency': IDA['invoiceCurrencyCode'],

              'customerBusiness': IDA['customerBusiness'],
              'customerPersonal': IDA['customerPersonal'],
              'dateClearanced': IDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': IDA['station'] ? IDA['station'] : null,
              'importerCode': IDA['importerCode'],
              'statusId': IDA['phase'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
              'order': IDA['order'],
              'taxNumberCode': IDA['taxCodeNumber'],
              'taxNumberDate': IDA['dateTaxCodeNumber'],
              'externalBoxName': IDA['externalBoxName'],
              'details': details,
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: MIC) => {
            let productId: any = [];
            let details: ImportDetail[] = MIC['importDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ImportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (MIC['manifest']) {
              orderNumber = MIC['manifest']['orderNumber']
              if (MIC['manifest']['originalOrderNumberClient']) {
                orderNumber = MIC['manifest']['originalOrderNumberClient']
              }
            }
            const mic: IImportReport = {
              'MAWB': MIC['MAWB'],
              'trackingNo': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': MIC['declarationNo'],
              'cdsNoCustomer': null,
              'lane': MIC['inspectionKindClassification'] ? String(MIC['inspectionKindClassification']).substring(0, 1) : null,
              'origin': MIC['countryCode'] ? `${MIC['countryCode']}${EConfigure.G}` : null,
              'country': MIC['countryCode'],
              'pcs': Number(MIC['cargoPiece']),
              'trueWeight': Number(MIC['cargoWeight']),
              'volWeight': Number(MIC['cargoWeight']),
              'manisfestImported': MIC['createdAt'],
              'arDate': MIC['dateCheckin'],
              'cdsDate': MIC['declarationPlannedDate'],
              'crDate': MIC['dateCheckout'],
              'importType': MIC['isHold'] ? EConfigure.HOLD : (MIC['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.MIC : MIC['classify']),
              'customsStatus': (MIC['isHold'] && MIC['holds'] && MIC['holds'].length > 0) ? MIC['holds'][0]['name'] : '',
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MIC['totalInvoicePrice']),
              'importTax': 0,
              'VAT': 0,
              'specialConsumptionTax': 0,
              'environmentTax': 0,
              'duty': 0,
              'priceVND': MIC['priceVND'],
              'totalTax': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': EConfigure._MIC,
              'consignorName': MIC['consignorName'],
              'consignorAddress': `${MIC['address1']}${MIC['address2'] ? MIC['address2'] : ''}${MIC['address3'] ? MIC['address3'] : ''}`,
              'consignorCityName': null,
              'consignorContactName': MIC['consignorName'],
              'consignorTelephone': null,
              'consigneeName': MIC['importerFullName'],
              'consigneeAddress': MIC['addressOfImporter'],
              'consigneeAddress2': null,
              'consigneeCityName': null,
              'consigneeContactName': MIC['importerFullName'],
              'consigneeTelephone': MIC['telephoneNumberOfImporter'],
              'remarks': null,
              'status': (MIC['isHold'] && MIC['holds'] && MIC['holds'].length > 0) ? EConfigure.HOLD : MIC['phase_name']['vi'],
              'currency': MIC['invoiceCurrencyCode'],

              'customerBusiness': MIC['customerBusiness'],
              'customerPersonal': MIC['customerPersonal'],
              'dateClearanced': MIC['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': MIC['station'] ? MIC['station'] : null,
              'importerCode': MIC['importerCode'],
              'statusId': MIC['phase'],
              'warehouseCheckin': MIC['warehouseCheckin'],
              'warehouseCheckout': MIC['warehouseCheckout'],
              'order': MIC['order'],
              'taxNumberCode': null,
              'taxNumberDate': null,
              'externalBoxName': MIC['externalBoxName'],
              'details': details,
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][importReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getStoreWarehouse(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      )
      const IDAQuery = this.idaRepository.queryAll(optional);
      const MICQuery = this.micRepository.queryAll(optional);
      const [IDAs, MICs] = await Promise.all([IDAQuery, MICQuery]);
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          idaData = IDAs.map((IDA: IDA) => {
            const ida: IStoreWarehouse = {
              'HAWBClearance': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'HAWB': IDA[EConfigure.HAWB_FIELD],
              'MAWB': IDA['MAWB'],
              'warehouseAddress': IDA['warehouseAddress'],
              'warehouseCheckin': IDA['warehouseCheckin'],
              'warehouseCheckout': IDA['warehouseCheckout'],
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          micData = MICs.map((MIC: MIC) => {
            const mic: IStoreWarehouse = {
              'HAWBClearance': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'HAWB': MIC[EConfigure.HAWB_FIELD],
              'MAWB': MIC['MAWB'],
              'warehouseAddress': MIC['warehouseAddress'],
              'warehouseCheckin': MIC['warehouseCheckin'],
              'warehouseCheckout': MIC['warehouseCheckout'],
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][managementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMICIDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': [{
            'manifests': [],
            'total': 0,
          }]
        }
      }
      const limit: number = optional.getLimit();
      let half: number = 0;
      if (limit > 0) {
        half = Number(limit / 2);
        optional.setLimit(half);
      }
      optional.setRelation(['country', 'importDetailItems', 'holds']);
      const IDAQuery = this.idaRepository.getAll(optional);
      const MICQuery = this.micRepository.getAll(optional);

      let [[IDAs, totalIDA], [MICs, totalMIC]] = await Promise.all([IDAQuery, MICQuery]);
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (IDAs.length > 0) {
          const total: number = MICs.length;
          if (half != total) {
            const remain: number = half + (half - total);
            optional.setLimit(remain);
            const addIDAs: IIDA[] = await this.idaRepository.queryAllPaging(optional);
            if (addIDAs.length > 0) {
              IDAs = addIDAs;
            }
          }
          idaData = IDAs.map((IDA: IIDA) => {
            const ida: IGetAll = {
              "HAWBClearance": IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": IDA[EConfigure.HAWB_FIELD],
              "declarationNo": IDA['declarationNo'],
              "MAWB": IDA['MAWB'],
              "invoiceValue": IDA['totalInvoicePrice'],
              "invoiceCurrency": IDA['invoiceCurrencyCode'],
              "priceVND": IDA['priceVND'],
              "country": IDA['country'],
              "cargoPiece": IDA['cargoPiece'],
              "weight": IDA['cargoWeightGross'],
              "massOfWeight": IDA['weightUnitCodeGross'],
              "inspectionKindClassification": IDA['inspectionKindClassification'],
              "phase": IDA['phase_name'],
              "classify": IDA['classify'],
              "type": String(EConfigure.IDA),
              "details": IDA['importDetailItems'],
              "reasonDetails": IDA['holds'],
              "inspectionKindTimes": IDA['inspectionKindTimes'],
              "clearanceDeclarationTimes": IDA['clearanceDeclarationTimes'],
              "isHold": IDA['isHold'],
              "externalBoxName": IDA['externalBoxName'],
              "internalBoxName": IDA['internalBoxName'],
              "warehouseAddress": IDA['warehouseAddress'],
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          const total: number = IDAs.length;
          if (half != total) {
            const remain: number = half + (half - total);
            optional.setLimit(remain);
            const addMICs: IMIC[] = await this.micRepository.queryAllPaging(optional);
            if (addMICs.length > 0) {
              MICs = addMICs;
            }
          }
          micData = MICs.map((MIC: IMIC) => {
            const mic: IGetAll = {
              "HAWBClearance": MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": MIC[EConfigure.HAWB_FIELD],
              "declarationNo": MIC['declarationNo'],
              "MAWB": MIC['MAWB'],
              "invoiceValue": MIC['totalInvoicePrice'],
              "invoiceCurrency": MIC['invoiceCurrencyCode'],
              "priceVND": MIC['priceVND'],
              "country": MIC['country'],
              "cargoPiece": MIC['cargoPiece'],
              "weight": MIC['cargoWeight'],
              "massOfWeight": WeightClearance.get('kg'),
              "inspectionKindClassification": MIC['inspectionKindClassification'],
              "phase": MIC['phase_name'],
              "classify": MIC['classify'],
              "type": String(EConfigure.MIC),
              "details": MIC['importDetailItems'],
              "reasonDetails": MIC['holds'],
              "inspectionKindTimes": MIC['inspectionKindTimes'],
              "clearanceDeclarationTimes": MIC['clearanceDeclarationTimes'],
              "isHold": MIC['isHold'],
              "externalBoxName": MIC['externalBoxName'],
              "internalBoxName": MIC['internalBoxName'],
              "warehouseAddress": MIC['warehouseAddress'],
            }
            return mic;
          });
        }
        manifests = [...micData, ...idaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': totalIDA + totalMIC
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getAllMICIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMicAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.micRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getMicAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getIdaAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.idaRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getIdaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneMICIDA(HAWB: string, optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      optional.getWhere().push(
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      );
      // ida
      const IDAQuery = this.idaRepository.getOneOptional(optional);

      // mic
      const MICQuery = this.micRepository.getOneOptional(optional);

      let [IDA, MIC] = await Promise.all([IDAQuery, MICQuery]);
      if (IDA) {
        const IDAData: IDA = IDA;
        IDAData.setDataValue('dataType', EConfigure.IDA);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = IDAData;
      }
      if (MIC) {
        const MICData: MIC = MIC;
        MICData.setDataValue('dataType', EConfigure.MIC);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = MICData;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getOneMICIDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async handlePushWebHook(HAWBs: string[], action: number, reasonIds?: any): Promise<any> {
    try {
      if (HAWBs.length > 0) {
        const optional: Optional = new Optional();
        const optionalHold: Optional = new Optional();
        let hold: any = null;
        optional.setAttributes(['orderId', 'clientId'])
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);
        optional.setGroup(['orderId', 'clientId']);
        if (reasonIds) {
          optionalHold.setAttributes(['id', 'name']);
          optionalHold.setWhere([
            new Where(EConfigure.AND, 'id', EConfigure.IN, reasonIds.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
          ]);

          hold = await this.holdRepository.getOneOptional(optionalHold);
        }
        const [mics, idas] = await Promise.all([
          this.micRepository.queryAll(optional),
          this.idaRepository.queryAll(optional),
        ]);
        if (mics.length > 0 || idas.length > 0) {
          const orders: Map<number, string[]> = new Map();
          const orderLazadas: Set<string> = new Set();
          if (mics.length > 0) {
            mics.forEach((mic: MIC) => {
              const clientId: number = Number(mic[EConfigure.CLIENT_ID_FIELD]);
              const orderId: string = mic[EConfigure.ORDER_ID_FIELD];
              orderLazadas.add(orderId);
              if (orders.has(clientId)) {
                const orderIds: string[] = orders.get(clientId) || [];
                orderIds.push(orderId);
                orders.set(clientId, orderIds)
              } else {
                orders.set(clientId, [orderId])
              }
            });
          }
          if (idas.length > 0) {
            idas.forEach((ida: IDA) => {
              const clientId: number = Number(ida[EConfigure.CLIENT_ID_FIELD]);
              const orderId: string = ida[EConfigure.ORDER_ID_FIELD];
              orderLazadas.add(orderId);
              if (orders.has(clientId)) {
                const orderIds: string[] = orders.get(clientId) || [];
                orderIds.push(orderId);
                orders.set(clientId, orderIds)
              } else {
                orders.set(clientId, [orderId])
              }
            });
          }
          let holdDetail: any = null;
          if (hold) {
            holdDetail = {
              "code": hold['id'],
              "name": hold['name']
            }
          }
          if (orders.size > 0) {
            for (const [key, value] of orders) {
              const data: IWebHookRequest = {
                'action': action,
                'clientId': key,
                'orderIds': [...new Set(value)],
                'reasonDetailId': null,
                'holdClearance': holdDetail,
                'stationOriginId': null,
                'stationDestinationId': null
              }
              await new RabbitSendInternal().send(EConfigure.PUSH_WEBHOOK_PARTNER, [data], { durable: true, autoDelete: false });

            }
          }
          if (orderLazadas.size > 0) {
            for (let item of orderLazadas) {

              const data: any = {
                'orderId': item,
                'isClearance': true,
                'statusId': EConfigure.HOLD_CLEANCE,
                'now': moment().format(EConfigure.FULL_TIME),
                'hold': holdDetail,
              }
              await new RabbitSendInternal().send(EConfigure.UPDATE_LASTEST_MANIFEST_STATUS, [data], { durable: true, autoDelete: false });
            }
          }

        }
      }
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][handlePushWebHook]', (error as Error).message);
      throw new Error(error as any);
    }
  }


  public async updateTaxCodeNumber(HAWB: string): Promise<void> {
    try {
      const taxURL = process.env.TAX_URL || 'https://devapi.globex.vn/taxcode-sync/api/';
      Utilities.callAPINonAuth(`${taxURL}tax_number_sync?HAWB=${HAWB}`);
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][updateTaxCodeNumber]', (error as Error).message);
    }
  }

  public async getMAWB(MAWB: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
          'data': {
            'shipment': null,
            'HAWBClearances': [],
            'totalHAWBClearance': 0
          }
        }
      }
      const micOptional: Optional = new Optional();
      micOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeight', 'totalInvoicePrice', 'invoiceCurrencyCode', 'importerFullName',
        'addressOfImporter', 'consignorName', 'address1', 'address2', 'address3', 'address4'
      ]);
      micOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      micOptional.setRelation(['importDetailItems']);

      const idaOptional: Optional = new Optional();
      idaOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeightGross', 'totalInvoicePrice', 'invoiceCurrencyCode', 'importerFullName',
        'addressOfImporter', 'consignorName', 'address1', 'address2', 'address3', 'address4'
      ]);
      idaOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      idaOptional.setRelation(['importDetailItems']);

      const mecOptional: Optional = new Optional();
      mecOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeight', 'totalOfTaxValue', 'currencyCodeOfTaxValue', 'exporterFullName',
        'addressOfExporter', 'consigneeName', 'address1', 'address2', 'address3', 'address4'
      ]);
      mecOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      mecOptional.setRelation(['exportDetailItems']);

      const edaOptional: Optional = new Optional();
      edaOptional.setAttributes([
        EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'cargoPiece', 'cargoWeightGross', 'totalInvoicePrice', 'invoiceCurrencyCode', 'exporterFullName',
        'addressOfExporter', 'consigneeName', 'address1', 'address2', 'address3', 'address4'
      ]);
      edaOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      edaOptional.setRelation(['exportDetailItems']);

      const shipmentOptional: Optional = new Optional();
      shipmentOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      const [shipment, idas, mics, edas, mecs] = await Promise.all([
        this.shipmentRepository.getOneOptional(shipmentOptional),
        this.idaRepository.queryAll(idaOptional),
        this.micRepository.queryAll(micOptional),
        this.edaRepository.queryAll(edaOptional),
        this.mecRepository.queryAll(mecOptional),

      ]);
      if (shipment && (mics.length > 0 || idas.length > 0 || mecs.length > 0 || edas.length > 0)) {
        const HAWBClearances: IMAWBReport[] = [];
        if (edas.length > 0) {
          edas.forEach((element: EDA) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeightGross'],
              'invoicePrice': element['totalInvoicePrice'],
              'invoiceCurrency': element['invoiceCurrencyCode'],
              'consignorFullName': element['exporterFullName'],
              'consignorAddress': element['addressOfExporter'],
              'consigneeName': element['consigneeName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['exportDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        if (mecs.length > 0) {
          mecs.forEach((element: MEC) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeight'],
              'invoicePrice': element['totalOfTaxValue'],
              'invoiceCurrency': element['currencyCodeOfTaxValue'],
              'consignorFullName': element['exporterFullName'],
              'consignorAddress': element['addressOfExporter'],
              'consigneeName': element['consigneeName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['exportDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        if (idas.length > 0) {
          idas.forEach((element: IDA) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeightGross'],
              'invoicePrice': element['totalInvoicePrice'],
              'invoiceCurrency': element['invoiceCurrencyCode'],
              'consignorFullName': element['importerFullName'],
              'consignorAddress': element['addressOfImporter'],
              'consigneeName': element['consignorName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['importDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        if (mics.length > 0) {
          mics.forEach((element: MIC) => {
            const dataReturn: IMAWBReport = {
              'HAWB': String(element[EConfigure.HAWB_FIELD]),
              'HAWBClearance': element[EConfigure.HAWB_CLEARANCE_FIELD] ? element[EConfigure.HAWB_CLEARANCE_FIELD] : '',
              'piece': +(element['cargoPiece']),
              'weight': element['cargoWeight'],
              'invoicePrice': element['totalInvoicePrice'],
              'invoiceCurrency': element['invoiceCurrencyCode'],
              'consignorFullName': element['importerFullName'],
              'consignorAddress': element['addressOfImporter'],
              'consigneeName': element['consignorName'],
              'consigneeAddress': `${element['address1']} ${element['address2'] ? element['address2'] : ''} ${element['address3'] ? element['address3'] : ''} ${element['address4'] ? element['address4'] : ''}`,
              'details': element['importDetailItems']
            }
            HAWBClearances.push(dataReturn)
          });
        }
        reponze['status'] = true;
        reponze['message'] = {
          'message': EMessage.SUCCESS,
          'data': {
            'shipment': shipment,
            'HAWBClearances': HAWBClearances,
            'totalHAWBClearance': HAWBClearances.length
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getMAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMAWBs(optional: Optional): Promise<any> {
    try {
      const [data, total] = await this.shipmentRepository.getAll(optional);
      if (data.length > 0) {
        await Promise.all(data.map(async (element: any) => {
          const optinalMAWB: Optional = new Optional();
          optinalMAWB.setWhere([
            new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, element['MAWB']),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          ]);
          const [mic, mec, ida, eda] = await Promise.all([
            this.micRepository.countData(optinalMAWB),
            this.mecRepository.countData(optinalMAWB),
            this.idaRepository.countData(optinalMAWB),
            this.edaRepository.countData(optinalMAWB),
          ]);
          element.setDataValue('totalHAWB', mic + mec + ida + eda);
        }))
      }
      return [data, total];
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][getMAWBs]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async MICGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const importOptional: Optional = new Optional();
      importOptional.setRelation(["importDetailItems"]);
      importOptional.setAttributes(value['select'].split(','));
      importOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);
      if (value['classify']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        importOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (value['dateCheckout']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      // mic
      const MICs = await this.micRepository.queryAll(importOptional);

      const obj: any = {};
      if (MICs.length > 0) {
        if (value['boxes'] && value['boxes'].length > 0) {
          const micExternalBoxes = _.groupBy(MICs, MIC => MIC['externalBoxName']);
          const micInternalBoxes = _.groupBy(MICs, MIC => MIC['internalBoxName']);
          value['boxes'].forEach((item: any) => {
            obj[item] = [
              ...micExternalBoxes[item] ? micExternalBoxes[item] : [],
              ...micInternalBoxes[item] ? micInternalBoxes[item] : []
            ];
          })
        } else {
          MICs.forEach((mic: MIC) => {
            let key: string = 'none';
            if (mic['externalBoxName']) {
              key = mic['externalBoxName'];
            } else if (mic['internalBoxName']) {
              key = mic['internalBoxName'];
            }
            if (obj[key]) {
              obj[key].push(mic);
            } else {
              obj[key] = [mic];
            }
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = obj;
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][MICGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async IDAGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const importOptional: Optional = new Optional();
      importOptional.setRelation(["importDetailItems"]);
      importOptional.setAttributes(value['select'].split(','));
      importOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);

      if (value['classify']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        importOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (value['dateCheckout']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        importOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      // ida
      const IDAs = await this.idaRepository.queryAll(importOptional);
      if (IDAs.length > 0) {
        const obj: any = {};
        if (value['boxes'] && value['boxes'].length > 0) {
          const idaExternalBoxes = _.groupBy(IDAs, IDA => IDA['externalBoxName']);
          const idaInternalBoxes = _.groupBy(IDAs, IDA => IDA['internalBoxName']);
          value['boxes'].forEach((item: any) => {
            obj[item] = [
              ...idaExternalBoxes[item] ? idaExternalBoxes[item] : [],
              ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []
            ];
          })
        } else {
          IDAs.forEach((ida: IDA) => {
            let key: string = 'none';
            if (ida['externalBoxName']) {
              key = ida['externalBoxName'];
            } else if (ida['internalBoxName']) {
              key = ida['internalBoxName'];
            }
            if (obj[key]) {
              obj[key].push(ida);
            } else {
              obj[key] = [ida];
            }
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = obj;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][IDAGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async totalManagementGate(optional: Optional): Promise<any> {
    try {
      let responze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      let queryAll: number = 0;
      optional.getWhere().forEach((where: Where, index: number) => {
        if (where.getKey() === 'type') {
          queryAll = Number(where.getValue());
          optional.getWhere().splice(index, EConfigure.INDEX_1);
        }
      });
      let totalWeight: number = 0, totalMIC = 0, totalIDA = 0, totalHold = 0, totalDOC = 0, totalCOM = 0, totalHAWB = 0;
      if (queryAll == 1 || queryAll == 0) {
        const micWeigthOptional: Optional = new Optional();
        micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
        micWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);

        const micTotalOtptional: Optional = new Optional();
        micTotalOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const micHoldOtptional: Optional = new Optional();
        micHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const micDOCOtptional: Optional = new Optional();
        micDOCOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const [micWeight, micTotal, micHoldTotal, DOCTotal] = await Promise.all([
          this.micRepository.queryOneRaw(micWeigthOptional),
          this.micRepository.countData(micTotalOtptional),
          this.micRepository.countData(micHoldOtptional),
          this.micRepository.countData(micDOCOtptional)
        ]);
        if (micTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(micWeight['totalMICWeigth']);
        totalMIC = micTotal;
        totalHold += micHoldTotal;
        totalDOC += DOCTotal;
        totalHAWB += micTotal;
      }
      if (queryAll == 2 || queryAll == 0) {
        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false)
        ]);

        const idaTotalOtptional: Optional = new Optional();
        idaTotalOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const idaHoldOtptional: Optional = new Optional();
        idaHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const idaCOMOtptional: Optional = new Optional();
        idaCOMOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);
        const [idaWeight, idaTotal, idaHoldTotal, COMTotal] = await Promise.all([
          this.idaRepository.queryOneRaw(idaWeigthOptional),
          this.idaRepository.countData(idaTotalOtptional),
          this.idaRepository.countData(idaHoldOtptional),
          this.idaRepository.countData(idaCOMOtptional)
        ]);
        if (idaTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(idaWeight['totalIDAWeigth']);
        totalIDA = idaTotal;
        totalHold += idaHoldTotal;
        totalCOM += COMTotal;
        totalHAWB += idaTotal;

      }

      if (queryAll == 3) {
        const micWeigthOptional: Optional = new Optional();
        micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
        micWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
        ]);

        const micDOCOtptional: Optional = new Optional();
        micDOCOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const [micWeight, DOCTotal] = await Promise.all([
          this.micRepository.queryOneRaw(micWeigthOptional),
          this.micRepository.countData(micDOCOtptional)
        ]);
        if (DOCTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(micWeight['totalMICWeigth']);
        totalDOC += DOCTotal;
      }

      if (queryAll == 4) {
        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
        ]);

        const idaCOMOtptional: Optional = new Optional();
        idaCOMOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        ]);

        const [idaWeight, COMTotal] = await Promise.all([
          this.idaRepository.queryOneRaw(idaWeigthOptional),
          this.idaRepository.countData(idaCOMOtptional)
        ]);
        if (COMTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += Number(idaWeight['totalIDAWeigth']);
        totalCOM += COMTotal;
      }

      if (queryAll == 5) {
        const micWeigthOptional: Optional = new Optional();
        micWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeight") as "totalMICWeigth"')]);
        micWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const idaWeigthOptional: Optional = new Optional();
        idaWeigthOptional.setAttributes([this._sequelize.literal('sum("cargoWeightGross") as "totalIDAWeigth"')]);
        idaWeigthOptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const micHoldOtptional: Optional = new Optional();
        micHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);

        const idaHoldOtptional: Optional = new Optional();
        idaHoldOtptional.setWhere([
          ...optional.getWhere(),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        ]);


        const [micWeight, idaWeight, micHoldTotal, idaHoldTotal] = await Promise.all([
          this.micRepository.queryOneRaw(micWeigthOptional),
          this.idaRepository.queryOneRaw(idaWeigthOptional),
          this.micRepository.countData(micHoldOtptional),
          this.idaRepository.countData(idaHoldOtptional),
        ]);
        if (micHoldTotal > 0 || idaHoldTotal > 0) {
          responze['status'] = true;
          responze['message']['message'] = EMessage.FOUND;
        }
        totalWeight += (Number(idaWeight['totalIDAWeigth']) + Number(micWeight['totalIDAWeigth']));
        totalHold += micHoldTotal + idaHoldTotal;
      }

      responze['message']['data'] = {
        totalWeight: +(totalWeight.toFixed(2)),
        totalMIC,
        totalIDA,
        totalHold,
        totalDOC,
        totalCOM,
        totalHAWB
      }
      return responze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][managementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async exportManagementGate(optional: Optional, employeeId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      let queryAll: number = 0;
      let title: string = '';
      let MICs: MIC[] = [];
      let IDAs: IDA[] = [];
      let classify: any = null;
      let startCheckout: any = null;
      let endCheckout: any = null;
      let removeIndex: number[] = [];
      let printType: number = 0;
      let contentTitle: string = '';
      optional.getWhere().forEach((where: Where, index: number) => {
        if (where.getKey() === 'type') {
          queryAll = Number(where.getValue());
          removeIndex.push(index);
        }
        if (where.getKey() === 'statusName') {
          title = where.getValue() ? String(where.getValue()) : '';
          if (title !== 'Tất cả') {
            title = `DANH SÁCH ${title.toUpperCase()}`;
          } else {
            title = `DANH SÁCH MANIFEST`;
          }
          removeIndex.push(index);
        }
        if (where.getKey() === 'startCheckout') {
          startCheckout = where.getValue();
          removeIndex.push(index);
        }
        if (where.getKey() === 'endCheckout') {
          endCheckout = where.getValue();
          removeIndex.push(index);
        }
        if (where.getKey() === 'printType') {
          printType = where.getValue();
          removeIndex.push(index);
        }
      });
      const query: Where[] = optional.getWhere().filter((value, index) => {
        return removeIndex.indexOf(index) == -1;
      })
      const micOptional: Optional = new Optional();
      micOptional.setAttributes([`${EConfigure.HAWB_CLEARANCE_FIELD}`, "dateCheckin", "dateClearanced", "MAWB", "declarationNo", "cargoPiece", "cargoWeight", "classify", "dateCheckout", "hubId", "inspectionKindClassification"]);
      micOptional.setWhere(query);
      micOptional.setRelation(['client']);

      const idaOptional: Optional = new Optional();
      idaOptional.setAttributes([`${EConfigure.HAWB_CLEARANCE_FIELD}`, "dateCheckin", "dateClearanced", "MAWB", "declarationNo", "cargoPiece", "cargoWeightGross", "classify", "dateCheckout", "hubId", "inspectionKindClassification"]);
      idaOptional.setWhere(query);
      idaOptional.setRelation(['client']);
      if (queryAll === 0) {
        contentTitle = `${title.toLocaleLowerCase()} tất cả`;
        micOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        );
        [IDAs, MICs] = await Promise.all([
          this.idaRepository.queryAll(idaOptional),
          this.micRepository.queryAll(micOptional)
        ]);
      }
      if (queryAll === 1) {
        contentTitle = `${title.toLocaleLowerCase()} ${EConfigure.MIC}`;
        micOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        MICs = await this.micRepository.queryAll(micOptional);
      }
      if (queryAll === 2) {
        contentTitle = `${title.toLocaleLowerCase()} ${EConfigure.H11}`;
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.PAR)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        IDAs = await this.idaRepository.queryAll(idaOptional);
      }

      if (queryAll === 3) {
        contentTitle = `${title.toLocaleLowerCase()} ${String(EConfigure.DOC).toLocaleLowerCase()}`;
        micOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.DOC)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        MICs = await this.micRepository.queryAll(micOptional);
      }

      if (queryAll === 4) {
        contentTitle = `${title.toLocaleLowerCase()} ${String(EConfigure.COM).toLocaleLowerCase()}`;
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, ClassifyName.get(ClassifyKey.COM)),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false)
        );
        IDAs = await this.idaRepository.queryAll(idaOptional);
      }

      if (queryAll === 5) {
        contentTitle = `${title.toLocaleLowerCase()} ${String(EConfigure.HOLD).toLocaleLowerCase()}`;
        micOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
          new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true)
        );
        [IDAs, MICs] = await Promise.all([
          this.idaRepository.queryAll(idaOptional),
          this.micRepository.queryAll(micOptional)
        ]);;
      }
      if (MICs.length > 0 || IDAs.length > 0) {
        let micData: IManagement[] = [];
        let idaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        let hubId: number = 0;
        if (IDAs.length > 0) {
          hubId = IDAs[0]['hubId'] ? IDAs[0]['hubId'] : 0;
          let classify: string = "";
          idaData = IDAs.map((IDA: IIDA) => {
            if (IDA['isHold'] == true) {
              classify = 'HOLD';
            } else {
              if (IDA['classify'] == "PAR") {
                classify = "IDA";
              } else {
                classify = "Mậu dịch";
              }
            }
            const ida: IManagement = {
              'HAWB': IDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'declarationNo': IDA['declarationNo'],
              'cargoPiece': IDA['cargoPiece'],
              'weight': IDA['cargoWeightGross'],
              'weightRound': Number(IDA['cargoWeightGross']),
              'MAWB': IDA['MAWB'],
              'classify': classify,
              'dateCheckout': IDA['dateCheckout'] ? moment(IDA['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateGate': IDA['dateCheckout'] ? moment(IDA['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateCheckin': IDA['dateCheckin'] ? moment(IDA['dateCheckin']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateClearanced': IDA['dateClearanced'] ? moment(IDA['dateClearanced']).format("DD-MM-YYYY HH:mm:ss") : '',
              'clearanceInspection': Number(IDA['inspectionKindClassification']),
              'clientName': IDA['client'] ? IDA['client']['code'] : '',
            }
            return ida;
          });
        }
        if (MICs.length > 0) {
          hubId = MICs[0]['hubId'] ? MICs[0]['hubId'] : 0;
          let classify: string = "";
          micData = MICs.map((MIC: IMIC) => {
            if (MIC['isHold'] == true) {
              classify = 'HOLD';
            } else {
              if (MIC['classify'] == "PAR") {
                classify = "MIC";
              } else {
                classify = "DOC";
              }
            }
            const mic: IManagement = {
              'HAWB': MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              'declarationNo': MIC['declarationNo'],
              'cargoPiece': MIC['cargoPiece'],
              'weight': MIC['cargoWeight'],
              'weightRound': Number(MIC['cargoWeight']),
              'MAWB': MIC['MAWB'],
              'classify': classify,
              'dateCheckout': MIC['dateCheckout'] ? moment(MIC['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateGate': MIC['dateCheckout'] ? moment(MIC['dateCheckout']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateCheckin': MIC['dateCheckin'] ? moment(MIC['dateCheckin']).format("DD-MM-YYYY HH:mm:ss") : '',
              'dateClearanced': MIC['dateClearanced'] ? moment(MIC['dateClearanced']).format("DD-MM-YYYY HH:mm:ss") : '',
              'clearanceInspection': Number(MIC['inspectionKindClassification']),
              'clientName': MIC['client'] ? MIC['client']['code'] : '',
            }
            return mic;
          });
        }

        manifests = [...micData, ...idaData];
        if (manifests.length > 0 && (printType == PrintKey.MANAGEMENT_PDF || printType == PrintKey.MANAGEMENT_EXCEL)) {
          const createHistory = await this.historyMonitorRepository.createData({
            title: `${contentTitle}_${moment().format("YYMMDD_HHmmssSS")}`,
            employeeId: employeeId,
            printType: printType,
            hubId: hubId,
            isSucess: false,
          });

          if (printType == PrintKey.MANAGEMENT_PDF) {
            this.exportPDF(manifests, createHistory, classify, startCheckout, endCheckout, title);
          } else {
            this.exportExcel(manifests, createHistory);
          }
          reponze['message']['data'] = createHistory;
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][exportManagementGate]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async exportExcel(manifests: any, createHistory: any): Promise<void> {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("sheet 1", { properties: { tabColor: { argb: 'FFC0000' } } });

      worksheet.columns = [
        { header: "Mã vận đơn", key: "HAWB", width: 18 },
        { header: "Số tờ khai", key: "declarationNo", width: 16 },
        { header: "MAWB", key: "MAWB", width: 16 },
        { header: "Loại hình", key: "classify", width: 10 },
        { header: "Kiện", key: "cargoPiece", width: 5 },
        { header: "Trọng lượng", key: "weightRound", width: 15 },
        { header: "Ra cổng", key: "dateGate", width: 20 },
        { header: "Thông quan", key: "dateClearanced", width: 20 },
        { header: "VNS luồng", key: "clearanceInspection", width: 14 },
        { header: "Checkin", key: "dateCheckin", width: 20 },
        { header: "Checkout", key: "dateCheckout", width: 20 },
        { header: "Khách hàng", key: "clientName", width: 14 },
        { header: "Lý do", key: "note", width: 10 },
      ];
      worksheet.getRow(1).eachCell((cell: any) => {
        cell.font = { bold: true, name: 'Calibri' };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '96C8FB' },
          bgColor: { argb: '96C8FB' },
        };
        cell.alignment = { vertical: 'middle', horizontal: 'center' },
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
      });
      worksheet.addRows(manifests);

      let filename = `DS_MANIFEST_RA_CONG_${moment().format(EConfigure.DAY_TIME)}_${moment().format("X")}.xlsx`;
      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}/${filename}`)
      createHistory.isSucess = true;
      createHistory.successDate = moment().format(EConfigure.FULL_TIME);
      createHistory.link = `${new Config().clearanceDomain()}/clearance/invoice/${filename}`;
      createHistory.save();
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][exportManagementGate][exportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async exportPDF(manifests: any, createHistory: any, classify: any, startCheckout: any, endCheckout: any, title: any): Promise<void> {
    try {
      const config: pdf.CreateOptions = {
        "format": "A4",
        "orientation": "portrait",
        "border": {
          "top": "8px",
          "right": "10px",
          "bottom": "20px",
          "left": "10px"
        },
        timeout: 300000
      }

      const now: string = moment().format('DD-MM-YYYY');
      let date: string = '';
      let filename = `bangkeracong${classify ? '_' + classify : ''}_${moment().format("X")}.pdf`;
      let pathTemplate = `../../../../../template/bangke/bangkeracong.ejs`;

      if (startCheckout && startCheckout) {
        if (moment(startCheckout).isSame(endCheckout, 'date')) {
          date = `Ngày ${moment(startCheckout).format('DD-MM-YYYY')}`;
        } else {
          date = `Từ ngày ${moment(startCheckout).format('DD-MM-YYYY')} đến ngày ${moment(endCheckout).format('DD-MM-YYYY')}`;
        }
      }

      config['footer'] = {
        "height": "10mm",
        "contents": {
          default: `<table width="100%;" style="font-size: 8px;">
            <tr>
              <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
              <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
            </tr>
          </table>`,
        }
      }
      let template = await ejs.renderFile(path.join(__dirname, pathTemplate), { manifests, title, date }, { async: true });

      pdf.create(template, config).toFile(`${EConfigure.FOLDER_INVOICE}/${filename}`, function (err, res) {
        if (res) {
          createHistory.isSucess = true;
          createHistory.successDate = moment().format(EConfigure.FULL_TIME);
          createHistory.link = `${new Config().clearanceDomain()}/clearance/invoice/${filename}`;
          createHistory.save();
        }
        if (err) {
          Utilities.sendDiscordErr('[service][PdfGenerate][create][pdfPrint]', err.toString());
        }
      });
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][exportManagementGate][exportPDF]', (error as Error).message);
      throw new Error(error as any);
    }
  }
}

export default ImportClearanceService;