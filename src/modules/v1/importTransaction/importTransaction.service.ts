'use strict';

import ImportTransactionRepository from "./importTransaction.reporsitory";
import BaseService from '../../index.service';

class ImportTransactionService extends BaseService {
  private importTransactionRepository: ImportTransactionRepository;
  constructor () {
    super(new ImportTransactionRepository());
    this.importTransactionRepository = new ImportTransactionRepository();
  }
}

export default ImportTransactionService;