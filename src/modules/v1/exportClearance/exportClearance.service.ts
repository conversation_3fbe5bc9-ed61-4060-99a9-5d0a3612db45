'use strict';

import MECClearanceRepository from "./MECClearance.reporsitory";
import EDAClearanceRepository from "./EDAClearance.reporsitory";
import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import { Database } from '../../../database/index';
import { ClassifyName, ClassifyKey, ClassifyValidateName, ClassifyValidateKey } from "../../../emuns/classifyExport";
import { ActionKey } from "../../../emuns/action";
import { IResponze } from "../../../https/responze";
import { WeightName, WeightKey, WeightClearance } from "../../../emuns/weight";
import ExportDetailRepository from "../exportClearanceDetail/exportClearanceDetail.reporsitory";
import { IMEC, IMECCreate, ICountry, IWarehouse, IExportValidate, IExportDetailValidate, IExportDetailCreate, IEDACreate, IEDA, EDA, IMECMaster, IEDAMaster, IHSCodeDetail, IExportDetail, IMasterValidate, Country, ExportDetail, MEC, TaxCode, User, CustomerBusiness, CustomerPersonal, Hold } from "../../../models/index.model";
import Utilities from "../../../util/utilities";
import { Clearance } from "../../../xmlClearance/clearance";
import { ClearanceTypeId } from "../../../xmlClearance/enum";
import moment from "moment";
import HSCodeDetailRepository from "../hscodeDetail/hscodeDetail.reporsitory";
import ExportClearanceDetailService from "../exportClearanceDetail/exportClearanceDetail.service";
import HSCodeRepository from "../hscode/hscode.reporsitory";
import TaxCodeRepository from "../taxCode/taxCode.reporsitory";
import OrderBy from "../../../parser/orderBy";
import { IHoldTransaction, IReturnCargo } from "../../../models/importTransaction.model";
import UserRepository from "../user/user.reporsitory";
import ExportClearanceValidate from "./exportClearance.validate";
import ErrorValidate from "../../../util/error.validate";
import { ClearanceCreateKey } from "../../../emuns/clearanceCreate";
import ClearanceCreateLogMongo from "../../../models/mongo/clearanceCreateLog.model";

interface IStoreWarehouse {
  HAWB?: string;
  MAWB?: string;
  HAWBClearance?: string;
  warehouseAddress?: string;
  warehouseCheckin?: any;
  warehouseCheckout?: any;
}

interface IHold {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IExportDetail[];
  reasonDetails?: Hold[];
}

interface IClearanceCreateLog {
  clearanceCreateLogId?: number;
  data?: any;
  message?: string;
  cause?: any;
  processResult?: any;
  handleData?: any;
  type?: number;
  classify?: any;
  isSuccess?: boolean;
}

interface ICheckout {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  details?: IDetail[];
}

interface IDetail {
  itemNameVN: string;
  quantity: number;
}

interface IExportReport {
  expDate: string;
  trackingNo: string;
  name: string;
  MAWB: string;
  country: string;
  pcs: number;
  grossWeight: number;
  puDate: string;
  cdsDate: string;
  crDate: string;
  confirmUpliftDate: string;
  cdsNo: string;
  importType: any;
  lane: number;
  customsStatus: string;
  customsClearance: any;
  handling: any;
  shipmemntValue: any;
  dutyTaxVND: number;
  otherFee: number;
  notes: string;
  type: string;
  partner: string;
}

interface IManagement {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  bussiness?: CustomerBusiness;
  personal?: CustomerPersonal;
  exportDetails?: ExportDetail[];
  isHold?: boolean;
  reasonHold?: Hold[];
}

interface IGetAll {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IExportDetail[];
  inspectionKindTimes?: any;
  clearanceDeclarationTimes?: any;
  reasonDetails?: Hold[];
  isHold?: any;
  isSortLane: boolean;
  externalBoxName?: string;
  internalBoxName?: string;
  createdAt: string;
}

interface IAccountExportReport {
  trackingNo: any;
  MAWB: any;
  origin: any;
  country: any;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: any;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: any;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: any;
  consignorName: any
  consignorAddress: any;
  consignorCityName: any;
  consignorContactName: any;
  consignorTelephone: any;
  consigneeName: any;
  consigneeAddress: any;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: any;
  consigneeTelephone: any;
  remarks: any;
  status?: any;
  statusId: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;
  customerBusiness?: any;
  customerPersonal?: any;
  dateClearanced?: any;
  orderNumber?: any;
  itemIds?: any;
  itemCount?: any;
  station?: any;
  exporterCode?: any;
  warehouseCheckin?: any;
  warehouseCheckout?: any;

}


class ExportClearanceService extends BaseService {
  private sequelize: any;
  private mecRepository: MECClearanceRepository;
  private edaRepository: EDAClearanceRepository;
  private taxCodeRepository: TaxCodeRepository;
  private exportDetailRepository: ExportDetailRepository;
  private hscodeDetailRepository: HSCodeDetailRepository;
  private hscodeRepository: HSCodeRepository;
  private _sequelize: any;
  constructor() {
    super(new MECClearanceRepository());
    this.sequelize = Database.Sequelize;
    this.mecRepository = new MECClearanceRepository();
    this.edaRepository = new EDAClearanceRepository();
    this.exportDetailRepository = new ExportDetailRepository();
    this.hscodeDetailRepository = new HSCodeDetailRepository();
    this.taxCodeRepository = new TaxCodeRepository();
    this.hscodeRepository = new HSCodeRepository();
    this._sequelize = Database.Sequelize;
  }

  public async getHoldManifest(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.setRelation(['country', 'exportDetailItems', 'holds']);
      const EDAQuery = this.edaRepository.queryAll(optional);
      const MECQuery = this.mecRepository.queryAll(optional);

      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);


      if (MECs.length > 0 || EDAs.length > 0) {
        let mecData: IManagement[] = [];
        let edaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: IEDA) => {
            const eda: IHold = {
              "HAWBClearance": EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": EDA[EConfigure.HAWB_FIELD],
              "declarationNo": EDA['declarationNo'],
              "MAWB": EDA['MAWB'],
              "invoiceValue": EDA['totalInvoicePrice'],
              "invoiceCurrency": EDA['invoiceCurrencyCode'],
              "priceVND": EDA['priceVND'],
              "country": EDA['country'],
              "cargoPiece": EDA['cargoPiece'],
              "weight": EDA['cargoWeightGross'],
              "massOfWeight": EDA['weightUnitCodeGross'],
              "inspectionKindClassification": EDA['inspectionKindClassification'],
              "phase": EDA['phase_name'],
              "classify": EDA['classify'],
              "type": String(EConfigure.EDA),
              "details": EDA['exportDetailItems'],
              "reasonDetails": EDA['holds']
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          mecData = MECs.map((MEC: IMEC) => {
            const mec: IHold = {
              "HAWBClearance": MEC[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": MEC[EConfigure.HAWB_FIELD],
              "declarationNo": MEC['declarationNo'],
              "MAWB": MEC['MAWB'],
              "invoiceValue": MEC['totalOfTaxValue'],
              "invoiceCurrency": MEC['currencyCodeOfTaxValue'],
              "priceVND": MEC['priceVND'],
              "country": MEC['country'],
              "cargoPiece": MEC['cargoPiece'],
              "weight": MEC['cargoWeight'],
              "massOfWeight": WeightClearance.get('kg'),
              "inspectionKindClassification": MEC['inspectionKindClassification'],
              "phase": MEC['phase_name'],
              "classify": MEC['classify'],
              "type": String(EConfigure.MEC),
              "details": MEC['exportDetailItems'],
              "reasonDetails": MEC['holds']
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getHoldManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneMEC(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.mecRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getOneMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneEDA(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.edaRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getOneEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMEC(optional: Optional): Promise<any> {
    try {
      const objData = await this.mecRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getAllMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllEDA(optional: Optional): Promise<any> {
    try {
      const objData = await this.edaRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getAllEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async createExport(manifestExports: any[]): Promise<any> {
    // let transaction: any;
    try {
      let HAWBs: string[] = [];
      let HAWBClearances: string[] = [];
      let processingHAWBs: string[] = [];
      let processingHAWBClearances: string[] = [];
      HAWBs = manifestExports.map((manifest: any) => {
        return manifest[EConfigure.HAWB_FIELD];
      });
      const processOptional: Optional = new Optional();
      processOptional.setAttributes([EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD]);
      processOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      const mecQuery: any = this.mecRepository.queryAll(processOptional);
      const edaQuery: any = this.edaRepository.queryAll(processOptional);

      const [mecs, edas] = await Promise.all([mecQuery, edaQuery]);
      if (mecs.length > 0) {
        mecs.forEach((mec: IMEC) => {
          processingHAWBs.push(mec[EConfigure.HAWB_FIELD] || '');
          processingHAWBClearances.push(mec[EConfigure.HAWB_CLEARANCE_FIELD] || '');
        });
      }
      if (edas.length > 0) {
        edas.forEach((eda: IEDA) => {
          processingHAWBs.push(eda[EConfigure.HAWB_FIELD] || '');
          processingHAWBClearances.push(eda[EConfigure.HAWB_CLEARANCE_FIELD] || '');
        });
      }
      HAWBs = [];
      const dataMECs: IMECCreate[] = [];
      const dataEDAs: IEDACreate[] = [];
      const dataDetails: any[] = [];
      let mecData: any;
      let edaData: any;
      let detailData: any;
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.CREATE_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          }
        }
      }
      const actionCreate: number = ActionKey.CREATE;
      for (const manifest of manifestExports) {
        const cloneManifest: IExportValidate = { ...manifest };
        const HAWB: string = cloneManifest[EConfigure.HAWB_FIELD] || '';
        const HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD] || '';
        if (!processingHAWBs.includes(HAWB)) {
          HAWBs.push(HAWB);
          HAWBClearances.push(HAWBClearance);
          let country: any;
          let warehouse: any;
          let valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
          const totalPrice: number = Number(cloneManifest['totalPrice']);
          const priceVND: number = +(totalPrice * valueClearanceVND).toFixed(0);
          const stationId: number = cloneManifest['stationId'] || 0;
          if (cloneManifest['warehouseId']) {
            warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
          }
          const countryCode: any = cloneManifest['consigneeCountryCode'];
          if (countryCode) {
            country = await Utilities.getCountryByCode(countryCode);
          }
          let declareCargoNameMEC: boolean = false;
          let declareCargoNameEDA: boolean = false;
          let details: IExportDetailValidate[] = [];
          let classifyDOC: boolean = false;
          if (manifest['items'].length > 0) {
            details = manifest['items'];
            details.forEach((detail: IExportDetailValidate, index: number) => {
              detailData = {};
              const cloneDetail: IExportDetailValidate = { ...detail };
              const itemNameEN: string = cloneDetail['itemName'] || '';
              const checkName: string = itemNameEN;
              if (checkName && checkName.toUpperCase().includes("DOC")) {
                classifyDOC = true;
              }
              const totalPrice: number = Number(cloneDetail['invoiceValue']);
              const priceDetailVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
              const currencyCode: string = cloneDetail['currencyCode'];
              detailData['HSCode'] = cloneDetail['HSCode'] ? String(cloneDetail['HSCode']) : null;
              detailData['itemName'] = itemNameEN;
              detailData['itemNameVN'] = cloneDetail['itemNameVN'];
              detailData['url'] = cloneDetail['url'];
              detailData['quantity1'] = cloneDetail['quantity'];
              detailData['unitPriceCurrencyCode'] = currencyCode;
              detailData['position'] = index;
              if (currencyCode === EConfigure.CURRENCY_VND) {
                detailData['invoiceValue'] = +(totalPrice.toFixed(0));
                detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
              } else {
                detailData['invoiceValue'] = totalPrice;
                detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
              }
              detailData['priceVND'] = priceDetailVND;
              detailData['valueClearanceVND'] = valueClearanceVND;
              detailData['priceQuantityUnit'] = EConfigure.PIECE;
              detailData['quantity1'] = cloneDetail['quantity'];
              detailData['quantity2'] = cloneDetail['quantity'];
              detailData['quantityUnitCode1'] = EConfigure.PIECE;
              detailData['quantityUnitCode2'] = EConfigure.PIECE;
              detailData[EConfigure.HAWB_FIELD] = HAWB;
              detailData['placeOfOriginCode'] = EConfigure.VN;
              if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
                detailData['weightKG'] = Number(cloneManifest['weight']);
              } else {
                detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
              }
              detailData['productId'] = detail['productId'];
              dataDetails.push(detailData);
            });
          }
          if (cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND <= 5000000) {
            if (details.length > 0) {
              declareCargoNameMEC = details.every((detail: IExportDetailValidate) => {
                return detail.itemNameVN;
              });
            }
            mecData = {
              'HAWB': HAWB,
              'HAWBClearance': HAWBClearance,
              'stationId': stationId,
              'phase': actionCreate,
              'serviceId': cloneManifest['serviceId'],
            };
            if (declareCargoNameMEC) {
              mecData['phase'] = ActionKey.UPDATE_CARGO_NAME;
            }
            if (!cloneManifest['exporterCode']) {
              mecData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
            } else {
              mecData['exporterCode'] = Utilities.subString(cloneManifest['exporterCode'], 0, 15);
            }
            if (!cloneManifest['exporterName']) {
              mecData['exporterName'] = EConfigure.IMPORTER_NAME
            } else {
              mecData['exporterName'] = cloneManifest['exporterName'];
              mecData['exporterFullName'] = cloneManifest['exporterName'];
            }
            mecData['postCode'] = cloneManifest['exporterPostCode'];
            mecData['addressOfExporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['exporterAddress'] as string), 0, 200);
            mecData['telephoneNumber'] = cloneManifest['exporterTelephoneNumber'];
            mecData['consigneeCode'] = cloneManifest['consigneeCode'];
            mecData['consigneeName'] = cloneManifest['consigneeName'];
            mecData['consigneeTelephoneNumber'] = Utilities.subString(cloneManifest['consigneeTelephoneNumber'], 0, 20);
            const addresses = Utilities.handleAddress(cloneManifest['consigneeAddress'] as string);
            if (addresses) {
              for (const [key, value] of Object.entries(addresses)) {
                if (key <= '3') {
                  mecData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
                }
              }
            }
            if (cloneManifest['departureDate'] && cloneManifest['flightNo']) {
              mecData['flightNo'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['departureDate']);
              mecData['departureDate'] = cloneManifest['departureDate'];
            } else {
              mecData['flightNo'] = cloneManifest['flightNo'];
            }
            if (country && Object.keys(country).length > 0) {
              mecData['address4'] = country['fullName'];
              mecData['countryCode'] = countryCode;
              mecData['theFinalDestination'] = `${countryCode}${EConfigure.OTHER_CODE}`;
            }
            mecData['MAWB'] = cloneManifest['MAWB'];
            mecData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
            if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
              mecData['cargoWeight'] = cloneManifest['weight'];
            } else {
              mecData['cargoWeight'] = Utilities.convertKG(Number(cloneManifest['weight']), String(cloneManifest['unitOfMass']));
            }
            if (warehouse && Object.keys(warehouse).length > 0) {
              mecData['customsWarehouseCode'] = warehouse['code'];
              mecData['loadingPortCode'] = warehouse['unloadingPortCode'];
              mecData['customsOffice'] = warehouse['customsOffice'];
              mecData['customsSubSection'] = warehouse['customsSubSection'];
              mecData['terminalName'] = warehouse['id'];
            }
            mecData['currencyCodeOfTaxValue'] = cloneManifest['currencyCode'];
            if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
              mecData['totalOfTaxValue'] = +(totalPrice.toFixed(0));
            } else {
              mecData['totalOfTaxValue'] = totalPrice;
            }
            mecData['valueClearanceVND'] = valueClearanceVND;
            mecData['customerBusinessId'] = cloneManifest['customerBusinessId'];
            mecData['customerPersonalId'] = cloneManifest['customerPersonalId'];
            mecData['notes'] = cloneManifest['note'];
            mecData['priceVND'] = priceVND;
            mecData['hubId'] = cloneManifest['hubId'];
            mecData['clientId'] = cloneManifest['clientId'];
            mecData['internalBoxName'] = cloneManifest['internalBoxName'];
            mecData['externalBoxName'] = cloneManifest['externalBoxName'];
            mecData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
            if (totalPrice === 0 || (totalPrice >= 0 && classifyDOC)) {
              mecData['classify'] = ClassifyName.get(ClassifyKey.DOC);
            } else {
              mecData['classify'] = cloneManifest['classify'];
            }
            dataMECs.push(mecData);
          } else {
            if (details.length > 0) {
              declareCargoNameEDA = details.every((detail: IExportDetailValidate) => {
                return detail['itemNameVN'] && detail['HSCode'] && detail['HSCode'].match(/^0+/) === null && detail['HSCode'].match(/^[0-9]+$/) !== null;
              });
            }
            edaData = {
              'HAWB': HAWB,
              'HAWBClearance': HAWBClearance,
              'cargoNo': HAWBClearance,
              'stationId': stationId,
              'phase': actionCreate,
              'serviceId': cloneManifest['serviceId'],
            };
            if (declareCargoNameEDA) {
              edaData['phase'] = ActionKey.UPDATE_CARGO_NAME;
            }
            edaData['MAWB'] = cloneManifest['MAWB'];
            edaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
            if (!cloneManifest['exporterCode']) {
              edaData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
            } else {
              edaData['exporterCode'] = Utilities.subString(cloneManifest['exporterCode'], 0, 15);
            }
            if (!cloneManifest['exporterName']) {
              edaData['exporterName'] = EConfigure.IMPORTER_NAME
            } else {
              edaData['exporterName'] = cloneManifest['exporterName'];
              edaData['exporterFullName'] = cloneManifest['exporterName'];
            }
            edaData['postCode'] = cloneManifest['exporterPostCode'];
            edaData['addressOfExporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['exporterAddress'] as string), 0, 200);
            edaData['telephoneNumber'] = Utilities.subString(cloneManifest['exporterTelephoneNumber'], 0, 20);
            edaData['consigneeCode'] = cloneManifest['consigneeCode'];
            edaData['consigneeName'] = cloneManifest['consigneeName'];
            edaData['consigneeTelephoneNumber'] = cloneManifest['consigneeTelephoneNumber'];
            edaData['postCodeIdentification'] = cloneManifest['consigneePostCode'];
            const addresses = Utilities.handleAddress(cloneManifest['consigneeAddress'] as string);
            if (addresses) {
              for (const [key, value] of Object.entries(addresses)) {
                if (key <= '3') {
                  edaData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
                }
              }
            }
            edaData['declarationKindCode'] = EConfigure.H21;
            edaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT as string;
            edaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
            edaData['termOfPayment'] = EConfigure.KHONGTT;
            if (warehouse && Object.keys(warehouse).length > 0) {
              edaData['customsWarehouseCode'] = warehouse['code'];
              edaData['plannedDeclarantCode'] = warehouse['agencyCode'];
              edaData['loadingPortCode'] = warehouse['unloadingPortCode'];
              edaData['loadingPortName'] = warehouse['unloadingPortCode'];
              edaData['customsOffice'] = warehouse['customsOffice'];
              edaData['customsSubSection'] = warehouse['customsSubSection'];
              edaData['terminalName'] = warehouse['id'];
            }
            if (cloneManifest['departureDate'] && cloneManifest['flightNo']) {
              edaData['loadingPlannedVesselName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['departureDate']);
              edaData['departurePlannedDate'] = cloneManifest['departureDate'];
            } else {
              edaData['loadingPlannedVesselName'] = cloneManifest['flightNo'];
            }
            if (country && Object.keys(country).length > 0) {
              edaData['address4'] = country['fullName'];
              edaData['countryCode'] = countryCode;
              edaData['theFinalDestinationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
              edaData['theFinalDestinationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
            }
            if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
              edaData['cargoWeightGross'] = Number(cloneManifest['weight']);
            } else {
              edaData['cargoWeightGross'] = Utilities.convertKG(Number(cloneManifest['weight']), String(cloneManifest['unitOfMass']));
            }
            edaData['weightUnitCodeGross'] = String(WeightClearance.get('kg'));
            edaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
            edaData['currencyCodeOfTaxValue'] = cloneManifest['currencyCode'];
            if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
              edaData['totalOfTaxValue'] = +(totalPrice.toFixed(0));;
              edaData['totalInvoicePrice'] = +(totalPrice.toFixed(0));;
            } else {
              edaData['totalOfTaxValue'] = totalPrice;
              edaData['totalInvoicePrice'] = totalPrice;
            }
            edaData['valueClearanceVND'] = valueClearanceVND;
            edaData['customerBusinessId'] = cloneManifest['customerBusinessId'] ? String(cloneManifest['customerBusinessId']) : null;
            edaData['customerPersonalId'] = cloneManifest['customerPersonalId'] ? String(cloneManifest['customerPersonalId']) : null;
            edaData['notes'] = cloneManifest['note'];
            edaData['classify'] = cloneManifest['classify'];
            edaData['priceVND'] = priceVND;
            edaData['hubId'] = cloneManifest['hubId'];
            edaData['taxPayer'] = EConfigure.TAX_PAYER;
            edaData['invoicePriceKindCode'] = EConfigure.TYPE_A;
            edaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
            edaData['codeOfExtendingDueDate'] = EConfigure.TYPE_A;
            edaData['invoicePriceConditionCode'] = EConfigure.CIF;
            edaData['clientId'] = cloneManifest['clientId'];
            edaData['internalBoxName'] = cloneManifest['internalBoxName'];
            edaData['externalBoxName'] = cloneManifest['externalBoxName'];
            edaData['originalOrderNumberClient'] = cloneManifest['originalOrderNumberClient'];
            dataEDAs.push(edaData);
          }
        }
      };
      if (dataMECs.length > 0 || dataEDAs.length > 0) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.NOT_IN, processingHAWBs.join())
        ]);
        try {
          // transaction = await this._sequelize.transaction();

          await this.mecRepository.destroyData(optional);
          await this.edaRepository.destroyData(optional);
          await this.exportDetailRepository.destroyData(optional);

          const mecCreated = await this.mecRepository.createBulk(dataMECs);
          const edaCreated = await this.edaRepository.createBulk(dataEDAs);
          await this.exportDetailRepository.createBulk(dataDetails);

          // await transaction.commit();
          if (mecCreated.length > 0 || edaCreated.length > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.CREATED_DATA;
            reponze['message']['data'] = {
              'succesfull': HAWBClearances,
              'fail': processingHAWBClearances
            }
          }
        } catch (error) {
          // await transaction.rollback();
          Utilities.sendDiscordErr('[service][export][createExport]', (error as Error).message);
          console.log('---- create exports error: %o', error);
          reponze['message']['data']['fail'].push(...HAWBClearances, ...processingHAWBClearances)
        }
      } else {
        reponze['message']['data']['fail'].push(...HAWBClearances, ...processingHAWBClearances)
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][createExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async createImportExcel(manifestImport: any): Promise<any> {
    let reponze: IResponze = {
      'status': false,
      'message': {
        'message': EMessage.CREATE_FAIL,
        'data': {
          'fail': [],
          'succesfull': []
        }
      }
    }
    let { error, value } = ExportClearanceValidate.importExcel.validate(manifestImport, { abortEarly: false });
    try {
      value = Utilities.convertNull(value);
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let messages: string[] = errorValidate.handleError();
        reponze['message']['message'] = EMessage.VALIDATE_ERROR;
        reponze['message']['error'] = messages;
        const clearanceCreateLogId: number = value['clearanceCreateLogId'];
        const mongoLog: IClearanceCreateLog = {
          "clearanceCreateLogId": clearanceCreateLogId,
          "data": value,
          "message": reponze['message']['message'],
          "cause": messages,
          "processResult": reponze,
          "type": ClearanceCreateKey.ONLY_OUTBOUND,
          "isSuccess": reponze['status']
        }
        ClearanceCreateLogMongo.createLog(mongoLog);
        Utilities.updateCreateLog(clearanceCreateLogId, false);
        return reponze;
      }

      const cloneManifest: IExportValidate = { ...value };
      let storeDetail: Map<string, ExportDetail[]> = new Map();
      const HAWBClearance: string = cloneManifest[EConfigure.HAWB_CLEARANCE_FIELD] || '';


      const existOptional: Optional = new Optional();
      existOptional.setAttributes([EConfigure.HAWB_FIELD, EConfigure.HAWB_CLEARANCE_FIELD, 'declarationNo']);
      existOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWBClearance),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      existOptional.setOrderby([
        new OrderBy('createdAt', EConfigure.DESCENDING)
      ]);

      const mecQuery: any = this.mecRepository.getOneOptional(existOptional);
      const edaQuery: any = this.edaRepository.getOneOptional(existOptional);

      const [existMEC, existEDA] = await Promise.all([mecQuery, edaQuery]);

      let internalHAWB: string = Utilities.randomString(20);
      const valueClearanceVND: number = (cloneManifest['valueClearanceVND'] && cloneManifest['valueClearanceVND'] > 0) ? Number(cloneManifest['valueClearanceVND']) : EConfigure.NUMBER_ONE;
      let totalPrice: number = Number(cloneManifest['totalPrice']);
      const priceVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
      if (existMEC) {
        if ((cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND <= 5000000) && !existMEC['declarationNo']) {
          internalHAWB = existMEC[EConfigure.HAWB_FIELD];
        }
      }
      if (existEDA) {
        if ((cloneManifest['classify'] !== ClassifyName.get(ClassifyKey.DOC) && priceVND > 5000000) && !existEDA['declarationNo']) {
          internalHAWB = existEDA[EConfigure.HAWB_FIELD];

        }
      }
      if ((cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND <= 5000000)) {
        if (existEDA) {
          const HAWB: string = existEDA[EConfigure.HAWB_FIELD];
          const destroyOptional: Optional = new Optional();
          destroyOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
            new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null)
          ]);
          const total = await this.edaRepository.destroyData(destroyOptional);
          if (total) {
            await this.exportDetailRepository.destroyDataObj({ HAWB })
          }

        }
      }
      if ((cloneManifest['classify'] !== ClassifyName.get(ClassifyKey.DOC) && priceVND > 5000000)) {
        if (existMEC) {
          const HAWB: string = existMEC[EConfigure.HAWB_FIELD];
          const destroyOptional: Optional = new Optional();
          destroyOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
            new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null)
          ]);
          const total = await this.mecRepository.destroyData(destroyOptional);
          if (total > 0) {
            await this.exportDetailRepository.destroyDataObj({ HAWB })
          }
        }
      }
      const detailOptional: Optional = new Optional();
      detailOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, internalHAWB),
      ]);
      const details: ExportDetail[] = await this.exportDetailRepository.queryAll(detailOptional);

      if (details.length > 0) {
        details.forEach((detail: ExportDetail) => {
          const HAWB: string = String(detail[EConfigure.HAWB_FIELD]);
          if (!storeDetail.has(HAWB)) {
            storeDetail.set(HAWB, [detail]);
          } else {
            const store: any = storeDetail.get(HAWB);
            store.push(detail);
            storeDetail.set(HAWB, store);
          }
        });
      }
      const dataDetails: any[] = [];
      let mecData: any;
      let edaData: any;
      let detailData: IExportDetailCreate;

      const actionCreate: number = ActionKey.CREATE;
      let warehouse: any;

      const countryCode: string = String(cloneManifest['consigneeCountryCode']);
      const stationId: number = cloneManifest['stationId'] || 0;
      if (cloneManifest['warehouseId']) {
        warehouse = await Utilities.getWarehouse(cloneManifest['warehouseId']);
      }
      const country: ICountry = await Utilities.getCountryByCode(countryCode);
      let declareCargoNameMEC: boolean = false;
      let declareCargoNameEDA: boolean = false;
      let cause = null;
      let classifyDOC: boolean = false;
      let createDetails: IExportDetailValidate[] = cloneManifest['items'] || []
      if (createDetails.length > 0) {
        createDetails.forEach((detail: IExportDetailValidate, index: number) => {
          detail = Utilities.convertNull(detail);
          detailData = {};
          const cloneDetail: IExportDetailValidate = { ...detail };
          const itemNameEN: string = cloneDetail['itemName'] || '';
          const checkName: string = itemNameEN;
          if (checkName && checkName.toUpperCase().includes("DOC")) {
            classifyDOC = true;
          }
          const currencyCode: string = cloneDetail['currencyCode'];

          if (cloneDetail['invoiceValue'] && !cloneDetail['invoiceUnitPrice']) {
            const totalPrice: number = Number(cloneDetail['invoiceValue']);
            const priceDetailVND: number = +((totalPrice * valueClearanceVND).toFixed(0));
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
              detailData['invoiceUnitPrice'] = +((totalPrice / Number(cloneDetail['quantity'])).toFixed(0));
            } else {
              detailData['invoiceValue'] = totalPrice;
              detailData['invoiceUnitPrice'] = totalPrice / Number(cloneDetail['quantity']);
            }
            detailData['priceVND'] = priceDetailVND;
          }
          if (!cloneDetail['invoiceValue'] && cloneDetail['invoiceUnitPrice']) {
            const invoiceUnitPrice: number = Number(cloneDetail['invoiceUnitPrice']);
            const totalPrice: number = invoiceUnitPrice * Number(cloneDetail['quantity']);
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceUnitPrice'] = +(invoiceUnitPrice.toFixed(0));
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
            } else {
              detailData['invoiceUnitPrice'] = invoiceUnitPrice;
              detailData['invoiceValue'] = totalPrice;
            }
            detailData['priceVND'] = +(totalPrice * valueClearanceVND).toFixed(0);
          }
          if (cloneDetail['invoiceValue'] && cloneDetail['invoiceUnitPrice']) {
            const totalPrice: number = Number(cloneDetail['invoiceValue']);
            if (currencyCode === EConfigure.CURRENCY_VND) {
              detailData['invoiceUnitPrice'] = +(cloneDetail['invoiceUnitPrice'].toFixed(0));
              detailData['invoiceValue'] = +(totalPrice.toFixed(0));
            } else {
              detailData['invoiceUnitPrice'] = cloneDetail['invoiceUnitPrice'];
              detailData['invoiceValue'] = totalPrice;
            }
            detailData['priceVND'] = +(totalPrice * valueClearanceVND).toFixed(0);
          }



          detailData['HSCode'] = cloneDetail['HSCode'] ? String(cloneDetail['HSCode']) : '';
          detailData['itemName'] = itemNameEN;
          detailData['itemNameVN'] = cloneDetail['itemNameVN'];
          detailData['url'] = cloneDetail['url'];
          detailData['quantity1'] = cloneDetail['quantity'];
          detailData['unitPriceCurrencyCode'] = currencyCode;
          detailData['position'] = index;



          detailData['valueClearanceVND'] = valueClearanceVND;
          detailData['priceQuantityUnit'] = EConfigure.PIECE;
          detailData['quantity1'] = cloneDetail['quantity'];
          detailData['quantity2'] = cloneDetail['quantity'];
          detailData['quantityUnitCode1'] = EConfigure.PIECE;
          detailData['quantityUnitCode2'] = EConfigure.PIECE;

          detailData[EConfigure.HAWB_FIELD] = internalHAWB;
          detailData['placeOfOriginCode'] = EConfigure.VN;

          if (detail['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
            detailData['weightKG'] = Number(cloneManifest['weight']);
          } else {
            detailData['weightKG'] = Utilities.convertKG(Number(detail['weight']), String(detail['unitOfMass']));
          }
          detailData['productId'] = detail['productId'];

          dataDetails.push(detailData);
        });
      }
      if (cloneManifest['classify'] === ClassifyName.get(ClassifyKey.DOC) || priceVND <= 5000000) {
        if (details.length > 0) {
          declareCargoNameMEC = details.every((detail: ExportDetail) => {
            return detail.itemNameVN;
          });
        }

        mecData = {
          'HAWB': internalHAWB,
          'HAWBClearance': HAWBClearance,
          'stationId': stationId,
          'phase': actionCreate,
          'serviceId': cloneManifest['serviceId'],
        };

        if (declareCargoNameMEC) {
          mecData['phase'] = ActionKey.UPDATE_CARGO_NAME;
        }
        mecData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
        //if(!cloneManifest['exporterCode']) {
        //  mecData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
        //} else {
        //  mecData['exporterCode'] = Utilities.subString(cloneManifest['exporterCode'], 0, 15);
        //}

        if (!cloneManifest['exporterName']) {
          mecData['exporterName'] = EConfigure.IMPORTER_NAME
        } else {
          mecData['exporterName'] = cloneManifest['exporterName'];
          mecData['exporterFullName'] = cloneManifest['exporterName'];
        }
        mecData['postCode'] = cloneManifest['exporterPostCode'];
        mecData['addressOfExporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['exporterAddress'] as string), 0, 200);
        mecData['telephoneNumber'] = cloneManifest['exporterTelephoneNumber'];
        mecData['consigneeCode'] = cloneManifest['consigneeCode'];
        mecData['consigneeName'] = cloneManifest['consigneeName'];
        mecData['consigneeTelephoneNumber'] = Utilities.subString(cloneManifest['consigneeTelephoneNumber'], 0, 20);

        const addresses = Utilities.handleAddress(cloneManifest['consigneeAddress'] as string);
        if (addresses) {
          for (const [key, value] of Object.entries(addresses)) {
            if (key <= '3') {
              mecData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
            }
          }
        }
        if (cloneManifest['departureDate'] && cloneManifest['flightNo']) {
          mecData['flightNo'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['departureDate']);
          mecData['departureDate'] = cloneManifest['departureDate'];
        } else {
          mecData['flightNo'] = cloneManifest['flightNo'];
        }
        if (country && Object.keys(country).length > 0) {
          mecData['address4'] = country['fullName'];
          mecData['countryCode'] = countryCode;
          mecData['theFinalDestination'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        }
        mecData['MAWB'] = cloneManifest['MAWB'];
        mecData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;
        if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
          mecData['cargoWeight'] = cloneManifest['weight'];
        } else {
          mecData['cargoWeight'] = Utilities.convertKG(Number(cloneManifest['weight']), String(cloneManifest['unitOfMass']));
        }
        if (warehouse && Object.keys(warehouse).length > 0) {
          mecData['customsWarehouseCode'] = warehouse['code'];
          mecData['loadingPortCode'] = warehouse['unloadingPortCode'];
          mecData['customsOffice'] = warehouse['customsOffice'];
          mecData['customsSubSection'] = warehouse['customsSubSection'];
          mecData['terminalName'] = warehouse['id'];
        }
        mecData['currencyCodeOfTaxValue'] = cloneManifest['currencyCode'];
        if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
          mecData['totalOfTaxValue'] = +(totalPrice.toFixed(0));
        } else {
          mecData['totalOfTaxValue'] = totalPrice;
        }

        mecData['valueClearanceVND'] = valueClearanceVND;
        mecData['customerBusinessId'] = cloneManifest['customerBusinessId'];
        mecData['customerPersonalId'] = cloneManifest['customerPersonalId'];
        mecData['notes'] = cloneManifest['note'];
        mecData['priceVND'] = priceVND;
        mecData['hubId'] = cloneManifest['hubId'];
        mecData['clientId'] = cloneManifest['clientId'];

        if (totalPrice === 0 || (totalPrice >= 0 && classifyDOC)) {
          mecData['classify'] = ClassifyName.get(ClassifyKey.DOC);
        } else {
          mecData['classify'] = cloneManifest['classify'];
        }

        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
          new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
        ]);
        try {
          const total = await this.mecRepository.destroyData(optional);

          if (total > 0) {
            const optional: Optional = new Optional();
            optional.setWhere([
              new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
            ]);
            await this.exportDetailRepository.destroyData(optional);
          }

          const [micCreated] = await Promise.all([
            this.mecRepository.createData(mecData),
            this.exportDetailRepository.createBulk(dataDetails),
          ]);
          if (micCreated) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.CREATED_DATA;
            reponze['message']['data'] = {
              'succesfull': null,
              'fail': null
            }

          }
        } catch (error) {
          console.log('---- create imports error: %o', error);
          cause = (error as any).message;
          reponze['message']['data']['fail'].push()
        }
        mecData['details'] = dataDetails;
        const clearanceCreateLogId: number = value['clearanceCreateLogId'];
        const mongoLog: IClearanceCreateLog = {
          "clearanceCreateLogId": clearanceCreateLogId,
          "data": value,
          "message": reponze['message']['message'],
          "cause": cause,
          "handleData": mecData,
          "processResult": reponze,
          "classify": ClassifyValidateName.get(ClassifyValidateKey.MEC) as string,
          "type": ClearanceCreateKey.ONLY_OUTBOUND,
          "isSuccess": reponze['status']
        }
        ClearanceCreateLogMongo.createLog(mongoLog);
        if (!cause && reponze['status']) {
          Utilities.updateCreateLog(clearanceCreateLogId, true);
        } else {
          Utilities.updateCreateLog(clearanceCreateLogId, false);
        }
      } else {
        if (dataDetails.length > 0) {
          declareCargoNameEDA = dataDetails.every((detail: ExportDetail) => {
            return detail['itemNameVN'] && detail['HSCode'] && detail['HSCode'].match(/^0+/) === null && detail['HSCode'].match(/^[0-9]+$/) !== null;
          });
        }

        edaData = {
          'HAWB': internalHAWB,
          'HAWBClearance': HAWBClearance,
          'cargoNo': HAWBClearance,
          'stationId': stationId,
          'phase': actionCreate,
          'serviceId': cloneManifest['serviceId'],
        };

        if (declareCargoNameEDA) {
          edaData['phase'] = ActionKey.UPDATE_CARGO_NAME;
        }
        edaData['MAWB'] = cloneManifest['MAWB'];
        edaData['cargoPiece'] = cloneManifest['piece'] ? cloneManifest['piece'] : EConfigure.CARGO_PIECE;

        edaData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
        //if(!cloneManifest['exporterCode']) {
        //  edaData['exporterCode'] = EConfigure.IMPORTER_CODE as string;
        //} else {
        //  edaData['exporterCode'] = Utilities.subString(cloneManifest['exporterCode'], 0, 15);
        //}

        if (!cloneManifest['exporterName']) {
          edaData['exporterName'] = EConfigure.IMPORTER_NAME
        } else {
          edaData['exporterName'] = cloneManifest['exporterName'];
          edaData['exporterFullName'] = cloneManifest['exporterName'];
        }
        edaData['postCode'] = cloneManifest['exporterPostCode'];
        edaData['addressOfExporter'] = Utilities.subString(Utilities.handleRemoveSpaceComma(cloneManifest['exporterAddress'] as string), 0, 200);
        edaData['telephoneNumber'] = Utilities.subString(cloneManifest['exporterTelephoneNumber'], 0, 20);
        edaData['consigneeCode'] = cloneManifest['consigneeCode'];
        edaData['consigneeName'] = cloneManifest['consigneeName'];
        edaData['consigneeTelephoneNumber'] = cloneManifest['consigneeTelephoneNumber'];
        edaData['postCodeIdentification'] = cloneManifest['consigneePostCode'];
        const addresses = Utilities.handleAddress(cloneManifest['consigneeAddress'] as string);
        if (addresses) {
          for (const [key, value] of Object.entries(addresses)) {
            if (key <= '3') {
              edaData[`address${key}`] = Utilities.subString(Utilities.handleRemoveSpaceComma(String(value)), 0, 50);
            }
          }
        }
        edaData['declarationKindCode'] = EConfigure.H21;
        edaData['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT as string;
        edaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;
        edaData['termOfPayment'] = EConfigure.KHONGTT;
        if (warehouse && Object.keys(warehouse).length > 0) {
          edaData['customsWarehouseCode'] = warehouse['code'];
          edaData['plannedDeclarantCode'] = warehouse['agencyCode'];
          edaData['loadingPortCode'] = warehouse['unloadingPortCode'];
          edaData['loadingPortName'] = warehouse['unloadingPortCode'];
          edaData['customsOffice'] = warehouse['customsOffice'];
          edaData['customsSubSection'] = warehouse['customsSubSection'];
          edaData['terminalName'] = warehouse['id'];
        }
        if (cloneManifest['departureDate'] && cloneManifest['flightNo']) {
          edaData['loadingPlannedVesselName'] = Utilities.flightCode(cloneManifest['flightNo'] as string, cloneManifest['departureDate']);
          edaData['departurePlannedDate'] = cloneManifest['departureDate'];
        } else {
          edaData['loadingPlannedVesselName'] = cloneManifest['flightNo'];
        }

        if (country && Object.keys(country).length > 0) {
          edaData['address4'] = country['fullName'];
          edaData['countryCode'] = countryCode;
          edaData['theFinalDestinationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
          edaData['theFinalDestinationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        }

        if (cloneManifest['unitOfMass'] === WeightName.get(WeightKey.KILOGRAM)) {
          edaData['cargoWeightGross'] = Number(cloneManifest['weight']);
        } else {
          edaData['cargoWeightGross'] = Utilities.convertKG(Number(cloneManifest['weight']), String(cloneManifest['unitOfMass']));
        }
        edaData['weightUnitCodeGross'] = String(WeightClearance.get('kg'));

        edaData['invoiceCurrencyCode'] = cloneManifest['currencyCode'];
        edaData['currencyCodeOfTaxValue'] = cloneManifest['currencyCode'];
        if (cloneManifest['currencyCode'] === EConfigure.CURRENCY_VND) {
          edaData['totalOfTaxValue'] = +(totalPrice.toFixed(0));;
          edaData['totalInvoicePrice'] = +(totalPrice.toFixed(0));;
        } else {
          edaData['totalOfTaxValue'] = totalPrice;
          edaData['totalInvoicePrice'] = totalPrice;
        }

        edaData['valueClearanceVND'] = valueClearanceVND;
        edaData['customerBusinessId'] = cloneManifest['customerBusinessId'] ? String(cloneManifest['customerBusinessId']) : null;
        edaData['customerPersonalId'] = cloneManifest['customerPersonalId'] ? String(cloneManifest['customerPersonalId']) : null;
        edaData['notes'] = cloneManifest['note'];
        edaData['classify'] = cloneManifest['classify'];
        edaData['priceVND'] = priceVND;
        edaData['hubId'] = cloneManifest['hubId'];

        edaData['taxPayer'] = EConfigure.TAX_PAYER;
        edaData['invoicePriceKindCode'] = EConfigure.TYPE_A;
        edaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
        edaData['codeOfExtendingDueDate'] = EConfigure.TYPE_A;
        edaData['invoicePriceConditionCode'] = EConfigure.CIF;

        edaData['clientId'] = cloneManifest['clientId'];

        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
          new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
        ]);
        try {
          const total = await this.edaRepository.destroyData(optional);
          if (total > 0) {
            const optional: Optional = new Optional();
            optional.setWhere([
              new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, internalHAWB),
            ]);
            await this.exportDetailRepository.destroyData(optional);

          }
          const [idaCreated] = await Promise.all([
            this.edaRepository.createData(edaData),
            this.exportDetailRepository.createBulk(dataDetails),
          ]);
          if (idaCreated) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.CREATED_DATA;
            reponze['message']['data'] = {
              'succesfull': null,
              'fail': null
            }
          }
        } catch (error) {
          console.log('---- create imports error: %o', error);
          cause = (error as any).message;
          reponze['message']['data']['fail'].push()
        }

        edaData['details'] = dataDetails;
        const clearanceCreateLogId: number = value['clearanceCreateLogId'];
        const mongoLog: IClearanceCreateLog = {
          "clearanceCreateLogId": clearanceCreateLogId,
          "data": value,
          "message": reponze['message']['message'],
          "cause": cause,
          "handleData": edaData,
          "processResult": reponze,
          "classify": ClassifyValidateName.get(ClassifyValidateKey.EDA) as string,
          "type": ClearanceCreateKey.ONLY_OUTBOUND,
          "isSuccess": reponze['status']
        }
        ClearanceCreateLogMongo.createLog(mongoLog);
        if (!cause && reponze['status']) {
          Utilities.updateCreateLog(clearanceCreateLogId, true);
        } else {
          Utilities.updateCreateLog(clearanceCreateLogId, false);
        }

      }
      return reponze;
    } catch (error) {
      const clearanceCreateLogId: number = value['clearanceCreateLogId'];
      const mongoLog: IClearanceCreateLog = {
        "clearanceCreateLogId": clearanceCreateLogId,
        "data": value,
        "message": reponze['message']['message'],
        "cause": (error as Error).message,
        "handleData": null,
        "processResult": reponze,
        "classify": null,
        "type": ClearanceCreateKey.ONLY_OUTBOUND,
        "isSuccess": reponze['status']
      }
      ClearanceCreateLogMongo.createLog(mongoLog);
      Utilities.updateCreateLog(clearanceCreateLogId, false);
      Utilities.sendDiscordErr('[service][import][createImportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerEDE(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.edaRepository.queryAll(serviceOptional);
      const EDAQuery: any = this.edaRepository.queryAll(optional);
      const [EDAs, sameService] = await Promise.all([EDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'EDE';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (EDAs.length > 0) {
        EDAs.forEach((eda: IEDA, index: number) => {
          foundHAWB[String(eda[EConfigure.HAWB_FIELD])] = String(eda[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(eda[EConfigure.HAWB_CLEARANCE_FIELD]));
          let cause: any = null;
          if (eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
            }
          }
          if (eda['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && eda['isEditProcessing'] === true) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang chờ thông điệp ECUS`
            }
          }
          if (eda['isIEDAed'] === false) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': 'Chưa khai báo thời khai EDA0x'
            }
          }
          if (cause) {
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push(cause);
          }

          // const register: IRegister = {
          //   'HAWB': String(ida['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(ida['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.XacNhanChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'typeAction': typeAction,
                'isIEDAed': false,
                'isEditProcessing': true,
                'dateAction': now,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.edaRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerEDE]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerIEDA(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.edaRepository.queryAll(serviceOptional);
      const EDAQuery: any = this.edaRepository.queryAll(optional);
      const [EDAs, sameService] = await Promise.all([EDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      let typeAction: string = 'EDA0';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (EDAs.length > 0) {
        EDAs.forEach((eda: IEDA, index: number) => {
          foundHAWB[String(eda[EConfigure.HAWB_FIELD])] = String(eda[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(eda[EConfigure.HAWB_CLEARANCE_FIELD]));
          let cause: any = null;
          if (!eda['isEDCed'] || eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
            }
          }
          if (eda['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && eda['isEditProcessing'] === true) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang chờ thông điệp ECUS`
            }
          }
          if (eda['times'] === 9) {
            reponze['status'] = false;
            cause = {
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Sỗ lần gửi cập nhật vượt quá quy định 9 lần`
            }
          }
          if (cause) {
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push(cause);
          }
          typeAction = `${typeAction}${(eda['times']) ? Number(eda['times']) + 1 : 1}`;
          // const register: IRegister = {
          //   'HAWB': String(ida['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(ida['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.ChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              await Promise.all(EDAs.map(async (eda: EDA) => {
                eda['phase'] = action;
                eda['dateAction'] = now;
                eda['typeAction'] = `${typeAction}${(eda['times']) ? Number(eda['times']) + 1 : 1}`;
                eda['isEditProcessing'] = true;
                eda['isIEDAed'] = true;
                if (isPrioritize != undefined) {
                  eda['isPrioritize'] = isPrioritize;
                }
                await eda.save()
              }));
              // await this.importTransactionRepository.createBulk(transactions);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerIEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerEDC(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.edaRepository.queryAll(serviceOptional);
      const EDAQuery: any = this.edaRepository.queryAll(optional);
      const [EDAs, sameService] = await Promise.all([EDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'EDC';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (EDAs.length > 0) {
        EDAs.forEach((eda: IEDA, index: number) => {
          foundHAWB[String(eda[EConfigure.HAWB_FIELD])] = String(eda[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(eda[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (eda['phase'] === ActionKey.CREATE
            || eda['phase'] === ActionKey.UPDATE_CARGO_NAME
            || eda['phase'] === ActionKey.SUBMIT_INFOMATION
            || (eda['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE && eda['isError'] === false)
            || eda['phase'] === ActionKey.INSPECTION_KIND
            || eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
            });
          }
          // const register: IRegister = {
          //   'HAWB': String(ida['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(ida['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.ChinhThuc, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isEDCed': true,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);

              await Promise.all([
                this.edaRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerEDC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerEDA(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.edaRepository.queryAll(serviceOptional);
      const EDAQuery: any = this.edaRepository.queryAll(optional);
      const [EDAs, sameService] = await Promise.all([EDAQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_INFOMATION;
      const typeAction: string = 'EDA';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (EDAs.length > 0) {
        EDAs.forEach((eda: IEDA, index: number) => {
          foundHAWB[String(eda[EConfigure.HAWB_FIELD])] = String(eda[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(eda[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (eda['phase'] === ActionKey.CREATE
            || eda['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE
            || eda['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE
            || eda['phase'] === ActionKey.INSPECTION_KIND
            || eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
            });
          }
          // const register: IRegister = {
          //   'HAWB': String(ida['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(ida['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, false, ClearanceTypeId.Tam, HAWBs, isPrioritize);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isError': false,
                'isECUSError': false,
                'isPrioritize': isPrioritize
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);

              await Promise.all([
                this.edaRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
                Utilities.updateDeclaration(HAWBs)
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerMEE(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.mecRepository.queryAll(serviceOptional);
      const MECQuery: any = this.mecRepository.queryAll(optional);
      const [MECs, sameService] = await Promise.all([MECQuery, sameServiceQuery]);
      const action: number = ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE;
      const typeAction: string = 'MEE';
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      // let transactions: IRegister[] = [];
      if (MECs.length > 0) {
        MECs.forEach((mec: IMEC, index: number) => {
          foundHAWB[String(mec[EConfigure.HAWB_FIELD])] = String(mec[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(mec[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (mec['phase'] === ActionKey.CREATE
            || mec['phase'] === ActionKey.UPDATE_CARGO_NAME
            || mec['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE
            || (mec['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE && mec['isError'] === false)
            || mec['phase'] === ActionKey.ACCEPT_CLEARANCE
          ) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mec[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(mec['phase_name']['vi']).toLowerCase()}`
            });
          }
          // const register: IRegister = {
          //   'HAWB': String(mic['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(mic['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, true, ClearanceTypeId.ChinhSua, HAWBs);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
              }
              if (isPrioritize != undefined) {
                update['isPrioritize'] = isPrioritize;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.mecRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerMEE]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async registerMEC(HAWBs: string[], isPrioritize: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      const cloneHAWBs: string[] = [...HAWBs];
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const serviceOptional: Optional = new Optional();
      serviceOptional.setAttributes(['serviceId']);
      serviceOptional.setGroup(['serviceId']);
      const sameServiceQuery: any = this.mecRepository.queryAll(serviceOptional);
      const MECQuery: any = this.mecRepository.queryAll(optional);
      const [MECs, sameService] = await Promise.all([MECQuery, sameServiceQuery]);
      let foundHAWB: any = {};
      let HAWBClearances: string[] = [];
      const typeAction: string = EConfigure.MEC;
      const action: number = ActionKey.SUBMIT_CUSTOM_CLEARANCE;
      // let transactions: IRegister[] = [];

      if (MECs.length > 0) {
        MECs.forEach((mec: IMEC, index: number) => {
          foundHAWB[String(mec[EConfigure.HAWB_FIELD])] = String(mec[EConfigure.HAWB_CLEARANCE_FIELD]);
          HAWBClearances.push(String(mec[EConfigure.HAWB_CLEARANCE_FIELD]));
          if (mec['phase'] === ActionKey.CREATE
            || (mec['phase'] === ActionKey.SUBMIT_CUSTOM_CLEARANCE && mec['isError'] === false)
            || mec['phase'] === ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE
            || mec['phase'] === ActionKey.ACCEPT_CLEARANCE
            || mec['phase'] === ActionKey.INSPECTION_KIND
          ) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.UPDATE_FAIL;
            reponze['message']['error'].push({
              'HAWB': mec[EConfigure.HAWB_CLEARANCE_FIELD],
              'cause': `Đang ở trạng thái ${String(mec['phase_name']['vi']).toLowerCase()}`
            });
          }
          // const register: IRegister = {
          //   'HAWB': String(mic['HAWB']),
          //   'action': action,
          //   'employeeId': employeeId,
          //   'classify': String(mic['classify']),
          //   'typeAction': typeAction,
          // }
          // transactions.push(register);
        });
        let difference: string[] = cloneHAWBs.filter((x: any) => !foundHAWB[x]);
        if (difference.length > 0) {
          reponze['status'] = false;
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': difference.join(','),
            'cause': EMessage.NOT_FOUND
          });
        }
        if (reponze['status']) {
          if (sameService.length > 1) {
            reponze['status'] = false;
            reponze['message']['message'] = EMessage.SERVICE_NOT_SAME
          } else {
            const { serviceId } = Object.assign({}, ...sameService);
            let clearance: any = await new Clearance().push(serviceId, true, ClearanceTypeId.ChinhThuc, HAWBs, isPrioritize);
            if (clearance['isSuccess']) {
              const now: string = moment().format(EConfigure.FULL_TIME);
              const update: any = {
                'phase': action,
                'dateAction': now,
                'typeAction': typeAction,
                'isError': false,
                'isECUSError': false,
                'isPrioritize': isPrioritize
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
              ]);
              await Promise.all([
                this.mecRepository.updateData(update, updateOptional),
                // this.importTransactionRepository.createBulk(transactions),
                Utilities.updateDeclaration(HAWBs)
              ]);
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
              reponze['message']['data']['succesfull'] = HAWBClearances;
            } else {
              reponze['status'] = clearance['isSuccess'];
              reponze['message']['message'] = clearance['message'];
              reponze['message']['data']['fail'] = HAWBClearances;
            }
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][registerMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateMaster(manifestMasters: any[]): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      let failHAWBs: string[] = [];
      let successHAWBs: string[] = [];
      // let transactions: IMaster[] = [];
      transaction = await this._sequelize.transaction();
      await Promise.all(manifestMasters.map(async (manifestMaster: any) => {
        let warehouse: any;
        const cloneMaster: IMasterValidate = { ...manifestMaster };
        const HAWB: string = manifestMaster[EConfigure.HAWB_FIELD];
        const HAWBClearance: string = manifestMaster[EConfigure.HAWB_CLEARANCE_FIELD];
        const MAWB: any = cloneMaster['MAWB'] ? String(cloneMaster['MAWB']) : null;
        const departureDate: any = cloneMaster['departureDate'] ? String(cloneMaster['departureDate']) : null;
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWBClearance),
          new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.IN, `${ActionKey.CREATE}, ${ActionKey.UPDATE_CARGO_NAME}`),
        ]);
        let flightCode: any = null;
        if (cloneMaster['departureDate']) {
          flightCode = Utilities.flightCode(String(cloneMaster['flightNo']), String(cloneMaster['departureDate']));
        }
        if (cloneMaster['warehouseId']) {
          warehouse = await Utilities.getWarehouse(cloneMaster['warehouseId']);
        }

        const mecUpdate: IMECMaster = {
          'MAWB': MAWB,
          'flightNo': flightCode,
          'departureDate': departureDate,
          'hubId': cloneMaster['hubId']
        }
        const edaUpdate: IEDAMaster = {
          'MAWB': MAWB,
          'departurePlannedDate': departureDate,
          'loadingPlannedVesselName': flightCode,
          'hubId': cloneMaster['hubId']
        }

        edaUpdate['meansOfTransportationCode'] = EConfigure.TRANSPORT_FLIGHT;
        if (cloneMaster['meanOfTransportationCode']) {
          edaUpdate['meansOfTransportationCode'] = cloneMaster['meanOfTransportationCode'];
        }

        if (warehouse && Object.keys(warehouse).length > 0) {
          mecUpdate['customsWarehouseCode'] = warehouse['code'];
          mecUpdate['loadingPortCode'] = warehouse['unloadingPortCode'];
          mecUpdate['customsOffice'] = warehouse['customsOffice'];
          mecUpdate['customsSubSection'] = warehouse['customsSubSection'];
          mecUpdate['terminalName'] = String(warehouse['id']);

          edaUpdate['customsWarehouseCode'] = warehouse['code'];
          edaUpdate['plannedDeclarantCode'] = warehouse['agencyCode'];
          edaUpdate['loadingPortCode'] = warehouse['unloadingPortCode'];
          edaUpdate['loadingPortName'] = warehouse['unloadingPortCode'];
          edaUpdate['customsOffice'] = warehouse['customsOffice'];
          edaUpdate['customsSubSection'] = warehouse['customsSubSection'];
          edaUpdate['terminalName'] = String(warehouse['id']);
        }

        // if(cloneMaster['departureCountry']) {
        //   let country: ICountry = null || {};
        //   const countryCode: any = cloneMaster['departureCountry'];
        //   if(countryCode){
        //     country = await Utilities.getCountryByCode(countryCode);
        //     if(country && Object.keys(country).length > 0) {
        //       // MEC
        //       mecUpdate['address4'] = country['fullName'];
        //       mecUpdate['countryCode'] = countryCode;
        //       mecUpdate['theFinalDestination'] = `${countryCode}${EConfigure.OTHER_CODE}`;

        //       // EDA
        //       edaUpdate['address4'] = country['fullName'];
        //       edaUpdate['countryCode'] = countryCode;
        //       edaUpdate['theFinalDestinationCode'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        //       edaUpdate['theFinalDestinationName'] = `${countryCode}${EConfigure.OTHER_CODE}`;
        //     }
        //   }
        // }
        const [mectotal] = await this.mecRepository.updateDataTrx(mecUpdate, optional, transaction);
        const [edatotal] = await this.edaRepository.updateDataTrx(edaUpdate, optional, transaction);
        if (mectotal > 0 || edatotal > 0) {
          // const master: IMaster = {
          //   'HAWB': HAWB,
          //   'MAWB': MAWB,
          //   'action': ActionKey.UPDATE_MASTER
          // }
          // await this.importTransactionRepository.createDataTrx(master, transaction);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
          successHAWBs.push(HAWBClearance);
        } else {
          reponze['status'] = false;
          failHAWBs.push(HAWBClearance);
        }
      }));
      await transaction.commit();

      reponze['message']['data'] = {
        'succesfull': successHAWBs,
        'fail': failHAWBs
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][updateMaster]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async updateMEC(HAWB: string, value: any, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optional.setRelation(['exportDetails']);
      const mec: IMEC = await this.mecRepository.getOneOptional(optional);
      if (mec) {
        if (mec['phase'] === ActionKey.ACCEPT_CLEARANCE) {
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': mec[EConfigure.HAWB_CLEARANCE_FIELD],
            'cause': `Đang ở trạng thái ${String(mec['phase_name']['vi']).toLowerCase()}`
          });
        } else {
          transaction = await this._sequelize.transaction();
          const mecUpdate: IMECCreate = value;
          const clearanceVND: any = value['valueClearanceVND'] ? (Number(value['valueClearanceVND'])) : 0;
          const micDetailUpdates: IExportDetailCreate[] = value['exportDetails'];
          const currencyCode: string = String(mecUpdate['currencyCodeOfTaxValue']);

          mecUpdate['valueClearanceVND'] = clearanceVND;
          if (mecUpdate['totalOfTaxValue']) {
            mecUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(mecUpdate['totalOfTaxValue']) * clearanceVND).toFixed(0) : +(Number(mecUpdate['totalOfTaxValue'])).toFixed(0);
          } else {
            mecUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(mec['totalOfTaxValue']) * clearanceVND).toFixed(0) : +(Number(mec['totalOfTaxValue'])).toFixed(0);
          }

          if (currencyCode === EConfigure.CURRENCY_VND) {
            mecUpdate['totalOfTaxValue'] = +(Number(mecUpdate['totalOfTaxValue']).toFixed(0));
          } else {
            mecUpdate['totalOfTaxValue'] = mecUpdate['totalOfTaxValue'];
          }

          mecUpdate['exporterFullName'] = value['importerName'];
          const micOptional: Optional = new Optional();
          micOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB)
          ]),
            await this.mecRepository.updateDataTrx(mecUpdate, micOptional, transaction);
          if (micDetailUpdates.length > 0) {
            await Promise.all(micDetailUpdates.map(async (micDetailUpdate: IExportDetailCreate) => {
              let itemNameVN: string = Utilities.removeSpeialCharacter(micDetailUpdate['itemNameVN'] as string);
              itemNameVN = Utilities.removeGeneralToken(itemNameVN);
              micDetailUpdate['itemNameVN'] = itemNameVN;
              micDetailUpdate['unitPriceCurrencyCode'] = currencyCode;
              micDetailUpdate['placeOfOriginCode'] = String(mecUpdate['countryCode']);
              micDetailUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(micDetailUpdate['invoiceValue']) * clearanceVND).toFixed(0) : +(Number(micDetailUpdate['invoiceValue'])).toFixed(0);

              if (currencyCode === EConfigure.CURRENCY_VND) {
                micDetailUpdate['invoiceValue'] = micDetailUpdate['invoiceValue'] ? +(micDetailUpdate['invoiceValue'].toFixed(0)) : 0;
                micDetailUpdate['invoiceUnitPrice'] = +(Number(micDetailUpdate['invoiceValue']) / Number(micDetailUpdate['quantity1'])).toFixed(0);
              } else {
                micDetailUpdate['invoiceValue'] = micDetailUpdate['invoiceValue'];
                micDetailUpdate['invoiceUnitPrice'] = Number(micDetailUpdate['invoiceValue']) / Number(micDetailUpdate['quantity1']);
              }

              const checkHSCodeDetail: Optional = new Optional();
              checkHSCodeDetail.setWhere([
                new Where(EConfigure.AND, 'name', EConfigure.EQUAL, micDetailUpdate['itemName']),
                new Where(EConfigure.AND, 'nameVN', EConfigure.EQUAL, itemNameVN),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);
              if (micDetailUpdate['HSCode']) {
                checkHSCodeDetail.getWhere().push(new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, micDetailUpdate['HSCode']))
              }
              const hsCodeDetail: IHSCodeDetail = await this.hscodeDetailRepository.getOneOptional(checkHSCodeDetail);
              if (!hsCodeDetail) {
                const newHSCodeDetail: any = {
                  'name': micDetailUpdate['itemName'],
                  'nameVN': itemNameVN
                }
                if (micDetailUpdate['HSCode']) {
                  newHSCodeDetail['hsCode'] = micDetailUpdate['HSCode'];
                }
                await this.hscodeDetailRepository.createData(newHSCodeDetail);
              }
              const optional: Optional = new Optional();
              optional.setWhere([
                new Where(EConfigure.AND, 'id', EConfigure.EQUAL, micDetailUpdate['id']),
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, micDetailUpdate[EConfigure.HAWB_FIELD])
              ]);
              await this.exportDetailRepository.updateDataTrx(micDetailUpdate, optional, transaction);
            }));
          }
          await transaction.commit();
          // const updateTransaction: IUpdateAction = {
          //   'HAWB': HAWB,
          //   'employeeId': employeeId,
          //   'action': ActionKey.UPDATE_MIC,
          //   'classify': mic['classify'] as string,
          //   'data': mic,
          //   'newData': value,
          // }
          // await this.importTransactionRepository.createData(updateTransaction);
          const exportDetailService = new ExportClearanceDetailService();
          await exportDetailService.checkUpdateItemNameVN([HAWB]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][updateMEC]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async updateEDA(HAWB: string, value: any, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const eda: IEDA = await this.edaRepository.getOneOptional(optional);
      if (eda) {
        if (eda['phase'] === ActionKey.ACCEPT_CLEARANCE) {
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          reponze['message']['error'].push({
            'HAWB': eda[EConfigure.HAWB_CLEARANCE_FIELD],
            'cause': `Đang ở trạng thái ${String(eda['phase_name']['vi']).toLowerCase()}`
          });
        } else {
          transaction = await this._sequelize.transaction();
          const edaUpdate: IEDA = value;
          const clearanceVND: any = value['valueClearanceVND'] ? (Number(value['valueClearanceVND'])) : 0;
          const edaDetailUpdates: IExportDetailCreate[] = value['exportDetails'];
          const currencyCode: string = String(edaUpdate['invoiceCurrencyCode']);

          edaUpdate['valueClearanceVND'] = clearanceVND;
          if (edaUpdate['totalInvoicePrice']) {
            edaUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(edaUpdate['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(edaUpdate['totalInvoicePrice'])).toFixed(0);
          } else {
            edaUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(eda['totalInvoicePrice']) * clearanceVND).toFixed(0) : +(Number(eda['totalInvoicePrice'])).toFixed(0);
          }
          if (currencyCode === EConfigure.CURRENCY_VND) {
            edaUpdate['totalInvoicePrice'] = +(Number(edaUpdate['totalInvoicePrice']).toFixed(0));
          } else {
            edaUpdate['totalInvoicePrice'] = edaUpdate['totalInvoicePrice'];
          }
          edaUpdate['exporterFullName'] = edaUpdate['exporterName'];


          if (eda['classify'] === ClassifyName.get(ClassifyKey.COM)) {
            if (!edaUpdate['dateClearanced']) {
              edaUpdate['dateClearanced'] = moment().format(EConfigure.FULL_TIME);
            }
            edaUpdate['declarationNo'] = value['declarationNoCustomer'];
            edaUpdate['phase'] = ActionKey.ACCEPT_CLEARANCE;
          }
          const edaOptional: Optional = new Optional();
          edaOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          ]),
            await this.edaRepository.updateDataTrx(edaUpdate, edaOptional, transaction);
          if (edaDetailUpdates.length > 0) {
            await Promise.all(edaDetailUpdates.map(async (edaDetailUpdate: any) => {
              if (edaDetailUpdate['HSCode'] && (edaDetailUpdate['HSCode'].substring(0, 2) == 85 || edaDetailUpdate['HSCode'].substring(0, 2) == 84)) {
                edaDetailUpdate['otherLawCode'] = EConfigure.MO;
              }
              let itemNameVN: string = Utilities.removeSpeialCharacter(edaDetailUpdate['itemNameVN']);
              itemNameVN = Utilities.removeGeneralToken(itemNameVN);
              edaDetailUpdate['itemNameVN'] = itemNameVN;
              edaDetailUpdate['placeOfOrigin'] = edaUpdate['countryCode'];
              edaDetailUpdate['priceVND'] = (clearanceVND > 0) ? +(Number(edaDetailUpdate['invoiceValue']) * clearanceVND).toFixed(0) : +(Number(edaDetailUpdate['invoiceValue'])).toFixed(0);
              edaDetailUpdate['unitPriceCurrencyCode'] = currencyCode;

              if (currencyCode === EConfigure.CURRENCY_VND) {
                edaDetailUpdate['invoiceValue'] = +(edaDetailUpdate['invoiceValue'].toFixed(0));
                edaDetailUpdate['invoiceUnitPrice'] = +(Number(edaDetailUpdate['invoiceValue']) / Number(edaDetailUpdate['quantity1'])).toFixed(0);
              } else {
                edaDetailUpdate['invoiceValue'] = edaDetailUpdate['invoiceValue'];
                edaDetailUpdate['invoiceUnitPrice'] = Number(edaDetailUpdate['invoiceValue']) / Number(edaDetailUpdate['quantity1']);
              }
              edaDetailUpdate['quantityUnitCode1'] = edaDetailUpdate['priceQuantityUnit'];

              const checkHSCodeDetail: Optional = new Optional();
              checkHSCodeDetail.setWhere([
                new Where(EConfigure.AND, 'name', EConfigure.EQUAL, edaDetailUpdate['itemName']),
                new Where(EConfigure.AND, 'nameVN', EConfigure.EQUAL, itemNameVN),
                new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, edaDetailUpdate['HSCode']),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);

              const checkHSCode: Optional = new Optional();
              checkHSCode.setWhere([
                new Where(EConfigure.AND, 'hsCode', EConfigure.EQUAL, edaDetailUpdate['HSCode']),
                new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
              ]);
              const hscodeDetailQuery: any = this.hscodeDetailRepository.getOneOptional(checkHSCodeDetail);
              const hscodeQuery: any = this.hscodeRepository.getOneOptional(checkHSCode);
              let [hscodeDetail, hscode] = await Promise.all([hscodeDetailQuery, hscodeQuery]);
              if (!hscode) {
                const newHSCode: any = {
                  'hsCode': edaDetailUpdate['HSCode'],
                  'importTaxCode': edaDetailUpdate['exportTaxCode'],
                  'importTaxValue': edaDetailUpdate['exportTax'],
                  'VATCode': edaDetailUpdate['VATTaxCode'],
                  'VATValue': edaDetailUpdate['VATTax'],
                  'specialConsumptionTaxCode': edaDetailUpdate['specialConsumptionTaxCode'],
                  'specialConsumptionTaxValue': edaDetailUpdate['specialConsumptionTax'],
                  'environmentTaxCode': edaDetailUpdate['environmentTaxCode'],
                  'environmentTaxPrice': edaDetailUpdate['environmentTax'],
                }
                hscode = await this.hscodeRepository.createDataTrx(newHSCode, transaction);
              } else {
                hscode['importTaxCode'] = edaDetailUpdate['exportTaxCode'],
                  hscode['importTaxValue'] = edaDetailUpdate['exportTax'];
                hscode['VATCode'] = edaDetailUpdate['VATTaxCode'],
                  hscode['VATValue'] = edaDetailUpdate['VATTax'];
                hscode['specialConsumptionTaxCode'] = edaDetailUpdate['specialConsumptionTaxCode'],
                  hscode['specialConsumptionTaxValue'] = edaDetailUpdate['specialConsumptionTax'];
                hscode['environmentTaxCode'] = edaDetailUpdate['environmentTaxCode'],
                  hscode['environmentTaxPrice'] = edaDetailUpdate['environmentTax'];
                await hscode.save();
              }
              if (!hscodeDetail) {
                const newHSCodeDetail: any = {
                  'name': edaDetailUpdate['itemName'],
                  'nameVN': edaDetailUpdate['itemNameVN'],
                  'quantityUnitCode1': edaDetailUpdate['priceQuantityUnit'],
                  'priceQuantityUnit': edaDetailUpdate['priceQuantityUnit'],
                  'hsCode': edaDetailUpdate['HSCode'],
                }
                await this.hscodeDetailRepository.createDataTrx(newHSCodeDetail, transaction);
              } else {
                hscodeDetail['quantityUnitCode1'] = edaDetailUpdate['priceQuantityUnit'];
                hscodeDetail['priceQuantityUnit'] = edaDetailUpdate['priceQuantityUnit'];
                hscodeDetail.save();
              }

              const optional: Optional = new Optional();
              optional.setWhere([
                new Where(EConfigure.AND, 'id', EConfigure.EQUAL, edaDetailUpdate['id']),
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, edaDetailUpdate[EConfigure.HAWB_FIELD])
              ]);
              edaDetailUpdate = Utilities.taxAndCollection(edaDetailUpdate, hscode);
              await this.exportDetailRepository.updateDataTrx(edaDetailUpdate, optional, transaction);
            }));
          }
          await transaction.commit();
          // const updateTransaction: IUpdateAction = {
          //   'HAWB': HAWB,
          //   'employeeId': employeeId,
          //   'action': ActionKey.UPDATE_IDA,
          //   'classify': ida['classify'] as string,
          //   'data': ida,
          //   'newData': value,
          // }
          // await this.importTransactionRepository.createData(updateTransaction);
          const exportDetailService = new ExportClearanceDetailService();
          await exportDetailService.updateTotalTax([HAWB]);
          await exportDetailService.checkUpdateItemNameVN([HAWB]);
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][updateEDA]', (error as Error).message);
      if (transaction && transaction['finished'] !== EConfigure.COMMIT) {
        await transaction.rollback();
      }
      throw new Error(error as any);
    }
  }

  public async changeMECToEDA(HAWBs: string[], classify: string, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      const MECOptional: Optional = new Optional();
      MECOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        MECOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const infoMECs: IMEC[] = await this.mecRepository.queryAll(MECOptional);
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_CLASSIFY_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      let edaData: IEDACreate;
      let dataEDAs: IEDACreate[] = [];
      // let changeTransactions: IChangeClassify[] = [];
      if (infoMECs.length > 0) {
        if (classify === ClassifyValidateName.get(ClassifyValidateKey.DOC) || classify === ClassifyValidateName.get(ClassifyValidateKey.MEC)) {
          transaction = await this._sequelize.transaction();
          await Promise.all(infoMECs.map(async (infoMic: IMEC) => {
            const HAWB: string = String(infoMic[EConfigure.HAWB_FIELD]);
            const HAWBClearance: string = String(infoMic[EConfigure.HAWB_CLEARANCE_FIELD]);
            if (!infoMic['declarationNo'] || !infoMic['inspectionKindClassification'] || infoMic['phase'] !== ActionKey.ACCEPT_CLEARANCE) {
              const classifyName: any = (classify === ClassifyValidateName.get(ClassifyValidateKey.MEC)) ? ClassifyName.get(ClassifyKey.PAR) : ClassifyName.get(ClassifyKey.DOC);
              const updateData: any = {
                'classify': classifyName
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB)
              ]);
              const [total] = await this.mecRepository.updateDataTrx(updateData, updateOptional, transaction);
              if (total > 0) {
                // const changeTransaction: IChangeClassify = {
                //   'HAWB': HAWB,
                //   'action': ActionKey.CHANGE_CLASSIFY,
                //   'classify': classify,
                //   'employeeId': employeeId,
                // }
                // changeTransactions.push(changeTransaction);
                reponze['message']['data']['succesfull'].push(HAWBClearance);
              } else {
                reponze['message']['data']['fail'].push({
                  'HAWB': HAWBClearance,
                  'cause': EMessage.UPDATE_FAIL
                });
              }
            } else {
              reponze['message']['data']['fail'].push({
                'HAWB': HAWBClearance,
                'cause': `Đang ở trạng thái ${String(infoMic['phase_name']['vi']).toLowerCase()}`
              });
            }
          }));
          await transaction.commit();
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
        } else {
          const HAWBClearances: string[] = [];
          const classifyName: any = (classify === ClassifyValidateName.get(ClassifyValidateKey.EDA)) ? ClassifyName.get(ClassifyKey.PAR) : ClassifyName.get(ClassifyKey.COM);
          for (const infoMEC of infoMECs) {
            const cloneInfoMEC: IMEC = infoMEC;
            const HAWB: string = String(cloneInfoMEC[EConfigure.HAWB_FIELD]);
            const HAWBClearance: string = String(cloneInfoMEC[EConfigure.HAWB_CLEARANCE_FIELD]);
            if (!cloneInfoMEC['declarationNo'] || !cloneInfoMEC['inspectionKindClassification'] || cloneInfoMEC['phase'] !== EConfigure.ACCEPTED_CLEARANCE) {
              const stationId: number = Number(cloneInfoMEC[EConfigure.STATION_ID_FIELD]);
              const warehouse: IWarehouse = await Utilities.getWarehouse(stationId);
              edaData = {
                'HAWB': HAWB,
                'HAWBClearance': HAWBClearance,
                'cargoNo': HAWBClearance,
                'stationId': stationId,
                'phase': ActionKey.CREATE,
                'serviceId': Number(cloneInfoMEC['serviceId']),
              };
              edaData['declarationNo'] = cloneInfoMEC['declarationNo'];
              edaData['MAWB'] = cloneInfoMEC['MAWB'];

              edaData['declarationKindCode'] = EConfigure.H21;
              edaData['meansOfTransportationCode'] = String(EConfigure.TRANSPORT_FLIGHT);
              edaData['pieceUnitCode'] = EConfigure.PACKAGE_CODE;

              edaData['exporterCode'] = cloneInfoMEC['exporterCode'];
              edaData['exporterName'] = cloneInfoMEC['exporterName'];
              edaData['exporterFullName'] = cloneInfoMEC['exporterFullName'];
              edaData['postCode'] = cloneInfoMEC['postCode'];
              edaData['addressOfExporter'] = cloneInfoMEC['addressOfExporter'];
              edaData['telephoneNumber'] = cloneInfoMEC['telephoneNumber'];
              edaData['consigneeCode'] = cloneInfoMEC['consigneeCode'];
              edaData['consigneeName'] = cloneInfoMEC['consigneeName'];
              edaData['postCodeIdentification'] = cloneInfoMEC['postCodeIdentification'];
              edaData['address1'] = cloneInfoMEC['address1'];
              edaData['address2'] = cloneInfoMEC['address2'];
              edaData['address3'] = cloneInfoMEC['address3'];
              edaData['address4'] = cloneInfoMEC['address4'];
              edaData['countryCode'] = cloneInfoMEC['countryCode'];
              edaData['cargoPiece'] = cloneInfoMEC['cargoPiece'];
              edaData['cargoWeightGross'] = cloneInfoMEC['cargoWeight'];
              edaData['weightUnitCodeGross'] = WeightClearance.get('kg');
              if (warehouse) {
                edaData['plannedDeclarantCode'] = warehouse['agencyCode'];
              }
              edaData['customsWarehouseCode'] = cloneInfoMEC['customsWarehouseCode'];
              edaData['destinationLocationForBondedTransport'] = cloneInfoMEC['customsWarehouseCode'];
              edaData['theFinalDestinationCode'] = cloneInfoMEC['theFinalDestination'];
              edaData['theFinalDestinationName'] = cloneInfoMEC['theFinalDestination'];

              edaData['loadingPortCode'] = cloneInfoMEC['loadingPortCode'];
              edaData['loadingPortName'] = cloneInfoMEC['loadingPortCode'];

              edaData['customsOffice'] = cloneInfoMEC['customsOffice'];
              edaData['customsSubSection'] = cloneInfoMEC['customsSubSection'];
              edaData['loadingPlannedVesselName'] = cloneInfoMEC['flightNo'];

              if (cloneInfoMEC['departureDate']) {
                edaData['departurePlannedDate'] = cloneInfoMEC['departureDate'];
                edaData['startDate'] = cloneInfoMEC['departureDate'];
                edaData['arrivalDateOfTransport'] = cloneInfoMEC['departureDate'];
              }

              edaData['invoiceCurrencyCode'] = cloneInfoMEC['currencyCodeOfTaxValue'];
              edaData['currencyCodeOfTaxValue'] = cloneInfoMEC['currencyCodeOfTaxValue'];
              edaData['totalInvoicePrice'] = cloneInfoMEC['totalOfTaxValue'];
              edaData['totalOfTaxValue'] = cloneInfoMEC['totalOfTaxValue'];
              edaData['invoiceClassificationCode'] = EConfigure.TYPE_A;
              edaData['termOfPayment'] = EConfigure.KHONGTT;
              edaData['invoicePriceConditionCode'] = EConfigure.CIF;
              edaData['taxPayer'] = EConfigure.TAX_PAYER;
              edaData['codeOfExtendingDueDate'] = EConfigure.CIF;

              edaData['valueClearanceVND'] = cloneInfoMEC['valueClearanceVND'];
              edaData['notes'] = cloneInfoMEC['notes'];
              edaData['classify'] = classifyName;
              edaData['priceVND'] = cloneInfoMEC['priceVND'];

              edaData['customerBusinessId'] = cloneInfoMEC['customerBusinessId'];
              edaData['customerPersonalId'] = cloneInfoMEC['customerPersonalId'];
              edaData['hubId'] = cloneInfoMEC['hubId'];
              edaData['terminalName'] = cloneInfoMEC['terminalName'];

              edaData['inspectionKindClassification'] = cloneInfoMEC['inspectionKindClassification'];
              edaData['dateCheckin'] = cloneInfoMEC['dateCheckin'];
              edaData['dateClearanced'] = cloneInfoMEC['dateClearanced'];
              edaData['dateCheckout'] = cloneInfoMEC['dateCheckout'];
              edaData['clientId'] = cloneInfoMEC['clientId'];

              if (classifyName === ClassifyName.get(ClassifyKey.COM)) {
                edaData['phase'] = ActionKey.ACCEPT_CLEARANCE;
              }
              dataEDAs.push(edaData);
              HAWBClearances.push(HAWBClearance);

              // const changeTransaction: IChangeClassify = {
              //   'HAWB': HAWB,
              //   'action': ActionKey.CHANGE_CLASSIFY,
              //   'classify': classify,
              //   'employeeId': employeeId,
              // }
              // changeTransactions.push(changeTransaction);
            } else {
              reponze['message']['data']['fail'].push({
                'HAWB': HAWBClearance,
                'cause': `Đang ở trạng thái ${String(cloneInfoMEC['phase_name']['vi']).toLowerCase()}`
              });
            }
          }
          const detailOptional: Optional = new Optional();
          detailOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          ]);
          try {
            if (dataEDAs.length > 0) {
              transaction = await this._sequelize.transaction();
              const [mecDeleted, edaCreated] = await Promise.all([
                this.mecRepository.destroyDataTrx(MECOptional, transaction),
                this.edaRepository.createBulkTrx(dataEDAs, transaction),
                // this.importTransactionRepository.createBulkTrx(changeTransactions, transaction),
              ]);
              await transaction.commit();
              if (edaCreated.length > 0) {
                reponze['status'] = true;
                reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
                reponze['message']['data']['succesfull'].push(...HAWBClearances);
              }
            }
          } catch (error) {
            await transaction.rollback();
            console.log('---- MIC to IDA error: %o', error);
            reponze['message']['data']['fail'].push(...HAWBClearances);
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][changeMECToEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async changeEDAToMEC(HAWBs: string[], classify: string, employeeId: number, hubs: any): Promise<any> {
    let transaction: any;
    try {
      const EDAOptional: Optional = new Optional();
      EDAOptional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        EDAOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const infoEDAs: EDA[] = await this.edaRepository.queryAll(EDAOptional);
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_CLASSIFY_FAIL,
          'data': {
            'fail': [],
            'succesfull': [],
          },
          'error': []
        }
      }
      let mecData: IMECCreate;
      let dataMECs: IMECCreate[] = [];
      // let changeTransactions: IChangeClassify[] = [];
      if (infoEDAs.length > 0) {
        if (classify === ClassifyValidateName.get(ClassifyValidateKey.COM) || classify === ClassifyValidateName.get(ClassifyValidateKey.EDA)) {
          transaction = await this._sequelize.transaction();
          const classifyName: any = (classify === ClassifyValidateName.get(ClassifyValidateKey.EDA)) ? ClassifyName.get(ClassifyKey.PAR) : ClassifyName.get(ClassifyKey.COM);
          await Promise.all(infoEDAs.map(async (infoEDA: IEDA) => {
            const HAWB: string = String(infoEDA[EConfigure.HAWB_FIELD]);
            const HAWBClearance: string = String(infoEDA[EConfigure.HAWB_CLEARANCE_FIELD]);
            if (!infoEDA['declarationNo'] || !infoEDA['inspectionKindClassification'] || infoEDA['phase'] !== ActionKey.ACCEPT_CLEARANCE) {
              let updateData: any = {
                'classify': classifyName,
              }
              if (classifyName === ClassifyName.get(ClassifyKey.COM)) {
                updateData['termOfPayment'] = EConfigure.TTR;
              } else {
                updateData['termOfPayment'] = EConfigure.KHONGTT;
              }
              const updateOptional: Optional = new Optional();
              updateOptional.setWhere([
                new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB)
              ]);
              const [total] = await this.edaRepository.updateDataTrx(updateData, updateOptional, transaction);
              if (total > 0) {
                // const changeTransaction: IChangeClassify = {
                //   'HAWB': HAWB,
                //   'action': ActionKey.CHANGE_CLASSIFY,
                //   'classify': classify,
                //   'employeeId': employeeId,
                // }
                // changeTransactions.push(changeTransaction);
                reponze['message']['data']['succesfull'].push(HAWBClearance);
              } else {
                reponze['message']['data']['fail'].push(HAWBClearance);
              }
            } else {
              reponze['message']['data']['fail'].push({
                'HAWB': HAWBClearance,
                'cause': `Đang ở trạng thái ${String(infoEDA['phase_name']['vi']).toLowerCase()}`
              });
            }

          }));
          await transaction.commit();
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
        } else {
          const HAWBClearances: string[] = [];
          const classifyName: any = (classify === ClassifyValidateName.get(ClassifyValidateKey.MEC)) ? ClassifyName.get(ClassifyKey.PAR) : ClassifyName.get(ClassifyKey.DOC);
          for (const infoEDA of infoEDAs) {
            const cloneInfoEDA: IEDA = infoEDA;
            const HAWB: string = String(cloneInfoEDA[EConfigure.HAWB_FIELD]);
            const HAWBClearance: string = String(cloneInfoEDA[EConfigure.HAWB_CLEARANCE_FIELD]);
            if (!cloneInfoEDA['declarationNo'] || !cloneInfoEDA['inspectionKindClassification'] || cloneInfoEDA['phase'] !== ActionKey.ACCEPT_CLEARANCE) {
              const stationId: number = Number(cloneInfoEDA[EConfigure.STATION_ID_FIELD]);
              mecData = {
                'HAWB': HAWB,
                'HAWBClearance': HAWBClearance,
                'stationId': stationId,
                'phase': ActionKey.CREATE,
                'serviceId': Number(cloneInfoEDA['serviceId']),
              };
              mecData['MAWB'] = cloneInfoEDA['MAWB'];
              mecData['customsOffice'] = cloneInfoEDA['customsOffice'];
              mecData['customsSubSection'] = cloneInfoEDA['customsSubSection'];

              mecData['declarationNo'] = cloneInfoEDA['declarationNo'];
              mecData['exporterCode'] = cloneInfoEDA['exporterCode'];
              mecData['exporterName'] = cloneInfoEDA['exporterName'];
              mecData['exporterFullName'] = cloneInfoEDA['exporterFullName'];

              mecData['postCode'] = cloneInfoEDA['postCode'];
              mecData['addressOfExporter'] = cloneInfoEDA['addressOfExporter'];
              mecData['telephoneNumber'] = cloneInfoEDA['telephoneNumber'];
              mecData['consigneeCode'] = cloneInfoEDA['consigneeCode'];
              mecData['consigneeName'] = cloneInfoEDA['consigneeName'];
              mecData['postCodeIdentification'] = cloneInfoEDA['postCodeIdentification'];
              mecData['address1'] = cloneInfoEDA['address1'];
              mecData['address2'] = cloneInfoEDA['address2'];
              mecData['address3'] = cloneInfoEDA['address3'];
              mecData['address4'] = cloneInfoEDA['address4'];
              mecData['countryCode'] = cloneInfoEDA['countryCode'];
              mecData['cargoPiece'] = cloneInfoEDA['cargoPiece'];
              mecData['cargoWeight'] = cloneInfoEDA['cargoWeightGross'];
              mecData['customsWarehouseCode'] = cloneInfoEDA['customsWarehouseCode'];
              mecData['theFinalDestination'] = cloneInfoEDA['theFinalDestinationCode'];
              mecData['loadingPortCode'] = cloneInfoEDA['loadingPortCode'];

              mecData['flightNo'] = cloneInfoEDA['loadingPlannedVesselName'];
              mecData['departureDate'] = cloneInfoEDA['departurePlannedDate'];

              mecData['currencyCodeOfTaxValue'] = cloneInfoEDA['invoiceCurrencyCode'];
              mecData['totalOfTaxValue'] = cloneInfoEDA['totalInvoicePrice'];
              mecData['valueClearanceVND'] = cloneInfoEDA['valueClearanceVND'];
              mecData['notes'] = cloneInfoEDA['notes'];
              mecData['classify'] = classifyName;
              mecData['priceVND'] = cloneInfoEDA['priceVND'];

              mecData['customerBusinessId'] = cloneInfoEDA['customerBusinessId'];
              mecData['customerPersonalId'] = cloneInfoEDA['customerPersonalId'];
              mecData['hubId'] = cloneInfoEDA['hubId'];
              mecData['terminalName'] = cloneInfoEDA['terminalName'];

              mecData['inspectionKindClassification'] = cloneInfoEDA['inspectionKindClassification'];
              mecData['dateCheckin'] = cloneInfoEDA['dateCheckin'];
              mecData['dateClearanced'] = cloneInfoEDA['dateClearanced'];
              mecData['dateCheckout'] = cloneInfoEDA['dateCheckout'];
              mecData['clientId'] = cloneInfoEDA['clientId'];

              dataMECs.push(mecData);
              HAWBClearances.push(HAWBClearance);

              // const changeTransaction: IChangeClassify = {
              //   'HAWB': HAWB,
              //   'action': ActionKey.CHANGE_CLASSIFY,
              //   'classify': classify,
              //   'employeeId': employeeId,
              // }
              // changeTransactions.push(changeTransaction);
            } else {
              reponze['message']['data']['fail'].push({
                'HAWB': HAWBClearance,
                'cause': `Đang ở trạng thái ${String(cloneInfoEDA['phase_name']['vi']).toLowerCase()}`
              });
            }

          }
          const detailOptional: Optional = new Optional();
          detailOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          ]);
          try {
            if (dataMECs.length > 0) {
              transaction = await this._sequelize.transaction();
              const [edaDeleted, mecCreated] = await Promise.all([
                this.edaRepository.destroyDataTrx(EDAOptional, transaction),
                this.mecRepository.createBulkTrx(dataMECs, transaction),
                // this.importTransactionRepository.createBulkTrx(changeTransactions, transaction),
              ])
              await transaction.commit();
              if (mecCreated.length > 0) {
                reponze['status'] = true;
                reponze['message']['message'] = EMessage.UPDATE_CLASSIFY_SUCCESS;
                reponze['message']['data']['succesfull'].push(...HAWBClearances);
              }
            }
          } catch (error) {
            await transaction.rollback();
            console.log('---- MIC to IDA error: %o', error);
            reponze['message']['data']['fail'].push(...HAWBClearances)
          }
        }

      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][changeEDAToMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filterMEC(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      // mec
      const MECQuery = this.mecRepository.getAll(optional);
      const MECTotalCreateQuery = this.mecRepository.countData(optionalCreate);
      const MECTotalUpdateCarageNameQuery = this.mecRepository.countData(optionalUpdateCarageName);
      const MECTotalSendClearanceQuery = this.mecRepository.countData(optionalSendClearance);
      const MECTotalEditClearanceQuery = this.mecRepository.countData(optionalEditClearance);
      const MECTotalInspectionKindQuery = this.mecRepository.countData(optionalInspectionKind);
      const MECTotalAcceptClearanceQuery = this.mecRepository.countData(optionalAcceptClearance);
      const MECErrorQuery = this.mecRepository.countData(optionalError);

      const [[MECs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError] =
        await Promise.all([MECQuery, MECTotalCreateQuery, MECTotalUpdateCarageNameQuery, MECTotalSendClearanceQuery, MECTotalEditClearanceQuery, MECTotalInspectionKindQuery,
          MECTotalAcceptClearanceQuery, MECErrorQuery]);
      if (MECs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': MECs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][filterMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filterEDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalRegisterTemp': 0,
            'totalSentTemp': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalRegisterTemp: Optional = new Optional();
      optionalRegisterTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSentTemp: Optional = new Optional();
      optionalSentTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SENT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      // eda
      const EDAQuery = this.edaRepository.getAll(optional);
      const EDATotalCreateQuery = this.edaRepository.countData(optionalCreate);
      const EDATotalUpdateCarageNameQuery = this.edaRepository.countData(optionalUpdateCarageName);
      const EDATotalRegisterTempQuery = this.edaRepository.countData(optionalRegisterTemp);
      const EDATotalSentTempQuery = this.edaRepository.countData(optionalSentTemp);
      const EDATotalSendClearanceQuery = this.edaRepository.countData(optionalSendClearance);
      const EDATotalEditClearanceQuery = this.edaRepository.countData(optionalEditClearance);
      const EDATotalInspectionKindQuery = this.edaRepository.countData(optionalInspectionKind);
      const EDATotalAcceptClearanceQuery = this.edaRepository.countData(optionalAcceptClearance);
      const EDAErrorQuery = this.edaRepository.countData(optionalError);

      const [[EDAs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError, registerTemp, sentTemp] =
        await Promise.all([EDAQuery, EDATotalCreateQuery, EDATotalUpdateCarageNameQuery, EDATotalSendClearanceQuery, EDATotalEditClearanceQuery, EDATotalInspectionKindQuery,
          EDATotalAcceptClearanceQuery, EDAErrorQuery, EDATotalRegisterTempQuery, EDATotalSentTempQuery]);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': EDAs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalRegisterTemp': registerTemp,
          'totalSentTemp': sentTemp,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][filterEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async master(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const masterOptional: Optional = new Optional();
      masterOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'MAWB', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      masterOptional.setAttributes(['MAWB']);
      masterOptional.setGroup(['MAWB', 'createdAt']);
      masterOptional.setOrderby([
        new OrderBy('createdAt', EConfigure.DESCENDING)
      ]);
      // ida
      const EDAQuery = this.edaRepository.queryAll(masterOptional);
      // mic
      const MECQuery = this.mecRepository.queryAll(masterOptional);
      let masters: string[] = [];
      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (EDAs.length > 0 || MECs.length > 0) {
        masters = [...EDAs, ...MECs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][master]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
          }
        }
      }

      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ])

      // eda
      const EDAQuery = this.edaRepository.queryAll(optionalTotal);

      // mec
      const MECQuery = this.mecRepository.queryAll(optionalTotal);

      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (EDAs.length > 0 || MECs.length > 0) {
        let mecData: ICheckout[] = [];
        let edaData: ICheckout[] = [];
        let manifests: ICheckout[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: IEDA) => {
            const eda: ICheckout = {
              'HAWB': EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': EDA['MAWB'],
              'declarationNo': EDA['declarationNo'],
              'clearanceInspection': EDA['inspectionKindClassification'],
              'dateCheckout': EDA['dateCheckout'],
              'dateCheckin': EDA['dateCheckin'],
              'dateClearanced': EDA['dateClearanced'],
              'cargoPiece': EDA['cargoPiece'],
              'weight': EDA['cargoWeightGross'],
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          mecData = MECs.map((MEC: IMEC) => {
            const mec: ICheckout = {
              'HAWB': MEC[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': MEC['MAWB'],
              'declarationNo': MEC['declarationNo'],
              'clearanceInspection': String(MEC['inspectionKindClassification']),
              'dateCheckout': MEC['dateCheckout'],
              'dateCheckin': MEC['dateCheckin'],
              'dateClearanced': MEC['dateClearanced'],
              'cargoPiece': MEC['cargoPiece'],
              'weight': MEC['cargoWeight'],
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': manifests.length,
          'manifests': manifests,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][clearanceReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportExport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }

      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      optionalQuery.setRelation(['exportDetails', 'customerPersonal', 'customerBusiness']);

      // eda
      const EDAQuery = this.edaRepository.queryAll(optionalQuery);
      // mec
      const MECQuery = this.mecRepository.queryAll(optionalQuery);

      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (EDAs.length > 0 || MECs.length > 0) {
        let manifests: any[] = [];
        const customReleased: string = 'custom released';
        if (EDAs.length > 0) {
          EDAs.forEach((EDA: IEDA) => {
            const importTax: number = EDA['totalExportTax'] ? Number(EDA['totalExportTax']) : 0;
            const VATTax: number = EDA['totalVATTax'] ? Number(EDA['totalVATTax']) : 0;

            let itemname: string[] = [];
            let partner: any = null;
            if (EDA['exportDetails'].length > 0) {
              itemname = EDA['exportDetails'].map((exportDetail: ExportDetail) => {
                return String(exportDetail['itemNameVN']);
              });
            }
            if (EDA['customerBusiness']) {
              partner = EDA['customerBusiness']['customerShortName'];
            }
            if (EDA['customerPersonal']) {
              partner = EDA['customerPersonal']['customerLastName'];
            }
            const eda: IExportReport = {
              'expDate': moment(String(EDA['departurePlannedDate'])).format(EConfigure.EXPORT_DATE),
              'trackingNo': String(EDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'name': itemname.join(','),
              'MAWB': String(EDA['MAWB']),
              'country': String(EDA['countryCode']),
              'pcs': Number(EDA['cargoPiece']),
              'grossWeight': Number(EDA['cargoWeightGross']),
              'puDate': moment(String(EDA['createdAt'])).format(EConfigure.EXPORT_DATE),
              'cdsDate': EDA['declarationPlannedDate'] ? moment(String(EDA['declarationPlannedDate'])).format(EConfigure.EXPORT_DATE) : '',
              'crDate': EDA['dateCheckout'] ? moment(String(EDA['dateCheckout'])).format(EConfigure.EXPORT_DATE) : '',
              'confirmUpliftDate': moment(String(EDA['departurePlannedDate'])).format(EConfigure.EXPORT_DATE),
              'cdsNo': String(EDA['declarationNo']),
              'importType': null,
              'lane': Number(EDA['inspectionKindClassification']),
              'customsStatus': customReleased,
              'customsClearance': null,
              'handling': null,
              'shipmemntValue': Number(EDA['totalOfTaxValue']),
              'dutyTaxVND': importTax + VATTax,
              'otherFee': Number(EDA['totalEnvironmentTax']) + Number(EDA['totalSpecialConsumptionTax']),
              'notes': 'Bảng kê EDA',
              'type': 'EDA',
              'partner': partner
            }
            manifests.push(eda);
          });
        }
        if (MECs.length > 0) {
          MECs.forEach((MEC: IMEC) => {
            let itemname: string[] = [];
            let partner: any = null;
            if (MEC['exportDetails'].length > 0) {
              itemname = MEC['exportDetails'].map((exportDetail: ExportDetail) => {
                return String(exportDetail['itemNameVN']);
              });
            }
            if (MEC['customerBusiness']) {
              partner = MEC['customerBusiness']['customerShortName'];
            }
            if (MEC['customerPersonal']) {
              partner = MEC['customerPersonal']['customerLastName'];
            }
            const mec: IExportReport = {
              'expDate': moment(String(MEC['departureDate'])).format(EConfigure.EXPORT_DATE),
              'trackingNo': String(MEC[EConfigure.HAWB_CLEARANCE_FIELD]),
              'name': itemname.join(','),
              'MAWB': String(MEC['MAWB']),
              'country': String(MEC['countryCode']),
              'pcs': Number(MEC['cargoPiece']),
              'grossWeight': Number(MEC['cargoWeight']),
              'puDate': moment(String(MEC['createdAt'])).format(EConfigure.EXPORT_DATE),
              'cdsDate': MEC['declarationPlannedDate'] ? moment(String(MEC['declarationPlannedDate'])).format(EConfigure.EXPORT_DATE) : '',
              'crDate': MEC['dateCheckout'] ? moment(String(MEC['dateCheckout'])).format(EConfigure.EXPORT_DATE) : '',
              'confirmUpliftDate': moment(String(MEC['departureDate'])).format(EConfigure.EXPORT_DATE),
              'cdsNo': String(MEC['declarationNo']),
              'importType': null,
              'lane': Number(MEC['inspectionKindClassification']),
              'customsStatus': customReleased,
              'customsClearance': null,
              'handling': null,
              'shipmemntValue': Number(MEC['totalOfTaxValue']),
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': 'Bảng kê MEC',
              'type': 'MEC',
              'partner': partner
            }
            manifests.push(mec);
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][clearanceReportExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async taxCodeBussiness(HAWBs: string[]): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'successful': null,
            'fail': null
          }
        }
      }

      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      optionalQuery.setAttributes(['exporterFullName', 'HAWB']);

      // eda
      const EDAQuery = this.edaRepository.queryAll(optionalQuery);
      // mec
      const MECQuery = this.mecRepository.queryAll(optionalQuery);

      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (EDAs.length > 0 || MECs.length > 0) {
        let success: any[] = [];
        let fail: any[] = [];
        if (EDAs.length > 0) {
          await Promise.all(EDAs.map(async (EDA: EDA) => {
            const optionalEDA: Optional = new Optional();
            optionalEDA.setAttributes(['code']);
            optionalEDA.setWhere([
              new Where(EConfigure.AND, 'name_vn', EConfigure.EQUAL, EDA['exporterFullName']),
              new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
            ]);
            const taxCode: TaxCode = await this.taxCodeRepository.getOneOptional(optionalEDA);
            if (taxCode) {
              EDA['exporterCode'] = taxCode['code'];
              await EDA.save();
              success.push(EDA[EConfigure.HAWB_FIELD]);
            } else {
              fail.push(EDA[EConfigure.HAWB_FIELD]);
            }
          }));
        }
        if (MECs.length > 0) {
          await Promise.all(MECs.map(async (MIC: MEC) => {
            const optionalMIC: Optional = new Optional();
            optionalMIC.setAttributes(['code']);
            optionalMIC.setWhere([
              new Where(EConfigure.AND, 'name_vn', EConfigure.EQUAL, MIC['exporterFullName']),
              new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
            ]);
            const taxCode: TaxCode = await this.taxCodeRepository.getOneOptional(optionalMIC);
            if (taxCode) {
              MIC['exporterCode'] = taxCode['code'];
              await MIC.save();
              success.push(MIC[EConfigure.HAWB_FIELD]);
            } else {
              fail.push(MIC[EConfigure.HAWB_FIELD]);
            }
          }));
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data']['successful'] = success;
        reponze['message']['data']['fail'] = fail;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][taxCodeBussiness]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async mecCheckin(HAWBs: string[], dateCheckin: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'successful': null,
            'fail': null
          },
          'error': []
        }
      }

      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
      ]);
      if (hubs) {
        optionalQuery.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optionalQuery.setAttributes(['HAWB', 'isDeleted']);
      // mec
      const MECs = await this.mecRepository.queryAll(optionalQuery);
      if (MECs.length > 0) {
        let success: any[] = [];
        let fail: any[] = [];
        if (MECs.length > 0) {
          MECs.forEach((mec: MEC) => {
            if (mec[EConfigure.DELETED_FIELD]) {
              reponze['message']['error'].push({
                'HAWB': mec[EConfigure.HAWB_FIELD],
                'cause': `${EConfigure.HAWB_FIELD} đã bị xóa`
              });
              reponze['message']['message'] = EMessage.FAIL;
              fail.push(mec[EConfigure.HAWB_FIELD]);
            } else {
              success.push(mec[EConfigure.HAWB_FIELD]);
            }
          });
          if (fail.length === 0) {
            const updateData: any = {
              'dateCheckin': dateCheckin ? dateCheckin : moment().format(EConfigure.FULL_TIME),
            }
            const optionalUpdate: Optional = new Optional();
            optionalUpdate.setWhere([
              new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, success.join())
            ])
            const [total] = await this.mecRepository.updateData(updateData, optionalUpdate);
            if (total > 0) {
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.SUCCESS;
              reponze['message']['data']['successful'] = success;
            } else {
              reponze['message']['data']['fail'] = success;
            }
          } else {
            reponze['message']['data']['fail'] = fail;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][mecCheckin]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async edaCheckin(HAWBs: string[], dateCheckin: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'successful': null,
            'fail': null
          },
          'error': []
        }
      }

      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
      ]);
      if (hubs) {
        optionalQuery.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      optionalQuery.setAttributes(['HAWB', 'isDeleted']);

      // eda
      const EDAs = await this.edaRepository.queryAll(optionalQuery);
      if (EDAs.length > 0) {
        let success: any[] = [];
        let fail: any[] = [];
        if (EDAs.length > 0) {
          EDAs.forEach((eda: EDA) => {
            if (eda[EConfigure.DELETED_FIELD]) {
              reponze['message']['error'].push({
                'HAWB': eda[EConfigure.HAWB_FIELD],
                'cause': `${EConfigure.HAWB_FIELD} đã bị xóa`
              });
              reponze['message']['message'] = EMessage.FAIL;
              fail.push(eda[EConfigure.HAWB_FIELD]);
            } else {
              success.push(eda[EConfigure.HAWB_FIELD]);
            }
          });
          if (fail.length === 0) {
            const updateData: any = {
              'dateCheckin': dateCheckin ? dateCheckin : moment().format(EConfigure.FULL_TIME),
            }
            const optionalUpdate: Optional = new Optional();
            optionalUpdate.setWhere([
              new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, success.join())
            ])
            const [total] = await this.edaRepository.updateData(updateData, optionalUpdate);
            if (total > 0) {
              reponze['status'] = true;
              reponze['message']['message'] = EMessage.SUCCESS;
              reponze['message']['data']['successful'] = success;
            } else {
              reponze['message']['data']['fail'] = success;
            }
          } else {
            reponze['message']['data']['fail'] = fail;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][edaCheckin]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async mecAssignCargoName(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'success': null,
            'assigned': [],
          },
          'error': []
        }
      }

      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'HAWBClearance', 'employeeUpdateCargoName', 'phase']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      optional.setRelation(['employeeUpdateCargo']);
      const MECs: MEC[] = await this.mecRepository.queryAll(optional);
      if (MECs.length > 0) {
        const HAWBCreates: string[] = [];
        let transactions: any[] = [];
        MECs.forEach((mec: MEC) => {
          const HAWBClearance: string = mec[EConfigure.HAWB_CLEARANCE_FIELD] || '';
          if (mec['phase'] === ActionKey.ASSIGN_UPDATE_CARGO_NAME && mec['employeeUpdateCargoName']) {
            reponze['message']['data']['assigned'].push({
              'HAWB': HAWBClearance,
              'employee': mec['employeeUpdateCargo'],
            });
          } else {
            HAWBCreates.push(mec[EConfigure.HAWB_FIELD]);
            let transactionUpdate: IReturnCargo = {
              'HAWB': HAWBClearance,
              'action': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
              'employeeId': employeeId
            }
            transactions.push(transactionUpdate);
          }
        });
        if (HAWBCreates.length > 0) {
          const updateCargo: any = {
            'phase': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
            'employeeUpdateCargoName': employeeId
          }
          const updateOptional: Optional = new Optional();
          updateOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBCreates.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
          ])
          const [total] = await Promise.all([
            this.mecRepository.updateData(updateCargo, updateOptional),
            // this.importTransactionRepository.createBulk(transactions)
          ]);
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          if (total > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
            reponze['message']['data']['success'] = HAWBCreates;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][mecAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async edaAssignCargoName(HAWBs: string[], employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'success': null,
            'assigned': [],
          },
          'error': []
        }
      }

      const optional: Optional = new Optional();
      optional.setAttributes(['HAWB', 'HAWBClearance', 'employeeUpdateCargoName', 'phase']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);
      optional.setRelation(['employeeUpdateCargo']);
      const EDAs: EDA[] = await this.edaRepository.queryAll(optional);
      if (EDAs.length > 0) {
        const HAWBCreates: string[] = [];
        let transactions: any[] = [];
        EDAs.forEach((eda: EDA) => {
          const HAWBClearance: string = eda[EConfigure.HAWB_CLEARANCE_FIELD] || '';
          if (eda['phase'] === ActionKey.ASSIGN_UPDATE_CARGO_NAME && eda['employeeUpdateCargoName']) {
            reponze['message']['data']['assigned'].push({
              'HAWB': HAWBClearance,
              'employee': eda['employeeUpdateCargo'],
            });
          } else {
            HAWBCreates.push(eda[EConfigure.HAWB_FIELD] || '');
            let transactionUpdate: IReturnCargo = {
              'HAWB': HAWBClearance,
              'action': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
              'employeeId': employeeId
            }
            transactions.push(transactionUpdate);
          }
        });
        if (HAWBCreates.length > 0) {
          const updateCargo: any = {
            'phase': ActionKey.ASSIGN_UPDATE_CARGO_NAME,
            'employeeUpdateCargoName': employeeId
          }
          const updateOptional: Optional = new Optional();
          updateOptional.setWhere([
            new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBCreates.join()),
            new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
          ])
          const [total] = await Promise.all([
            this.edaRepository.updateData(updateCargo, updateOptional),
            // this.importTransactionRepository.createBulk(transactions)
          ]);
          reponze['message']['message'] = EMessage.UPDATE_FAIL;
          if (total > 0) {
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
            reponze['message']['data']['success'] = HAWBCreates;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][edaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMecAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.mecRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getMecAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getEdaAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.edaRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getEdaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMECEDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': [{
            'manifests': [],
            'total': 0,
          }]
        }
      }
      const limit: number = optional.getLimit();
      let half: number = 0;
      if (limit > 0) {
        half = Number(limit / 2);
        optional.setLimit(half);
      }
      optional.setRelation(['country', 'exportDetailItems']);
      const EDAQuery = this.edaRepository.getAll(optional);
      const MECQuery = this.mecRepository.getAll(optional);

      let [[EDAs, totalEDA], [MECs, totalMEC]] = await Promise.all([EDAQuery, MECQuery]);

      if (MECs.length > 0 || EDAs.length > 0) {
        let mecData: IManagement[] = [];
        let edaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (EDAs.length > 0) {
          if (half != totalMEC) {
            const remain: number = half + (half - totalMEC);
            optional.setLimit(remain);
            const addIDAs: IEDA[] = await this.edaRepository.queryAllPaging(optional);
            if (addIDAs.length > 0) {
              EDAs = addIDAs;
            }
          }
          edaData = EDAs.map((EDA: EDA) => {
            const eda: IGetAll = {
              "HAWBClearance": EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": EDA[EConfigure.HAWB_FIELD],
              "declarationNo": EDA['declarationNo'],
              "MAWB": EDA['MAWB'],
              "invoiceValue": EDA['totalInvoicePrice'],
              "invoiceCurrency": EDA['invoiceCurrencyCode'],
              "priceVND": EDA['priceVND'],
              "country": EDA['country'],
              "cargoPiece": EDA['cargoPiece'],
              "weight": EDA['cargoWeightGross'],
              "massOfWeight": EDA['weightUnitCodeGross'],
              "inspectionKindClassification": EDA['inspectionKindClassification'],
              "phase": EDA['phase_name'],
              "classify": EDA['classify'],
              "type": String(EConfigure.EDA),
              "details": EDA['exportDetailItems'],
              "reasonDetails": [],
              "inspectionKindTimes": EDA['inspectionKindTimes'],
              "clearanceDeclarationTimes": EDA['clearanceDeclarationTimes'],
              "isHold": EDA['isHold'],
              "isSortLane": EDA['isSortLane'],
              "externalBoxName": EDA['externalBoxName'],
              "internalBoxName": EDA['internalBoxName'],
              "createdAt": EDA['createdAt']
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          if (half != totalEDA) {
            const remain: number = half + (half - totalEDA);
            optional.setLimit(remain);
            const addMECs: IMEC[] = await this.mecRepository.queryAllPaging(optional);
            if (addMECs.length > 0) {
              MECs = addMECs;
            }
          }
          mecData = MECs.map((MIC: MEC) => {
            const mec: IGetAll = {
              "HAWBClearance": MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": MIC[EConfigure.HAWB_FIELD],
              "declarationNo": MIC['declarationNo'],
              "MAWB": MIC['MAWB'],
              "invoiceValue": MIC['totalOfTaxValue'],
              "invoiceCurrency": MIC['currencyCodeOfTaxValue'],
              "priceVND": MIC['priceVND'],
              "country": MIC['country'],
              "cargoPiece": MIC['cargoPiece'],
              "weight": MIC['cargoWeight'],
              "massOfWeight": WeightClearance.get('kg'),
              "inspectionKindClassification": MIC['inspectionKindClassification'],
              "phase": MIC['phase_name'],
              "classify": MIC['classify'],
              "type": String(EConfigure.MEC),
              "details": MIC['exportDetailItems'],
              "reasonDetails": [],
              "inspectionKindTimes": MIC['inspectionKindTimes'],
              "clearanceDeclarationTimes": MIC['clearanceDeclarationTimes'],
              "isHold": MIC['isHold'],
              "isSortLane": MIC['isSortLane'],
              "externalBoxName": MIC['externalBoxName'],
              "internalBoxName": MIC['internalBoxName'],
              "createdAt": MIC['createdAt']
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': totalEDA + totalMEC
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getAllMECEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async exportReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      optional.setRelation(['manifest', 'exportDetailItems', 'station', 'customerBusiness', 'customerPersonal']);
      const EDAQuery = this.edaRepository.queryAll(optional);
      const MECQuery = this.mecRepository.queryAll(optional);
      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (MECs.length > 0 || EDAs.length > 0) {
        let mecData: IManagement[] = [];
        let edaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: EDA) => {
            const importTax: number = EDA['totalExportTax'] ? Number(EDA['totalExportTax']) : 0;
            const VATTax: number = EDA['totalVATTax'] ? Number(EDA['totalVATTax']) : 0;
            const fee: number = 0;
            let productId: any = [];
            let details: ExportDetail[] = EDA['exportDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ExportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (EDA['manifest']) {
              orderNumber = EDA['manifest']['orderNumber']
              if (EDA['manifest']['originalOrderNumberClient']) {
                orderNumber = EDA['manifest']['originalOrderNumberClient']
              }
            }

            const eda: IAccountExportReport = {
              'MAWB': EDA['MAWB'],
              'trackingNo': EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': EDA['declarationNo'],
              'lane': EDA['inspectionKindClassification'] ? String(EDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': EDA['countryCode'] ? `${EDA['countryCode']}${EConfigure.G}` : null,
              'country': EDA['countryCode'],
              'pcs': Number(EDA['cargoPiece']),
              'trueWeight': Number(EDA['cargoWeightGross']),
              'volWeight': Number(EDA['cargoWeightGross']),
              'manisfestImported': EDA['createdAt'],
              'arDate': EDA['dateCheckin'],
              'cdsDate': EDA['declarationPlannedDate'],
              'crDate': EDA['dateCheckout'],
              'importType': EDA['isHold'] ? EConfigure.HOLD : (EDA['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.H11 : EConfigure.COM),
              'customsStatus': null,
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(EDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': EDA['totalSpecialConsumptionTax'],
              'environmentTax': EDA['totalEnvironmentTax'],
              'duty': fee,
              'priceVND': EDA['priceVND'],
              'totalTax': EDA['totalTax'],
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(EDA['totalEnvironmentTax']) + Number(EDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H21,
              'consignorName': EDA['exporterFullName'],
              'consignorAddress': EDA['addressOfExporter'],
              'consignorCityName': null,
              'consignorContactName': EDA['exporterFullName'],
              'consignorTelephone': EDA['telephoneNumber'],
              'consigneeName': EDA['consigneeName'],
              'consigneeAddress': `${EDA['address1']}${EDA['address2'] ? EDA['address2'] : ''}${EDA['address3'] ? EDA['address3'] : ''}`,
              'consigneeAddress2': `${EDA['address4'] ? EDA['address4'] : null}`,
              'consigneeCityName': null,
              'consigneeContactName': EDA['consigneeName'],
              'consigneeTelephone': EDA['consigneeTelephoneNumber'],
              'remarks': null,
              'status': EDA['phase_name']['vi'],
              'cdsNoCustomer': null,
              'currency': EDA['invoiceCurrencyCode'],
              'customerBusiness': EDA['customerBusiness'],
              'customerPersonal': EDA['customerPersonal'],
              'dateClearanced': EDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': EDA['station'] ? EDA['station'] : null,
              'exporterCode': EDA['exporterCode'],
              'statusId': EDA['phase'],
              'warehouseCheckin': null,
              'warehouseCheckout': null
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          mecData = MECs.map((MEC: MEC) => {
            let productId: any = [];
            let details: ExportDetail[] = MEC['exportDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ExportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (MEC['manifest']) {
              orderNumber = MEC['manifest']['orderNumber']
              if (MEC['manifest']['originalOrderNumberClient']) {
                orderNumber = MEC['manifest']['originalOrderNumberClient']
              }
            }
            const mec: IAccountExportReport = {
              'MAWB': MEC['MAWB'],
              'trackingNo': MEC[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': MEC['declarationNo'],
              'cdsNoCustomer': null,
              'lane': MEC['inspectionKindClassification'] ? String(MEC['inspectionKindClassification']).substring(0, 1) : null,
              'origin': MEC['countryCode'] ? `${MEC['countryCode']}${EConfigure.G}` : null,
              'country': MEC['countryCode'],
              'pcs': Number(MEC['cargoPiece']),
              'trueWeight': Number(MEC['cargoWeight']),
              'volWeight': Number(MEC['cargoWeight']),
              'manisfestImported': MEC['createdAt'],
              'arDate': MEC['dateCheckin'],
              'cdsDate': MEC['declarationPlannedDate'],
              'crDate': MEC['dateCheckout'],
              'importType': MEC['isHold'] ? EConfigure.HOLD : (MEC['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.MEC : MEC['classify']),
              'customsStatus': null,
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MEC['totalOfTaxValue']),
              'importTax': 0,
              'VAT': 0,
              'specialConsumptionTax': 0,
              'environmentTax': 0,
              'duty': 0,
              'priceVND': MEC['priceVND'],
              'totalTax': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': EConfigure._MEC,
              'consignorName': MEC['exporterFullName'],
              'consignorAddress': MEC['addressOfExporter'],
              'consignorCityName': null,
              'consignorContactName': MEC['exporterFullName'],
              'consignorTelephone': null,
              'consigneeName': MEC['consigneeName'],
              'consigneeAddress': `${MEC['address1']}${MEC['address2'] ? MEC['address2'] : ''}${MEC['address3'] ? MEC['address3'] : ''}`,
              'consigneeAddress2': `${MEC['address4'] ? MEC['address4'] : null}`,
              'consigneeCityName': null,
              'consigneeContactName': MEC['consigneeName'],
              'consigneeTelephone': MEC['consigneeTelephoneNumber'],
              'remarks': null,
              'status': MEC['phase_name']['vi'],
              'currency': MEC['currencyCodeOfTaxValue'],
              'customerBusiness': MEC['customerBusiness'],
              'customerPersonal': MEC['customerPersonal'],
              'dateClearanced': MEC['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': MEC['station'] ? MEC['station'] : null,
              'exporterCode': MEC['exporterCode'],
              'statusId': MEC['phase'],
              'warehouseCheckin': null,
              'warehouseCheckout': null
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][exportReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async holdManifest(data: any[], isRemoveMAWB: boolean, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL
        }
      }
      const HAWBs: string[] = [];
      await Promise.all(data.map(async (element: any) => {
        const HAWB: string = element[EConfigure.HAWB_FIELD];
        HAWBs.push(HAWB)
        const isHold: boolean = element['isHold'];
        const dataUpdate: any = {
          'isHold': isHold,
          'reasonIds': element['reasonIds'] ? String(element['reasonIds']).split(',') : null,
        }
        const transactionHold: IHoldTransaction = {
          'HAWB': HAWB,
          'action': isHold ? ActionKey.HOLD : ActionKey.UN_HOLD,
          'holdId': element['reasonIds'],
          'employeeId': employeeId,
        }
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        if (hubs) {
          optional.getWhere().push(
            new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
          )
        }
        await this.mecRepository.updateData(dataUpdate, optional);
        await this.edaRepository.updateData(dataUpdate, optional);
        // await this.exportTransactionRepository.createData(transactionHold);
        Utilities.updatePartnerStatusHAWB([HAWB], 112);
        return true;
      }));
      if (isRemoveMAWB) {
        const optional: Optional = new Optional();
        optional.setWhere([
          new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join()),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        let updateClearance: any = {
          "MAWB": null,
        }
        await Promise.all([
          Utilities.callTMMApi('manifests/removeMAWB', EConfigure.PUT_METHOD, { HAWBs }),
          this.edaRepository.updateData(updateClearance, optional),
          this.mecRepository.updateData(updateClearance, optional),
        ]);
      }
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.SUCCESS;
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][holdManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async MECGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      const exportOptional: Optional = new Optional();
      exportOptional.setRelation(["exportDetailItems"]);
      exportOptional.setAttributes(value['select'].split(','));
      exportOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);
      if (value['classify']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['dateCheckout']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }

      const MECs = await this.mecRepository.queryAll(exportOptional);
      const obj: any = {};
      if (MECs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { 'none': MECs };
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][MECGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }


  public async EDAGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      const exportOptional: Optional = new Optional();
      exportOptional.setRelation(["exportDetailItems"]);
      exportOptional.setAttributes(value['select'].split(','));
      exportOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);
      if (value['classify']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['dateCheckout']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      const EDAs = await this.edaRepository.queryAll(exportOptional);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { 'none': EDAs };
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][EDAGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async checkClearanced(employeeId: number, MAWB: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.FAIL,
          'data': null
        }
      }

      const totalOptional: Optional = new Optional();
      totalOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      ]);

      const cleanrancedOptional: Optional = new Optional();
      cleanrancedOptional.setAttributes([this._sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
      cleanrancedOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, 'dateClearanced', EConfigure.NOT_EQUAL, null),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, 7),
      ]);

      const notClearancedOptional: Optional = new Optional();
      notClearancedOptional.setAttributes([this._sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
      notClearancedOptional.setWhere([
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'declarationNo', EConfigure.EQUAL, null),
        new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, null),
        new Where(EConfigure.AND, 'dateClearanced', EConfigure.EQUAL, null),
      ]);

      const [totalMECs, totalEDAs, MECClearanced, EDAClearanced, MECNotClearance, EDANotClearance] = await Promise.all([
        this.mecRepository.countData(totalOptional),
        this.edaRepository.countData(totalOptional),
        this.mecRepository.queryOneRaw(cleanrancedOptional),
        this.edaRepository.queryOneRaw(cleanrancedOptional),
        this.mecRepository.queryOneRaw(notClearancedOptional),
        this.edaRepository.queryOneRaw(notClearancedOptional),
      ]);

      const HAWBClearanced: any = [];
      const HAWBNotClearanced: any = [];
      if (MECClearanced && MECClearanced['HAWBs'] && MECClearanced['HAWBs'].length > 0) {
        HAWBClearanced.push(...MECClearanced['HAWBs'].split(','));
      }
      if (EDAClearanced && EDAClearanced['HAWBs'] && EDAClearanced['HAWBs'].length > 0) {
        HAWBClearanced.push(...EDAClearanced['HAWBs'].split(','))
      }
      if (MECNotClearance && MECNotClearance['HAWBs'] && MECNotClearance['HAWBs'].length > 0) {
        HAWBNotClearanced.push(...MECNotClearance['HAWBs'].split(','));
      }
      if (EDANotClearance && EDANotClearance['HAWBs'] && EDANotClearance['HAWBs'].length > 0) {
        HAWBNotClearanced.push(...EDANotClearance['HAWBs'].split(','))
      }
      reponze['status'] = true;
      reponze['message']['message'] = EMessage.SUCCESS;
      reponze['message']['data'] = {
        'total': Number(totalMECs) + Number(totalEDAs),
        'HAWBClearanced': HAWBClearanced,
        'HAWBNotClearanced': HAWBNotClearanced,
      };
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][checkClearanced]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async saveWarehouse(HAWB: string, address: string, employeeId: number, hubs: any, date: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const EDAQuery: any = this.edaRepository.getOneOptional(optional);
      const MECQuery: any = this.mecRepository.getOneOptional(optional);
      const [EDA, MEC] = await Promise.all([EDAQuery, MECQuery]);
      const updateWarehouse: any = {
        'warehouseAddress': address,
        'warehouseCheckout': null
      }
      const now: string = moment().format(EConfigure.TIME);
      if (MEC) {
        const mec: MEC = MEC;
        // if(!mec['warehouseCheckin']) {
        updateWarehouse['warehouseCheckin'] = `${date} ${now}`;
        // }

        const [total] = await this.mecRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }

      }
      if (EDA) {
        const eda: EDA = EDA;
        // if(!eda['warehouseCheckin']) {
        updateWarehouse['warehouseCheckin'] = `${date} ${now}`;
        // }
        const [total] = await this.edaRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][storeWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getStoreWarehouse(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
      )
      const EDAQuery = this.edaRepository.queryAll(optional);
      const MECQuery = this.mecRepository.queryAll(optional);
      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (MECs.length > 0 || EDAs.length > 0) {
        let mecData: IManagement[] = [];
        let edaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: EDA) => {
            const eda: IStoreWarehouse = {
              'HAWBClearance': EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'HAWB': EDA[EConfigure.HAWB_FIELD],
              'MAWB': EDA['MAWB'],
              'warehouseAddress': EDA['warehouseAddress'],
              'warehouseCheckin': EDA['warehouseCheckin'],
              'warehouseCheckout': EDA['warehouseCheckout'],
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          mecData = MECs.map((MEC: MEC) => {
            const mec: IStoreWarehouse = {
              'HAWBClearance': MEC[EConfigure.HAWB_CLEARANCE_FIELD],
              'HAWB': MEC[EConfigure.HAWB_FIELD],
              'MAWB': MEC['MAWB'],
              'warehouseAddress': MEC['warehouseAddress'],
              'warehouseCheckin': MEC['warehouseCheckin'],
              'warehouseCheckout': MEC['warehouseCheckout'],
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getStoreWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async deleteWarehouse(HAWB: string, address: string, employeeId: number, hubs: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
        }
      }
      const optional: Optional = new Optional();
      optional.setAttributes(['HAWBClearance', 'warehouseCheckin']);
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        new Where(EConfigure.AND, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.EQUAL, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      if (hubs) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        )
      }
      const EDAQuery: any = this.edaRepository.getOneOptional(optional);
      const MECQuery: any = this.mecRepository.getOneOptional(optional);
      const [EDA, MEC] = await Promise.all([EDAQuery, MECQuery]);
      const updateWarehouse: any = {
        'warehouseCheckout': moment().format(EConfigure.FULL_TIME)
      }

      if (MEC) {
        const mec: MEC = MEC;
        if (!mec['warehouseCheckin']) {
          reponze['message'] = {
            'message': EMessage.MANIFEST_NOT_CHECKIN_WAREHOUSE,
            'data': HAWB
          }
          return reponze;
        }
        const [total] = await this.mecRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }

      }
      if (EDA) {
        const eda: EDA = EDA;
        if (!eda['warehouseCheckin']) {
          reponze['message'] = {
            'message': EMessage.MANIFEST_NOT_CHECKIN_WAREHOUSE,
            'data': HAWB
          }
          return reponze;
        }
        const [total] = await this.edaRepository.updateData(updateWarehouse, optional)
        if (total > 0) {
          reponze['status'] = true;
          reponze['message'] = {
            'message': EMessage.UPDATE_SUCCESS,
            'data': HAWB
          }
        } else {
          reponze['message'] = {
            'message': EMessage.UPDATE_FAIL,
            'data': HAWB
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][deleteWarehouse]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async masterLimit(hubId: number): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const [MICs, IDAs] = await Promise.all([
        this.sequelize.query(`
          SELECT "MAWB"
            FROM (
                SELECT "MAWB", MAX("createdAt") AS "createdAt"
                FROM "clearance_mecs" AS "MIC"
                WHERE "hubId" = ${hubId} AND "MAWB" IS NOT NULL AND "isDeleted" = false
                GROUP BY "MAWB"
            ) AS "MIC"
            ORDER BY "createdAt" DESC
            LIMIT 200;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB"
            FROM (
                SELECT "MAWB", MAX("createdAt") AS "createdAt"
                FROM "clearance_edas" AS "MIC"
                WHERE "hubId" = ${hubId} AND "MAWB" IS NOT NULL AND "isDeleted" = false
                GROUP BY "MAWB"
            ) AS "MIC"
            ORDER BY "createdAt" DESC
            LIMIT 200;`,
          { type: this.sequelize.QueryTypes.SELECT }),
      ]);
      let masters: string[] = [];
      if (IDAs.length > 0 || MICs.length > 0) {
        masters = [...IDAs, ...MICs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][masterLimit]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async boxMAWB(externalBoxName: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const [MECs, EDAs, MICs, IDAs] = await Promise.all([
        this.sequelize.query(`
          SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "countryCode", "isHold"  
          FROM "clearance_mecs"
          WHERE "externalBoxName" = '${externalBoxName}' AND "isDeleted" = false;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "countryCode", "isHold" 
          FROM "clearance_edas"
          WHERE "externalBoxName" = '${externalBoxName}' AND "isDeleted" = false;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "countryCode", "isHold" 
          FROM "clearance_mics"
          WHERE "externalBoxName" = '${externalBoxName}' AND "isDeleted" = false;`,
          { type: this.sequelize.QueryTypes.SELECT }),
        this.sequelize.query(`
          SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "countryCode", "isHold" 
          FROM "clearance_idas"
          WHERE "externalBoxName" = '${externalBoxName}' AND "isDeleted" = false;`,
          { type: this.sequelize.QueryTypes.SELECT }),
      ]);
      let data: any[] = [];
      if (EDAs.length > 0) {
        data = [...data, ...EDAs];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = data;
      }
      if (IDAs.length > 0) {
        data = [...data, ...IDAs];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = data;
      }
      if (MICs.length > 0) {
        data = [...data, ...MICs];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = data;
      }
      if (MECs.length > 0) {
        data = [...data, ...MECs];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = data;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][boxMAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async updateSortLane(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.OR, EConfigure.HAWB_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.OR, EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.IN, HAWB),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      let [mec, eda] = await Promise.all([
        this.mecRepository.getOneOptional(optional),
        this.edaRepository.getOneOptional(optional)
      ]);
      if (mec || eda) {
        let mecObj: MEC = mec;
        let edaObj: EDA = mec;
        mecObj["isSortLane"] = true;
        edaObj["isSortLane"] = true;
        await mecObj.save();
        await edaObj.save();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = true;
      }
      return reponze;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async exportLane(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      if (HAWB != "") {
        let [mec, eda] = await Promise.all([
          this.sequelize.query(`
            SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "phase", "externalBoxName", "isHold" 
            FROM "clearance_mecs" WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}') AND "isDeleted" = false LIMIT 1;`,
            { type: this.sequelize.QueryTypes.SELECT }),
          this.sequelize.query(`
            SELECT "MAWB", "HAWB", "HAWBClearance", "isSortLane", "inspectionKindClassification", "phase", "externalBoxName", "isHold" 
            FROM "clearance_edas" WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}') AND "isDeleted" = false LIMIT 1;`,
            { type: this.sequelize.QueryTypes.SELECT }),
        ]);
        if (mec.length > 0) {
          mec = mec[0];
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = mec;
        }
        if (eda.length > 0) {
          eda = eda[0];
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = eda;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][printAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async printAWB(HAWB: string): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      if (HAWB != "") {
        let [mec, eda] = await Promise.all([
          this.sequelize.query(`
            SELECT "inspectionKindClassification", "phase", "originalOrderNumberClient" 
            FROM "clearance_mecs" WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}' OR "originalOrderNumberClient" = '${HAWB}') AND "isDeleted" = false 
            ORDER BY "createdAt" DESC;`,
            { type: this.sequelize.QueryTypes.SELECT }),
          this.sequelize.query(`
            SELECT "inspectionKindClassification", "phase", "originalOrderNumberClient" 
            FROM "clearance_edas" WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}' OR "originalOrderNumberClient" = '${HAWB}') AND "isDeleted" = false 
            ORDER BY "createdAt" DESC;`,
            { type: this.sequelize.QueryTypes.SELECT }),
        ]);
        if (mec.length > 0) {
          mec = mec[0];
          await this.sequelize.query(`
            UPDATE clearance_mecs SET "isSortLane" = true, "updatedAt" = '${moment.utc()}'
            WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}' OR "originalOrderNumberClient" = '${HAWB}') AND "isDeleted" = false;`,
            { type: this.sequelize.QueryTypes.UPDATE });
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = mec;
        }
        if (eda.length > 0) {
          eda = eda[0];
          await this.sequelize.query(`
            UPDATE clearance_edas SET "isSortLane" = true, "updatedAt" = '${moment.utc()}'
            WHERE ("HAWBClearance" = '${HAWB}' OR "HAWB" = '${HAWB}' OR "originalOrderNumberClient" = '${HAWB}') AND "isDeleted" = false;`,
            { type: this.sequelize.QueryTypes.UPDATE });
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = eda;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][printAWB]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async boxCheckSort(value: any): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const MAWB = value['MAWB'];
      const boxName = value['externalBoxName'];
      const type = value['type'];
      if (MAWB != "") {
        let addBoxMec = "";
        let addBoxEda = "";
        if (boxName != undefined && boxName != "") {
          addBoxMec = `AND cm."externalBoxName" = '${boxName}'`;
          addBoxEda = `AND ce."externalBoxName" = '${boxName}'`;
        }
        let fullSort = "";
        switch (type) {
          case 1:
            fullSort = 'HAVING CAST(SUM(CASE WHEN "isSortLane" = false THEN 1 ELSE 0 END) AS int) <> 0';
            break;
          case 2:
            fullSort = 'HAVING CAST(SUM(CASE WHEN "isSortLane" = false THEN 1 ELSE 0 END) AS int) = 0';
            break;
        }
        const hawbs = await this.sequelize.query(`
          WITH outbound_tables AS (
            SELECT cm."externalBoxName", cm."MAWB", cm."isSortLane" FROM public.clearance_mecs cm WHERE cm."MAWB"  = '${MAWB}' ${addBoxMec} 
            UNION ALL
            SELECT ce."externalBoxName", ce."MAWB", ce."isSortLane" FROM public.clearance_edas ce WHERE ce."MAWB"  = '${MAWB}' ${addBoxEda} 
          )
          SELECT 
            "MAWB", "externalBoxName",
            CAST(SUM(CASE WHEN "isSortLane" = false THEN 1 ELSE 0 END) AS int) AS "unSort",
            CAST(SUM(CASE WHEN "isSortLane" = true THEN 1 ELSE 0 END)AS int) AS "sort"
          FROM outbound_tables 
          GROUP BY "externalBoxName", "MAWB"
          ${fullSort};`,
          { type: this.sequelize.QueryTypes.SELECT });

        const counter = await this.sequelize.query(`
          WITH outbound_hawbs AS (
            SELECT cm."HAWBClearance", cm."externalBoxName", cm."MAWB", cm."isSortLane"
            FROM public.clearance_mecs cm WHERE cm."MAWB" = '${MAWB}' ${addBoxMec}
            UNION ALL
            SELECT ce."HAWBClearance", ce."externalBoxName", ce."MAWB", ce."isSortLane"
            FROM public.clearance_edas ce WHERE ce."MAWB" = '${MAWB}' ${addBoxEda}
          ),
          box_sort_status AS (
            SELECT 
              "externalBoxName",
              COUNT(1) AS "totalItems",
              SUM(CASE WHEN "isSortLane" = true THEN 1 ELSE 0 END) AS "sortedItems",
              COUNT(1) FILTER (WHERE "isSortLane" = false) AS "unSortItems",
              COUNT(1) FILTER (WHERE "isSortLane" = true) AS "sortItems"
            FROM outbound_hawbs
            GROUP BY "externalBoxName"
          )
          SELECT 
            CAST(COUNT(DISTINCT "externalBoxName") AS int) AS "totalBox",
            CAST(SUM(CASE WHEN "totalItems" = "sortedItems" THEN 1 ELSE 0 END) AS int) AS "sortBox",
            CAST(SUM(CASE WHEN "totalItems" != "sortedItems" THEN 1 ELSE 0 END) AS int) AS "unSortBox",
            CAST(SUM("totalItems") AS int) AS "totalHAWB",
            CAST(SUM("unSortItems") AS int) AS "unSort",
            CAST(SUM("sortItems") AS int) AS "sort"
          FROM box_sort_status;
          `, { type: this.sequelize.QueryTypes.SELECT });
        if (hawbs.length > 0) {
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.SUCCESS;
          reponze['message']['data'] = { counter, hawbs };
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][boxCheckSort]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async counterBoxAll(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const data: Record<string, number> = {
        unSort: 0,
        sort: 0,
        boxName: 0,
        unBoxName: 0,
        unDeclaration: 0,
        blue: 0,
        yellow: 0,
        red: 0,
      }

      optional.setAttributes([
        this.sequelize.literal('CAST(SUM(case when "isSortLane" = false then 1 else 0 end) as int) as "unSort"'),
        this.sequelize.literal('CAST(SUM(case when "isSortLane" = true then 1 else 0 end)as int) as "sort"'),
        this.sequelize.literal('CAST(COUNT(DISTINCT "externalBoxName") as int) as "boxName"'),
        this.sequelize.literal('CAST(SUM(case when "externalBoxName" is null then 1 else 0 end)as int) as "unBoxName"'),
        this.sequelize.literal('CAST(SUM(case when "inspectionKindClassification" is null then 1 else 0 end)as int) as "unDeclaration"'),
        this.sequelize.literal('CAST(SUM(case when "inspectionKindClassification" = \'1\' then 1 else 0 end)as int) as "blue"'),
        this.sequelize.literal('CAST(SUM(case when "inspectionKindClassification" = \'2\' then 1 else 0 end)as int) as "yellow"'),
        this.sequelize.literal('CAST(SUM(case when "inspectionKindClassification" = \'3\' then 1 else 0 end)as int) as "red"'),
      ]);
      const EDAQuery = this.edaRepository.queryOneRaw(optional);
      const MECQuery = this.mecRepository.queryOneRaw(optional);

      let [mec, eda] = await Promise.all([MECQuery, EDAQuery]);
      if (mec || eda) {
        Object.keys(data).forEach(key => {
          if (mec.hasOwnProperty(key)) {
            data[key] = data[key] + mec[key]
          }
          if (eda.hasOwnProperty(key)) {
            data[key] = data[key] + eda[key]
          }
        });
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = data;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][counterBoxAll]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async externalBoxes(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }
      const masterExternalOptional: Optional = new Optional();
      masterExternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterExternalOptional.setAttributes(['externalBoxName']);
      masterExternalOptional.setGroup(['externalBoxName']);

      const masterInternalOptional: Optional = new Optional();
      masterInternalOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      masterInternalOptional.setAttributes(['internalBoxName']);
      masterInternalOptional.setGroup(['internalBoxName']);
      // eda
      const EDAExternalQuery = this.edaRepository.queryAll(masterExternalOptional);
      // mec
      const MECExternalQuery = this.mecRepository.queryAll(masterExternalOptional);
      // eda
      const EDAInternalQuery = this.edaRepository.queryAll(masterInternalOptional);
      // mec
      const MECInternalQuery = this.mecRepository.queryAll(masterInternalOptional);

      const [EDAExs, MECExs, EDAIns, MECIns] = await Promise.all([EDAExternalQuery, MECExternalQuery, EDAInternalQuery, MECInternalQuery]);
      if (EDAExs.length > 0 || MECExs.length > 0 || EDAIns.length > 0 || MECIns.length > 0) {
        let externalBoxes: string[] = [];
        externalBoxes = [...EDAExs, ...MECExs];
        if (externalBoxes.length > 0) {
          externalBoxes = [...new Set(externalBoxes.map((item: any) => item['externalBoxName']))];
        }

        let internalBoxes: string[] = [];
        internalBoxes = [...EDAExs, ...MECExs];
        if (externalBoxes.length > 0) {
          internalBoxes = [...new Set(internalBoxes.map((item: any) => item['internalBoxName']))];
        }

        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { externalBoxes, internalBoxes };
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][externalBoxes]', (error as Error).message);
      throw new Error(error as any);
    }
  }
}
export default ExportClearanceService;