'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import Middlewares from '../../../middleware/middlewares';
import ParserUrl from '../../../parser/url';
import EConfigure from '../../../emuns/configures';
import Optional from '../../../parser/optional';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import Meta from "../../../https/meta";
import HttpData from "../../../https/data";
import ErrorValidate from "../../../util/error.validate";
import ExportClearanceValidate from "./exportClearance.validate";
import { IResponze } from "../../../https/responze";
import RedisPromise from "../../../util/redis.promise";
import UserService from "../user/user.service";
import Utilities from "../../../util/utilities";
import Where from "../../../parser/where";
import { Clearance } from "../../../xmlClearance/clearance";
import PdfGenerate from "../pdfGenerate/pdfGenerate.service";
import ExportClearanceService from "./exportClearance.service";


class ExportClearanceController {
  public path = '/exports';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get(`${this.path}/mec`, this.getAllMEC);
    this.router.get(`${this.path}/eda`, this.getAllEDA);
    this.router.get(`${this.path}/getAll`, this.getAll);
    this.router.get(`${this.path}/counterBox`, this.counterBoxAll);
    this.router.get(`${this.path}/master`, this.master);
    this.router.get(`${this.path}/clearanceReport`, this.clearanceReport);
    this.router.get(`${this.path}/holdManifest`, this.getHoldManifest);
    this.router.get(`${this.path}/externalBoxes`, this.externalBoxes);

    this.router.get(`${this.path}/storeWarehouse`, this.getStoreWarehouse);

    this.router.get(`${this.path}/exportReport`, this.exportReport);
    this.router.get(`${this.path}/clearanceReportExport`, this.clearanceReportExport);
    this.router.get(`${this.path}/mic/assignCargoName`, this.getMecAssignCargoName);
    this.router.get(`${this.path}/ida/assignCargoName`, this.getEdaAssignCargoName);
    this.router.get(`${this.path}/mec/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneMEC);
    this.router.get(`${this.path}/eda/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneEDA);


    this.router.post(this.path, this.create);

    this.router.post(`${this.path}/checkClearanced`, middlewares.CheckPermission('post:v1:clearance.checkClearanced'), this.checkClearanced);

    this.router.post(`${this.path}/mec`, this.allMEC);
    this.router.post(`${this.path}/eda`, this.allEDA);
    this.router.post(`${this.path}/filterMEC`, this.filterMEC);
    this.router.post(`${this.path}/filterEDA`, this.filterEDA);
    this.router.post(`${this.path}/retryMEC`, middlewares.CheckPermission('put:v1:clearance.retry'), this.retryMEC);
    this.router.post(`${this.path}/MECGroupBox`, this.MECGroupBox);
    this.router.post(`${this.path}/EDAGroupBox`, this.EDAGroupBox);
    this.router.post(`${this.path}/pdfPrint`, this.pdfPrint);
    this.router.post(`${this.path}/storeWarehouse`, this.saveWarehouse);
    this.router.post(`${this.path}/boxCheckSort`, this.boxCheckSort);
    this.router.post(`${this.path}/ibcExport`, this.ibcExport);

    this.router.put(`${this.path}/mecToEDA`, middlewares.CheckPermission('get:v1:clearance.changeClassify'), this.changeMECToEDA);
    this.router.put(`${this.path}/edaToMEC`, middlewares.CheckPermission('get:v1:clearance.changeClassify'), this.changeEDAToMEC);

    this.router.put(`${this.path}/registerMEC`, middlewares.CheckPermission('put:v1:clearance.registerMEC'), this.registerMEC);
    this.router.put(`${this.path}/registerMEE`, middlewares.CheckPermission('put:v1:clearance.registerMEE'), this.registerMEE);

    this.router.put(`${this.path}/registerEDA`, middlewares.CheckPermission('put:v1:clearance.registerEDA'), this.registerEDA);
    this.router.put(`${this.path}/registerEDC`, middlewares.CheckPermission('put:v1:clearance.registerEDC'), this.registerEDC);
    this.router.put(`${this.path}/registerIEDA`, middlewares.CheckPermission('put:v1:clearance.registerIEDA'), this.registerIEDA);
    this.router.put(`${this.path}/registerEDE`, middlewares.CheckPermission('put:v1:clearance.registerEDE'), this.registerEDE);

    this.router.put(`${this.path}/master`, this.updateMaster);
    this.router.put(`${this.path}/taxCodeBussiness`, this.taxCodeBussiness);

    this.router.put(`${this.path}/mecCheckin`, middlewares.CheckPermission('get:v1:clearance.checkin'), this.mecCheckin);
    this.router.put(`${this.path}/edaCheckin`, middlewares.CheckPermission('get:v1:clearance.checkin'), this.edaCheckin);
    this.router.put(`${this.path}/holdManifest`, this.updateHoldManifest);

    this.router.put(`${this.path}/mec/assignCargoName`, this.mecAssignCargoName);
    this.router.put(`${this.path}/eda/assignCargoName`, this.edaAssignCargoName);

    this.router.put(`${this.path}/sortLane/:HAWB`, this.updateSortLane);
    this.router.put(`${this.path}/mec/:HAWB`, middlewares.CheckPermission('put:v1:clearance.update'), this.updateMEC);
    this.router.put(`${this.path}/eda/:HAWB`, middlewares.CheckPermission('put:v1:clearance.update'), this.updateEDA);
    this.router.put(`${this.path}/com/:HAWB`, middlewares.CheckPermission('put:v1:clearance.update'), this.updateCOM);
    //////////
    this.router.delete(`${this.path}/storeWarehouse`, this.deleteWarehouse);
    this.router.delete(`${this.path}/:id`, this.deleteOne);

    this.router.patch(`${this.path}/importExcel`, this.importExcel);
  }

  private async ibcExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ExportClearanceValidate.ibcExport.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);

        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
        if (profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];

        }
        if (userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const pdfGenerate = new PdfGenerate();
      value = Utilities.convertNull(value);
      const message: IResponze = await pdfGenerate.ibcExport(value, hubs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][pdfPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async externalBoxes(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const outboundService = new ExportClearanceService();
      const data: IResponze = await outboundService.externalBoxes(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][externalBoxes]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async counterBoxAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const message: IResponze = await exportClearanceService.counterBoxAll(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][getAllMECEDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async boxCheckSort(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ExportClearanceValidate.boxCheckSort.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportService = new ExportClearanceService();
      value = Utilities.convertNull(value);
      const message: IResponze = await exportService.boxCheckSort(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][pdfPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateSortLane(req: Request, res: Response, next: NextFunction) {
    try {
      const exportClearanceService = new ExportClearanceService();
      const { HAWB } = req.params;
      const data: IResponze = await exportClearanceService.updateSortLane(HAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][updateSortLane]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async pdfPrint(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ExportClearanceValidate.historyInvoice.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
        if (profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
        if (userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const pdfGenerate = new PdfGenerate();
      value = Utilities.convertNull(value);
      const message: IResponze = await pdfGenerate.outboundPdfGenerate(value, hubs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][pdfPrint]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async deleteWarehouse(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.HAWB.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWB, address } = value;
      const data: IResponze = await exportClearanceService.deleteWarehouse(HAWB, address, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][deleteWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getStoreWarehouse(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.getStoreWarehouse(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound]][getStoreWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async saveWarehouse(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.storeWarehouse.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWB, address, date } = value;
      const data: IResponze = await exportClearanceService.saveWarehouse(HAWB, address, employeeId, hubs, date);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][storeWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async checkClearanced(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.checkClearanced.validate(req.body, { abortEarly: false });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const exportClearanceService = new ExportClearanceService();
      const { MAWB } = value;
      const data: IResponze = await exportClearanceService.checkClearanced(employeeId, MAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][update_master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async EDAGroupBox(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ExportClearanceValidate.groupBox.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      value = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.EDAGroupBox(value, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][EDAGroupBox]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async MECGroupBox(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ExportClearanceValidate.groupBox.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      value = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.MECGroupBox(value, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][MECGroupBox]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateHoldManifest(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.arrHoldManifest.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const { holdData, isRemoveMAWB } = value;
      const data: IResponze = await exportClearanceService.holdManifest(Utilities.convertNull(holdData), isRemoveMAWB, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][update_hold_manifest]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getHoldManifest(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.getHoldManifest(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async retryMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ExportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const message: any = await new Clearance().getHawbData(value['HAWBs'], 'MEC');
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['isSuccess'], message['hawbSuccess'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][retryMEC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async exportReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.exportReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const data: IResponze = await exportClearanceService.getAllMECEDA(optional);
      const meta = new Meta(limit, optional.getOffset(), data['message']['data']['total'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data']['manifests'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][getAllMECEDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getEdaAssignCargoName(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const [data, total] = await exportClearanceService.getEdaAssignCargoName(optional, employeeId);
      const meta = new Meta(limit, optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][getEdaAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMecAssignCargoName(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const [data, total] = await exportClearanceService.getMecAssignCargoName(optional, employeeId);
      const meta = new Meta(limit, optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][getMecAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async edaAssignCargoName(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs } = value;
      const data: IResponze = await exportClearanceService.edaAssignCargoName(HAWBs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][edaAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async mecAssignCargoName(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs } = value;
      const data: IResponze = await exportClearanceService.mecAssignCargoName(HAWBs, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][mecAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async edaCheckin(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.checkIn.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, dateCheckin } = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.edaCheckin(HAWBs, dateCheckin, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][change_MIC_to_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async mecCheckin(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.checkIn.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, dateCheckin } = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.mecCheckin(HAWBs, dateCheckin, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][change_MIC_to_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async taxCodeBussiness(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs } = value;
      const data: IResponze = await exportClearanceService.taxCodeBussiness(HAWBs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][change_MIC_to_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReportExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.clearanceReportExport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.clearanceReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async master(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.master(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filterEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.filterEDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), data['message']['data']['totalManifest'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][filer_ida]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filterMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.filterMEC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), data['message']['data']['totalManifest'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][filer_mic]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAllMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllMEC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][get_all_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAllEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter(); const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllEDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][get_all_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async changeMECToEDA(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.changeClassify.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, classify } = value;
      const data: IResponze = await exportClearanceService.changeMECToEDA(HAWBs, classify, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][change_MIC_to_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }


  private async changeEDAToMEC(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.changeClassify.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, classify } = value;
      const data: IResponze = await exportClearanceService.changeEDAToMEC(HAWBs, classify, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][change_IDA_to_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateEDA(req: Request, res: Response, next: NextFunction) {
    try {
      let { error, value } = ExportClearanceValidate.updateEDA.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const { HAWB } = req.params;
      value = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.updateEDA(HAWB, value, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][update_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateCOM(req: Request, res: Response, next: NextFunction) {
    try {
      let { error, value } = ExportClearanceValidate.updateCOM.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const { HAWB } = req.params;
      value = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.updateEDA(HAWB, value, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][update_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateMEC(req: Request, res: Response, next: NextFunction) {
    try {
      let { error, value } = ExportClearanceValidate.updateMEC.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const { HAWB } = req.params;
      value = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.updateMEC(HAWB, value, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][update_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerEDE(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await exportClearanceService.registerEDE(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][register_EDE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerIEDA(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await exportClearanceService.registerIEDA(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][register_IEDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerEDC(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await exportClearanceService.registerEDC(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][register_EDC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerEDA(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await exportClearanceService.registerEDA(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][register_EDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerMEE(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await exportClearanceService.registerMEE(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][register_MEE]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async registerMEC(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.register.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if (profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if (userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const { HAWBs, isPrioritize } = value;
      const data: IResponze = await exportClearanceService.registerMEC(HAWBs, isPrioritize, employeeId, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][register_MEC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: any = await exportClearanceService.getOneMEC(HAWB, optional);
      let status: boolean = false;
      if (data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][get_one_MEC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: any = await exportClearanceService.getOneEDA(HAWB, optional);
      let status: boolean = false;
      if (data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][get_one_EDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async allMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllMEC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][all_MEC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async allEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if (profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if (userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllEDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][all_EDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateMaster(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceValidate.arrMaster.validate(req.body, { abortEarly: false });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.updateMaster(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][update_master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async importExcel(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.createImportExcel(req.body);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][importExcel]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async create(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ExportClearanceValidate.arrCreate.validate(req.body, { abortEarly: false });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.createExport(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
  //////////////
  private async deleteOne(req: Request, res: Response, next: NextFunction): Promise<any> {
    // try {
    //   const id: number = Number(req.params.id);
    //   const cityService =  new ImportClearanceService();
    //   const [total] = await cityService.deleteOne(id);
    //   if(total > 0) {
    //     return HttpResponse.sendMessage(res, httpStatus.OK, new HttpException(true, EMessage.DELETE_SUCCESS));
    //   } else {
    //     return HttpResponse.sendMessage(res, httpStatus.EXPECTATION_FAILED, new HttpException(true, EMessage.DELETE_FAIL));
    //   }
    // } catch (error) {
    //   console.log(' \t --- [%s] [error][controller][outbound][delete_one]: %o', moment().format(EConfigure.FULL_TIME), error);
    //   return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    // }
  }
}

export default ExportClearanceController;