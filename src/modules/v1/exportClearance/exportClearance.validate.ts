'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';
import Utilities from '../../../util/utilities';
import { ClassifyName, ClassifyValidateName } from '../../../emuns/classifyExport';
import { WeightName } from '../../../emuns/weight';
import ExportClearanceDetailValidate from '../exportClearanceDetail/exportClearanceDetail.validate';

class ExportClearanceValidate {
  static create: any = Joi.object({
    items: Joi.array().min(1).items(ExportClearanceDetailValidate.create).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    HAWBClearance: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWBClearance ${EMessage.REQUIRED}`,
      "string.empty": `HAWBClearance ${EMessage.EMPTYED}`,
      "string.max": `HAWBClearance ${EMessage.MAX_50}`,
    }),
    stationId: Joi.number().min(0).greater(0).required().messages({
      "any.required": `stationId ${EMessage.REQUIRED}`,
      "number.base": `stationId ${EMessage.NUMBER}`,
      "number.min": `stationId ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `stationId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    MAWB: Joi.string().trim().max(50).allow(null).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    flightNo: Joi.string().max(50).trim().allow(null).messages({
      "string.empty": `flightCode ${EMessage.EMPTYED}`,
      "string.max": `flightCode ${EMessage.MAX_50}`,
    }),
    departureDate: Joi.string().max(20).allow(null).messages({
      "string.empty": `departureDate ${EMessage.EMPTYED}`,
      "string.max": `departureDate ${EMessage.MAX_20}`,
    }),
    exporterCode: Joi.string().trim().allow(null).messages({
      "string.empty": `exporterCode ${EMessage.EMPTYED}`,
    }),
    exporterName: Joi.string().trim().max(255).messages({
      "string.empty": `exporterName ${EMessage.EMPTYED}`,
      "string.max": `exporterName ${EMessage.MAX_255}`,
    }),
    exporterPostCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `exporterPostCode ${EMessage.EMPTYED}`,
      "string.max": `exporterPostCode ${EMessage.MAX_10}`,
    }),
    exporterAddress: Joi.string().trim().required().messages({
      "any.required": `exporterAddress ${EMessage.REQUIRED}`,
      "string.empty": `exporterAddress ${EMessage.EMPTYED}`,
    }),
    exporterTelephoneNumber: Joi.string().trim().allow(null).messages({
      "string.empty": `exporterTelephoneNumber ${EMessage.EMPTYED}`,
    }),
    consigneeCode: Joi.string().trim().max(255).allow(null).messages({
      "string.empty": `consigneeCode ${EMessage.EMPTYED}`,
      "string.max": `consigneeCode ${EMessage.MAX_255}`,
    }),
    consigneeName: Joi.string().trim().max(255).messages({
      "string.empty": `consigneeName ${EMessage.EMPTYED}`,
      "string.max": `consigneeName ${EMessage.MAX_255}`,
    }),
    consigneeTelephoneNumber: Joi.string().trim().max(30).allow(null).messages({
      "string.empty": `consigneeTelephoneNumber ${EMessage.EMPTYED}`,
      "string.max": `consigneeTelephoneNumber ${EMessage.MAX_30}`,
    }),
    consigneePostCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `consigneePostCode ${EMessage.EMPTYED}`,
      "string.max": `consigneePostCode ${EMessage.MAX_10}`,
    }),
    consigneeAddress: Joi.string().messages({
      "string.empty": `consigneeAddress ${EMessage.EMPTYED}`,
    }),
    consigneeCountryCode: Joi.string().trim().max(2).allow(null).messages({
      "string.empty": `consigneeCountryCode ${EMessage.EMPTYED}`,
      "string.max": `consigneeCountryCode ${EMessage.MAX_2}`,
    }),
    weight: Joi.number().messages({
      "number.base": `weight ${EMessage.NUMBER}`,
    }),
    currencyCode: Joi.string().trim().max(3).required().messages({
      "any.required": `currencyCode ${EMessage.REQUIRED}`,
      "string.empty": `currencyCode ${EMessage.EMPTYED}`,
      "string.max": `currencyCode ${EMessage.MAX_3}`,
    }),
    totalPrice: Joi.number().required().messages({
      "any.required": `totalPrice ${EMessage.REQUIRED}`,
      "number.base": `totalPrice ${EMessage.NUMBER}`,
    }),
    valueClearanceVND: Joi.number().required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
    }),
    originalOrderNumberClient: Joi.string().trim().max(255).allow(null).messages({
      "string.empty": `originalOrderNumberClient ${EMessage.EMPTYED}`,
      "string.max": `originalOrderNumberClient ${EMessage.MAX_255}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).required().max(3).trim().messages({
      "any.required": `unitOfMass ${EMessage.REQUIRED}`,
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    hubId: Joi.number().min(0).allow(null).messages({
      "number.base": `hubId ${EMessage.NUMBER}`,
      "number.min": `hubId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    warehouseId: Joi.number().min(0).allow(null).messages({
      "number.base": `warehouseId ${EMessage.NUMBER}`,
      "number.min": `warehouseId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    meanOfTransportationCode: Joi.number().allow(null).messages({
      "number.base": `meanOfTransportationCode ${EMessage.NUMBER}`,
    }),
    piece: Joi.number().allow(null).messages({
      "number.base": `piece ${EMessage.NUMBER}`,
    }),
    //
    serviceId: Joi.number().integer().required().min(1).messages({
      "any.required": `serviceId ${EMessage.REQUIRED}`,
      "number.base": `serviceId ${EMessage.NUMBER}`,
      "number.integer": `serviceId ${EMessage.INTEGER}`,
      "number.min": `serviceId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    classify: Utilities.stringValid(ClassifyName).required().trim().max(3).messages({
      "any.required": `classify ${EMessage.REQUIRED}`,
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_3}`,
    }),
    customerBusinessId: Joi.string().trim().messages({
      "string.empty": `customerBusinessId ${EMessage.EMPTYED}`,
    }),
    customerPersonalId: Joi.string().trim().messages({
      "string.empty": `customerPersonalId ${EMessage.EMPTYED}`,
    }),
    note: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `note ${EMessage.EMPTYED}`,
    }),
    clientId: Joi.number().integer().required().min(1).messages({
      "any.required": `clientId ${EMessage.REQUIRED}`,
      "number.base": `clientId ${EMessage.NUMBER}`,
      "number.integer": `clientId ${EMessage.INTEGER}`,
      "number.min": `clientId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    internalBoxName: Joi.string().trim().max(33).allow(null).messages({
      "string.max": `internalBoxName ${EMessage.MAX_33}`,
    }),
    externalBoxName: Joi.string().trim().max(50).allow(null).messages({
      "string.max": `externalBoxName ${EMessage.MAX_50}`,
    }),
  });

  static HAWBs: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `items ${EMessage.REQUIRED}`,
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
  });

  static arrCreate = Joi.array().min(1).items(ExportClearanceValidate.create).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`,
  });

  static updateMEC: any = Joi.object({
    exportDetails: Joi.array().min(1).items(ExportClearanceDetailValidate.update).messages({
      "array.base": `exportDetails ${EMessage.ARRAY}`,
      "array.min": `exportDetails ${EMessage.LEAST_ITEM_1}`,
    }),
    customsOffice: Joi.string().trim().max(8).allow(null).allow('').messages({
      "string.empty": `customsOffice ${EMessage.EMPTYED}`,
      "string.max": `customsOffice ${EMessage.MAX_8}`,
    }),
    customsSubSection: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `customsSubSection ${EMessage.EMPTYED}`,
      "string.max": `customsSubSection ${EMessage.MAX_4}`,
    }),
    exporterCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `exporterCode ${EMessage.EMPTYED}`,
      "string.max": `exporterCode ${EMessage.MAX_15}`,
    }),
    exporterName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `exporterName ${EMessage.EMPTYED}`,
      "string.max": `exporterName ${EMessage.MAX_255}`,
    }),
    postCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `postCode ${EMessage.EMPTYED}`,
      "string.max": `postCode ${EMessage.MAX_10}`,
    }),
    addressOfExporter: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `addressOfExporter ${EMessage.EMPTYED}`,
    }),
    telephoneNumber: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `telephoneNumber ${EMessage.EMPTYED}`,
      "string.max": `telephoneNumber ${EMessage.MAX_15}`,
    }),
    consigneeCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `consigneeCode ${EMessage.EMPTYED}`,
      "string.max": `consigneeCode ${EMessage.MAX_15}`,
    }),
    consigneeName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `consigneeName ${EMessage.EMPTYED}`,
      "string.max": `consigneeName ${EMessage.MAX_255}`,
    }),
    consigneeTelephoneNumber: Joi.string().trim().allow(null).messages({
      "string.empty": `consigneeTelephoneNumber ${EMessage.EMPTYED}`,
    }),
    postCodeIdentification: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `postCodeIdentification ${EMessage.EMPTYED}`,
      "string.max": `postCodeIdentification ${EMessage.MAX_10}`,
    }),
    address1: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    address2: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address2 ${EMessage.EMPTYED}`,
    }),
    address3: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address3 ${EMessage.EMPTYED}`,
    }),
    address4: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address4 ${EMessage.EMPTYED}`,
    }),
    countryCode: Joi.string().trim().max(2).required().messages({
      "any.required": `countryCode ${EMessage.REQUIRED}`,
      "string.empty": `countryCode ${EMessage.EMPTYED}`,
      "string.max": `countryCode ${EMessage.MAX_2}`,
    }),
    cargoPiece: Joi.number().messages({
      "number.base": `cargoPiece ${EMessage.NUMBER}`,
    }),
    cargoWeight: Joi.number().messages({
      "number.base": `cargoWeight ${EMessage.NUMBER}`,
    }),
    totalOfTaxValue: Joi.number().min(0).greater(0).required().messages({
      "any.required": `totalOfTaxValue ${EMessage.REQUIRED}`,
      "number.base": `totalOfTaxValue ${EMessage.NUMBER}`,
      "number.min": `totalOfTaxValue ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `totalOfTaxValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    customsWarehouseCode: Joi.string().trim().max(10).required().messages({
      "any.required": `customsWarehouseCode ${EMessage.REQUIRED}`,
      "string.empty": `customsWarehouseCode ${EMessage.EMPTYED}`,
      "string.max": `customsWarehouseCode ${EMessage.MAX_10}`,
    }),
    theFinalDestination: Joi.string().trim().max(6).required().messages({
      "any.required": `theFinalDestination ${EMessage.REQUIRED}`,
      "string.empty": `theFinalDestination ${EMessage.EMPTYED}`,
      "string.max": `theFinalDestination ${EMessage.MAX_6}`,
    }),
    loadingPortCode: Joi.string().trim().max(6).required().messages({
      "any.required": `loadingPortCode ${EMessage.REQUIRED}`,
      "string.empty": `loadingPortCode ${EMessage.EMPTYED}`,
      "string.max": `loadingPortCode ${EMessage.MAX_6}`,
    }),
    currencyCodeOfTaxValue: Joi.string().trim().max(3).required().messages({
      "any.required": `currencyCodeOfTaxValue ${EMessage.REQUIRED}`,
      "string.empty": `currencyCodeOfTaxValue ${EMessage.EMPTYED}`,
      "string.max": `currencyCodeOfTaxValue ${EMessage.MAX_3}`,
    }),
    notes: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `notes ${EMessage.EMPTYED}`,
    }),
    ///
    valueClearanceVND: Joi.number().min(0).greater(0).required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
      "number.min": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    customerBusinessId: Joi.string().trim().messages({
      "string.empty": `customerBusinessId ${EMessage.EMPTYED}`,
    }),
    customerPersonalId: Joi.string().trim().messages({
      "string.empty": `customerPersonalId ${EMessage.EMPTYED}`,
    }),
  });

  static updateEDA: any = Joi.object({
    exportDetails: Joi.array().min(1).items(ExportClearanceDetailValidate.updateEDA).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    meansOfTransportationCode: Joi.number().messages({
      "number.base": `meansOfTransportationCode ${EMessage.NUMBER}`,
    }),
    customsOffice: Joi.string().trim().max(8).allow(null).allow('').messages({
      "string.empty": `customsOffice ${EMessage.EMPTYED}`,
      "string.max": `customsOffice ${EMessage.MAX_8}`,
    }),
    customsSubSection: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `customsSubSection ${EMessage.EMPTYED}`,
      "string.max": `customsSubSection ${EMessage.MAX_4}`,
    }),
    exporterCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `exporterCode ${EMessage.EMPTYED}`,
      "string.max": `exporterCode ${EMessage.MAX_15}`,
    }),
    exporterName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `exporterName ${EMessage.EMPTYED}`,
      "string.max": `exporterName ${EMessage.MAX_255}`,
    }),
    postCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `postCode ${EMessage.EMPTYED}`,
      "string.max": `postCode ${EMessage.MAX_10}`,
    }),
    addressOfExporter: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `addressOfExporter ${EMessage.EMPTYED}`,
    }),
    telephoneNumber: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `telephoneNumber ${EMessage.EMPTYED}`,
      "string.max": `telephoneNumber ${EMessage.MAX_15}`,
    }),
    consigneeCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `consigneeCode ${EMessage.EMPTYED}`,
      "string.max": `consigneeCode ${EMessage.MAX_15}`,
    }),
    consigneeName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `consigneeName ${EMessage.EMPTYED}`,
      "string.max": `consigneeName ${EMessage.MAX_255}`,
    }),
    consigneeTelephoneNumber: Joi.string().trim().max(30).allow(null).messages({
      "string.empty": `consigneeTelephoneNumber ${EMessage.EMPTYED}`,
      "string.max": `consigneeTelephoneNumber ${EMessage.MAX_30}`,
    }),
    postCodeIdentification: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `postCodeIdentification ${EMessage.EMPTYED}`,
      "string.max": `postCodeIdentification ${EMessage.MAX_10}`,
    }),
    address1: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    address2: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address2 ${EMessage.EMPTYED}`,
    }),
    address3: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address3 ${EMessage.EMPTYED}`,
    }),
    address4: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address4 ${EMessage.EMPTYED}`,
    }),
    countryCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `countryCode ${EMessage.EMPTYED}`,
      "string.max": `countryCode ${EMessage.MAX_2}`,
    }),
    plannedDeclarantCode: Joi.string().trim().max(5).allow(null).allow('').messages({
      "string.empty": `plannedDeclarantCode ${EMessage.EMPTYED}`,
      "string.max": `plannedDeclarantCode ${EMessage.MAX_5}`,
    }),
    cargoPiece: Joi.number().messages({
      "number.base": `cargoPiece ${EMessage.NUMBER}`,
    }),
    pieceUnitCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `pieceUnitCode ${EMessage.EMPTYED}`,
      "string.max": `pieceUnitCode ${EMessage.MAX_2}`,
    }),
    cargoWeightGross: Joi.number().messages({
      "number.base": `cargoWeightGross ${EMessage.NUMBER}`,
    }),
    weightUnitCodeGross: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `weightUnitCodeGross ${EMessage.EMPTYED}`,
      "string.max": `weightUnitCodeGross ${EMessage.MAX_4}`,
    }),
    customsWarehouseCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `customsWarehouseCode ${EMessage.EMPTYED}`,
      "string.max": `customsWarehouseCode ${EMessage.MAX_10}`,
    }),
    theFinalDestinationCode: Joi.string().trim().max(6).allow(null).allow('').messages({
      "string.empty": `theFinalDestinationCode ${EMessage.EMPTYED}`,
      "string.max": `theFinalDestinationCode ${EMessage.MAX_6}`,
    }),
    theFinalDestinationName: Joi.string().trim().max(6).allow(null).allow('').messages({

      "string.empty": `theFinalDestinationName ${EMessage.EMPTYED}`,
      "string.max": `theFinalDestinationName ${EMessage.MAX_6}`,
    }),
    loadingPortCode: Joi.string().trim().max(6).allow(null).allow('').messages({
      "string.empty": `loadingPortCode ${EMessage.EMPTYED}`,
      "string.max": `loadingPortCode ${EMessage.MAX_6}`,
    }),
    loadingPortName: Joi.string().trim().max(6).allow(null).allow('').messages({
      "string.empty": `loadingPortName ${EMessage.EMPTYED}`,
      "string.max": `loadingPortName ${EMessage.MAX_6}`,
    }),
    loadingPlannedVesselName: Joi.string().max(100).allow(null).allow('').messages({
      "string.empty": `loadingPlannedVesselName ${EMessage.EMPTYED}`,
      "string.max": `loadingPlannedVesselName ${EMessage.MAX_100}`,
    }),
    departurePlannedDate: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `departurePlannedDate ${EMessage.EMPTYED}`,
      "string.max": `departurePlannedDate ${EMessage.MAX_20}`,
    }),
    invoiceClassificationCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `invoiceClassificationCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceClassificationCode ${EMessage.MAX_2}`,
    }),
    termOfPayment: Joi.string().trim().max(7).allow(null).allow('').messages({
      "string.empty": `termOfPayment ${EMessage.EMPTYED}`,
      "string.max": `termOfPayment ${EMessage.MAX_7}`,
    }),
    invoicePriceConditionCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `invoicePriceConditionCode ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceConditionCode ${EMessage.MAX_4}`,
    }),
    invoiceCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `invoiceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceCurrencyCode ${EMessage.MAX_3}`,
    }),
    totalInvoicePrice: Joi.number().messages({
      "number.base": `totalInvoicePrice ${EMessage.NUMBER}`,
    }),
    invoicePriceKindCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `invoicePriceKindCode ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceKindCode ${EMessage.MAX_2}`,
    }),
    notes: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `notes ${EMessage.EMPTYED}`,
    }),
    ///
    valueClearanceVND: Joi.number().required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
    }),
    invoiceNo: Joi.string().max(255).allow(null).allow('').messages({
      "string.empty": `invoiceNo ${EMessage.EMPTYED}`,
      "string.max": `invoiceNo ${EMessage.MAX_255}`,
    }),
    invoiceDate: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `invoiceDate ${EMessage.EMPTYED}`,
      "string.max": `invoiceDate ${EMessage.MAX_10}`,
    }),
  });

  static master = Joi.object({
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    HAWBClearance: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWBClearance ${EMessage.REQUIRED}`,
      "string.empty": `HAWBClearance ${EMessage.EMPTYED}`,
      "string.max": `HAWBClearance ${EMessage.MAX_50}`,
    }),
    MAWB: Joi.string().trim().allow(null).max(50).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    departureDate: Joi.string().max(20).trim().allow(null).messages({
      "string.empty": `departureDate ${EMessage.EMPTYED}`,
      "string.max": `departureDate ${EMessage.MAX_20}`,
    }),
    flightNo: Joi.string().max(50).trim().allow(null).messages({
      "string.empty": `flightCode ${EMessage.EMPTYED}`,
      "string.max": `flightCode ${EMessage.MAX_50}`,
    }),
    hubId: Joi.number().min(0).allow(null).messages({
      "number.base": `hubId ${EMessage.NUMBER}`,
      "number.min": `hubId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    warehouseId: Joi.number().min(0).allow(null).messages({
      "number.base": `warehouseId ${EMessage.NUMBER}`,
      "number.min": `warehouseId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    meanOfTransportationCode: Joi.number().allow(null).messages({
      "number.base": `meanOfTransportationCode ${EMessage.NUMBER}`,
    }),
    // departureCountry: Joi.string().max(4).trim().allow(null).messages({
    //   "string.empty": `departureCountry ${EMessage.EMPTYED}`,
    //   "string.max": `departureCountry ${EMessage.MAX_20}`,
    // }),
  })

  static arrMaster = Joi.array().min(1).items(ExportClearanceValidate.master).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`,
    "array.max": `data ${EMessage.MAX_ITEM_50}`,
  });

  static changeClassify: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `items ${EMessage.REQUIRED}`,
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    classify: Utilities.stringValid(ClassifyValidateName).max(4).trim().messages({
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_4}`,
    }),
  });

  static createDetail: any = Joi.object({
    HSCode: Joi.string().trim().max(50).allow(null).allow('').messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().required().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().allow(null).messages({
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    url: Joi.string().trim().allow(null).messages({
      "string.empty": EMessage.EMPTYED,
    }),
    quantity: Joi.number().integer().min(1).messages({
      "number.base": `quantity ${EMessage.NUMBER}`,
      "number.integer": `quantity ${EMessage.INTEGER}`,
      "number.min": `quantity ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    currencyCode: Joi.string().trim().max(3).messages({
      "string.empty": `currencyCode ${EMessage.EMPTYED}`,
      "string.max": `currencyCode ${EMessage.MAX_3}`,
    }),
    invoiceValue: Joi.number().allow(null).allow('').messages({
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
    }),
    invoiceUnitPrice: Joi.number().allow(null).allow('').messages({
      "number.base": `invoiceUnitPrice ${EMessage.NUMBER}`,
    }),
    weight: Joi.number().min(0).allow(null).allow('').messages({
      "number.base": `weight ${EMessage.NUMBER}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).required().max(3).trim().messages({
      "any.required": `unitOfMass ${EMessage.REQUIRED}`,
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    productId: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `productId ${EMessage.EMPTYED}`,
      "string.max": `productId ${EMessage.MAX_255}`,
    }),
  });

  static importExcel: any = Joi.object({
    items: Joi.array().min(1).items(ExportClearanceValidate.createDetail).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    HAWBClearance: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWBClearance ${EMessage.REQUIRED}`,
      "string.empty": `HAWBClearance ${EMessage.EMPTYED}`,
      "string.max": `HAWBClearance ${EMessage.MAX_50}`,
    }),
    stationId: Joi.number().min(0).greater(0).required().messages({
      "any.required": `stationId ${EMessage.REQUIRED}`,
      "number.base": `stationId ${EMessage.NUMBER}`,
      "number.min": `stationId ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `stationId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    MAWB: Joi.string().trim().max(50).allow(null).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    flightNo: Joi.string().max(50).trim().allow(null).messages({
      "string.empty": `flightCode ${EMessage.EMPTYED}`,
      "string.max": `flightCode ${EMessage.MAX_50}`,
    }),
    departureDate: Joi.string().max(20).allow(null).messages({
      "string.empty": `departureDate ${EMessage.EMPTYED}`,
      "string.max": `departureDate ${EMessage.MAX_20}`,
    }),
    exporterCode: Joi.string().trim().allow(null).messages({
      "string.empty": `exporterCode ${EMessage.EMPTYED}`,
    }),
    exporterName: Joi.string().trim().max(255).messages({
      "string.empty": `exporterName ${EMessage.EMPTYED}`,
      "string.max": `exporterName ${EMessage.MAX_255}`,
    }),
    exporterPostCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `exporterPostCode ${EMessage.EMPTYED}`,
      "string.max": `exporterPostCode ${EMessage.MAX_10}`,
    }),
    exporterAddress: Joi.string().trim().required().messages({
      "any.required": `exporterAddress ${EMessage.REQUIRED}`,
      "string.empty": `exporterAddress ${EMessage.EMPTYED}`,
    }),
    exporterTelephoneNumber: Joi.string().trim().allow(null).messages({
      "string.empty": `exporterTelephoneNumber ${EMessage.EMPTYED}`,
    }),
    consigneeCode: Joi.string().trim().max(255).allow(null).messages({
      "string.empty": `consigneeCode ${EMessage.EMPTYED}`,
      "string.max": `consigneeCode ${EMessage.MAX_255}`,
    }),
    consigneeName: Joi.string().trim().max(255).messages({
      "string.empty": `consigneeName ${EMessage.EMPTYED}`,
      "string.max": `consigneeName ${EMessage.MAX_255}`,
    }),
    consigneeTelephoneNumber: Joi.string().trim().max(30).allow(null).messages({
      "string.empty": `consigneeTelephoneNumber ${EMessage.EMPTYED}`,
      "string.max": `consigneeTelephoneNumber ${EMessage.MAX_30}`,
    }),
    consigneePostCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `consigneePostCode ${EMessage.EMPTYED}`,
      "string.max": `consigneePostCode ${EMessage.MAX_10}`,
    }),
    consigneeAddress: Joi.string().messages({
      "string.empty": `consigneeAddress ${EMessage.EMPTYED}`,
    }),
    consigneeCountryCode: Joi.string().trim().max(2).allow(null).messages({
      "string.empty": `consigneeCountryCode ${EMessage.EMPTYED}`,
      "string.max": `consigneeCountryCode ${EMessage.MAX_2}`,
    }),
    weight: Joi.number().messages({
      "number.base": `weight ${EMessage.NUMBER}`,
    }),
    currencyCode: Joi.string().trim().max(3).required().messages({
      "any.required": `currencyCode ${EMessage.REQUIRED}`,
      "string.empty": `currencyCode ${EMessage.EMPTYED}`,
      "string.max": `currencyCode ${EMessage.MAX_3}`,
    }),
    totalPrice: Joi.number().required().messages({
      "any.required": `totalPrice ${EMessage.REQUIRED}`,
      "number.base": `totalPrice ${EMessage.NUMBER}`,
    }),
    valueClearanceVND: Joi.number().required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
    }),
    originalOrderNumberClient: Joi.string().trim().max(255).allow(null).messages({
      "string.empty": `originalOrderNumberClient ${EMessage.EMPTYED}`,
      "string.max": `originalOrderNumberClient ${EMessage.MAX_255}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).required().max(3).trim().messages({
      "any.required": `unitOfMass ${EMessage.REQUIRED}`,
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    hubId: Joi.number().min(0).allow(null).messages({
      "number.base": `hubId ${EMessage.NUMBER}`,
      "number.min": `hubId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    warehouseId: Joi.number().min(0).allow(null).messages({
      "number.base": `warehouseId ${EMessage.NUMBER}`,
      "number.min": `warehouseId ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    meanOfTransportationCode: Joi.number().allow(null).messages({
      "number.base": `meanOfTransportationCode ${EMessage.NUMBER}`,
    }),
    piece: Joi.number().allow(null).messages({
      "number.base": `piece ${EMessage.NUMBER}`,
    }),
    //
    serviceId: Joi.number().integer().required().min(1).messages({
      "any.required": `serviceId ${EMessage.REQUIRED}`,
      "number.base": `serviceId ${EMessage.NUMBER}`,
      "number.integer": `serviceId ${EMessage.INTEGER}`,
      "number.min": `serviceId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    classify: Utilities.stringValid(ClassifyName).required().trim().max(3).messages({
      "any.required": `classify ${EMessage.REQUIRED}`,
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_3}`,
    }),
    customerBusinessId: Joi.string().trim().allow(null).messages({
      "string.empty": `customerBusinessId ${EMessage.EMPTYED}`,
    }),
    customerPersonalId: Joi.string().trim().allow(null).messages({
      "string.empty": `customerPersonalId ${EMessage.EMPTYED}`,
    }),
    note: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `note ${EMessage.EMPTYED}`,
    }),
    clientId: Joi.number().integer().required().min(1).messages({
      "any.required": `clientId ${EMessage.REQUIRED}`,
      "number.base": `clientId ${EMessage.NUMBER}`,
      "number.integer": `clientId ${EMessage.INTEGER}`,
      "number.min": `clientId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    orderId: Joi.string().trim().allow(null).messages({
      "string.empty": `orderId ${EMessage.EMPTYED}`,
    }),
    originalPrice: Joi.number().allow(null),
    currencyOriginalPrice: Joi.string().trim().max(5).allow(null).messages({
      "string.max": `externalBoxName ${EMessage.MAX_5}`,
    }),
    internalBoxName: Joi.string().trim().max(33).allow(null).messages({
      "string.max": `internalBoxName ${EMessage.MAX_33}`,
    }),
    externalBoxName: Joi.string().trim().max(50).allow(null).messages({
      "string.max": `externalBoxName ${EMessage.MAX_50}`,
    }),
    orderNumber: Joi.string().trim().max(50).allow(null).messages({
      "string.max": `orderNumber ${EMessage.MAX_50}`,
    }),
    childOrderNumber: Joi.string().trim().max(30).allow(null).messages({
      "string.max": `childOrderNumber ${EMessage.MAX_30}`,
    }),
    clearanceCreateLogId: Joi.number().allow(null)
  });

  static holdManifest: any = Joi.object({
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    isHold: Joi.boolean().allow(null).allow('').messages({
      "boolean.base": `isHold ${EMessage.BOOLEAN}`,
    }),
    reasonIds: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `reasonIds ${EMessage.EMPTYED}`,
    }),
  });

  static arrHoldManifest = Joi.object({
    holdData: Joi.array().min(1).items(ExportClearanceValidate.holdManifest).required().messages({
      "any.required": `data ${EMessage.REQUIRED}`,
      "array.base": `data ${EMessage.ARRAY}`,
      "array.min": `data ${EMessage.LEAST_ITEM_1}`,
    }),
    isRemoveMAWB: Joi.boolean().required().messages({
      "any.required": `isRemoveMAWB ${EMessage.REQUIRED}`,
      "boolean.base": `isRemoveMAWB ${EMessage.BOOLEAN}`,
    }),
  });

  static updateCOM: any = Joi.object({
    exportDetails: Joi.array().items(ExportClearanceDetailValidate.updateEDA).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    declarationNoCustomer: Joi.string().max(50).required().messages({
      "any.required": `declarationNoCustomer ${EMessage.REQUIRED}`,
      "string.empty": `declarationNoCustomer ${EMessage.EMPTYED}`,
      "string.max": `declarationNoCustomer ${EMessage.MAX_50}`,
    }),
    meansOfTransportationCode: Joi.number().messages({
      "number.base": `meansOfTransportationCode ${EMessage.NUMBER}`,
    }),
    customsOffice: Joi.string().trim().max(8).allow(null).allow('').messages({
      "string.empty": `customsOffice ${EMessage.EMPTYED}`,
      "string.max": `customsOffice ${EMessage.MAX_8}`,
    }),
    customsSubSection: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `customsSubSection ${EMessage.EMPTYED}`,
      "string.max": `customsSubSection ${EMessage.MAX_4}`,
    }),
    exporterCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `exporterCode ${EMessage.EMPTYED}`,
      "string.max": `exporterCode ${EMessage.MAX_15}`,
    }),
    exporterName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `exporterName ${EMessage.EMPTYED}`,
      "string.max": `exporterName ${EMessage.MAX_255}`,
    }),
    postCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `postCode ${EMessage.EMPTYED}`,
      "string.max": `postCode ${EMessage.MAX_10}`,
    }),
    addressOfExporter: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `addressOfExporter ${EMessage.EMPTYED}`,
    }),
    telephoneNumber: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `telephoneNumber ${EMessage.EMPTYED}`,
      "string.max": `telephoneNumber ${EMessage.MAX_15}`,
    }),
    consigneeCode: Joi.string().trim().max(15).allow(null).allow('').messages({
      "string.empty": `consigneeCode ${EMessage.EMPTYED}`,
      "string.max": `consigneeCode ${EMessage.MAX_15}`,
    }),
    consigneeName: Joi.string().trim().max(255).allow(null).allow('').messages({
      "string.empty": `consigneeName ${EMessage.EMPTYED}`,
      "string.max": `consigneeName ${EMessage.MAX_255}`,
    }),
    consigneeTelephoneNumber: Joi.string().trim().max(30).allow(null).messages({
      "string.empty": `consigneeTelephoneNumber ${EMessage.EMPTYED}`,
      "string.max": `consigneeTelephoneNumber ${EMessage.MAX_30}`,
    }),
    postCodeIdentification: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `postCodeIdentification ${EMessage.EMPTYED}`,
      "string.max": `postCodeIdentification ${EMessage.MAX_10}`,
    }),
    address1: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    address2: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address2 ${EMessage.EMPTYED}`,
    }),
    address3: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address3 ${EMessage.EMPTYED}`,
    }),
    address4: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `address4 ${EMessage.EMPTYED}`,
    }),
    countryCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `countryCode ${EMessage.EMPTYED}`,
      "string.max": `countryCode ${EMessage.MAX_2}`,
    }),
    plannedDeclarantCode: Joi.string().trim().max(5).allow(null).allow('').messages({
      "string.empty": `plannedDeclarantCode ${EMessage.EMPTYED}`,
      "string.max": `plannedDeclarantCode ${EMessage.MAX_5}`,
    }),
    cargoPiece: Joi.number().messages({
      "number.base": `cargoPiece ${EMessage.NUMBER}`,
    }),
    pieceUnitCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `pieceUnitCode ${EMessage.EMPTYED}`,
      "string.max": `pieceUnitCode ${EMessage.MAX_2}`,
    }),
    cargoWeightGross: Joi.number().messages({
      "number.base": `cargoWeightGross ${EMessage.NUMBER}`,
    }),
    weightUnitCodeGross: Joi.string().trim().max(4).allow(null).allow('').messages({
      "string.empty": `weightUnitCodeGross ${EMessage.EMPTYED}`,
      "string.max": `weightUnitCodeGross ${EMessage.MAX_4}`,
    }),
    customsWarehouseCode: Joi.string().trim().max(10).allow(null).allow('').messages({
      "string.empty": `customsWarehouseCode ${EMessage.EMPTYED}`,
      "string.max": `customsWarehouseCode ${EMessage.MAX_10}`,
    }),
    theFinalDestinationCode: Joi.string().trim().max(6).allow(null).allow('').messages({
      "string.empty": `theFinalDestinationCode ${EMessage.EMPTYED}`,
      "string.max": `theFinalDestinationCode ${EMessage.MAX_6}`,
    }),
    theFinalDestinationName: Joi.string().trim().max(6).allow(null).allow('').messages({

      "string.empty": `theFinalDestinationName ${EMessage.EMPTYED}`,
      "string.max": `theFinalDestinationName ${EMessage.MAX_6}`,
    }),
    loadingPortCode: Joi.string().trim().max(6).allow(null).allow('').messages({
      "string.empty": `loadingPortCode ${EMessage.EMPTYED}`,
      "string.max": `loadingPortCode ${EMessage.MAX_6}`,
    }),
    loadingPortName: Joi.string().trim().max(6).allow(null).allow('').messages({
      "string.empty": `loadingPortName ${EMessage.EMPTYED}`,
      "string.max": `loadingPortName ${EMessage.MAX_6}`,
    }),
    loadingPlannedVesselName: Joi.string().max(100).allow(null).allow('').messages({
      "string.empty": `loadingPlannedVesselName ${EMessage.EMPTYED}`,
      "string.max": `loadingPlannedVesselName ${EMessage.MAX_100}`,
    }),
    departurePlannedDate: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `departurePlannedDate ${EMessage.EMPTYED}`,
      "string.max": `departurePlannedDate ${EMessage.MAX_20}`,
    }),
    invoiceClassificationCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `invoiceClassificationCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceClassificationCode ${EMessage.MAX_2}`,
    }),
    termOfPayment: Joi.string().trim().max(7).allow(null).allow('').messages({
      "string.empty": `termOfPayment ${EMessage.EMPTYED}`,
      "string.max": `termOfPayment ${EMessage.MAX_7}`,
    }),
    invoicePriceConditionCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `invoicePriceConditionCode ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceConditionCode ${EMessage.MAX_4}`,
    }),
    invoiceCurrencyCode: Joi.string().trim().max(3).allow(null).allow('').messages({
      "string.empty": `invoiceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceCurrencyCode ${EMessage.MAX_3}`,
    }),
    totalInvoicePrice: Joi.number().messages({
      "number.base": `totalInvoicePrice ${EMessage.NUMBER}`,
    }),
    invoicePriceKindCode: Joi.string().trim().max(2).allow(null).allow('').messages({
      "string.empty": `invoicePriceKindCode ${EMessage.EMPTYED}`,
      "string.max": `invoicePriceKindCode ${EMessage.MAX_2}`,
    }),
    notes: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `notes ${EMessage.EMPTYED}`,
    }),
    declarationKindCode: Joi.string().max(4).allow(null).allow('').messages({
      "string.empty": `declarationKindCode ${EMessage.EMPTYED}`,
      "string.max": `declarationKindCode ${EMessage.MAX_4}`,
    }),
    ///
    valueClearanceVND: Joi.number().required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
    }),
    invoiceNo: Joi.string().max(255).allow(null).allow('').messages({
      "string.empty": `invoiceNo ${EMessage.EMPTYED}`,
      "string.max": `invoiceNo ${EMessage.MAX_255}`,
    }),
    invoiceDate: Joi.string().max(10).allow(null).allow('').messages({
      "string.empty": `invoiceDate ${EMessage.EMPTYED}`,
      "string.max": `invoiceDate ${EMessage.MAX_10}`,
    }),
    dateClearanced: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateClearanced ${EMessage.EMPTYED}`,
      "string.max": `dateClearanced ${EMessage.MAX_20}`,
    }),
    inspectionKindClassification: Joi.number().required().messages({
      "any.required": `inspectionKindClassification ${EMessage.REQUIRED}`,
      "number.base": `inspectionKindClassification ${EMessage.NUMBER}`,
    }),
  });

  static register: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `HAWBs ${EMessage.REQUIRED}`,
      "array.base": `HAWBs ${EMessage.ARRAY}`,
      "array.min": `HAWBs ${EMessage.LEAST_ITEM_1}`,
    }),
    isPrioritize: Joi.boolean().messages({
      "boolean.base": `isPrioritize ${EMessage.BOOLEAN}`,
    }),
  });

  static groupBox: any = Joi.object({
    MAWB: Joi.string().trim().max(50).allow('').allow(null).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    boxes: Joi.array().min(1).items(Joi.string().trim()).unique().allow('').allow(null).messages({
      "any.required": `boxes ${EMessage.REQUIRED}`,
      "array.base": `boxes ${EMessage.ARRAY}`,
      "array.min": `boxes ${EMessage.LEAST_ITEM_1}`,
    }),
    dateCheckout: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateCheckout ${EMessage.EMPTYED}`,
      "string.max": `dateCheckout ${EMessage.MAX_20}`,
    }),
    dateCreate: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateCreate ${EMessage.EMPTYED}`,
      "string.max": `dateCreate ${EMessage.MAX_20}`,
    }),
    classify: Joi.string().max(3).allow(null).allow('').messages({
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_20}`,
    }),
    phase: Joi.number().required().messages({
      "any.required": `phase ${EMessage.REQUIRED}`,
      "number.base": `phase ${EMessage.NUMBER}`,
    }),
    select: Joi.string().trim().required().messages({
      "string.empty": `select ${EMessage.EMPTYED}`,
    }),
  });

  static checkClearanced: any = Joi.object({
    MAWB: Joi.string().trim().max(50).required().messages({
      "any.required": `MAWB ${EMessage.REQUIRED}`,
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
  });

  static checkIn: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).unique().required().messages({
      "any.required": `HAWBs ${EMessage.REQUIRED}`,
      "array.base": `HAWBs ${EMessage.ARRAY}`,
      "array.min": `HAWBs ${EMessage.LEAST_ITEM_1}`,
    }),
    dateCheckin: Joi.string().max(20).allow(null).allow('').messages({
      "string.empty": `dateCheckin ${EMessage.EMPTYED}`,
      "string.max": `dateCheckin ${EMessage.MAX_20}`,
    }),
  });

  static storeWarehouse: any = Joi.object({
    HAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
    address: Joi.string().trim().max(200).required().messages({
      "any.required": `address ${EMessage.REQUIRED}`,
      "string.empty": `address ${EMessage.EMPTYED}`,
      "string.max": `address ${EMessage.MAX_200}`,
    }),
    date: Joi.string().trim().max(10).required().messages({
      "any.required": `date ${EMessage.REQUIRED}`,
      "string.empty": `date ${EMessage.EMPTYED}`,
      "string.max": `date ${EMessage.MAX_10}`,
    }),
  });

  static HAWB: any = Joi.object({
    HAWB: Joi.string().trim().uppercase().required().max(50).messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_50}`,
    }),
  });

  static historyInvoice: any = Joi.object({
    MAWB: Joi.string().trim().max(50).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    phase: Joi.allow('').allow(null),
    printType: Joi.number().required().messages({
      "any.required": `printType ${EMessage.REQUIRED}`,
      "number.base": `printType ${EMessage.NUMBER}`,
    }),
    classify: Joi.string().max(3).allow(null).allow('').messages({
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_20}`,
    }),
    boxes: Joi.array().min(1).items(Joi.string().trim().uppercase()).allow('').allow(null).messages({
      "array.base": `boxes ${EMessage.ARRAY}`,
      "array.min": `boxes ${EMessage.LEAST_ITEM_1}`,
    }),
    note: Joi.string().allow(null).allow(''),
    groupBox: Joi.boolean().allow('').allow(null),
    disLandscape: Joi.boolean().allow(null).allow('').messages({
      "boolean.base": `isLandscape ${EMessage.BOOLEAN}`,
    }),
  });

  static boxCheckSort: any = Joi.object({
    MAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `MAWB ${EMessage.REQUIRED}`,
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    externalBoxName: Joi.string().trim().max(50).allow(null).allow("").messages({
      "string.max": `externalBoxName ${EMessage.MAX_50}`,
    }),
    type: Joi.number().required().messages({
      "any.required": `type ${EMessage.REQUIRED}`,
      "number.base": `type ${EMessage.NUMBER}`,
    }),
  });

  static ibcExport: any = Joi.object({
    codes: Joi.array().min(1).items(Joi.string().trim().max(50)).required().messages({
      "any.required": `codes ${EMessage.REQUIRED}`,
      "array.base": `codes ${EMessage.ARRAY}`,
      "array.min": `codes ${EMessage.LEAST_ITEM_1}`,
    }),
  });
}

export default ExportClearanceValidate;
