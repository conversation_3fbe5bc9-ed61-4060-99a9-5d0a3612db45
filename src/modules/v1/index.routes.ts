'use strict';

import ImportClearanceController from './importClearance/importClearance.controller';
import ImportClearanceDetailController from './importClearanceDetail/importClearanceDetail.controller';

import ExportClearanceController from './exportClearance/exportClearance.controller';
import ExportClearanceDetailController from './exportClearanceDetail/exportClearanceDetail.controller';
import HSCodeDetailController from './hscodeDetail/hscodeDetail.controller';
import XmlClearanceController from './xmlClearance/xmlClearance.controller';
import ActionController from './action/action.controller';
import HSCodeController from './hscode/hscode.controller';
import EnviromentCodeController from './tableEnvironmentCode/tableEnvironmentCode.controller';
import ImportCodeController from './tableImportCode/tableImportCode.controller';
import SpecialConsumptionCodeController from './tableSpecialConsumptionCode/tableSpecialConsumptionCode.controller';
import VatCodeController from './tableVatCode/tableVatCode.controller';
import UnitController from './clearanceUnit/clearanceUnit.controller';
import PackageController from './clearancePackage/clearancePackage.controller';
import InvoiceConditionController from './invoiceCondition/invoiceCondition.controller';
import TerminalController from './terminal/terminal.controller';
import WarehouseController from './warehouse/warehouse.controller';
import TaxFreeCodeController from './tableTaxFreeCode/tableTaxFreeCode.controller';
import LogPrintController from './logPrint/logPrint.controller';
import TaxCodeController from './taxCode/taxCode.controller';
import HoldController from './hold/hold.controller';
import ImportTransactionController from './importTransaction/importTransaction.controller';
import InvoiceNumberController from './invoiceNumber/invoiceNumber.controller';
import AutoV5Controller from './autoV5/autoV5.controller';
import HistoryInvoiceController from './historyInvoice/historyInvoice.controller';
import MasterAnalyzerController from './masterAnalyzer/masterAnalyzer.controller';
import HistoryMonitorController from './historyMonitor/historyMonitor.controller';
import ImportClearanceArchiveController from './importClearanceArchive/importClearance.controller';
import ExportClearanceArchiveController from './exportClearanceArchive/exportClearanceArchive.controller';
import eMICClearanceController from './eImportClearance/eMICClearance.controller';
import eMECClearanceController from './eExportClearance/eMECClearance.controller';
import eMECDetailController from './eExportClearanceDetail/exportClearanceDetail.controller';
import eMICDetailController from './eImportClearanceDetail/eMICClearanceDetail.controller';

class Routes {
  static controllers: Array<any> = [
    new ImportClearanceController(),
    new ImportClearanceDetailController(),

    new ExportClearanceController(),
    new ExportClearanceDetailController(),

    new HSCodeController(),
    new HSCodeDetailController(),

    new XmlClearanceController(),

    new ActionController(),

    new VatCodeController(),
    new ImportCodeController(),
    new SpecialConsumptionCodeController(),
    new EnviromentCodeController(),
    new TaxFreeCodeController(),

    new UnitController(),
    new PackageController(),
    new InvoiceConditionController(),
    new TerminalController(),
    new WarehouseController(),

    new LogPrintController(),
    new TaxCodeController(),
    new HoldController(),
    new ImportTransactionController(),

    new InvoiceNumberController(),

    new AutoV5Controller(),

    new HistoryInvoiceController(),
    new MasterAnalyzerController(),
    new HistoryMonitorController(),
    new ImportClearanceArchiveController(),
    new ExportClearanceArchiveController(),

    new eMICClearanceController(),
    new eMECClearanceController(),

    new eMECDetailController(),
    new eMICDetailController(),
  ];
}

export default Routes;
