'use strict';

import TaxCodeRepository from "./taxCode.reporsitory";
import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';

class TaxCodeService extends BaseService {
  private taxCodeRepository: TaxCodeRepository;
  constructor () {
    super(new TaxCodeRepository());
    this.taxCodeRepository = new TaxCodeRepository();
  }

  public async removeTaxCode(ids: string[]): Promise<any> {
    try {
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, 'code', EConfigure.IN, ids.join())
      ])
      const [total] = await this.taxCodeRepository.destroyData(optional);
      let status: boolean = false;
      if(total > 0) {
        status = true;
      }
      return status;
    } catch (error) {
      throw new Error(error as any);
    }
  }
}

export default TaxCodeService;