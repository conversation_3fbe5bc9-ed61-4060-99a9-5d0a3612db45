'user strict'

import Jo<PERSON> from '@hapi/joi';
import EMessage from '../../../emuns/messages';

class TaxCodeValidate {
  static create: any = Joi.object({
    code: Joi.string().trim().required().max(20).messages({
      "any.required": `code ${EMessage.REQUIRED}`,
      "string.empty": `code ${EMessage.EMPTYED}`,
      "string.max": `code ${EMessage.MAX_20}`,
    }),
    name_vn: Joi.string().trim().messages({
      "string.empty": `name_vn ${EMessage.EMPTYED}`,
    }),
    name: Joi.string().trim().allow(null).messages({
      "string.empty": `name ${EMessage.EMPTYED}`,
    }),
    address: Joi.string().trim().allow(null).messages({
      "string.empty": `addres ${EMessage.EMPTYED}`,
    }),
    
  });

  static arrCreate: any = Joi.array().items(TaxCodeValidate.create).unique().required();
}

export default TaxCodeValidate;
