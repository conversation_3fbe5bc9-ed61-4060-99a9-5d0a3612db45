'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import Middlewares from '../../../middleware/middlewares';
import ParserUrl from '../../../parser/url';
import ExportClearanceService from './exportClearanceArchive.service';
import EConfigure from '../../../emuns/configures';
import Optional from '../../../parser/optional';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import Meta from "../../../https/meta";
import HttpData from "../../../https/data";
import ErrorValidate from "../../../util/error.validate";
import { IResponze } from "../../../https/responze";
import RedisPromise from "../../../util/redis.promise";
import UserService from "../user/user.service";
import Utilities from "../../../util/utilities";
import Where from "../../../parser/where";
import { Clearance } from "../../../xmlClearance/clearance";
import ExportClearanceValidate from "../exportClearance/exportClearance.validate";

class ExportClearanceArchiveController {
  public path = '/export_archives';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get(`${this.path}/mec`, this.getAllMEC);
    this.router.get(`${this.path}/eda`, this.getAllEDA);
    this.router.get(`${this.path}/getAll`, this.getAll);
    this.router.get(`${this.path}/master`, this.master);
    this.router.get(`${this.path}/clearanceReport`, this.clearanceReport);
    this.router.get(`${this.path}/holdManifest`, this.getHoldManifest);

    this.router.get(`${this.path}/exportReport`, this.exportReport);
    this.router.get(`${this.path}/clearanceReportExport`, this.clearanceReportExport);
    this.router.get(`${this.path}/mic/assignCargoName`, this.getMecAssignCargoName);
    this.router.get(`${this.path}/ida/assignCargoName`, this.getEdaAssignCargoName);
    this.router.get(`${this.path}/mec/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneMEC);
    this.router.get(`${this.path}/eda/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneEDA);

    this.router.post(`${this.path}/mec`, this.allMEC);
    this.router.post(`${this.path}/eda`, this.allEDA);
    this.router.post(`${this.path}/filterMEC`, this.filterMEC);
    this.router.post(`${this.path}/filterEDA`, this.filterEDA);
    this.router.post(`${this.path}/retryMEC`, middlewares.CheckPermission('put:v1:clearance.retry'), this.retryMEC);
    this.router.post(`${this.path}/MECGroupBox`, this.MECGroupBox);
    this.router.post(`${this.path}/EDAGroupBox`, this.EDAGroupBox);
  }

  private async EDAGroupBox(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ExportClearanceValidate.groupBox.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      value = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.EDAGroupBox(value, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][EDAGroupBox]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async MECGroupBox(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let { error, value } = ExportClearanceValidate.groupBox.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const exportClearanceService = new ExportClearanceService();
      value = Utilities.convertNull(value);
      const data: IResponze = await exportClearanceService.MECGroupBox(value, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][MECGroupBox]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getHoldManifest(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.getHoldManifest(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async retryMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ExportClearanceValidate.HAWBs.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const message: any = await new Clearance().getHawbData(value['HAWBs'], 'MEC');
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['isSuccess'], message['hawbSuccess'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][retryMEC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async exportReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.exportReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const data: IResponze = await exportClearanceService.getAllMECEDA(optional);
      const meta = new Meta(limit, optional.getOffset(), data['message']['data']['total'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK,  data['message']['data']['manifests'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][getAllMECEDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getEdaAssignCargoName(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const [data, total] = await exportClearanceService.getEdaAssignCargoName(optional, employeeId);
      const meta = new Meta(limit, optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK,  data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][getEdaAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMecAssignCargoName(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const exportClearanceService = new ExportClearanceService();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const [data, total] = await exportClearanceService.getMecAssignCargoName(optional, employeeId);
      const meta = new Meta(limit, optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK,  data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][getMecAssignCargoName]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReportExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.clearanceReportExport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async clearanceReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.clearanceReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound]][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async master(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.master(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filterEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.filterEDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), data['message']['data']['totalManifest'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][filer_ida]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async filterMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.filterMEC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), data['message']['data']['totalManifest'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data['message']['data'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][filer_mic]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAllMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllMEC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][get_all_MIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAllEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllEDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][get_all_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: any = await exportClearanceService.getOneMEC(HAWB, optional);
      let status: boolean = false;
      if(data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][get_one_MEC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: any = await exportClearanceService.getOneEDA(HAWB, optional);
      let status: boolean = false;
      if(data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][get_one_EDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async allMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.body);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllMEC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][all_MEC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async allEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllEDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][all_EDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default ExportClearanceArchiveController;