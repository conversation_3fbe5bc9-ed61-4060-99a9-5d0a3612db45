'use strict';

import MECClearanceRepository from "./MECClearanceArchive.reporsitory";
import EDAClearanceRepository from "../exportClearance/EDAClearance.reporsitory";
import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import { Database } from '../../../database/index';
import { ClassifyName, ClassifyKey, ClassifyValidateName, ClassifyValidateKey } from "../../../emuns/classifyExport";
import { ActionKey } from "../../../emuns/action";
import { IResponze } from "../../../https/responze";
import { WeightName, WeightKey, WeightClearance } from "../../../emuns/weight";
import ExportDetailRepository from "../exportClearanceDetail/exportClearanceDetail.reporsitory";
import { IMEC, IMECCreate, ICountry, IWarehouse, IExportValidate, IExportDetailValidate, IExportDetailCreate, IEDACreate, IEDA, EDA, IMECMaster, IEDAMaster, IHSCodeDetail, IExportDetail, IMasterValidate, Country, ExportDetail, MEC, TaxCode, User, CustomerBusiness, CustomerPersonal, Hold } from "../../../models/index.model";
import Utilities from "../../../util/utilities";
import { Clearance } from "../../../xmlClearance/clearance";
import { ClearanceTypeId } from "../../../xmlClearance/enum";
import moment from "moment";
import HSCodeDetailRepository from "../hscodeDetail/hscodeDetail.reporsitory";
import HSCodeRepository from "../hscode/hscode.reporsitory";
import TaxCodeRepository from "../taxCode/taxCode.reporsitory";
import OrderBy from "../../../parser/orderBy";
import { IHoldTransaction, IReturnCargo } from "../../../models/importTransaction.model";
import UserRepository from "../user/user.reporsitory";

interface IHold {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IExportDetail[];
  reasonDetails?: Hold[];
}

interface IClearanceCreateLog {
  clearanceCreateLogId?: number;
  data?: any;
  message?: string;
  cause?: any;
  processResult?: any;
  handleData?: any;
  type?: number;
  classify?: any;
  isSuccess?: boolean;
}

interface ICheckout {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  details?: IDetail[];
}

interface IDetail {
  itemNameVN: string;
  quantity: number;
}

interface IExportReport {
  expDate: string;
  trackingNo: string;
  name: string;
  MAWB: string;
  country: string;
  pcs: number;
  grossWeight: number;
  puDate: string;
  cdsDate: string;
  crDate: string;
  confirmUpliftDate: string;
  cdsNo: string;
  importType: any;
  lane: number;
  customsStatus: string;
  customsClearance: any;
  handling: any;
  shipmemntValue: any;
  dutyTaxVND: number;
  otherFee: number;
  notes: string;
  type: string;
  partner: string;
}

interface IManagement {
  HAWB?: string;
  MAWB?: string;
  declarationType?: string;
  declarationNo?: string;
  clearanceInspection?: string;
  dateCheckin?: string;
  dateCheckout?: string;
  dateClearanced?: string;
  cargoPiece?: number;
  country?: Country;
  weight?: number;
  classify?: string;
  bussiness?: CustomerBusiness;
  personal?: CustomerPersonal;
  exportDetails?: ExportDetail[];
  isHold?: boolean;
  reasonHold?: Hold[];
}

interface IGetAll {
  HAWBClearance?: string;
  HAWB?: string;
  declarationNo?: string;
  MAWB?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  priceVND?: number;
  country?: Country;
  cargoPiece?: number;
  weight?: number;
  massOfWeight?: string;
  inspectionKindClassification?: any;
  phase?: any;
  classify?: string;
  type?: string;
  details?: IExportDetail[];
  inspectionKindTimes?: any;
  clearanceDeclarationTimes?: any;
  reasonDetails?: Hold[];
  isHold?: any;
}

interface IAccountExportReport {
  trackingNo: any;
  MAWB: any;
  origin: any;
  country: any;
  pcs: number;
  trueWeight: number;
  volWeight: any;
  manisfestImported: any;
  arDate: any;
  cdsDate: any;
  crDate: any;
  cdsNo: any;
  importType: any;
  lane: any;
  customsStatus: any;
  reasonDate: any;
  handover: any;
  shipmemntValueUSD: number;
  importTax: number;
  VAT: number;
  duty: number;
  dutyTaxVND: number;
  otherFee: number;
  notes: any;
  consignorName: any
  consignorAddress: any;
  consignorCityName: any;
  consignorContactName: any;
  consignorTelephone: any;
  consigneeName: any;
  consigneeAddress: any;
  consigneeAddress2: any;
  consigneeCityName: any;
  consigneeContactName: any;
  consigneeTelephone: any;
  remarks: any;
  status?: any;
  statusId: any;
  cdsNoCustomer?: any;
  currency?: any;
  specialConsumptionTax?: any;
  environmentTax?: any;
  totalTax?: any;
  priceVND?: any;
  customerBusiness?: any;
  customerPersonal?: any;
  dateClearanced?: any;
  orderNumber?: any;
  itemIds?: any;
  itemCount?: any;
  station?: any;
  exporterCode?: any;
  warehouseCheckin?: any;
  warehouseCheckout?: any;

}


class ExportClearanceService extends BaseService {
  private mecRepository: MECClearanceRepository;
  private edaRepository: EDAClearanceRepository;
  private taxCodeRepository: TaxCodeRepository;
  private exportDetailRepository: ExportDetailRepository;
  private hscodeDetailRepository: HSCodeDetailRepository;
  private hscodeRepository: HSCodeRepository;
  private _sequelize: any;
  constructor() {
    super(new MECClearanceRepository());
    this.mecRepository = new MECClearanceRepository();
    this.edaRepository = new EDAClearanceRepository();
    this.exportDetailRepository = new ExportDetailRepository();
    this.hscodeDetailRepository = new HSCodeDetailRepository();
    this.taxCodeRepository = new TaxCodeRepository();
    this.hscodeRepository = new HSCodeRepository();
    this._sequelize = Database.Sequelize;
  }

  public async getHoldManifest(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'manifests': []
          }
        }
      }
      optional.setRelation(['country', 'exportDetailItems', 'holds']);
      const EDAQuery = this.edaRepository.queryAll(optional);
      const MECQuery = this.mecRepository.queryAll(optional);

      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);


      if (MECs.length > 0 || EDAs.length > 0) {
        let mecData: IManagement[] = [];
        let edaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: IEDA) => {
            const eda: IHold = {
              "HAWBClearance": EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": EDA[EConfigure.HAWB_FIELD],
              "declarationNo": EDA['declarationNo'],
              "MAWB": EDA['MAWB'],
              "invoiceValue": EDA['totalInvoicePrice'],
              "invoiceCurrency": EDA['invoiceCurrencyCode'],
              "priceVND": EDA['priceVND'],
              "country": EDA['country'],
              "cargoPiece": EDA['cargoPiece'],
              "weight": EDA['cargoWeightGross'],
              "massOfWeight": EDA['weightUnitCodeGross'],
              "inspectionKindClassification": EDA['inspectionKindClassification'],
              "phase": EDA['phase_name'],
              "classify": EDA['classify'],
              "type": String(EConfigure.EDA),
              "details": EDA['exportDetailItems'],
              "reasonDetails": EDA['holds']
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          mecData = MECs.map((MEC: IMEC) => {
            const mec: IHold = {
              "HAWBClearance": MEC[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": MEC[EConfigure.HAWB_FIELD],
              "declarationNo": MEC['declarationNo'],
              "MAWB": MEC['MAWB'],
              "invoiceValue": MEC['totalOfTaxValue'],
              "invoiceCurrency": MEC['currencyCodeOfTaxValue'],
              "priceVND": MEC['priceVND'],
              "country": MEC['country'],
              "cargoPiece": MEC['cargoPiece'],
              "weight": MEC['cargoWeight'],
              "massOfWeight": WeightClearance.get('kg'),
              "inspectionKindClassification": MEC['inspectionKindClassification'],
              "phase": MEC['phase_name'],
              "classify": MEC['classify'],
              "type": String(EConfigure.MEC),
              "details": MEC['exportDetailItems'],
              "reasonDetails": MEC['holds']
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': manifests.length
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getHoldManifest]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneMEC(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.mecRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getOneMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getOneEDA(HAWB: string, optional: Optional): Promise<any> {
    try {
      optional.getWhere().push(new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.EQUAL, HAWB));
      const objData = await this.edaRepository.getOneOptional(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getOneEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMEC(optional: Optional): Promise<any> {
    try {
      const objData = await this.mecRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getAllMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllEDA(optional: Optional): Promise<any> {
    try {
      const objData = await this.edaRepository.getAll(optional);
      return objData;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getAllEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filterMEC(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      // mec
      const MECQuery = this.mecRepository.getAll(optional);
      const MECTotalCreateQuery = this.mecRepository.countData(optionalCreate);
      const MECTotalUpdateCarageNameQuery = this.mecRepository.countData(optionalUpdateCarageName);
      const MECTotalSendClearanceQuery = this.mecRepository.countData(optionalSendClearance);
      const MECTotalEditClearanceQuery = this.mecRepository.countData(optionalEditClearance);
      const MECTotalInspectionKindQuery = this.mecRepository.countData(optionalInspectionKind);
      const MECTotalAcceptClearanceQuery = this.mecRepository.countData(optionalAcceptClearance);
      const MECErrorQuery = this.mecRepository.countData(optionalError);

      const [[MECs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError] =
        await Promise.all([MECQuery, MECTotalCreateQuery, MECTotalUpdateCarageNameQuery, MECTotalSendClearanceQuery, MECTotalEditClearanceQuery, MECTotalInspectionKindQuery,
          MECTotalAcceptClearanceQuery, MECErrorQuery]);
      if (MECs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': MECs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][filterMEC]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async filterEDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
            'totalCreate': 0,
            'totalUpdateCarageName': 0,
            'totalRegisterTemp': 0,
            'totalSentTemp': 0,
            'totalSendClearance': 0,
            'totalEditClearance': 0,
            'totalInspectionKind': 0,
            'totalAcceptClearance': 0,
            'totalError': 0,
          }
        }
      }
      const optionalCreate: Optional = new Optional();
      optionalCreate.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.CREATE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalUpdateCarageName: Optional = new Optional();
      optionalUpdateCarageName.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.UPDATE_CARGO_NAME),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalRegisterTemp: Optional = new Optional();
      optionalRegisterTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSentTemp: Optional = new Optional();
      optionalSentTemp.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SENT_INFOMATION),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalSendClearance: Optional = new Optional();
      optionalSendClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalEditClearance: Optional = new Optional();
      optionalEditClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalInspectionKind: Optional = new Optional();
      optionalInspectionKind.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalAcceptClearance: Optional = new Optional();
      optionalAcceptClearance.setWhere([
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);

      const optionalError: Optional = new Optional();
      optionalError.setWhere([
        new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      // eda
      const EDAQuery = this.edaRepository.getAll(optional);
      const EDATotalCreateQuery = this.edaRepository.countData(optionalCreate);
      const EDATotalUpdateCarageNameQuery = this.edaRepository.countData(optionalUpdateCarageName);
      const EDATotalRegisterTempQuery = this.edaRepository.countData(optionalRegisterTemp);
      const EDATotalSentTempQuery = this.edaRepository.countData(optionalSentTemp);
      const EDATotalSendClearanceQuery = this.edaRepository.countData(optionalSendClearance);
      const EDATotalEditClearanceQuery = this.edaRepository.countData(optionalEditClearance);
      const EDATotalInspectionKindQuery = this.edaRepository.countData(optionalInspectionKind);
      const EDATotalAcceptClearanceQuery = this.edaRepository.countData(optionalAcceptClearance);
      const EDAErrorQuery = this.edaRepository.countData(optionalError);

      const [[EDAs, total], totalCreate, totalUpdateCarageName, totalSendClearance, totalEditClearance, totalInspectionKind, totalAcceptClearance, totalError, registerTemp, sentTemp] =
        await Promise.all([EDAQuery, EDATotalCreateQuery, EDATotalUpdateCarageNameQuery, EDATotalSendClearanceQuery, EDATotalEditClearanceQuery, EDATotalInspectionKindQuery,
          EDATotalAcceptClearanceQuery, EDAErrorQuery, EDATotalRegisterTempQuery, EDATotalSentTempQuery]);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': total,
          'manifests': EDAs,
          'totalCreate': totalCreate,
          'totalUpdateCarageName': totalUpdateCarageName,
          'totalRegisterTemp': registerTemp,
          'totalSentTemp': sentTemp,
          'totalSendClearance': totalSendClearance,
          'totalEditClearance': totalEditClearance,
          'totalInspectionKind': totalInspectionKind,
          'totalAcceptClearance': totalAcceptClearance,
          'totalError': totalError,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][filterEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async master(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'MAWB': [],
          }
        }
      }
      const masterOptional: Optional = new Optional();
      masterOptional.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'MAWB', EConfigure.NOT, null),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      masterOptional.setAttributes(['MAWB']);
      masterOptional.setGroup(['MAWB', 'createdAt']);
      masterOptional.setOrderby([
        new OrderBy('createdAt', EConfigure.DESCENDING)
      ]);
      // ida
      const EDAQuery = this.edaRepository.queryAll(masterOptional);
      // mic
      const MECQuery = this.mecRepository.queryAll(masterOptional);
      let masters: string[] = [];
      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (EDAs.length > 0 || MECs.length > 0) {
        masters = [...EDAs, ...MECs];
        masters = [...new Set(masters.map((item: any) => item['MAWB']))];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'MAWB': masters
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][master]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': {
            'totalManifest': 0,
            'manifests': [],
          }
        }
      }

      const optionalTotal: Optional = new Optional();
      optionalTotal.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ])

      // eda
      const EDAQuery = this.edaRepository.queryAll(optionalTotal);

      // mec
      const MECQuery = this.mecRepository.queryAll(optionalTotal);

      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (EDAs.length > 0 || MECs.length > 0) {
        let mecData: ICheckout[] = [];
        let edaData: ICheckout[] = [];
        let manifests: ICheckout[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: IEDA) => {
            const eda: ICheckout = {
              'HAWB': EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': EDA['MAWB'],
              'declarationNo': EDA['declarationNo'],
              'clearanceInspection': EDA['inspectionKindClassification'],
              'dateCheckout': EDA['dateCheckout'],
              'dateCheckin': EDA['dateCheckin'],
              'dateClearanced': EDA['dateClearanced'],
              'cargoPiece': EDA['cargoPiece'],
              'weight': EDA['cargoWeightGross'],
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          mecData = MECs.map((MEC: IMEC) => {
            const mec: ICheckout = {
              'HAWB': MEC[EConfigure.HAWB_CLEARANCE_FIELD],
              'MAWB': MEC['MAWB'],
              'declarationNo': MEC['declarationNo'],
              'clearanceInspection': String(MEC['inspectionKindClassification']),
              'dateCheckout': MEC['dateCheckout'],
              'dateCheckin': MEC['dateCheckin'],
              'dateClearanced': MEC['dateClearanced'],
              'cargoPiece': MEC['cargoPiece'],
              'weight': MEC['cargoWeight'],
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'totalManifest': manifests.length,
          'manifests': manifests,
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][clearanceReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async clearanceReportExport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }

      const optionalQuery: Optional = new Optional();
      optionalQuery.setWhere([
        ...optional.getWhere(),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      optionalQuery.setRelation(['exportDetails', 'customerPersonal', 'customerBusiness']);

      // eda
      const EDAQuery = this.edaRepository.queryAll(optionalQuery);
      // mec
      const MECQuery = this.mecRepository.queryAll(optionalQuery);

      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (EDAs.length > 0 || MECs.length > 0) {
        let manifests: any[] = [];
        const customReleased: string = 'custom released';
        if (EDAs.length > 0) {
          EDAs.forEach((EDA: IEDA) => {
            const importTax: number = EDA['totalExportTax'] ? Number(EDA['totalExportTax']) : 0;
            const VATTax: number = EDA['totalVATTax'] ? Number(EDA['totalVATTax']) : 0;

            let itemname: string[] = [];
            let partner: any = null;
            if (EDA['exportDetails'].length > 0) {
              itemname = EDA['exportDetails'].map((exportDetail: ExportDetail) => {
                return String(exportDetail['itemNameVN']);
              });
            }
            if (EDA['customerBusiness']) {
              partner = EDA['customerBusiness']['customerShortName'];
            }
            if (EDA['customerPersonal']) {
              partner = EDA['customerPersonal']['customerLastName'];
            }
            const eda: IExportReport = {
              'expDate': moment(String(EDA['departurePlannedDate'])).format(EConfigure.EXPORT_DATE),
              'trackingNo': String(EDA[EConfigure.HAWB_CLEARANCE_FIELD]),
              'name': itemname.join(','),
              'MAWB': String(EDA['MAWB']),
              'country': String(EDA['countryCode']),
              'pcs': Number(EDA['cargoPiece']),
              'grossWeight': Number(EDA['cargoWeightGross']),
              'puDate': moment(String(EDA['createdAt'])).format(EConfigure.EXPORT_DATE),
              'cdsDate': EDA['declarationPlannedDate'] ? moment(String(EDA['declarationPlannedDate'])).format(EConfigure.EXPORT_DATE) : '',
              'crDate': EDA['dateCheckout'] ? moment(String(EDA['dateCheckout'])).format(EConfigure.EXPORT_DATE) : '',
              'confirmUpliftDate': moment(String(EDA['departurePlannedDate'])).format(EConfigure.EXPORT_DATE),
              'cdsNo': String(EDA['declarationNo']),
              'importType': null,
              'lane': Number(EDA['inspectionKindClassification']),
              'customsStatus': customReleased,
              'customsClearance': null,
              'handling': null,
              'shipmemntValue': Number(EDA['totalOfTaxValue']),
              'dutyTaxVND': importTax + VATTax,
              'otherFee': Number(EDA['totalEnvironmentTax']) + Number(EDA['totalSpecialConsumptionTax']),
              'notes': 'Bảng kê EDA',
              'type': 'EDA',
              'partner': partner
            }
            manifests.push(eda);
          });
        }
        if (MECs.length > 0) {
          MECs.forEach((MEC: IMEC) => {
            let itemname: string[] = [];
            let partner: any = null;
            if (MEC['exportDetails'].length > 0) {
              itemname = MEC['exportDetails'].map((exportDetail: ExportDetail) => {
                return String(exportDetail['itemNameVN']);
              });
            }
            if (MEC['customerBusiness']) {
              partner = MEC['customerBusiness']['customerShortName'];
            }
            if (MEC['customerPersonal']) {
              partner = MEC['customerPersonal']['customerLastName'];
            }
            const mec: IExportReport = {
              'expDate': moment(String(MEC['departureDate'])).format(EConfigure.EXPORT_DATE),
              'trackingNo': String(MEC[EConfigure.HAWB_CLEARANCE_FIELD]),
              'name': itemname.join(','),
              'MAWB': String(MEC['MAWB']),
              'country': String(MEC['countryCode']),
              'pcs': Number(MEC['cargoPiece']),
              'grossWeight': Number(MEC['cargoWeight']),
              'puDate': moment(String(MEC['createdAt'])).format(EConfigure.EXPORT_DATE),
              'cdsDate': MEC['declarationPlannedDate'] ? moment(String(MEC['declarationPlannedDate'])).format(EConfigure.EXPORT_DATE) : '',
              'crDate': MEC['dateCheckout'] ? moment(String(MEC['dateCheckout'])).format(EConfigure.EXPORT_DATE) : '',
              'confirmUpliftDate': moment(String(MEC['departureDate'])).format(EConfigure.EXPORT_DATE),
              'cdsNo': String(MEC['declarationNo']),
              'importType': null,
              'lane': Number(MEC['inspectionKindClassification']),
              'customsStatus': customReleased,
              'customsClearance': null,
              'handling': null,
              'shipmemntValue': Number(MEC['totalOfTaxValue']),
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': 'Bảng kê MEC',
              'type': 'MEC',
              'partner': partner
            }
            manifests.push(mec);
          });
        }
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][clearanceReportExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getMecAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.mecRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getMecAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getEdaAssignCargoName(optional: Optional, employeeId: number): Promise<any> {
    try {
      const userRepository: UserRepository = new UserRepository();
      const userOptional: Optional = new Optional();
      userOptional.setAttributes(['isManagementUpdateCargoName']);
      userOptional.setWhere([
        new Where(EConfigure.AND, 'employeeId', EConfigure.EQUAL, employeeId)
      ]);
      const userManament: User = await userRepository.getOneOptional(userOptional);
      optional.getWhere().push(
        new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ASSIGN_UPDATE_CARGO_NAME)
      )
      if (userManament && userManament['isManagementUpdateCargoName'] === false) {
        optional.getWhere().push(
          new Where(EConfigure.AND, 'employeeUpdateCargoName', EConfigure.EQUAL, employeeId)
        )
      }
      const data: any = this.edaRepository.getAll(optional);
      return data;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getEdaAssignCargoName]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async getAllMECEDA(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': [{
            'manifests': [],
            'total': 0,
          }]
        }
      }
      const limit: number = optional.getLimit();
      let half: number = 0;
      if (limit > 0) {
        half = Number(limit / 2);
        optional.setLimit(half);
      }
      optional.setRelation(['country', 'exportDetailItems']);
      const IDAQuery = this.edaRepository.getAll(optional);
      const MICQuery = this.mecRepository.getAll(optional);

      let [[EDAs, totalEDA], [MECs, totalMEC]] = await Promise.all([IDAQuery, MICQuery]);

      if (MECs.length > 0 || EDAs.length > 0) {
        let mecData: IManagement[] = [];
        let edaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (EDAs.length > 0) {
          if (half != totalMEC) {
            const remain: number = half + (half - totalMEC);
            optional.setLimit(remain);
            const addIDAs: IEDA[] = await this.edaRepository.queryAllPaging(optional);
            if (addIDAs.length > 0) {
              EDAs = addIDAs;
            }
          }
          edaData = EDAs.map((EDA: EDA) => {
            const eda: IGetAll = {
              "HAWBClearance": EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": EDA[EConfigure.HAWB_FIELD],
              "declarationNo": EDA['declarationNo'],
              "MAWB": EDA['MAWB'],
              "invoiceValue": EDA['totalInvoicePrice'],
              "invoiceCurrency": EDA['invoiceCurrencyCode'],
              "priceVND": EDA['priceVND'],
              "country": EDA['country'],
              "cargoPiece": EDA['cargoPiece'],
              "weight": EDA['cargoWeightGross'],
              "massOfWeight": EDA['weightUnitCodeGross'],
              "inspectionKindClassification": EDA['inspectionKindClassification'],
              "phase": EDA['phase_name'],
              "classify": EDA['classify'],
              "type": String(EConfigure.EDA),
              "details": EDA['exportDetailItems'],
              "reasonDetails": [],
              "inspectionKindTimes": EDA['inspectionKindTimes'],
              "clearanceDeclarationTimes": EDA['clearanceDeclarationTimes'],
              "isHold": EDA['isHold'],
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          if (half != totalEDA) {
            const remain: number = half + (half - totalEDA);
            optional.setLimit(remain);
            const addMECs: IMEC[] = await this.mecRepository.queryAllPaging(optional);
            if (addMECs.length > 0) {
              MECs = addMECs;
            }
          }
          mecData = MECs.map((MIC: MEC) => {
            const mec: IGetAll = {
              "HAWBClearance": MIC[EConfigure.HAWB_CLEARANCE_FIELD],
              "HAWB": MIC[EConfigure.HAWB_FIELD],
              "declarationNo": MIC['declarationNo'],
              "MAWB": MIC['MAWB'],
              "invoiceValue": MIC['totalOfTaxValue'],
              "invoiceCurrency": MIC['currencyCodeOfTaxValue'],
              "priceVND": MIC['priceVND'],
              "country": MIC['country'],
              "cargoPiece": MIC['cargoPiece'],
              "weight": MIC['cargoWeight'],
              "massOfWeight": WeightClearance.get('kg'),
              "inspectionKindClassification": MIC['inspectionKindClassification'],
              "phase": MIC['phase_name'],
              "classify": MIC['classify'],
              "type": String(EConfigure.MEC),
              "details": MIC['exportDetailItems'],
              "reasonDetails": [],
              "inspectionKindTimes": MIC['inspectionKindTimes'],
              "clearanceDeclarationTimes": MIC['clearanceDeclarationTimes'],
              "isHold": MIC['isHold'],
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = {
          'manifests': manifests,
          'total': totalEDA + totalMEC
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][getAllMECEDA]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async exportReport(optional: Optional): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': []
        }
      }
      optional.setRelation(['manifest', 'exportDetailItems', 'station', 'customerBusiness', 'customerPersonal']);
      const EDAQuery = this.edaRepository.queryAll(optional);
      const MECQuery = this.mecRepository.queryAll(optional);
      const [EDAs, MECs] = await Promise.all([EDAQuery, MECQuery]);
      if (MECs.length > 0 || EDAs.length > 0) {
        let mecData: IManagement[] = [];
        let edaData: IManagement[] = [];
        let manifests: IManagement[] = [];
        if (EDAs.length > 0) {
          edaData = EDAs.map((EDA: EDA) => {
            const importTax: number = EDA['totalExportTax'] ? Number(EDA['totalExportTax']) : 0;
            const VATTax: number = EDA['totalVATTax'] ? Number(EDA['totalVATTax']) : 0;
            const fee: number = 0;
            let productId: any = [];
            let details: ExportDetail[] = EDA['exportDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ExportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (EDA['manifest']) {
              orderNumber = EDA['manifest']['orderNumber']
              if (EDA['manifest']['originalOrderNumberClient']) {
                orderNumber = EDA['manifest']['originalOrderNumberClient']
              }
            }

            const eda: IAccountExportReport = {
              'MAWB': EDA['MAWB'],
              'trackingNo': EDA[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': EDA['declarationNo'],
              'lane': EDA['inspectionKindClassification'] ? String(EDA['inspectionKindClassification']).substring(0, 1) : null,
              'origin': EDA['countryCode'] ? `${EDA['countryCode']}${EConfigure.G}` : null,
              'country': EDA['countryCode'],
              'pcs': Number(EDA['cargoPiece']),
              'trueWeight': Number(EDA['cargoWeightGross']),
              'volWeight': Number(EDA['cargoWeightGross']),
              'manisfestImported': EDA['createdAt'],
              'arDate': EDA['dateCheckin'],
              'cdsDate': EDA['declarationPlannedDate'],
              'crDate': EDA['dateCheckout'],
              'importType': EDA['isHold'] ? EConfigure.HOLD : (EDA['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.H11 : EConfigure.COM),
              'customsStatus': null,
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(EDA['totalInvoicePrice']),
              'importTax': importTax,
              'VAT': VATTax,
              'specialConsumptionTax': EDA['totalSpecialConsumptionTax'],
              'environmentTax': EDA['totalEnvironmentTax'],
              'duty': fee,
              'priceVND': EDA['priceVND'],
              'totalTax': EDA['totalTax'],
              'dutyTaxVND': importTax + VATTax + fee,
              'otherFee': Number(EDA['totalEnvironmentTax']) + Number(EDA['totalSpecialConsumptionTax']),
              'notes': EConfigure._H21,
              'consignorName': EDA['exporterFullName'],
              'consignorAddress': EDA['addressOfExporter'],
              'consignorCityName': null,
              'consignorContactName': EDA['exporterFullName'],
              'consignorTelephone': EDA['telephoneNumber'],
              'consigneeName': EDA['consigneeName'],
              'consigneeAddress': `${EDA['address1']}${EDA['address2'] ? EDA['address2'] : ''}${EDA['address3'] ? EDA['address3'] : ''}`,
              'consigneeAddress2': `${EDA['address4'] ? EDA['address4'] : null}`,
              'consigneeCityName': null,
              'consigneeContactName': EDA['consigneeName'],
              'consigneeTelephone': EDA['consigneeTelephoneNumber'],
              'remarks': null,
              'status': EDA['phase_name']['vi'],
              'cdsNoCustomer': null,
              'currency': EDA['invoiceCurrencyCode'],
              'customerBusiness': EDA['customerBusiness'],
              'customerPersonal': EDA['customerPersonal'],
              'dateClearanced': EDA['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': EDA['station'] ? EDA['station'] : null,
              'exporterCode': EDA['exporterCode'],
              'statusId': EDA['phase'],
              'warehouseCheckin': null,
              'warehouseCheckout': null
            }
            return eda;
          });
        }
        if (MECs.length > 0) {
          mecData = MECs.map((MEC: MEC) => {
            let productId: any = [];
            let details: ExportDetail[] = MEC['exportDetailItems'] || [];
            const totalItems: number = details.length;
            if (totalItems > 0) {
              details.forEach((element: ExportDetail) => {
                if (element['productId']) {
                  productId.push(element['productId']);
                }
              })
            }
            let orderNumber: any = null;
            if (MEC['manifest']) {
              orderNumber = MEC['manifest']['orderNumber']
              if (MEC['manifest']['originalOrderNumberClient']) {
                orderNumber = MEC['manifest']['originalOrderNumberClient']
              }
            }
            const mec: IAccountExportReport = {
              'MAWB': MEC['MAWB'],
              'trackingNo': MEC[EConfigure.HAWB_CLEARANCE_FIELD],
              'cdsNo': MEC['declarationNo'],
              'cdsNoCustomer': null,
              'lane': MEC['inspectionKindClassification'] ? String(MEC['inspectionKindClassification']).substring(0, 1) : null,
              'origin': MEC['countryCode'] ? `${MEC['countryCode']}${EConfigure.G}` : null,
              'country': MEC['countryCode'],
              'pcs': Number(MEC['cargoPiece']),
              'trueWeight': Number(MEC['cargoWeight']),
              'volWeight': Number(MEC['cargoWeight']),
              'manisfestImported': MEC['createdAt'],
              'arDate': MEC['dateCheckin'],
              'cdsDate': MEC['declarationPlannedDate'],
              'crDate': MEC['dateCheckout'],
              'importType': MEC['isHold'] ? EConfigure.HOLD : (MEC['classify'] === ClassifyName.get(ClassifyKey.PAR) ? EConfigure.MEC : MEC['classify']),
              'customsStatus': null,
              'reasonDate': null,
              'handover': null,
              'shipmemntValueUSD': Number(MEC['totalOfTaxValue']),
              'importTax': 0,
              'VAT': 0,
              'specialConsumptionTax': 0,
              'environmentTax': 0,
              'duty': 0,
              'priceVND': MEC['priceVND'],
              'totalTax': 0,
              'dutyTaxVND': 0,
              'otherFee': 0,
              'notes': EConfigure._MEC,
              'consignorName': MEC['exporterFullName'],
              'consignorAddress': MEC['addressOfExporter'],
              'consignorCityName': null,
              'consignorContactName': MEC['exporterFullName'],
              'consignorTelephone': null,
              'consigneeName': MEC['consigneeName'],
              'consigneeAddress': `${MEC['address1']}${MEC['address2'] ? MEC['address2'] : ''}${MEC['address3'] ? MEC['address3'] : ''}`,
              'consigneeAddress2': `${MEC['address4'] ? MEC['address4'] : null}`,
              'consigneeCityName': null,
              'consigneeContactName': MEC['consigneeName'],
              'consigneeTelephone': MEC['consigneeTelephoneNumber'],
              'remarks': null,
              'status': MEC['phase_name']['vi'],
              'currency': MEC['currencyCodeOfTaxValue'],
              'customerBusiness': MEC['customerBusiness'],
              'customerPersonal': MEC['customerPersonal'],
              'dateClearanced': MEC['dateClearanced'],
              'orderNumber': orderNumber,
              'itemIds': productId.join(','),
              'itemCount': totalItems,
              'station': MEC['station'] ? MEC['station'] : null,
              'exporterCode': MEC['exporterCode'],
              'statusId': MEC['phase'],
              'warehouseCheckin': null,
              'warehouseCheckout': null
            }
            return mec;
          });
        }
        manifests = [...mecData, ...edaData];
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = manifests;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][export][exportReport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async MECGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      const exportOptional: Optional = new Optional();
      exportOptional.setRelation(["exportDetailItems"]);
      exportOptional.setAttributes(value['select'].split(','));
      exportOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);
      if (value['classify']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['dateCheckout']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }

      const MECs = await this.mecRepository.queryAll(exportOptional);
      const obj: any = {};
      if (MECs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { 'none': MECs };
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][MECGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }


  public async EDAGroupBox(value: any, hubs: any): Promise<any> {
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null
        }
      }

      const exportOptional: Optional = new Optional();
      exportOptional.setRelation(["exportDetailItems"]);
      exportOptional.setAttributes(value['select'].split(','));
      exportOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase']),
      ]);
      if (value['classify']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['MAWB']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['dateCheckout']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        exportOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      const EDAs = await this.edaRepository.queryAll(exportOptional);
      if (EDAs.length > 0) {
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.FOUND;
        reponze['message']['data'] = { 'none': EDAs };
      }

      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][import][EDAGroupBox]', (error as Error).message);
      throw new Error(error as any);
    }
  }
}

export default ExportClearanceService;