'use strict';

import { <PERSON><PERSON><PERSON><PERSON>nze } from "../../../https/responze";
import Utilities from "../../../util/utilities";
import IDARepository from "../importClearance/IDAClearance.repository";
import MICRepository from "../importClearance/MICClearance.reporsitory";
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import MasterAnalyzerRepository from "./masterAnalyzer.reporsitory";
import { MasterAnalyzer } from "../../../models/masterAnalyzer.model";
import BaseRepository from "../../index.repository";
import BaseService from "../../index.service";
import OrderBy from "../../../parser/orderBy";
import { Database } from "../../../database";
import { ActionKey } from "../../../emuns/action";
import GroupWhere from "../../../parser/groupWhere";
import { IDA } from "../../../models/index.model";
import EDARepository from "../exportClearance/EDAClearance.reporsitory";
import MECRepository from "../exportClearance/MECClearance.reporsitory";


interface AnalyzerInbound {
  MAWB: string,
  boxName: String,
  IDA: number,
  IDA_0: number,
  IDA_R: number,
  IDA_Y: number,
  IDA_B: number,
  MIC: number,
  MIC_0: number,
  MIC_R: number,
  MIC_Y: number,
  MIC_B: number,
  delivery: string,
  clientId: number,
  HAWB_IDA_R: string,
  HAWB_IDA_Y: string,
  HAWB_IDA_N: string,
  HAWB_IDAs: string;
  HAWB_MIC_R: string;
  HAWB_MIC_Y: string;
  HAWB_MIC_N: string;
}

interface AnalyzerOutbound {
  MAWB: string,
  boxName: String,
  EDA: number,
  EDA_0: number,
  EDA_R: number,
  EDA_Y: number,
  EDA_B: number,
  MEC: number,
  MEC_0: number,
  MEC_R: number,
  MEC_Y: number,
  MEC_B: number,
  delivery: string,
  clientId: number,
  HAWB_EDA_R: string,
  HAWB_EDA_Y: string,
  HAWB_EDA_N: string,
  HAWB_EDAs: string;
  HAWB_MEC_R: string;
  HAWB_MEC_Y: string;
  HAWB_MEC_N: string;
  HOLD: number;
}


class MasterAnalyzerService extends BaseService {
  private micRepository: MICRepository;
  private idaRepository: IDARepository;
  private mecRepository: MECRepository;
  private edaRepository: EDARepository;
  private masterAnalyzerRepository: MasterAnalyzerRepository;
  private sequelize: any;
  constructor() {
    super(new MasterAnalyzerRepository());
    this.micRepository = new MICRepository();
    this.idaRepository = new IDARepository();
    this.edaRepository = new EDARepository();
    this.mecRepository = new MECRepository();
    this.masterAnalyzerRepository = new MasterAnalyzerRepository();
    this.sequelize = Database.Sequelize;
  }

  public async deleteAnalyzer(MAWB: string): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.DELETE_FAIL,
          'data': null,
          'error': []
        }
      }
      if (MAWB) {
        const MAWBOptional = new Optional();
        MAWBOptional.setWhere([
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        const total = await this.masterAnalyzerRepository.destroyData(MAWBOptional);
        if (total > 0) {
          reponze['status'] = true;
          reponze['message']['message'] = EMessage.DELETE_SUCCESS;
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][MasterAnalyzer][deleteAnalyzer]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async createAnalyzer(MAWBs: string[], serviceId: number, employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      if (MAWBs.length > 0) {
        const MAWBOptional = new Optional();
        MAWBOptional.setAttributes(['externalBoxName', 'MAWB']);
        MAWBOptional.setWhere([
          new Where(EConfigure.AND, 'MAWB', EConfigure.IN, MAWBs.join()),
          new Where(EConfigure.AND, 'externalBoxName', EConfigure.NOT, null),
          new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
        ]);
        MAWBOptional.setGroup(['externalBoxName', 'MAWB']);
        if (serviceId == EConfigure.INBOUND_SERVICE) {
          const [idas, mics] = await Promise.all([
            this.idaRepository.queryAll(MAWBOptional),
            this.micRepository.queryAll(MAWBOptional),
          ]);
          if (idas.length > 0 || mics.length > 0) {
            const totalBoxes: any = [...idas, ...mics];
            this.handleAnalyzerInbound(totalBoxes, serviceId, employeeId);
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.SUCCESS;
          }
        }
        if (serviceId == EConfigure.OUTBOUND_SERVICE) {
          const [edas, mecs] = await Promise.all([
            this.edaRepository.queryAll(MAWBOptional),
            this.mecRepository.queryAll(MAWBOptional),
          ]);
          if (edas.length > 0 || mecs.length > 0) {
            const totalBoxes: any = [...edas, ...mecs];
            this.handleAnalyzerOutbound(totalBoxes, serviceId, employeeId);
            reponze['status'] = true;
            reponze['message']['message'] = EMessage.SUCCESS;
          }
        }
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][MasterAnalyzer][createAnalyzer]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async handleAnalyzerInbound(totalBoxes: any, serviceId: number, employeeId: number): Promise<void> {
    try {
      if (totalBoxes.length > 0) {
        const stored = new Map<String, AnalyzerInbound>();

        for (const item of totalBoxes) {
          const MAWB = item['MAWB'];
          const externalBoxName: String = item['externalBoxName'];
          const key = `${MAWB}_${externalBoxName}`;
          if (!stored.has(key)) {
            const checkAnalyzer = new Optional();
            checkAnalyzer.setWhere([
              new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
              new Where(EConfigure.AND, 'serviceId', EConfigure.EQUAL, serviceId),
              new Where(EConfigure.AND, 'boxName', EConfigure.EQUAL, externalBoxName),
              new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            ]);
            let existed: MasterAnalyzer = await this.masterAnalyzerRepository.getOneOptional(checkAnalyzer);
            if (existed) {
              existed['isSucess'] = false;
              await existed.save();
            } else {
              await this.masterAnalyzerRepository.createData({
                MAWB,
                'boxName': externalBoxName,
                employeeId,
                serviceId
              });
            }
            const totalOptional = new Optional();
            totalOptional.setWhere([
              new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
              new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
              new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            ]);
            const [totalIDA, totalMIC] = await Promise.all([
              this.idaRepository.countData(totalOptional),
              this.micRepository.countData(totalOptional)
            ]);
            let objOne = null;
            let idaNone = 0, idaBlue = 0, idaRed = 0, idaYellow = 0;
            let micNone = 0, micBlue = 0, micRed = 0, micYellow = 0;
            let HAWBNone = null, HAWBRed = null, HAWBYellow = null, idas = null;
            let HAWBMICNone = null, HAWBMICRed = null, HAWBMICYellow = null;
            if (totalIDA > 0) {
              const firstHAWBOptional = new Optional();
              firstHAWBOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.CLIENT_ID_FIELD]);
              firstHAWBOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
              ]);
              firstHAWBOptional.setOrderby([
                new OrderBy('createdAt', EConfigure.ASCENDING)
              ]);

              const noneOptional = new Optional();
              noneOptional.setGroupWhere(new GroupWhere(EConfigure.OR, [
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, 'isECUSError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.LESS_THAN, ActionKey.INSPECTION_KIND),
                ],
              ]));

              const HAWBNoneOptional = new Optional();
              HAWBNoneOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBNoneOptional.setGroupWhere(new GroupWhere(EConfigure.OR, [
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, 'isECUSError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.LESS_THAN, ActionKey.INSPECTION_KIND),
                ],
              ]));

              const blueOptional = new Optional();
              blueOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                // new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_1)),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
              ]);

              const yellowOptional = new Optional();
              yellowOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_2)),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBYellowOptional = new Optional();
              HAWBYellowOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBYellowOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_2)),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const redOptional = new Optional();
              redOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.ILIKE_START, EConfigure.INDEX_3),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBRedOptional = new Optional();
              HAWBRedOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBRedOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.ILIKE_START, EConfigure.INDEX_3),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const idaAnalyzer = new Optional();
              idaAnalyzer.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD]);
              idaAnalyzer.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
              ]);

              [objOne, idaNone, idaBlue, idaYellow, idaRed, HAWBNone, HAWBRed, HAWBYellow, idas] = await Promise.all([
                this.idaRepository.getOneOptional(firstHAWBOptional),
                this.idaRepository.countData(noneOptional),
                this.idaRepository.countData(blueOptional),
                this.idaRepository.countData(yellowOptional),
                this.idaRepository.countData(redOptional),

                this.idaRepository.queryOneRaw(HAWBNoneOptional),
                this.idaRepository.queryOneRaw(HAWBRedOptional),
                this.idaRepository.queryOneRaw(HAWBYellowOptional),
                this.idaRepository.queryAll(idaAnalyzer),
              ]);
            }
            if (totalMIC > 0) {
              const firstHAWBOptional = new Optional();
              firstHAWBOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.CLIENT_ID_FIELD]);
              firstHAWBOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
              ]);
              firstHAWBOptional.setOrderby([
                new OrderBy('createdAt', EConfigure.ASCENDING)
              ]);

              const noneOptional = new Optional();
              noneOptional.setGroupWhere(new GroupWhere(EConfigure.OR, [
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, 'isECUSError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.LESS_THAN, ActionKey.INSPECTION_KIND),
                ],
              ]));

              const blueOptional = new Optional();
              blueOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                // new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_1)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
              ]);

              const yellowOptional = new Optional();
              yellowOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_2)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const redOptional = new Optional();
              redOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_3)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBRedOptional = new Optional();
              HAWBRedOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBRedOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_3)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBYellowOptional = new Optional();
              HAWBYellowOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBYellowOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_2)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBNoneOptional = new Optional();
              HAWBNoneOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBNoneOptional.setGroupWhere(new GroupWhere(EConfigure.OR, [
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, 'isECUSError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.LESS_THAN, ActionKey.INSPECTION_KIND),
                ],
              ]));

              [objOne, micNone, micBlue, micYellow, micRed, HAWBMICNone, HAWBMICRed, HAWBMICYellow] = await Promise.all([
                this.micRepository.getOneOptional(firstHAWBOptional),
                this.micRepository.countData(noneOptional),
                this.micRepository.countData(blueOptional),
                this.micRepository.countData(yellowOptional),
                this.micRepository.countData(redOptional),

                this.micRepository.queryOneRaw(HAWBNoneOptional),
                this.micRepository.queryOneRaw(HAWBRedOptional),
                this.micRepository.queryOneRaw(HAWBYellowOptional),
              ]);
            }
            const analyzerResult: AnalyzerInbound = {
              MAWB: MAWB,
              boxName: externalBoxName,
              IDA: totalIDA,
              IDA_0: idaNone,
              IDA_B: idaBlue,
              IDA_R: idaRed,
              IDA_Y: idaYellow,
              MIC: totalMIC,
              MIC_0: micNone,
              MIC_B: micBlue,
              MIC_R: micRed,
              MIC_Y: micYellow,
              delivery: objOne ? objOne[EConfigure.HAWB_CLEARANCE_FIELD]!.substring(0, 3).trim(): '',
              clientId: objOne ? objOne[EConfigure.CLIENT_ID_FIELD]! : 0,
              HAWB_IDA_N: HAWBNone ? HAWBNone['HAWBs'] : null,
              HAWB_IDA_R: HAWBRed ? HAWBRed['HAWBs'] : null,
              HAWB_IDA_Y: HAWBYellow ? HAWBYellow['HAWBs'] : null,
              HAWB_IDAs: (idas && idas.length > 0) ? idas.map((ida: IDA) => ida[EConfigure.HAWB_CLEARANCE_FIELD]).join(',') : '',
              HAWB_MIC_N: HAWBMICNone ? HAWBMICNone['HAWBs'] : null,
              HAWB_MIC_R: HAWBMICRed ? HAWBMICRed['HAWBs'] : null,
              HAWB_MIC_Y: HAWBMICYellow ? HAWBMICYellow['HAWBs'] : null,
            }
            stored.set(key, analyzerResult);
          }
        }
        if (stored.size > 0) {
          for (const value of stored.values()) {
            const result: AnalyzerInbound = value;
            const checkAnalyzer = new Optional();
            checkAnalyzer.setWhere([
              new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, result['MAWB']),
              new Where(EConfigure.AND, 'boxName', EConfigure.EQUAL, result['boxName']),
              new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            ]);
            let existed: MasterAnalyzer = await this.masterAnalyzerRepository.getOneOptional(checkAnalyzer);
            if (existed) {
              existed['isSucess'] = true;
              existed['totalIDA'] = result['IDA'];
              existed['totalIDANone'] = result['IDA_0'];
              existed['totalIDABlue'] = result['IDA_B'];
              existed['totalIDAYellow'] = result['IDA_Y'];
              existed['totalIDARed'] = result['IDA_R'];

              existed['totalMIC'] = result['MIC'];
              existed['totalMICNone'] = result['MIC_0'];
              existed['totalMICBlue'] = result['MIC_B'];
              existed['totalMICYellow'] = result['MIC_Y'];
              existed['totalMICRed'] = result['MIC_R'];

              existed['delivery'] = result['delivery'];

              existed['clientId'] = result['clientId'];
              existed['IDANone'] = result['HAWB_IDA_N'];
              existed['IDARed'] = result['HAWB_IDA_R'];
              existed['IDAYellow'] = result['HAWB_IDA_Y'];
              existed['IDAs'] = result['HAWB_IDAs'];

              existed['MICNone'] = result['HAWB_MIC_N'];
              existed['MICRed'] = result['HAWB_MIC_R'];
              existed['MICYellow'] = result['HAWB_MIC_Y'];
              await existed.save();
            }
          }
        }

      }
    } catch (error) {
      Utilities.sendDiscordErr('[service][MasterAnalyzer][handleAnalyzer]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async handleAnalyzerOutbound(totalBoxes: any, serviceId: number, employeeId: number): Promise<void> {
    try {
      if (totalBoxes.length > 0) {
        const stored = new Map<String, AnalyzerOutbound>();

        for (const item of totalBoxes) {
          const MAWB = item['MAWB'];
          const externalBoxName: String = item['externalBoxName'];
          const key = `${MAWB}_${externalBoxName}`;
          if (!stored.has(key)) {
            const checkAnalyzer = new Optional();
            checkAnalyzer.setWhere([
              new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
              new Where(EConfigure.AND, 'serviceId', EConfigure.EQUAL, serviceId),
              new Where(EConfigure.AND, 'boxName', EConfigure.EQUAL, externalBoxName),
              new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            ]);
            let existed: MasterAnalyzer = await this.masterAnalyzerRepository.getOneOptional(checkAnalyzer);
            if (existed) {
              existed['isSucess'] = false;
              await existed.save();
            } else {
              await this.masterAnalyzerRepository.createData({
                MAWB,
                'boxName': externalBoxName,
                employeeId,
                serviceId
              });
            }
            const totalOptional = new Optional();
            totalOptional.setWhere([
              new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
              new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
              new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            ]);
            const [totalEDA, totalMEC] = await Promise.all([
              this.edaRepository.countData(totalOptional),
              this.mecRepository.countData(totalOptional)
            ]);
            let objOne = null;
            let edaNone = 0, edaBlue = 0, edaRed = 0, edaYellow = 0, holdEDA = 0;
            let mecNone = 0, mecBlue = 0, mecRed = 0, mecYellow = 0, holdMEC = 0;
            let HAWBNone = null, HAWBRed = null, HAWBYellow = null, edas = null;
            let HAWBMECNone = null, HAWBMECRed = null, HAWBMECYellow = null;
            if (totalEDA > 0) {
              const firstHAWBOptional = new Optional();
              firstHAWBOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.CLIENT_ID_FIELD]);
              firstHAWBOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
              ]);
              firstHAWBOptional.setOrderby([
                new OrderBy('createdAt', EConfigure.ASCENDING)
              ]);

              const noneOptional = new Optional();
              noneOptional.setGroupWhere(new GroupWhere(EConfigure.OR, [
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, 'isECUSError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.LESS_THAN, ActionKey.INSPECTION_KIND),
                ],
              ]));

              const HAWBNoneOptional = new Optional();
              HAWBNoneOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBNoneOptional.setGroupWhere(new GroupWhere(EConfigure.OR, [
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, 'isECUSError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.LESS_THAN, ActionKey.INSPECTION_KIND),
                ],
              ]));

              const blueOptional = new Optional();
              blueOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
              ]);

              const yellowOptional = new Optional();
              yellowOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_2)),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBYellowOptional = new Optional();
              HAWBYellowOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBYellowOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_2)),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const redOptional = new Optional();
              redOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.ILIKE_START, EConfigure.INDEX_3),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBRedOptional = new Optional();
              HAWBRedOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBRedOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.ILIKE_START, EConfigure.INDEX_3),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const edaAnalyzer = new Optional();
              edaAnalyzer.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD]);
              edaAnalyzer.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
              ]);

              const holdEDAOptional = new Optional();
              holdEDAOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
              ]);

              [objOne, edaNone, edaBlue, edaYellow, edaRed, HAWBNone, HAWBRed, HAWBYellow, edas, holdEDA] = await Promise.all([
                this.edaRepository.getOneOptional(firstHAWBOptional),
                this.edaRepository.countData(noneOptional),
                this.edaRepository.countData(blueOptional),
                this.edaRepository.countData(yellowOptional),
                this.edaRepository.countData(redOptional),

                this.edaRepository.queryOneRaw(HAWBNoneOptional),
                this.edaRepository.queryOneRaw(HAWBRedOptional),
                this.edaRepository.queryOneRaw(HAWBYellowOptional),
                this.edaRepository.queryAll(edaAnalyzer),
                this.edaRepository.countData(holdEDAOptional),
              ]);
            }
            if (totalMEC > 0) {
              const firstHAWBOptional = new Optional();
              firstHAWBOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, EConfigure.CLIENT_ID_FIELD]);
              firstHAWBOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
              ]);
              firstHAWBOptional.setOrderby([
                new OrderBy('createdAt', EConfigure.ASCENDING)
              ]);

              const noneOptional = new Optional();
              noneOptional.setGroupWhere(new GroupWhere(EConfigure.OR, [
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, 'isECUSError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.LESS_THAN, ActionKey.INSPECTION_KIND),
                ],
              ]));

              const blueOptional = new Optional();
              blueOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE),
              ]);

              const yellowOptional = new Optional();
              yellowOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_2)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const redOptional = new Optional();
              redOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_3)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBRedOptional = new Optional();
              HAWBRedOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBRedOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_3)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBYellowOptional = new Optional();
              HAWBYellowOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBYellowOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, String(EConfigure.INDEX_2)),
                new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.INSPECTION_KIND),
              ]);

              const HAWBNoneOptional = new Optional();
              HAWBNoneOptional.setAttributes([this.sequelize.literal('string_agg("HAWBClearance", \',\') as "HAWBs"')]);
              HAWBNoneOptional.setGroupWhere(new GroupWhere(EConfigure.OR, [
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.IS, null),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'isError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, 'isECUSError', EConfigure.EQUAL, true),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.EQUAL, ActionKey.SUBMIT_CUSTOM_CLEARANCE),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                ],
                [
                  new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                  new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                  new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
                  new Where(EConfigure.AND, 'declarationNo', EConfigure.NOT, null),
                  new Where(EConfigure.AND, EConfigure.CLASSIFY_FIELD, EConfigure.EQUAL, EConfigure.PAR),
                  new Where(EConfigure.AND, EConfigure.PHASE_FIELD, EConfigure.LESS_THAN, ActionKey.INSPECTION_KIND),
                ],
              ]));

              const holdMECOptional = new Optional();
              holdMECOptional.setWhere([
                new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB),
                new Where(EConfigure.AND, 'externalBoxName', EConfigure.EQUAL, externalBoxName),
                new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, true),
                new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
              ]);

              [objOne, mecNone, mecBlue, mecYellow, mecRed, HAWBMECNone, HAWBMECRed, HAWBMECYellow, holdMEC] = await Promise.all([
                this.mecRepository.getOneOptional(firstHAWBOptional),
                this.mecRepository.countData(noneOptional),
                this.mecRepository.countData(blueOptional),
                this.mecRepository.countData(yellowOptional),
                this.mecRepository.countData(redOptional),

                this.mecRepository.queryOneRaw(HAWBNoneOptional),
                this.mecRepository.queryOneRaw(HAWBRedOptional),
                this.mecRepository.queryOneRaw(HAWBYellowOptional),
                this.mecRepository.countData(holdMECOptional),
              ]);
            }
            const analyzerResult: AnalyzerOutbound = {
              MAWB: MAWB,
              boxName: externalBoxName,
              EDA: totalEDA,
              EDA_0: edaNone,
              EDA_B: edaBlue,
              EDA_R: edaRed,
              EDA_Y: edaYellow,
              MEC: totalMEC,
              MEC_0: mecNone,
              MEC_B: mecBlue,
              MEC_R: mecRed,
              MEC_Y: mecYellow,
              delivery: objOne[EConfigure.HAWB_CLEARANCE_FIELD].substring(0, 3).trim(),
              clientId: objOne[EConfigure.CLIENT_ID_FIELD],
              HAWB_EDA_N: HAWBNone ? HAWBNone['HAWBs'] : null,
              HAWB_EDA_R: HAWBRed ? HAWBRed['HAWBs'] : null,
              HAWB_EDA_Y: HAWBYellow ? HAWBYellow['HAWBs'] : null,
              HAWB_EDAs: (edas && edas.length > 0) ? edas.map((ida: IDA) => ida[EConfigure.HAWB_CLEARANCE_FIELD]).join(',') : null,
              HAWB_MEC_N: HAWBMECNone ? HAWBMECNone['HAWBs'] : null,
              HAWB_MEC_R: HAWBMECRed ? HAWBMECRed['HAWBs'] : null,
              HAWB_MEC_Y: HAWBMECYellow ? HAWBMECYellow['HAWBs'] : null,
              HOLD: holdEDA + holdMEC,
            }
            stored.set(key, analyzerResult);
          }
        }
        if (stored.size > 0) {
          for (const value of stored.values()) {
            const result: AnalyzerOutbound = value;
            const checkAnalyzer = new Optional();
            checkAnalyzer.setWhere([
              new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, result['MAWB']),
              new Where(EConfigure.AND, 'boxName', EConfigure.EQUAL, result['boxName']),
              new Where(EConfigure.AND, EConfigure.DELETED_FIELD, EConfigure.EQUAL, false),
            ]);
            let existed: MasterAnalyzer = await this.masterAnalyzerRepository.getOneOptional(checkAnalyzer);
            if (existed) {
              existed['isSucess'] = true;
              existed['totalEDA'] = result['EDA'];
              existed['totalEDANone'] = result['EDA_0'];
              existed['totalEDABlue'] = result['EDA_B'];
              existed['totalEDAYellow'] = result['EDA_Y'];
              existed['totalEDARed'] = result['EDA_R'];
              existed['totalMEC'] = result['MEC'];
              existed['totalMECNone'] = result['MEC_0'];
              existed['totalMECBlue'] = result['MEC_B'];
              existed['totalMECYellow'] = result['MEC_Y'];
              existed['totalMECRed'] = result['MEC_R'];
              existed['delivery'] = result['delivery'];
              existed['clientId'] = result['clientId'];
              existed['EDANone'] = result['HAWB_EDA_N'];
              existed['EDARed'] = result['HAWB_EDA_R'];
              existed['EDAYellow'] = result['HAWB_EDA_Y'];
              existed['EDAs'] = result['HAWB_EDAs'];
              existed['MECNone'] = result['HAWB_MEC_N'];
              existed['MECRed'] = result['HAWB_MEC_R'];
              existed['MECYellow'] = result['HAWB_MEC_Y'];
              existed['totalHold'] = result['HOLD'];
              await existed.save();
            }
          }
        }

      }
    } catch (error) {
      Utilities.sendDiscordErr('[service][MasterAnalyzer][handleAnalyzer]', (error as Error).message);
      throw new Error(error as any);
    }
  }
}

export default MasterAnalyzerService;