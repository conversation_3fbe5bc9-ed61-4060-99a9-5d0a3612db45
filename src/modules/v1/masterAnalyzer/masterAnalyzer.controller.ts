'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import Middlewares from '../../../middleware/middlewares';
import ParserUrl from '../../../parser/url';
import EMessage from '../../../emuns/messages';
import EConfigure from '../../../emuns/configures';
import Optional from '../../../parser/optional';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import HttpData from '../../../https/data';
import Meta from "../../../https/meta";
import MasterAnalyzerService from './masterAnalyzer.service';
import MasterAnalyzerValidate from "./masterAnalyzer.validate";
import ErrorValidate from "../../../util/error.validate";
import RedisPromise from "../../../util/redis.promise";
import UserService from "../user/user.service";
import { IResponze } from "../../../https/responze";

class MasterAnalyzerController {
  public path = '/master_analyzers';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get(this.path, this.getAll);
    this.router.post(this.path, this.create);
    this.router.delete(`${this.path}`, this.delete);
  }

private async create(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } =  MasterAnalyzerValidate.create.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        
        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
        if(profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
        if(userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const masterAnalyzerService =  new MasterAnalyzerService();
      const { MAWBs, serviceId } = value; 
      const message: IResponze = await masterAnalyzerService.createAnalyzer(MAWBs, serviceId, employeeId);
      return HttpResponse.sendData(res, httpStatus.CREATED, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][master_analyzers][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const masterAnalyzerService = new MasterAnalyzerService();
      const [data, total] = await masterAnalyzerService.getAll(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][master_analyzers][get_all]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async delete(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } =  MasterAnalyzerValidate.delete.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { MAWB } = value; 
      const masterAnalyzerService = new MasterAnalyzerService();
      const message: IResponze = await masterAnalyzerService.deleteAnalyzer(MAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(message['status'], message['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][master_analyzers][delete_master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default MasterAnalyzerController;