'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';

class MasterAnalyzerValidate {
  static create: any = Joi.object({
    MAWBs: Joi.array().items(Joi.string().trim()).unique().required().max(255).messages({
      "any.required": `MAWBs ${EMessage.REQUIRED}`,
      "string.empty": `MAWBs ${EMessage.EMPTYED}`,
      "string.max": `MAWBs ${EMessage.MAX_255}`,
    }),
    serviceId: Joi.number().required().messages({
      "any.required": `serviceId ${EMessage.REQUIRED}`,
      "number.base": `serviceId ${EMessage.NUMBER}`,
    }),
  });

  static delete: any = Joi.object({
    MAWB: Joi.string().max(255).trim().required().messages({
      "any.required": `MAWB ${EMessage.REQUIRED}`,
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_255}`,
    })
  });
}

export default MasterAnalyzerValidate;
