'user strict'

import Jo<PERSON> from '@hapi/joi';
import EMessage from '../../../emuns/messages';

class HSCodeValidate {
  static create: any = Joi.object({
    hsCode: Joi.string().trim().allow(null).max(50).messages({
      "any.required": `hsCode ${EMessage.REQUIRED}`,
      "string.empty": `hsCode ${EMessage.EMPTYED}`,
      "string.max": `hsCode ${EMessage.MAX_50}`,
    }),
    name: Joi.string().trim().allow(null).messages({
      "any.required": `name ${EMessage.REQUIRED}`,
      "string.empty": `name ${EMessage.EMPTYED}`,
    }),
    nameVN: Joi.string().trim().allow(null).messages({
      "any.required": `nameVN ${EMessage.REQUIRED}`,
      "string.empty": `nameVN ${EMessage.EMPTYED}`,
    }),
    unitCode: Joi.string().max(10).allow(null).trim().messages({
      "any.required": `unitCode ${EMessage.REQUIRED}`,
      "string.empty": `unitCode ${EMessage.EMPTYED}`,
      "string.max": `unitCode ${EMessage.MAX_10}`,
    }),
    unitName: Joi.string().max(20).allow(null).trim().messages({
      "any.required": `unitName ${EMessage.REQUIRED}`,
      "string.empty": `unitName ${EMessage.EMPTYED}`,
      "string.max": `unitName ${EMessage.MAX_20}`,
    }),
    importTaxValue: Joi.number().min(0).messages({
      "number.base": `importTaxValue ${EMessage.NUMBER}`,
      "number.min": `importTaxValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    importTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `importTaxCode ${EMessage.EMPTYED}`,
      "string.max": `importTaxCode ${EMessage.MAX_10}`,
    }),
    VATValue: Joi.number().min(0).allow(null).messages({
      "any.required": `VATValue ${EMessage.REQUIRED}`,
      "number.base": `VATValue ${EMessage.NUMBER}`,
      "number.min": `VATValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    VATCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATCode ${EMessage.EMPTYED}`,
      "string.max": `VATCode ${EMessage.MAX_10}`,
    }),
    specialConsumptionTaxValue: Joi.number().min(0).messages({
      "number.base": `specialConsumptionTaxValue ${EMessage.NUMBER}`,
      "number.min": `specialConsumptionTaxValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    specialConsumptionTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `specialConsumptionTaxCode ${EMessage.EMPTYED}`,
      "string.max": `specialConsumptionTaxCode ${EMessage.MAX_10}`,
    }),
    environmentTaxPrice: Joi.number().min(0).messages({
      "number.base": `environmentTaxPrice ${EMessage.NUMBER}`,
      "number.min": `environmentTaxPrice ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    environmentTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `environmentTaxCode ${EMessage.EMPTYED}`,
      "string.max": `environmentTaxCode ${EMessage.MAX_10}`,
    }),
  });

  static arrCreate: any = Joi.array().items(HSCodeValidate.create).unique().required();
}

export default HSCodeValidate;
