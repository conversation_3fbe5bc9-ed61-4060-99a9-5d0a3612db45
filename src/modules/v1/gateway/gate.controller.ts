'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import EConfigure from '../../../emuns/configures';
import moment = require('moment');
import httpStatus = require('http-status');
import Middlewares from "../../../middleware/middlewares";
import { ActionKey, ActionName } from "../../../emuns/action";
import HttpResponse from "../../../https/response";
import HttpData from "../../../https/data";
import HttpException from "../../../https/exception";
import ParserUrl from "../../../parser/url";
import Optional from "../../../parser/optional";
import RedisPromise from "../../../util/redis.promise";
import Where from "../../../parser/where";
import UserService from "../user/user.service";
import Meta from "../../../https/meta";
import ImportClearanceService from '../importClearance/importClearance.service';
import ExportClearanceService from '../exportClearance/exportClearance.service';
import { IResponze } from "../../../https/responze";
import ImportClearanceValidate from "../importClearance/importClearance.validate";
import ErrorValidate from "../../../util/error.validate";
import HistoryMonitorService from "../historyMonitor/historyMonitor.service";
import HistoryInvoiceService from "../historyInvoice/historyInvoice.service";
import ExportClearanceValidate from "../exportClearance/exportClearance.validate";

class GatewayController {
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get('/actions', this.getActions);
    this.router.get(`/imports/mic`, this.getMICs);
		this.router.get(`/imports/ida`, this.getIDAs);
		this.router.get(`/imports/mic/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneMIC);
		this.router.get(`/imports/ida/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneIDA);
    this.router.get(`/imports/MAWBs`, this.getMAWBs);
    this.router.get(`/imports/MAWB/:MAWB`, this.getMAWB);
		this.router.get(`/imports/holdManifest`, this.getHoldManifest);
		this.router.get(`/imports/getAll`, this.getAll);
		this.router.get(`/imports/master`, this.master);
		this.router.get(`/imports/managementGate`, this.managementGate);
		this.router.get(`/imports/clearanceReport`, this.clearanceReport);
    this.router.get(`/imports/clearanceReportCount`, this.clearanceReportCount);
    this.router.get(`/imports/clearanceReportExport`, this.clearanceReportExport);
    this.router.get(`/historyMonitors`, this.historyMonitors);
    this.router.get(`/imports/totalManagementGate`, this.totalManagementGate);
    this.router.get(`/imports/exportManagementGate`, this.exportManagementGate);
    this.router.get(`/imports/reportCustom`, this.reportCustom);
    this.router.get(`/imports/reportCustomExport`, this.reportCustomExport);
    this.router.get(`/historyInvoices`, this.historyInvoices);
    this.router.get(`/imports/storeWarehouse`, this.getInboundStoreWarehouse);
    this.router.get(`/exports/storeWarehouse`, this.getOutboundStoreWarehouse);
		//
		this.router.get(`/exports/mec`, this.getMECs);
		this.router.get(`/exports/eda`, this.getEDAs);
		this.router.get(`/exports/master`, this.exportMaster);
		this.router.get(`/exports/clearanceReport`, this.exportClearanceReport);
    this.router.get(`/exports/clearanceReportExport`, this.exportClearanceReportExport);
		this.router.get(`/exports/mec/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneMEC);
    this.router.get(`/exports/eda/:HAWB`, middlewares.CheckPermission('get:v1:clearance.detail'), this.getOneEDA);
    
    //
    this.router.post(`/imports/monitorGate`, this.monitorGate);

	}

  private async getOutboundStoreWarehouse(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.getStoreWarehouse(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway]][getOutboundStoreWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getInboundStoreWarehouse(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getStoreWarehouse(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway]][getInboundStoreWarehouse]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async historyInvoices(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const historyInvoiceService = new HistoryInvoiceService();
      const [data, total] = await historyInvoiceService.getAll(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][historyInvoice][get_all]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async reportCustomExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let hubIds: string = '';
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if(profile && profile['hubs']) {
          hubIds = profile['hubs'];
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if(userInfo && userInfo['hubs']) {
          hubIds = userInfo['hubs'];
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.reportCustomExport(optional, parseUrl, hubIds, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][inbound]][reportCustomExport]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async reportCustom(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.reportCustom(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][inbound][clearance_report]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async exportManagementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let employeeId: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
        if(profile && profile['employeeId']) {
          employeeId = profile['employeeId'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
        if(userInfo && userInfo['employeeId']) {
          employeeId = userInfo['employeeId'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.exportManagementGate(optional, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][management_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async totalManagementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.totalManagementGate(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][management_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async historyMonitors(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const historyMonitorService = new HistoryMonitorService();
      const [data, total] = await historyMonitorService.getAll(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][history_monitor]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMAWBs(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubTo', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubTo', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getMAWBs(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getMAWBs]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getMAWB(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { MAWB } = req.params;
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getMAWB(MAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getMAWB]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async monitorGate(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ImportClearanceValidate.HAWB.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let hubs: any = null;
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          hubs = profile['hubs'];
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          hubs = userInfo['hubs'];
        }
      }
      const importClearanceService = new ImportClearanceService();
      const { HAWB } = value;
      const data: IResponze = await importClearanceService.monitorGate(HAWB, hubs);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][monitor_gate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async exportClearanceReportExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.clearanceReportExport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway]][exportClearanceReport]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async exportClearanceReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.clearanceReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway]][clearanceReport]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async exportMaster(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.master(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][exportMaster]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async clearanceReportExport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReportExport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway]][clearanceReport]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async clearanceReportCount(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReportCount(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway]][clearanceReportCount]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async clearanceReport(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.clearanceReport(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway]][clearanceReport]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async managementGate(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.managementGate(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][managementGate]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async master(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.master(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][master]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async getAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const importClearanceService = new ImportClearanceService();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const limit: number = optional.getLimit();
      const data: IResponze = await importClearanceService.getAllMICIDA(optional);
      const meta = new Meta(limit, optional.getOffset(), data['message']['data']['total'], optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK,  data['message']['data']['manifests'], meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getAllMICIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async getHoldManifest(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getHoldManifest(optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway]][clearanceReport]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async getOneMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: any = await importClearanceService.getOneMIC(HAWB, optional);
      let status: boolean = false;
      if(data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getOneMIC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const data: any = await importClearanceService.getOneIDA(HAWB, optional);
      let status: boolean = false;
      if(data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getOneIDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async getOneEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: any = await exportClearanceService.getOneEDA(HAWB, optional);
      let status: boolean = false;
      if(data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getOneEDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async getOneMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const { HAWB } = req.params;
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const data: any = await exportClearanceService.getOneMEC(HAWB, optional);
      let status: boolean = false;
      if(data) {
        status = true;
      }
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(status, data));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getOneMEC]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async getMECs(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllMEC(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getMECs]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getEDAs(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const exportClearanceService = new ExportClearanceService();
      const [data, total] = await exportClearanceService.getAllEDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getEDAs]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
	
	private async getIDAs(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const { identity } = res['locals'];
      try {
        const profile: any = await RedisPromise.getData(identity);
        if(profile && profile['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
        }
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        if(userInfo && userInfo['hubs']) {
          optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
        }
      }
      const importClearanceService = new ImportClearanceService();
      const [data, total] = await importClearanceService.getAllIDA(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getIDAs]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

	private async getMICs(req: Request, res: Response, next: NextFunction): Promise<any> {
		try {
			const parseUrl: ParserUrl = new ParserUrl(req.query);
			const optional: Optional = parseUrl.handleParameter();
			const importClearanceService = new ImportClearanceService();
			const { identity } = res['locals'];
			try {
					const profile: any = await RedisPromise.getData(identity);
					if(profile && profile['hubs']) {
					optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, profile['hubs'].split(',').join()));
					}
			} catch (error) {
					const userService = new UserService();
					const userInfo = await userService.getUserByIdentity(identity);
					if(userInfo && userInfo['hubs']) {
					optional.getWhere().push(new Where(EConfigure.AND, 'hubId', EConfigure.IN, userInfo['hubs'].split(',').join()));
					}
			}
			const [data, total] = await importClearanceService.getAllMIC(optional);
			const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
			return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
		} catch (error) {
			console.log(' \t --- [%s] [error][controller][gateway][getMICs]: %o', moment().format(EConfigure.FULL_TIME), error);
			return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
		}
	}

  private async getActions(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let micClearance: any = {};
      let idaClearance: any = {};
      const micClearanceAction: number[] = [
        ActionKey.CREATE,
        ActionKey.UPDATE_CARGO_NAME,

        ActionKey.INSPECTION_KIND,
        ActionKey.ACCEPT_CLEARANCE,
      ];
      ActionName.forEach(function(value, key){
        if(micClearanceAction.includes(key)){
          micClearance[key] = value;
        }
      });

      const idaClearanceAction: number[] = [
        ActionKey.CREATE, 
        ActionKey.UPDATE_CARGO_NAME,
        
        ActionKey.SUBMIT_INFOMATION, // IDA / EDA
        ActionKey.SENT_INFOMATION,
        
        ActionKey.INSPECTION_KIND,
        ActionKey.ACCEPT_CLEARANCE,
      ];
      ActionName.forEach(function(value, key){
        if(idaClearanceAction.includes(key)){
          idaClearance[key] = value;
        }
      });


      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(true, 
        {
          'micClearance': micClearance,
          'idaClearance': idaClearance,
        }
      ));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][gateway][getActions]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  
}

export default GatewayController;