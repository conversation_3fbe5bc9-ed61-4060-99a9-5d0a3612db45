'use strict';

import * as express from 'express';
import { Request, Response, NextFunction } from 'express';
import Middlewares from '../../../middleware/middlewares';
import EMessage from '../../../emuns/messages';
import EConfigure from '../../../emuns/configures';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import ErrorValidate from "../../../util/error.validate";
import ExportClearanceDetailValidate from './exportClearanceDetail.validate';
import ImportClearanceDetailService from './exportClearanceDetail.service';
import { IResponze } from '../../../https/responze';
import HttpData from '../../../https/data';
import RedisPromise from '../../../util/redis.promise';
import UserService from '../user/user.service';
import ExportClearanceDetailService from './exportClearanceDetail.service';

class ExportClearanceDetailController {
  public path = '/exportDetails';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.put(`${this.path}/mecItemNameVN`, middlewares.CheckPermission('put:v1:clearance.update'), this.mecItemNameVN);
    this.router.put(`${this.path}/edaItemNameVN`, middlewares.CheckPermission('put:v1:clearance.update'), this.edaItemNameVN);
    this.router.put(`${this.path}/autoItemNameVN`, middlewares.CheckPermission('put:v1:clearance.update'), this.updateAutoItemNameVN);

    this.router.post(`${this.path}/nameExisted`, this.nameExisted);
  }

  private async edaItemNameVN(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceDetailValidate.arrUpdateNameEDA.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const exportClearanceDetailService = new ExportClearanceDetailService();
      const data: IResponze = await exportClearanceDetailService.edaItemNameVN(value, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
      
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][item_nameVN]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async mecItemNameVN(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceDetailValidate.arrUpdateNameMEC.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const exportClearanceDetailService = new ExportClearanceDetailService();
      const data: IResponze = await exportClearanceDetailService.mecItemNameVN(value, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
      
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][item_nameVN]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async nameExisted(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceDetailValidate.arrNameExisted.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const exportClearanceDetailService = new ExportClearanceDetailService();
      const data: IResponze = await exportClearanceDetailService.nameExisted(value);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
      
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][name_existed]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async updateAutoItemNameVN(req: Request, res: Response, next: NextFunction) {
    try {
      const { error, value } = ExportClearanceDetailValidate.autoItemNameVN.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: number = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const exportClearanceDetailService = new ExportClearanceDetailService();
      const data: IResponze = await exportClearanceDetailService.updateAutoItemNameVN(value, employeeId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
      
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][export][update_auto_item_nameVN]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default ExportClearanceDetailController;