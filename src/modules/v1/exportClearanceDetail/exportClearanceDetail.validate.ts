'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';
import Utilities from '../../../util/utilities';
import { WeightName } from '../../../emuns/weight';

class ExportClearanceDetailValidate {
  static create: any = Joi.object({
    HSCode: Joi.string().trim().max(50).allow(null).allow('').messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().required().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().allow(null).messages({
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    url: Joi.string().trim().allow(null).messages({
      "string.empty": EMessage.EMPTYED,
    }),
    quantity: Joi.number().integer().min(1).messages({
      "number.base": `quantity ${EMessage.NUMBER}`,
      "number.integer": `quantity ${EMessage.INTEGER}`,
      "number.min": `quantity ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    currencyCode: Joi.string().trim().max(3).messages({
      "string.empty": `currencyCode ${EMessage.EMPTYED}`,
      "string.max": `currencyCode ${EMessage.MAX_3}`,
    }),
    invoiceValue: Joi.number().messages({
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
    }), 
    weight: Joi.number().min(0).required().messages({
      "any.required": `weight ${EMessage.REQUIRED}`,
      "number.base": `weight ${EMessage.NUMBER}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).required().max(3).trim().messages({
      "any.required": `unitOfMass ${EMessage.REQUIRED}`,
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    productId: Joi.string().trim().max(255).allow(null).messages({
      "string.empty": `productId ${EMessage.EMPTYED}`,
      "string.max": `productId ${EMessage.MAX_255}`,
    }),
  });

  static updateNameMEC: any = Joi.object({
    id: Joi.number().integer().required().messages({
      "any.required": `id ${EMessage.REQUIRED}`,
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    itemName: Joi.string().trim().required().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().required().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
  });

  static arrUpdateNameMEC = Joi.array().min(1).items(ExportClearanceDetailValidate.updateNameMEC).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static updateNameEDA: any = Joi.object({
    id: Joi.number().integer().required().messages({
      "any.required": `id ${EMessage.REQUIRED}`,
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    HSCode: Joi.string().trim().max(50).required().messages({
      "any.required": `HSCode ${EMessage.REQUIRED}`,
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().trim().required().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().required().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
  });

  static arrUpdateNameEDA = Joi.array().min(1).items(ExportClearanceDetailValidate.updateNameEDA).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static autoItemNameVN = Joi.array().min(1).items(
    Joi.object({
      id: Joi.number().integer().required().messages({
        "any.required": `id ${EMessage.REQUIRED}`,
        "number.base": `id ${EMessage.NUMBER}`,
        "number.integer": `id ${EMessage.INTEGER}`,
      }),
      HAWB: Joi.string().trim().max(30).required().messages({
        "any.required": `HAWB ${EMessage.REQUIRED}`,
        "string.empty": `HAWB ${EMessage.EMPTYED}`,
        "string.max": `HAWB ${EMessage.MAX_30}`,
      }),
      itemName: Joi.string().trim().required().messages({
        "any.required": `itemName ${EMessage.REQUIRED}`,
        "string.empty": `itemName ${EMessage.EMPTYED}`,
      }),
    })
    ).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static nameExisted: any = Joi.object({
    id: Joi.number().integer().required().messages({
      "any.required": `id ${EMessage.REQUIRED}`,
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    itemName: Joi.string().trim().required().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
  });

  static arrNameExisted = Joi.array().min(1).items(ExportClearanceDetailValidate.nameExisted).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`
  });

  static update: any = Joi.object({
    id: Joi.number().integer().required().messages({
      "any.required": `id ${EMessage.REQUIRED}`,
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    HSCode: Joi.string().trim().max(50).messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    quantity1: Joi.number().integer().min(1).messages({
      "any.required": `itemAmount ${EMessage.REQUIRED}`,
      "number.base": `itemAmount ${EMessage.NUMBER}`,
      "number.integer": `itemAmount ${EMessage.INTEGER}`,
      "number.min": `itemAmount ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    url: Joi.string().trim().allow(null).allow('').messages({
      "string.empty": `url ${EMessage.EMPTYED}`,
    }),
    invoiceValue: Joi.number().messages({
      "any.required": `invoiceValue ${EMessage.REQUIRED}`,
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
    }),
  });

  static updateEDA: any = Joi.object({
    id: Joi.number().integer().required().messages({
      "any.required": `id ${EMessage.REQUIRED}`,
      "number.base": `id ${EMessage.NUMBER}`,
      "number.integer": `id ${EMessage.INTEGER}`,
    }),
    HAWB: Joi.string().trim().max(30).required().messages({
      "any.required": `HAWB ${EMessage.REQUIRED}`,
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_30}`,
    }),
    HSCode: Joi.string().trim().max(50).messages({
      "string.empty": `HSCode ${EMessage.EMPTYED}`,
      "string.max": `HSCode ${EMessage.MAX_50}`,
    }),
    itemName: Joi.string().trim().messages({
      "any.required": `itemName ${EMessage.REQUIRED}`,
      "string.empty": `itemName ${EMessage.EMPTYED}`,
    }),
    itemNameVN: Joi.string().trim().messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    quantity1: Joi.number().integer().min(1).messages({
      "any.required": `itemAmount ${EMessage.REQUIRED}`,
      "number.base": `itemAmount ${EMessage.NUMBER}`,
      "number.integer": `itemAmount ${EMessage.INTEGER}`,
      "number.min": `itemAmount ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    quantity2: Joi.number().integer().min(1).messages({
      "any.required": `itemAmount ${EMessage.REQUIRED}`,
      "number.base": `itemAmount ${EMessage.NUMBER}`,
      "number.integer": `itemAmount ${EMessage.INTEGER}`,
      "number.min": `itemAmount ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    quantityUnitCode1: Joi.string().trim().max(3).messages({
      "string.empty": `quantityUnitCode1 ${EMessage.EMPTYED}`,
      "string.max": `quantityUnitCode1 ${EMessage.MAX_3}`,
    }),
    quantityUnitCode2: Joi.string().trim().max(3).messages({
      "string.empty": `quantityUnitCode2 ${EMessage.EMPTYED}`,
      "string.max": `quantityUnitCode2 ${EMessage.MAX_3}`,
    }),
    url: Joi.string().trim().messages({
      "string.empty": `url ${EMessage.EMPTYED}`,
    }),
    invoiceValue: Joi.number().messages({
      "number.base": `invoiceValue ${EMessage.NUMBER}`,
    }),
    ortherLawCode: Joi.string().trim().allow(null).allow('').messages({
      "any.required": `itemNameVN ${EMessage.REQUIRED}`,
      "string.empty": `itemNameVN ${EMessage.EMPTYED}`,
    }),
    exportTax: Joi.number().messages({
      "number.base": `exportTax ${EMessage.NUMBER}`,
    }),
    exportTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `exportTaxCode ${EMessage.EMPTYED}`,
      "string.max": `exportTaxCode ${EMessage.MAX_10}`,
    }),
    exportTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `exportTaxFree ${EMessage.EMPTYED}`,
      "string.max": `exportTaxFree ${EMessage.MAX_10}`,
    }),
    VATTax: Joi.number().messages({
      "number.base": `VATTax ${EMessage.NUMBER}`,
    }),
    VATTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATTaxCode ${EMessage.EMPTYED}`,
      "string.max": `VATTaxCode ${EMessage.MAX_10}`,
    }),
    VATTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `VATTaxFree ${EMessage.EMPTYED}`,
      "string.max": `VATTaxFree ${EMessage.MAX_10}`,
    }),
    environmentTax: Joi.number().messages({
      "number.base": `environmentTax ${EMessage.NUMBER}`,
    }),
    environmentTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `environmentTaxCode ${EMessage.EMPTYED}`,
      "string.max": `environmentTaxCode ${EMessage.MAX_10}`,
    }),
    environmentTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `environmentTaxFree ${EMessage.EMPTYED}`,
      "string.max": `environmentTaxFree ${EMessage.MAX_10}`,
    }),
    specialConsumptionTax: Joi.number().messages({
      "number.base": `specialConsumptionTax ${EMessage.NUMBER}`,
    }),
    specialConsumptionTaxCode: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `specialConsumptionTaxCode ${EMessage.EMPTYED}`,
      "string.max": `specialConsumptionTaxCode ${EMessage.MAX_10}`,
    }),
    specialConsumptionTaxFree: Joi.string().trim().max(10).allow(null).messages({
      "string.empty": `specialConsumptionTaxFree ${EMessage.EMPTYED}`,
      "string.max": `specialConsumptionTaxFree ${EMessage.MAX_10}`,
    }),
  });

}

export default ExportClearanceDetailValidate;
