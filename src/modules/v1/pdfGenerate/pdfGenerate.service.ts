'use strict';

import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import ejs from 'ejs';
import * as path from 'path';
import * as pdf from 'html-pdf';
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import MICRepository from "../importClearance/MICClearance.reporsitory";
import IDARepository from "../importClearance/IDAClearance.repository";
import MECRepository from "../exportClearance/MECClearance.reporsitory";
import EDARepository from "../exportClearance/EDAClearance.reporsitory";

import { EDA, eIMEC, eIMIC, ExportDetail, HistoryInvoice, IDA, IMEC, IMIC, MEC, MIC } from "../../../models/index.model";
import moment from "moment";
import Utilities from "../../../util/utilities";
import _ from "lodash";
import { PrintKey } from "../../../emuns/print";
import HistoryInvoiceService from "../historyInvoice/historyInvoice.service";
import Configure from "../../../emuns/configures";
import Config from "../../../util/configure";
import { IResponze } from "../../../https/responze";
import OrderBy from "../../../parser/orderBy";
import HubRepository from "../hub/hub.reporsitory";
import ShipmentRepository from "../shipment/shipment.reporsitory";
import { ActionKey } from "../../../emuns/action";
import eMICRepository from "../eImportClearance/eMICClearance.repository";
import { label } from "joi";
import eMECRepository from "../eExportClearance/eMECClearance.reporsitory";
const ExcelJS = require('exceljs');

type IBCExport = {
  no: string,
  HAWBClearance: string,
  bagId: string,
  MAWB: string,
  orderId: string,
  refId: string,
  bagWeight: string,
  shipperWeight: string,
  processedWeight: string,
  sender: string,
  senderAddress: string,
  receiver: string,
  receiverPhone: string,
  receiverEmail: string,
  receiverAddress: string,
  currency: string,
  totalValue: string,
  itemCustomDes: string,
  productName: string,
  productNameVn: string,
  itemQty: string,
  unitValue: string,
  itemUrl: string,
  itemSkuId: string,
  hsCode: string,
  origin: string,
}

class PdfGenerate {
  private micRepository: MICRepository;
  private idaRepository: IDARepository;
  private mecRepository: MECRepository;
  private eMECRepository: eMECRepository;
  private edaRepository: EDARepository;
  private hubRepository: HubRepository;
  private historyInvoiceService: HistoryInvoiceService;
  private shipmentRepository: ShipmentRepository;
  private eMICRepository: eMICRepository;
  constructor() {
    this.micRepository = new MICRepository();
    this.idaRepository = new IDARepository();
    this.mecRepository = new MECRepository();
    this.edaRepository = new EDARepository();
    this.hubRepository = new HubRepository();
    this.eMICRepository = new eMICRepository();
    this.eMECRepository = new eMECRepository();
    this.historyInvoiceService = new HistoryInvoiceService();
    this.shipmentRepository = new ShipmentRepository();
  }

  public async pdfPrint(value: any, hubs: any, employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const itemLanguage = (value['itemLanguage'] != undefined || value['itemLanguage'] != null) ? value['itemLanguage'] : 'vi';
      const hasFooter = (value['isHasFooter'] != undefined) ? value['isHasFooter'] : true;
      const inspectionKind = (value['inspectionKindClassification'] != undefined) ? value['inspectionKindClassification'] : 0;
      const isLandscape: boolean = value['isLandscape'] || false;
      const printType: number = value['printType'];
      const micOptional: Optional = new Optional();
      const idaOptional: Optional = new Optional();
      micOptional.setRelation(["importDetails", "manifest"]);
      micOptional.setAttributes(["MAWB", "hubId", "declarationNo", "HAWBClearance", "hubId", "cargoPiece", "notes", "cargoWeight", "priceVND", "importerCode", "importerFullName",
        "externalBoxName", "internalBoxName", "consignorName", "importerFullName", "address1", "addressOfImporter", "percentTaxPrint"]);
      micOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      micOptional.setOrderby([new OrderBy("createdAt", Configure.DESCENDING)]);

      idaOptional.setRelation(["importDetails", "manifest"]);
      idaOptional.setAttributes(["MAWB", "hubId", "declarationNo", "HAWBClearance", "hubId", "cargoPiece", "notes", "cargoWeightGross", "priceVND", "importerCode", "importerFullName", "externalBoxName",
        "internalBoxName", "consignorName", "importerFullName", "address1", "addressOfImporter"]);
      idaOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      idaOptional.setOrderby([new OrderBy("createdAt", Configure.DESCENDING)]);
      if (value['classify']) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if ((printType == PrintKey.INVOICE_MIC || printType == PrintKey.INVOICE_MIC_TT29) && inspectionKind == 0) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE)
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE)
        );
      } else if ((printType == PrintKey.INVOICE_MIC || printType == PrintKey.INVOICE_MIC_TT29) && inspectionKind != 0) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.EQUAL, `${inspectionKind}`)
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.ILIKE_START, `${inspectionKind}`)
        );
      }
      if (printType == PrintKey.INVOICE_CARGO && value['phase']) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
      }
      if (value['MAWB']) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        micOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (value['dateCheckout']) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      // ida
      let [MICs, IDAs] = await Promise.all([
        this.micRepository.queryAll(micOptional),
        this.idaRepository.queryAll(idaOptional)
      ]);
      if (MICs.length > 0 || IDAs.length > 0) {
        let obj: any = {};
        let hubId: number = 0;
        let mawb: string = '';
        let mawbs: Set<string> = new Set();
        if (MICs.length > 0 && (printType == PrintKey.INVOICE_MIC || printType == PrintKey.INVOICE_MIC_TT29 || printType == PrintKey.INVOICE_CARGO || printType == PrintKey.DETAIL_INVOICE || printType == PrintKey.DETAIL_INVOICE_MIC_TT29)) {
          hubId = MICs[0]["hubId"]!;
          mawb = MICs[0]["MAWB"];
          MICs.forEach((mic: MIC) => {
            mawbs.add(mic["MAWB"])
          });
          MICs = _.shuffle(MICs);
          if (value['boxes'] && value['boxes'].length > 0) {
            const idaExternalBoxes = _.groupBy(MICs, MIC => MIC['externalBoxName']);
            const idaInternalBoxes = _.groupBy(MICs, MIC => MIC['internalBoxName']);
            value['boxes'].forEach((item: any) => {
              obj[item] = [
                ...idaExternalBoxes[item] ? idaExternalBoxes[item] : [],
                ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []
              ];
            })
          } else {
            MICs.forEach((mic: MIC) => {
              let key: string = 'none';
              if (mic['externalBoxName']) {
                key = mic['externalBoxName'];
              } else if (mic['internalBoxName']) {
                key = mic['internalBoxName'];
              }
              if (obj[key]) {
                obj[key].push(mic);
              } else {
                obj[key] = [mic];
              }
            });
          }
        }
        if (IDAs.length > 0 && (printType == PrintKey.INVOICE_IDA || printType == PrintKey.INVOICE_CARGO)) {
          hubId = IDAs[0]["hubId"]!;
          mawb = IDAs[0]["MAWB"];
          IDAs.forEach((ida: IDA) => {
            mawbs.add(ida["MAWB"])
          });
          IDAs = _.shuffle(IDAs);
          if (value['boxes'] && value['boxes'].length > 0) {
            const idaExternalBoxes = _.groupBy(IDAs, IDA => IDA['externalBoxName']);
            const idaInternalBoxes = _.groupBy(IDAs, IDA => IDA['internalBoxName']);
            value['boxes'].forEach((item: any) => {
              if (obj[item].length > 0) {
                obj[item].push(...idaExternalBoxes[item] ? idaExternalBoxes[item] : [], ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []);
              } else {
                obj[item] = [
                  ...idaExternalBoxes[item] ? idaExternalBoxes[item] : [],
                  ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []
                ];
              }
            })
          } else {
            IDAs.forEach((ida: IDA) => {
              let key: string = 'none';
              if (ida['externalBoxName']) {
                key = ida['externalBoxName'];
              } else if (ida['internalBoxName']) {
                key = ida['internalBoxName'];
              }
              if (obj[key]) {
                obj[key].push(ida);
              } else {
                obj[key] = [ida];
              }
            });
          }
        }
        const now: string = moment().format('DD-MM-YYYY');
        const config: pdf.CreateOptions = {
          "format": "A4",
          "orientation": isLandscape ? "landscape" : "portrait",
          "border": {
            "top": "8px",
            "right": "10px",
            "bottom": "20px",
            "left": "10px"
          },
          "timeout": 300000,
        }
        let label: string = '';
        let pathTemplate: string = '';
        let filename: string = '';
        let excelName: string = '';
        const subTitle = inspectionKind == 2 ? '(Luồng Vàng)' : inspectionKind == 3 ? '(Luồng Đỏ)' : '';
        if (printType == PrintKey.INVOICE_MIC_TT29) {
          let classify: string = 'MIC_TT29';
          label = `Danh sách hàng hóa nhập khẩu trị giá thấp`;
          const name: string = `bangke${classify}-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          excelName = `${name}.xlsx`;
          const arr10: Record<string, any>[] = [];
          const arr8: Record<string, any>[] = [];
          let no10 = 1;
          let totalWeight10 = 0;
          let totalPiece10 = 0;
          let totalPrice10 = 0;
          let no8 = 1;
          let totalWeight8 = 0;
          let totalPiece8 = 0;
          let totalPrice8 = 0;
          for (const key in obj) {
            if (obj[key].length == 0) {
              continue;
            }
            obj[key].forEach((mic: IMIC) => {
              const itemNameVN: String[] = [];
              if (mic['importDetails']!.length > 0) {
                mic['importDetails']!.forEach((detail: Record<string, any>) => {
                  itemNameVN.push(String(detail['itemNameVN']));
                });
              }
              const currentEntry = {
                "declarationNo": mic['declarationNo'] != null ? mic['declarationNo'] : "",
                "HAWBClearance": mic['HAWBClearance'],
                "cargoName": itemNameVN.join(','),
                "unitWeight": 'KGM',
                "weight": (mic['cargoWeight']! > 0) ? Number(mic['cargoWeight']).toFixed(2) : 0,
                "cargoPiece": mic['cargoPiece'],
                "price": (mic['priceVND']! > 0) ? Number(mic['priceVND']).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0,
                "note": mic['notes'],
                "boxName": mic['externalBoxName'],
              };
              if (mic['percentTaxPrint'] == null || mic['percentTaxPrint'] === '10') {
                arr10.push({ "no": no10, ...currentEntry });
                totalWeight10 += Number(mic['cargoWeight']);
                totalPiece10 += Number(mic['cargoPiece']);
                totalPrice10 += Number(mic['priceVND']);
                no10 += 1;
              } else {
                arr8.push({ "no": no8, ...currentEntry });
                totalWeight8 += Number(mic['cargoWeight']);
                totalPiece8 += Number(mic['cargoPiece']);
                totalPrice8 += Number(mic['priceVND']);
                no8 += 1;
              }
            });
          }
          this.excelTT29({
            "data8": { "arr8": arr8, "total8": { totalWeight8, totalPiece8, totalPrice8, taxPrice8: totalPrice8 * 0.08 } },
            "data10": { "arr10": arr10, "total10": { totalWeight10, totalPiece10, totalPrice10, taxPrice10: totalPrice10 * 0.1 } }
          }, excelName, label, subTitle);
          pathTemplate = `../../../../../template/bangke/bangke_mic_tt29.ejs`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `
              <table width="100%;" style="font-size: 8px;">
                <tr>
                  <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                  <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                  <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                </tr>
              </table>`,
            }
          }
        }
        if (printType == PrintKey.INVOICE_MIC || printType == PrintKey.INVOICE_IDA) {
          let classify: String = '';
          if (printType == PrintKey.INVOICE_MIC) {
            classify = 'MIC';
          }
          if (printType == PrintKey.INVOICE_IDA) {
            classify = 'IDA';
          }
          const name: string = `bangke${classify}-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          if (printType == PrintKey.INVOICE_MIC) {
            excelName = `${name}.xlsx`;
            const arr10: Record<string, any>[] = [];
            const arr8: Record<string, any>[] = [];
            let no10 = 1;
            let totalWeight10 = 0;
            let totalPiece10 = 0;
            let totalPrice10 = 0;
            let no8 = 1;
            let totalWeight8 = 0;
            let totalPiece8 = 0;
            let totalPrice8 = 0;
            for (const key in obj) {
              if (obj[key].length == 0) {
                continue;
              }
              obj[key].forEach((mic: IMIC) => {
                const itemNameVN: String[] = [];
                if (mic['importDetails']!.length > 0) {
                  mic['importDetails']!.forEach((detail: Record<string, any>) => {
                    itemNameVN.push(String(detail['itemNameVN']));
                  });
                }
                const currentEntry = {
                  "declarationNo": mic['declarationNo'] != null ? mic['declarationNo'] : "",
                  "HAWBClearance": mic['HAWBClearance'],
                  "cargoName": itemNameVN.join(','),
                  "unitWeight": 'KGM',
                  "weight": (mic['cargoWeight']! > 0) ? Number(mic['cargoWeight']).toFixed(2) : 0,
                  "cargoPiece": mic['cargoPiece'],
                  "price": (mic['priceVND']! > 0) ? Number(mic['priceVND']).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0,
                  "note": mic['notes'],
                  "boxName": mic['externalBoxName'],
                };
                if (mic['percentTaxPrint'] == null || mic['percentTaxPrint'] === '10') {
                  arr10.push({ "no": no10, ...currentEntry });
                  totalWeight10 += Number(mic['cargoWeight']);
                  totalPiece10 += Number(mic['cargoPiece']);
                  totalPrice10 += Number(mic['priceVND']);
                  no10 += 1;
                } else {
                  arr8.push({ "no": no8, ...currentEntry });
                  totalWeight8 += Number(mic['cargoWeight']);
                  totalPiece8 += Number(mic['cargoPiece']);
                  totalPrice8 += Number(mic['priceVND']);
                  no8 += 1;
                }
              });
            }
            this.micExcel({
              "data8": { "arr8": arr8, "total8": { totalWeight8, totalPiece8, totalPrice8, taxPrice8: totalPrice8 * 0.08 } },
              "data10": { "arr10": arr10, "total10": { totalWeight10, totalPiece10, totalPrice10, taxPrice10: totalPrice10 * 0.1 } }
            }, excelName, subTitle);
          }
          pathTemplate = `../../../../../template/bangke/bangke.ejs`;
          label = `Danh sách hàng hóa nhập khẩu trị giá thấp (${classify}) đã được thông quan`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                  <tr>
                      <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                      <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                      <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                  </tr>
              </table>`,
            }
          }
        }
        if (printType == PrintKey.INVOICE_CARGO) {
          const name: string = `bangkehanghoa-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          excelName = `${name}.xlsx`;
          const arr: Record<string, any>[] = [];
          let i: number = 1;
          let display = i.toString();
          let cache = new Set();
          for (const key in obj) {
            obj[key].forEach((mic: Record<string, any>) => {
              if (mic['importDetails'].length > 0) {
                mic['importDetails'].forEach((detail: Record<string, any>) => {
                  arr.push({
                    "no": display,
                    "HAWBClearance": mic['HAWBClearance'],
                    "sender": `${mic['consignorName']} - ${mic['address1']}`,
                    "receiver": `${mic['importerFullName']} - ${mic['addressOfImporter']}`,
                    "cargoName": (itemLanguage == 'vi') ? detail['itemNameVN'] : detail['itemName'],
                    "cargoId": detail['HSCode'],
                    "origin": detail['placeOfOrigin'] ? detail['placeOfOrigin'] : detail['placeOfOriginCode'],
                    "cargoPiece": 1,
                    "invoice": Number(detail['invoiceValue']).toFixed(0),
                    "unitInvoice": detail['unitPriceCurrencyCode'],
                    "price": detail['unitPriceCurrencyCode'] == 'VND' ? Number(detail['invoiceValue']).toFixed(0) : (Number(mic['invoiceUnitPrice']) * Number(mic['manifest']['valueClearanceVND'])).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,"),
                    "note": "",
                  });
                  display = "";
                });
                if (!cache.has(mic['HAWBClearance'])) {
                  cache.add(mic['HAWBClearance']);
                  i++;
                  display = i.toString();
                }
              }
            });
          }
          this.exportExcel(arr, excelName);
          pathTemplate = `../../../../../template/bangke/bangkehanghoa.ejs`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                  <tr>
                      <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                      <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                      <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                  </tr>
              </table>`, // fallback value
            }
          }
        }
        if (printType == PrintKey.DETAIL_INVOICE) {
          const name: string = `bangkechitiethanghoa-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          pathTemplate = `../../../../../template/bangke/bangkechitiethanghoa.ejs`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                  <tr>
                      <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                      <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                      <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                  </tr>
              </table>`, // fallback value
            }
          }
        }
        if (printType == PrintKey.DETAIL_INVOICE_MIC_TT29) {
          const name: string = `bangkechitiethanghoa_tt29-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          pathTemplate = `../../../../../template/bangke/bangkechitietmic_tt29.ejs`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                <tr>
                  <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                  <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                  <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                </tr>
              </table>`, // fallback value
            }
          }
        }
        const createHistory = this.historyInvoiceService.create({
          MAWB: mawb,
          employeeId: employeeId,
          printType: printType,
          hubId: hubId,
          isSucess: false,
          dataSearch: value,
          note: value['note'],
        });
        const [historyInvoice, template] = await Promise.all([
          createHistory,
          ejs.renderFile(path.join(__dirname, pathTemplate), { MICs: obj, label, now, printType, hasFooter, itemLanguage, subTitle }, { async: true }),
        ]);
        this.pdfCreater({ template, config, filename, historyInvoice, excelName: excelName });
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = historyInvoice;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][pdfPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async ePDFPrint(value: any, hubs: any, employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const itemLanguage = (value['itemLanguage'] != undefined || value['itemLanguage'] != null) ? value['itemLanguage'] : 'vi';
      const hasFooter = (value['isHasFooter'] != undefined) ? value['isHasFooter'] : true;
      const inspectionKind = (value['inspectionKindClassification'] != undefined) ? value['inspectionKindClassification'] : 0;
      const printType: number = value['printType'];

      const idaOptional: Optional = new Optional();
      idaOptional.setRelation(["importDetails", "manifest"]);
      idaOptional.setAttributes(["MAWB", "hubId", "declarationNo", "HAWBClearance", "hubId", "cargoPiece", "notes", "cargoWeightGross", "priceVND", "importerCode", "importerFullName", "externalBoxName",
        "internalBoxName", "consignorName", "importerFullName", "address1", "addressOfImporter"]);
      idaOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      idaOptional.setOrderby([new OrderBy("createdAt", Configure.DESCENDING)]);
      if (value['classify']) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (printType == PrintKey.INVOICE_MIC && inspectionKind == 0) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, ActionKey.ACCEPT_CLEARANCE)
        );
      } else if (printType == PrintKey.INVOICE_MIC && inspectionKind != 0) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'inspectionKindClassification', EConfigure.ILIKE_START, `${inspectionKind}`)
        );
      }
      if (printType == PrintKey.INVOICE_CARGO && value['phase']) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
      }
      if (value['MAWB']) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        idaOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (value['dateCheckout']) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'dateCheckout', EConfigure.BETWEEN, value['dateCheckout'])
        );
      }
      if (value['dateCreate']) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'createdAt', EConfigure.BETWEEN, value['dateCreate'])
        );
      }
      if (hubs) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      let IDAs = await this.eMICRepository.queryAll(idaOptional);
      if (IDAs.length > 0) {
        let obj: any = {};
        let hubId: number = 0;
        let mawb: string = '';
        let mawbs: Set<string> = new Set();
        if (IDAs.length > 0 && (printType == PrintKey.INVOICE_MIC_TT29 || printType == PrintKey.INVOICE_MIC || printType == PrintKey.INVOICE_CARGO || printType == PrintKey.DETAIL_INVOICE || printType == PrintKey.DETAIL_INVOICE_MIC_TT29)) {
          hubId = IDAs[0]["hubId"]!;
          mawb = IDAs[0]["MAWB"];
          IDAs.forEach((ida: IDA) => {
            mawbs.add(ida["MAWB"])
          });
          IDAs = _.shuffle(IDAs);
          if (value['boxes'] && value['boxes'].length > 0) {
            const idaExternalBoxes = _.groupBy(IDAs, IDA => IDA['externalBoxName']);
            const idaInternalBoxes = _.groupBy(IDAs, IDA => IDA['internalBoxName']);
            value['boxes'].forEach((item: any) => {
              if (obj[item].length > 0) {
                obj[item].push(...idaExternalBoxes[item] ? idaExternalBoxes[item] : [], ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []);
              } else {
                obj[item] = [
                  ...idaExternalBoxes[item] ? idaExternalBoxes[item] : [],
                  ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []
                ];
              }
            })
          } else {
            IDAs.forEach((ida: IDA) => {
              let key: string = 'none';
              if (ida['externalBoxName']) {
                key = ida['externalBoxName'];
              } else if (ida['internalBoxName']) {
                key = ida['internalBoxName'];
              }
              if (obj[key]) {
                obj[key].push(ida);
              } else {
                obj[key] = [ida];
              }
            });
          }
        }
        const now: string = moment().format('DD-MM-YYYY');
        const config: pdf.CreateOptions = {
          "format": "A4",
          "orientation": "portrait",
          "border": {
            "top": "8px",
            "right": "10px",
            "bottom": "20px",
            "left": "10px"
          },
          "timeout": 300000,
        }
        let label: string = '';
        let pathTemplate: string = '';
        let filename: string = '';
        let excelName: string = '';
        const subTitle = inspectionKind == 2 ? '(Luồng Vàng)' : inspectionKind == 3 ? '(Luồng Đỏ)' : '';
        if (printType == PrintKey.INVOICE_MIC_TT29) {
          let classify: string = 'MIC_TT29';
          label = `Danh sách hàng hóa nhập khẩu trị giá thấp`;
          const name: string = `bangke${classify}-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          excelName = `${name}.xlsx`;
          const arr10: Record<string, any>[] = [];
          const arr8: Record<string, any>[] = [];
          let no10 = 1;
          let totalWeight10 = 0;
          let totalPiece10 = 0;
          let totalPrice10 = 0;
          let no8 = 1;
          let totalWeight8 = 0;
          let totalPiece8 = 0;
          let totalPrice8 = 0;
          for (const key in obj) {
            if (obj[key].length == 0) {
              continue;
            }
            obj[key].forEach((mic: eIMIC) => {
              const itemNameVN: string[] = [];
              if (mic['importDetails']!.length > 0) {
                mic['importDetails']!.forEach((detail: Record<string, any>) => {
                  itemNameVN.push(String(detail['itemNameVN']));
                });
              }
              const currentEntry = {
                "declarationNo": mic['declarationNo'] != null ? mic['declarationNo'] : "",
                "HAWBClearance": mic['HAWBClearance'],
                "cargoName": itemNameVN.join(','),
                "unitWeight": 'KGM',
                "weight": (mic['cargoWeightGross']! > 0) ? Number(mic['cargoWeightGross']).toFixed(2) : 0,
                "cargoPiece": mic['cargoPiece'],
                "price": (mic['priceVND']! > 0) ? Number(mic['priceVND']).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0,
                "note": mic['notes'],
                "boxName": mic['externalBoxName'],
              };
              if (mic['percentTaxPrint'] == null || mic['percentTaxPrint'] === '10') {
                arr10.push({ "no": no10, ...currentEntry });
                totalWeight10 += Number(mic['cargoWeightGross']);
                totalPiece10 += Number(mic['cargoPiece']);
                totalPrice10 += Number(mic['priceVND']);
                no10 += 1;
              } else {
                arr8.push({ "no": no8, ...currentEntry });
                totalWeight8 += Number(mic['cargoWeightGross']);
                totalPiece8 += Number(mic['cargoPiece']);
                totalPrice8 += Number(mic['priceVND']);
                no8 += 1;
              }
            });
          }
          this.excelTT29({
            "data8": { "arr8": arr8, "total8": { totalWeight8, totalPiece8, totalPrice8, taxPrice8: totalPrice8 * 0.08 } },
            "data10": { "arr10": arr10, "total10": { totalWeight10, totalPiece10, totalPrice10, taxPrice10: totalPrice10 * 0.1 } }
          }, excelName, label, subTitle);
          pathTemplate = `../../../../../template/bangke/bangke_emic_tt29.ejs`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `
              <table width="100%;" style="font-size: 8px;">
                <tr>
                  <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                  <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                  <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                </tr>
              </table>`,
            }
          }
        }
        if (printType == PrintKey.DETAIL_INVOICE_MIC_TT29) {
          const name: string = `bangkechitiethanghoa-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          pathTemplate = `../../../../../template/bangke/bangkechitietemic_tt29.ejs`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                <tr>
                  <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                  <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                  <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                </tr>
              </table>`, // fallback value
            }
          }
        }
        const createHistory = this.historyInvoiceService.create({
          MAWB: mawb,
          employeeId: employeeId,
          printType: printType,
          hubId: hubId,
          isSucess: false,
          dataSearch: value,
          note: value['note'],
        });
        const [historyInvoice, template] = await Promise.all([
          createHistory,
          ejs.renderFile(path.join(__dirname, pathTemplate), { MICs: obj, label, now, printType, hasFooter, itemLanguage, subTitle }, { async: true }),
        ]);
        this.pdfCreater({ template, config, filename, historyInvoice, excelName: excelName });

        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = historyInvoice;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][pdfPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async pdfPickupPrint(value: any, hubs: any, employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const printType: number = value['printType'];
      const MAWB: string = value['MAWB'];
      const no: string = value['no'];
      const ola: string = value['ola'];
      const date: string = value['date'];
      const isLandscape: boolean = value['isLandscape'] || false;
      const clearanceName: string = value['clearanceName'];
      const micOptional: Optional = new Optional();
      const idaOptional: Optional = new Optional();
      const edaOptional: Optional = new Optional();
      const mecOptional: Optional = new Optional();
      const shipmentOptional: Optional = new Optional();

      shipmentOptional.setAttributes(["weightTotal", "boxIds"]);
      shipmentOptional.setWhere([
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB)
      ]);

      micOptional.setRelation(["importDetailItems"]);
      micOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, "cargoPiece", "hubId", "declarationNo"]);
      micOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB)
      ]);

      idaOptional.setRelation(["importDetailItems"]);
      idaOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, "cargoPiece", "hubId", "declarationNo"]);
      idaOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB)
      ]);
      if (hubs) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      if (value['phase']) {
        micOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
      }

      if (value['boxes'] && value['boxes'].length > 0) {
        micOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
        idaOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }

      edaOptional.setRelation(["exportDetailItems"]);
      edaOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, "cargoPiece", "hubId", "declarationNo"]);
      edaOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB)
      ]);
      if (hubs) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
        edaOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }

      mecOptional.setRelation(["exportDetailItems"]);
      mecOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, "cargoPiece", "hubId", "declarationNo"]);
      mecOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB)
      ]);
      if (value['phase']) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
        edaOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
      }

      if (value['boxes'] && value['boxes'].length > 0) {
        mecOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
        edaOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      // ida
      const [MICs, IDAs, MECs, EDAs, shipment] = await Promise.all([
        this.micRepository.queryAll(micOptional),
        this.idaRepository.queryAll(idaOptional),
        this.edaRepository.queryAll(edaOptional),
        this.mecRepository.queryAll(mecOptional),
        this.shipmentRepository.getOneOptional(shipmentOptional),
      ]);
      if (MICs.length > 0 || IDAs.length > 0 || MECs.length > 0 || EDAs.length > 0) {
        let HAWBs: any = [];
        let isImport: boolean = true;
        let weight: number = 0;
        let boxes: number = 0;
        if (MICs.length > 0 || IDAs.length > 0) {
          if (MICs.length > 0) {
            HAWBs = [...HAWBs, ...MICs];
          }
          if (IDAs.length > 0) {
            HAWBs = [...HAWBs, ...IDAs];
          }
          isImport = true;
        }
        if (MECs.length > 0 || EDAs.length > 0) {
          if (MECs.length > 0) {
            HAWBs = [...HAWBs, ...MECs];
          }
          if (EDAs.length > 0) {
            HAWBs = [...HAWBs, ...EDAs];
          }
          isImport = false;
        }
        HAWBs = _.shuffle(HAWBs);
        if (shipment && shipment['weightTotal'] > 0) {
          weight = shipment['weightTotal'];
        }
        if (shipment && shipment['boxIds'] && shipment['boxIds'].length > 0) {
          boxes = shipment['boxIds'].length;
        }
        const config: pdf.CreateOptions = {
          "format": "A4",        // allowed units: A3, A4, A5, Legal, Letter, Tabloid
          "orientation": isLandscape ? "landscape" : "portrait",
          "border": {
            "top": "8px",            // default is 0, units: mm, cm, in, px
            "right": "10px",
            "bottom": "20px",
            "left": "10px"
          },
          "base": 'file:///home/<USER>/Public/projects/clearance/template/',
          "timeout": 300000
        }
        const hubId: number = HAWBs[0]["hubId"];
        let hub: any = null;
        if (hubId > 0) {
          hub = await this.hubRepository.getOne(hubId);
        }

        let pathTemplate: string = `../../../../../template/bangke/bangkelayhang1.ejs`;
        let rootFileName: string = 'bangkelayhang_1';
        if (printType == PrintKey.PICK_UP_2) {
          rootFileName = 'bangkelayhang_2';
          pathTemplate = `../../../../../template/bangke/bangkelayhang2.ejs`;
        }
        const name: String = `${rootFileName}-${MAWB}_${moment().format("X")}`;
        const filename = `${name}.pdf`;
        const createHistory = this.historyInvoiceService.create({
          MAWB: MAWB,
          employeeId: employeeId,
          printType: printType,
          hubId: hubId,
          isSucess: false,
          dataSearch: value,
          note: null,
        });
        const imgSrc = path.join('file://', __dirname, '../../../../../template/image/signature.jpg');
        const [historyInvoice, template] = await Promise.all([
          createHistory,
          ejs.renderFile(path.join(__dirname, pathTemplate),
            {
              HAWBs,
              "total": HAWBs.length,
              "companyName": hub ? hub['fullname'] : null,
              "isImport": isImport,
              "weight": weight,
              "boxes": boxes,
              "no": no,
              "ola": ola,
              "date": date,
              "clearanceName": clearanceName,
              "imgSrc": imgSrc
            },
            { async: true })
        ]);
        if (printType == PrintKey.PICK_UP_2) {
          const arr: Record<string, any>[] = [];
          let totalPiece = 0;
          let totalWeight = '';
          let companyName: string = (hub != null && hub['fullname']) ? hub['fullname'] : 'Tên doanh nghiệp: .......................';
          let noData: string = no != null ? no : '..........................';
          let olaData: string = ola != null ? ola : '.................................';
          let dateData: string = date != null ? date : '.................................';
          let clearanceNameData: string = clearanceName != null ? clearanceName : '.................................';
          HAWBs.forEach((item: Record<string, any>, index: number) => {
            const itemNameVN: String[] = [];
            if (item['exportDetailItems'] && item['exportDetailItems'].length > 0) {
              item['exportDetailItems'].forEach((detail: Record<string, any>) => {
                itemNameVN.push(String(detail['itemNameVN']));
              });
            }

            if (item['importDetailItems'] && item['importDetailItems'].length > 0) {
              item['importDetailItems'].forEach((detail: Record<string, any>) => {
                itemNameVN.push(String(detail['itemNameVN']));
              });
            }
            arr.push({
              "no": index + 1,
              "HAWBClearance": `${item['HAWBClearance']} ${item['declarationNo'] != null ? `\r\n${item['declarationNo']}` : ""}`,
              "cargoName": itemNameVN.join(','),
              "cargoPiece": 1,
            });
            totalPiece += Number(item['cargoPiece']);
          });

          if (weight > 0 && boxes > 0) {
            totalWeight = `(${boxes} kiện, ${weight} Kgs)`;
          } if (weight == 0 && boxes > 0) {
            totalWeight = `(${boxes} kiện)`;
          } if (weight > 0 && boxes == 0) {
            totalWeight = `(${weight} Kgs)`;
          }
          this.pickUp2Excel(arr, { totalPiece, totalWeight, companyName, noData, olaData, dateData, clearanceNameData }, `${name}.xlsx`);
        }
        this.pdfCreater({ template, config, filename, historyInvoice, excelName: '' });
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = historyInvoice;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][pdfPickupPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async ePDFPickupPrint(value: any, hubs: any, employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const printType: number = value['printType'];
      const MAWB: string = value['MAWB'];
      const no: string = value['no'];
      const ola: string = value['ola'];
      const date: string = value['date'];
      const clearanceName: string = value['clearanceName'];
      const idaOptional: Optional = new Optional();
      const shipmentOptional: Optional = new Optional();
      shipmentOptional.setAttributes(["weightTotal", "boxIds"]);
      shipmentOptional.setWhere([
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB)
      ]);
      idaOptional.setRelation(["importDetailItems"]);
      idaOptional.setAttributes([EConfigure.HAWB_CLEARANCE_FIELD, "cargoPiece", "hubId", "declarationNo"]);
      idaOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, MAWB)
      ]);
      if (hubs) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      if (value['phase']) {
        idaOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
      }

      if (value['boxes'] && value['boxes'].length > 0) {
        idaOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      // ida
      const [IDAs, shipment] = await Promise.all([
        this.eMICRepository.queryAll(idaOptional),
        this.shipmentRepository.getOneOptional(shipmentOptional),
      ]);
      if (IDAs.length > 0) {
        let HAWBs: any = [];
        let isImport: boolean = true;
        let weight: number = 0;
        let boxes: number = 0;
        if (IDAs.length > 0) {
          if (IDAs.length > 0) {
            HAWBs = [...HAWBs, ...IDAs];
          }
          isImport = true;
        }

        HAWBs = _.shuffle(HAWBs);
        if (shipment && shipment['weightTotal'] > 0) {
          weight = shipment['weightTotal'];
        }
        if (shipment && shipment['boxIds'] && shipment['boxIds'].length > 0) {
          boxes = shipment['boxIds'].length;
        }
        const config: pdf.CreateOptions = {
          "format": "A4",        // allowed units: A3, A4, A5, Legal, Letter, Tabloid
          "orientation": "portrait",
          "border": {
            "top": "8px",            // default is 0, units: mm, cm, in, px
            "right": "10px",
            "bottom": "20px",
            "left": "10px"
          },
          "base": 'file:///home/<USER>/Public/projects/clearance/template/',
          "timeout": 300000
        }
        const hubId: number = HAWBs[0]["hubId"];
        let hub: any = null;
        if (hubId > 0) {
          hub = await this.hubRepository.getOne(hubId);
        }

        let pathTemplate: string = `../../../../../template/bangke/bangkelayhang1.ejs`;
        let rootFileName: string = 'bangkelayhang_1';
        if (printType == PrintKey.PICK_UP_2) {
          rootFileName = 'bangkelayhang_2';
          pathTemplate = `../../../../../template/bangke/bangkelayhang2.ejs`;
        }
        const name: String = `${rootFileName}-${MAWB}_${moment().format("X")}`;
        const filename = `${name}.pdf`;
        const createHistory = this.historyInvoiceService.create({
          MAWB: MAWB,
          employeeId: employeeId,
          printType: printType,
          hubId: hubId,
          isSucess: false,
          dataSearch: value,
          note: null,
        });
        const imgSrc = path.join('file://', __dirname, '../../../../../template/image/signature.jpg');
        const [historyInvoice, template] = await Promise.all([
          createHistory,
          ejs.renderFile(path.join(__dirname, pathTemplate),
            {
              HAWBs,
              "total": HAWBs.length,
              "companyName": hub ? hub['fullname'] : null,
              "isImport": isImport,
              "weight": weight,
              "boxes": boxes,
              "no": no,
              "ola": ola,
              "date": date,
              "clearanceName": clearanceName,
              "imgSrc": imgSrc
            },
            { async: true })
        ]);
        if (printType == PrintKey.PICK_UP_2) {
          const arr: Record<string, any>[] = [];
          let totalPiece = 0;
          let totalWeight = '';
          let companyName: string = (hub != null && hub['fullname']) ? hub['fullname'] : 'Tên doanh nghiệp: .......................';
          let noData: string = no != null ? no : '..........................';
          let olaData: string = ola != null ? ola : '.................................';
          let dateData: string = date != null ? date : '.................................';
          let clearanceNameData: string = clearanceName != null ? clearanceName : '.................................';
          HAWBs.forEach((item: Record<string, any>, index: number) => {
            const itemNameVN: String[] = [];
            if (item['exportDetailItems'] && item['exportDetailItems'].length > 0) {
              item['exportDetailItems'].forEach((detail: Record<string, any>) => {
                itemNameVN.push(String(detail['itemNameVN']));
              });
            }

            if (item['importDetailItems'] && item['importDetailItems'].length > 0) {
              item['importDetailItems'].forEach((detail: Record<string, any>) => {
                itemNameVN.push(String(detail['itemNameVN']));
              });
            }
            arr.push({
              "no": index + 1,
              "HAWBClearance": `${item['HAWBClearance']} ${item['declarationNo'] != null ? `\r\n${item['declarationNo']}` : ""}`,
              "cargoName": itemNameVN.join(','),
              "cargoPiece": 1,
            });
            totalPiece += Number(item['cargoPiece']);
          });

          if (weight > 0 && boxes > 0) {
            totalWeight = `(${boxes} kiện, ${weight} Kgs)`;
          } if (weight == 0 && boxes > 0) {
            totalWeight = `(${boxes} kiện)`;
          } if (weight > 0 && boxes == 0) {
            totalWeight = `(${weight} Kgs)`;
          }
          this.pickUp2Excel(arr, { totalPiece, totalWeight, companyName, noData, olaData, dateData, clearanceNameData }, `${name}.xlsx`);
        }
        this.pdfCreater({ template, config, filename, historyInvoice, excelName: '' });
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = historyInvoice;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][ePDFPickupPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async pickUp2Excel(arr: Record<string, any>, summary: Record<string, any>, filename: string): Promise<void> {
    try {
      const alignment = { vertical: 'middle', horizontal: 'center' };
      const fontBold = { bold: true, name: 'Calibri' };
      const fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '96C8FB' },
        bgColor: { argb: '96C8FB' },
      };
      const border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(`sheet ${EConfigure.INDEX_1}`, {
        properties: {
          tabColor: { argb: 'FFC0000' }
        },
      });
      // Title Center
      let titleCenter = worksheet.getCell(`D1`);
      titleCenter.value = `Phụ lục 2`;
      titleCenter.alignment = alignment;
      titleCenter.font = fontBold;

      // Title Left
      let titleFirst = worksheet.getCell(`B2`);
      titleFirst.value = `1. ${summary['companyName']}`;
      titleFirst.font = fontBold;

      const titleSecond = worksheet.getCell(`B3`);
      titleSecond.value = `2. Số: ${summary['noData']}`;
      titleSecond.font = fontBold;

      // Title Right
      const templateCell = worksheet.getCell('H2');
      templateCell.value = "......., ngày.......tháng.........năm.........";
      templateCell.alignment = alignment;
      templateCell.font = fontBold;

      // Title Center
      const title = worksheet.getCell('D5');
      title.value = "BẢNG KÊ VẬN ĐƠN/TỜ KHAI XUẤT KHẨU";
      title.alignment = alignment;
      title.font = fontBold;

      // Content Data
      worksheet.mergeCells('B7', 'G7');
      const title1 = worksheet.getCell('B7');
      title1.value = `3. Kèm theo Tờ khai hải quan vận chuyển số ${summary['olaData']} ${summary['dateData']}, đăng ký tại Chi cục Hải quan ${summary['clearanceNameData']}`;
      title1.font = fontBold;

      worksheet.mergeCells('B8', 'G8');
      const title2 = worksheet.getCell('B8');
      title2.value = "4. Số lượng phương tiện vận chuyển hàng hóa thuộc tờ khai.......................................................................................................";
      title2.font = fontBold;

      const stt = worksheet.getCell('A9');
      stt.value = "STT";
      stt.alignment = alignment;
      stt.font = fontBold;
      stt.fill = fill;
      stt.alignment = alignment;
      stt.border = border;
      const noStt = worksheet.getCell('A10');
      noStt.value = "(5)";
      noStt.alignment = alignment;
      noStt.font = fontBold;
      noStt.fill = fill;
      noStt.alignment = alignment;
      noStt.border = border;

      const declarationNo = worksheet.getCell('B9');
      declarationNo.value = "Số vận đơn/Số tờ khai xuất khẩu";
      declarationNo.alignment = alignment;
      declarationNo.font = fontBold;
      declarationNo.fill = fill;
      declarationNo.alignment = alignment;
      declarationNo.border = border;
      const noDeclarationNo = worksheet.getCell('B10');
      noDeclarationNo.value = "(6)";
      noDeclarationNo.alignment = alignment;
      noDeclarationNo.font = fontBold;
      noDeclarationNo.fill = fill;
      noDeclarationNo.alignment = alignment;
      noDeclarationNo.border = border;

      const hawb = worksheet.getCell('C9');
      hawb.value = "Tên hàng";
      hawb.alignment = alignment;
      hawb.font = fontBold;
      hawb.fill = fill;
      hawb.alignment = alignment;
      hawb.border = border;
      const noHawb = worksheet.getCell('C10');
      noHawb.value = "(7)";
      noHawb.alignment = alignment;
      noHawb.font = fontBold;
      noHawb.fill = fill;
      noHawb.alignment = alignment;
      noHawb.border = border;

      const cargoName = worksheet.getCell('D9');
      cargoName.value = "Số hiệu Container, số kiện, gói";
      cargoName.alignment = alignment;
      cargoName.font = fontBold;
      cargoName.fill = fill;
      cargoName.alignment = alignment;
      cargoName.border = border;
      const noCargoName = worksheet.getCell('D10');
      noCargoName.value = "(8)";
      noCargoName.alignment = alignment;
      noCargoName.font = fontBold;
      noCargoName.fill = fill;
      noCargoName.alignment = alignment;
      noCargoName.border = border;

      const cargo = worksheet.getCell('E9');
      cargo.value = "Số chì hãng vận chuyển";
      cargo.alignment = alignment;
      cargo.font = fontBold;
      cargo.fill = fill;
      cargo.alignment = alignment;
      cargo.border = border;
      const noCargo = worksheet.getCell('E10');
      noCargo.value = "(9)";
      noCargo.alignment = alignment;
      noCargo.font = fontBold;
      noCargo.fill = fill;
      noCargo.alignment = alignment;
      noCargo.border = border;

      const weight = worksheet.getCell('F9');
      weight.value = "Số hiệu niêm phong hải quan";
      weight.alignment = alignment;
      weight.font = fontBold;
      weight.fill = fill;
      weight.alignment = alignment;
      weight.border = border;
      const noWeight = worksheet.getCell('F10');
      noWeight.value = "(10)";
      noWeight.alignment = alignment;
      noWeight.font = fontBold;
      noWeight.fill = fill;
      noWeight.alignment = alignment;
      noWeight.border = border;

      const cargoPiece = worksheet.getCell('G9');
      cargoPiece.value = "Nội dung sửa đổi bổ sung";
      cargoPiece.alignment = alignment;
      cargoPiece.font = fontBold;
      cargoPiece.fill = fill;
      cargoPiece.alignment = alignment;
      cargoPiece.border = border;
      const noCargoPiece = worksheet.getCell('G10');
      noCargoPiece.value = "(11)";
      noCargoPiece.alignment = alignment;
      noCargoPiece.font = fontBold;
      noCargoPiece.fill = fill;
      noCargoPiece.alignment = alignment;
      noCargoPiece.border = border;

      const price = worksheet.getCell('H9');
      price.value = "Xác nhận sửa đổi của công chức";
      price.alignment = alignment;
      price.font = fontBold;
      price.fill = fill;
      price.alignment = alignment;
      price.border = border;
      const noPrice = worksheet.getCell('H10');
      noPrice.value = "(12)";
      noPrice.alignment = alignment;
      noPrice.font = fontBold;
      noPrice.fill = fill;
      noPrice.alignment = alignment;
      noPrice.border = border;

      const note = worksheet.getCell('I9');
      note.value = "Số PTVC hàng hóa";
      note.alignment = alignment;
      note.font = fontBold;
      note.fill = fill;
      note.alignment = alignment;
      note.border = border;
      const noNote = worksheet.getCell('I10');
      noNote.value = "(13)";
      noNote.alignment = alignment;
      noNote.font = fontBold;
      noNote.fill = fill;
      noNote.alignment = alignment;
      noNote.border = border;

      worksheet.columns = [
        { key: "no", width: EConfigure.INDEX_5 },
        { key: "HAWBClearance", width: EConfigure.INDEX_40 },
        { key: "cargoName", width: EConfigure.INDEX_20 },
        { key: "cargoPiece", width: EConfigure.INDEX_20 },
      ];
      arr.forEach(function (item: Record<string, any>) {
        worksheet.addRow({
          no: item.no,
          HAWBClearance: item.HAWBClearance,
          cargoName: item.cargoName,
          cargoPiece: item.cargoPiece,
        });
      });

      const total: number = arr.length;
      const summaryTotal = worksheet.getCell(`A${total + EConfigure.INDEX_11}`);
      summaryTotal.value = "Tổng cộng:";
      summaryTotal.alignment = alignment;
      summaryTotal.font = fontBold;
      summaryTotal.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D6EAF8' },
        bgColor: { argb: 'D6EAF8' },
      };
      summaryTotal.alignment = alignment;
      summaryTotal.border = border;

      const summaryItem = worksheet.getCell(`B${total + EConfigure.INDEX_11}`);
      summaryItem.value = summary['totalPiece'];
      summaryItem.alignment = alignment;
      summaryItem.font = fontBold;
      summaryItem.border = border;
      summaryItem.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D6EAF8' },
        bgColor: { argb: 'D6EAF8' },
      };

      worksheet.mergeCells(`C${total + EConfigure.INDEX_11}`, `I${total + EConfigure.INDEX_11}`);
      const summaryWeight = worksheet.getCell(`C${total + EConfigure.INDEX_11}`);
      summaryWeight.value = `${summary['totalWeight']}`;
      summaryWeight.alignment = alignment;
      summaryWeight.font = fontBold;
      summaryWeight.border = border;
      summaryWeight.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D6EAF8' },
        bgColor: { argb: 'D6EAF8' },
      };


      // Footer
      // Signature Left
      const signatureLeft = total + EConfigure.INDEX_15;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).value = `14. Đại diện Doanh nghiệp kê khai`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).value = `(Ký tên, đóng dấu)`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).font = {
        italic: true
      };

      const imageId = workbook.addImage({
        filename: path.join(__dirname, '../../../../../template/signature2.jpg'),
        extension: 'jpeg',
      });

      worksheet.addImage(imageId, {
        tl: { col: 1, row: total + EConfigure.INDEX_15 },
        ext: { width: 300, height: 300 },
      });

      // Signature center
      const signatureCenter = total + EConfigure.INDEX_15;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_1}`).value = `15. Xác nhận của CCHQ nơi đi`;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_2}`).value = `Tên chi cục Hải quan nơi đi`;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_2}`).font = fontBold;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_3}`).value = `(Ký tên, đóng dấu công chức)`;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_3}`).alignment = alignment;
      worksheet.getCell(`E${signatureCenter + EConfigure.INDEX_3}`).font = {
        italic: true
      };

      // Signature right
      const signatureRigth = total + EConfigure.INDEX_15;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_1}`).value = `16. Xác nhận của CCHQ nơi đến`;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_2}`).value = `Tên chi cục Hải quan nơi đến`;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_2}`).font = fontBold;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_3}`).value = `(ký, đóng dấu xác nhận)`;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_3}`).alignment = alignment;
      worksheet.getCell(`I${signatureRigth + EConfigure.INDEX_3}`).font = {
        italic: true
      };

      // NOTE
      const footer = total + EConfigure.INDEX_30;
      worksheet.getCell(`B${footer + EConfigure.INDEX_1}`).value = `Ghi chú:`;
      worksheet.getCell(`B${footer + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`B${footer + EConfigure.INDEX_2}`).value = `1. Phần doanh nghiệp khai:`;
      worksheet.getCell(`B${footer + EConfigure.INDEX_2}`).font = fontBold;
      worksheet.getCell(`B${footer + EConfigure.INDEX_3}`).value = `(1): Tên của doanh nghiệp vận chuyển.`;
      worksheet.getCell(`B${footer + EConfigure.INDEX_4}`).value = `(2): Số của Bảng kê vận đơn/Tờ khai xuất khẩu tối đa 35 ký tự do doanh nghiệp lập để quản lý.`;
      worksheet.getCell(`B${footer + EConfigure.INDEX_5}`).value = `(3): Số, ngày tờ khai vận chuyển độc lập và tên Chi cục Hải quan mà Bảng kê hàng hóa cần khai báo.`;

      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}${filename}`)
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][pickUp2Excel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async pdfCreater(objCreate: any): Promise<void> {
    try {
      pdf.create(objCreate['template'], objCreate['config']).toFile(`${EConfigure.FOLDER_INVOICE}/${objCreate['filename']}`, function (err, res) {
        if (res) {
          if (objCreate['historyInvoice']) {
            const historyInvoice: HistoryInvoice = objCreate['historyInvoice'];
            historyInvoice.isSucess = true;
            historyInvoice.successDate = moment().format(EConfigure.FULL_TIME);
            historyInvoice.link = `${new Config().clearanceDomain()}/clearance/invoice/${objCreate['filename']}`;
            historyInvoice.excelLink = objCreate['excelName'] != '' ? `${new Config().clearanceDomain()}/clearance/invoice/${objCreate['excelName']}` : '';
            historyInvoice.save();
          }
        }
        if (err) {
          Utilities.sendDiscordErr('[service][PdfGenerate][create][pdfPrint]', err.toString());
        }
      });
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][pdfCreater]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async ibcExport(value: any, hubs: any, employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      const printType: number = PrintKey.IBCExport;
      const mecOptional: Optional = new Optional();
      const edaOptional: Optional = new Optional();
      mecOptional.setRelation(["exportDetails", "manifest"]);
      mecOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      mecOptional.setOrderby([new OrderBy("createdAt", Configure.DESCENDING)]);
      edaOptional.setRelation(["exportDetails", "manifest"]);
      edaOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      edaOptional.setOrderby([new OrderBy("createdAt", Configure.DESCENDING)]);
      if (hubs && hubs.length > 0) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.join())
        );
        edaOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.join())
        );
      }
      if (value['codes'] && value['codes'].length > 0) {
        mecOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['codes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['codes'].join()),
          new Where(EConfigure.OR, 'MAWB', EConfigure.IN, value['codes'].join()),
          new Where(EConfigure.OR, 'HAWB', EConfigure.IN, value['codes'].join()),
          new Where(EConfigure.OR, 'HAWBClearance', EConfigure.IN, value['codes'].join()),
        );
        edaOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['codes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['codes'].join()),
          new Where(EConfigure.OR, 'MAWB', EConfigure.IN, value['codes'].join()),
          new Where(EConfigure.OR, 'HAWB', EConfigure.IN, value['codes'].join()),
          new Where(EConfigure.OR, 'HAWBClearance', EConfigure.IN, value['codes'].join()),
        );
      }

      let [MECs, EDAs] = await Promise.all([
        this.mecRepository.queryAll(mecOptional),
        this.edaRepository.queryAll(edaOptional)
      ]);
      if (MECs.length > 0 || EDAs.length > 0) {
        const name: string = `MANIFEST_CHUYEN_TUYEN__${moment().format(EConfigure.FULL_TIME)}.xlsx`;
        const arr: IBCExport[] = [];
        let i: number = 1;
        let display = i.toString();
        MECs.forEach((mec: MEC) => {
          if (mec['exportDetails']!.length > 0) {
            mec['exportDetails']!.forEach((detail: ExportDetail) => {
              arr.push({
                "no": display,
                "HAWBClearance": mec['HAWBClearance']!,
                "bagId": mec['internalBoxName'] ? mec['internalBoxName'] : mec['externalBoxName']!,
                "MAWB": mec['MAWB'],
                "orderId": mec['manifest']!['orderNumberClient']!,
                "refId": mec['manifest']!['orderNumber']!,
                "bagWeight": `${detail['weightKG']}`,
                "shipperWeight": '',
                "processedWeight": "",
                "sender": mec['exporterFullName'],
                "senderAddress": mec['addressOfExporter'],
                "receiver": mec['consigneeName'],
                "receiverPhone": mec['consigneeTelephoneNumber'],
                "receiverEmail": "",
                "receiverAddress": mec['address1'],
                "currency": `${detail['unitPriceCurrencyCode']}`,
                "totalValue": Number(detail['invoiceValue']).toFixed(0),
                "itemCustomDes": "",
                "productName": detail['itemName']!,
                "productNameVn": detail['itemNameVN']!,
                "itemQty": `${detail['quantity1']!}`,
                "unitValue": `${detail['invoiceUnitPrice']}`,
                "origin": detail['placeOfOriginCode']!,
                "hsCode": detail['HSCode']!,
                "itemUrl": "",
                "itemSkuId": ""
              });
              display = "";
            });
            i++;
            display = i.toString();
          }
        });
        EDAs.forEach((mec: EDA) => {
          if (mec['exportDetails']!.length > 0) {
            mec['exportDetails']!.forEach((detail: ExportDetail) => {
              arr.push({
                "no": display,
                "HAWBClearance": mec['HAWBClearance'],
                "bagId": mec['internalBoxName'] ? mec['internalBoxName'] : mec['externalBoxName']!,
                "MAWB": mec['MAWB'],
                "orderId": mec['manifest']!['orderNumberClient']!,
                "refId": mec['manifest']!['orderNumber']!,
                "bagWeight": `${detail['weightKG']}`,
                "shipperWeight": '',
                "processedWeight": "",
                "sender": mec['exporterFullName'],
                "senderAddress": mec['addressOfExporter'],
                "receiver": mec['consigneeName'],
                "receiverPhone": mec['consigneeTelephoneNumber'],
                "receiverEmail": "",
                "receiverAddress": mec['address1'],
                "currency": detail['unitPriceCurrencyCode']!,
                "totalValue": Number(detail['invoiceValue']).toFixed(0),
                "itemCustomDes": "",
                "productName": detail['itemName']!,
                "productNameVn": detail['itemNameVN']!,
                "itemQty": `${detail['quantity1']}`,
                "unitValue": `${detail['invoiceUnitPrice']}`,
                "origin": detail['placeOfOriginCode']!,
                "hsCode": detail['HSCode']!,
                "itemUrl": "",
                "itemSkuId": ""
              });
              display = "";
            });
            i++;
            display = i.toString();
          }
        });
        const history = await this.historyInvoiceService.create({
          MAWB: '',
          employeeId: employeeId,
          printType: printType,
          hubId: hubs,
          isSucess: false,
          dataSearch: value,
          note: value['note'],
        });
        this.ibcExcel(arr, name, history);
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = history;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][ibcExport]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async ibcExcel(arr: IBCExport[], filename: string, history: HistoryInvoice): Promise<void> {
    try {
      const alignment = { vertical: 'middle', horizontal: 'center' };
      const fontBold = { bold: true, name: 'Calibri' };
      const fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '96C8FB' },
        bgColor: { argb: '96C8FB' },
      };
      const border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(`sheet ${EConfigure.INDEX_1}`, {
        properties: {
          tabColor: { argb: 'FFC0000' }
        },
      });
      const stt = worksheet.getCell('A1');
      stt.value = "S/N";
      stt.alignment = alignment;
      stt.font = fontBold;
      stt.fill = fill;
      stt.alignment = alignment;
      stt.border = border;

      const HAWBNo = worksheet.getCell('B1');
      HAWBNo.value = "HAWB";
      HAWBNo.alignment = alignment;
      HAWBNo.font = fontBold;
      HAWBNo.fill = fill;
      HAWBNo.alignment = alignment;
      HAWBNo.border = border;

      const bagId = worksheet.getCell('C1');
      bagId.value = "BAG_ID";
      bagId.alignment = alignment;
      bagId.font = fontBold;
      bagId.fill = fill;
      bagId.alignment = alignment;
      bagId.border = border;

      const MAWB = worksheet.getCell('D1');
      MAWB.value = "MAWB";
      MAWB.alignment = alignment;
      MAWB.font = fontBold;
      MAWB.fill = fill;
      MAWB.alignment = alignment;
      MAWB.border = border;

      const orderId = worksheet.getCell('E1');
      orderId.value = "Order ID";
      orderId.alignment = alignment;
      orderId.font = fontBold;
      orderId.fill = fill;
      orderId.alignment = alignment;
      orderId.border = border;

      const refId = worksheet.getCell('F1');
      refId.value = "Reference ID";
      refId.alignment = alignment;
      refId.font = fontBold;
      refId.fill = fill;
      refId.alignment = alignment;
      refId.border = border;

      const bagWeight = worksheet.getCell('G1');
      bagWeight.value = "Bag_Weight";
      bagWeight.alignment = alignment;
      bagWeight.font = fontBold;
      bagWeight.fill = fill;
      bagWeight.alignment = alignment;
      bagWeight.border = border;

      const shipWeight = worksheet.getCell('H1');
      shipWeight.value = "Shipper_Weight";
      shipWeight.alignment = alignment;
      shipWeight.font = fontBold;
      shipWeight.fill = fill;
      shipWeight.alignment = alignment;
      shipWeight.border = border;

      const processWeight = worksheet.getCell('I1');
      processWeight.value = "Processed_Weight";
      processWeight.alignment = alignment;
      processWeight.font = fontBold;
      processWeight.fill = fill;
      processWeight.alignment = alignment;
      processWeight.border = border;

      const senderName = worksheet.getCell('J1');
      senderName.value = "Sender_Name";
      senderName.alignment = alignment;
      senderName.font = fontBold;
      senderName.fill = fill;
      senderName.alignment = alignment;
      senderName.border = border;

      const senderAddress = worksheet.getCell('K1');
      senderAddress.value = "Sender_Address";
      senderAddress.alignment = alignment;
      senderAddress.font = fontBold;
      senderAddress.fill = fill;
      senderAddress.alignment = alignment;
      senderAddress.border = border;

      const consigneeName = worksheet.getCell('L1');
      consigneeName.value = "Consignee_Name";
      consigneeName.alignment = alignment;
      consigneeName.font = fontBold;
      consigneeName.fill = fill;
      consigneeName.alignment = alignment;
      consigneeName.border = border;

      const consigneeContact = worksheet.getCell('M1');
      consigneeContact.value = "Consignee_Contact";
      consigneeContact.alignment = alignment;
      consigneeContact.font = fontBold;
      consigneeContact.fill = fill;
      consigneeContact.alignment = alignment;
      consigneeContact.border = border;

      const consigneeEmail = worksheet.getCell('N1');
      consigneeEmail.value = "Consignee_Email";
      consigneeEmail.alignment = alignment;
      consigneeEmail.font = fontBold;
      consigneeEmail.fill = fill;
      consigneeEmail.alignment = alignment;
      consigneeEmail.border = border;

      const consigneeAddress = worksheet.getCell('O1');
      consigneeAddress.value = "Consignee_Address";
      consigneeAddress.alignment = alignment;
      consigneeAddress.font = fontBold;
      consigneeAddress.fill = fill;
      consigneeAddress.alignment = alignment;
      consigneeAddress.border = border;

      const currency = worksheet.getCell('P1');
      currency.value = "Currency";
      currency.alignment = alignment;
      currency.font = fontBold;
      currency.fill = fill;
      currency.alignment = alignment;
      currency.border = border;

      const parcelGoodValue = worksheet.getCell('Q1');
      parcelGoodValue.value = "Parcel_Goods_Value";
      parcelGoodValue.alignment = alignment;
      parcelGoodValue.font = fontBold;
      parcelGoodValue.fill = fill;
      parcelGoodValue.alignment = alignment;
      parcelGoodValue.border = border;

      const itemCustomDes = worksheet.getCell('R1');
      itemCustomDes.value = "Item_Customs_Description";
      itemCustomDes.alignment = alignment;
      itemCustomDes.font = fontBold;
      itemCustomDes.fill = fill;
      itemCustomDes.alignment = alignment;
      itemCustomDes.border = border;

      const itemNativeDes = worksheet.getCell('S1');
      itemNativeDes.value = "Item_Native_Description";
      itemNativeDes.alignment = alignment;
      itemNativeDes.font = fontBold;
      itemNativeDes.fill = fill;
      itemNativeDes.alignment = alignment;
      itemNativeDes.border = border;

      const productname = worksheet.getCell('T1');
      productname.value = "Product name";
      productname.alignment = alignment;
      productname.font = fontBold;
      productname.fill = fill;
      productname.alignment = alignment;
      productname.border = border;

      const itemQty = worksheet.getCell('U1');
      itemQty.value = "Item_Qty";
      itemQty.alignment = alignment;
      itemQty.font = fontBold;
      itemQty.fill = fill;
      itemQty.alignment = alignment;
      itemQty.border = border;

      const itemValue = worksheet.getCell('V1');
      itemValue.value = "Item_Value";
      itemValue.alignment = alignment;
      itemValue.font = fontBold;
      itemValue.fill = fill;
      itemValue.alignment = alignment;
      itemValue.border = border;

      const itemUrl = worksheet.getCell('W1');
      itemUrl.value = "Item_URL";
      itemUrl.alignment = alignment;
      itemUrl.font = fontBold;
      itemUrl.fill = fill;
      itemUrl.alignment = alignment;
      itemUrl.border = border;

      const itemSkuId = worksheet.getCell('X1');
      itemSkuId.value = "Item_SKU_ID";
      itemSkuId.alignment = alignment;
      itemSkuId.font = fontBold;
      itemSkuId.fill = fill;
      itemSkuId.alignment = alignment;
      itemSkuId.border = border;

      const hscode = worksheet.getCell('Y1');
      hscode.value = "HSCode";
      hscode.alignment = alignment;
      hscode.font = fontBold;
      hscode.fill = fill;
      hscode.alignment = alignment;
      hscode.border = border;

      const countryOfOrigin = worksheet.getCell('Z1');
      countryOfOrigin.value = "Country of Origin";
      countryOfOrigin.alignment = alignment;
      countryOfOrigin.font = fontBold;
      countryOfOrigin.fill = fill;
      countryOfOrigin.alignment = alignment;
      countryOfOrigin.border = border;

      worksheet.columns = [
        { key: "no", width: EConfigure.INDEX_5 },
        { key: "HAWBClearance", width: EConfigure.INDEX_20 },
        { key: "bagId", width: EConfigure.INDEX_20 },
        { key: "MAWB", width: EConfigure.INDEX_20 },
        { key: "orderId", width: EConfigure.INDEX_20 },
        { key: "refId", width: EConfigure.INDEX_20 },
        { key: "bagWeight", width: EConfigure.INDEX_20 },
        { key: "shipperWeight", width: EConfigure.INDEX_20 },
        { key: "processedWeight", width: EConfigure.INDEX_20 },
        { key: "sender", width: EConfigure.INDEX_50 },
        { key: "senderAddress", width: EConfigure.INDEX_50 },
        { key: "receiver", width: EConfigure.INDEX_50 },
        { key: "receiverPhone", width: EConfigure.INDEX_20 },
        { key: "receiverEmail", width: EConfigure.INDEX_20 },
        { key: "receiverAddress", width: EConfigure.INDEX_50 },
        { key: "currency", width: EConfigure.INDEX_15 },
        { key: "totalValue", width: EConfigure.INDEX_20 },
        { key: "itemCustomDes", width: EConfigure.INDEX_50 },
        { key: "productName", width: EConfigure.INDEX_50 },
        { key: "productNameVn", width: EConfigure.INDEX_50 },
        { key: "itemQty", width: EConfigure.INDEX_20 },
        { key: "unitValue", width: EConfigure.INDEX_20 },
        { key: "itemUrl", width: EConfigure.INDEX_50 },
        { key: "itemSkuId", width: EConfigure.INDEX_20 },
        { key: "hsCode", width: EConfigure.INDEX_20 },
        { key: "origin", width: EConfigure.INDEX_15 },
      ];
      arr.forEach((item: IBCExport, _) => {
        worksheet.addRow({
          no: item.no,
          HAWBClearance: item.HAWBClearance,
          bagId: item.bagId,
          MAWB: item.MAWB,
          orderId: item.orderId,
          refId: item.refId,
          bagWeight: item.bagWeight,
          shipperWeight: item.shipperWeight,
          processedWeight: item.processedWeight,
          sender: item.sender,
          senderAddress: item.senderAddress,
          receiver: item.receiver,
          receiverPhone: item.receiverPhone,
          receiverEmail: item.receiverEmail,
          receiverAddress: item.receiverAddress,
          currency: item.currency,
          totalValue: item.totalValue,
          itemCustomDes: item.itemCustomDes,
          productName: item.productName,
          productNameVn: item.productNameVn,
          itemQty: item.itemQty,
          unitValue: item.unitValue,
          itemUrl: item.itemUrl,
          itemSkuId: item.itemSkuId,
          hsCode: item.hsCode,
          origin: item.origin,
        });
      });
      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}${filename}`);
      history.isSucess = true;
      history.successDate = moment().format(EConfigure.FULL_TIME);
      history.excelLink = `${new Config().clearanceDomain()}/clearance/invoice/${filename}`;
      await history.save();
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][ibcExcel]', (error as Error).message);
    }
  }

  private async exportExcel(arr: Record<string, any>[], filename: string): Promise<void> {
    try {
      const alignment = { vertical: 'middle', horizontal: 'center' };
      const fontBold = { bold: true, name: 'Calibri' };
      const fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '96C8FB' },
        bgColor: { argb: '96C8FB' },
      };
      const border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(`sheet ${EConfigure.INDEX_1}`, {
        properties: {
          tabColor: { argb: 'FFC0000' }
        },
      });

      // Title Left
      let titleFirst = worksheet.getCell(`B1`);
      titleFirst.value = `CÔNG TY CỔ PHẦN CHUYỂN PHÁT NHANH`;
      titleFirst.alignment = alignment,
        titleFirst.font = fontBold;

      const titleSecond = worksheet.getCell(`B2`);
      titleSecond.value = `HÀNG HÓA SÀI GÒN`;
      titleSecond.alignment = alignment,
        titleSecond.font = fontBold;

      const titleThird = worksheet.getCell(`B3`);
      titleThird.value = `-------`;
      titleThird.alignment = alignment,
        titleThird.font = fontBold;

      const titleFour = worksheet.getCell(`B4`);
      titleFour.value = `Số: ……………../`;
      titleFour.alignment = alignment,
        titleFour.font = fontBold;

      // Title Right
      worksheet.mergeCells('K1', 'L4');
      const templateCell = worksheet.getCell('K1');
      templateCell.value = "Mẫu số HQ 05-BLKHH";
      templateCell.alignment = alignment;
      templateCell.font = fontBold;

      // Title Center
      worksheet.mergeCells('E5', 'F5');
      const title = worksheet.getCell('E5');
      title.value = "BẢN LƯỢC KHAI HÀNG HÓA";
      title.alignment = alignment;
      title.font = fontBold;

      // Content Data
      worksheet.mergeCells('A8', 'A9');
      const stt = worksheet.getCell('A8');
      stt.value = "STT";
      stt.alignment = alignment;
      stt.font = fontBold;
      stt.fill = fill;
      stt.alignment = alignment;
      stt.border = border;

      worksheet.mergeCells('B8', 'B9');
      const hawb = worksheet.getCell('B8');
      hawb.value = "Số vận đơn";
      hawb.alignment = alignment;
      hawb.font = fontBold;
      hawb.fill = fill;
      hawb.alignment = alignment;
      hawb.border = border;

      worksheet.mergeCells('C8', 'D8');
      const group = worksheet.getCell('C8');
      group.value = "Họ tên, địa chỉ, số CMND (nếu có)";
      group.alignment = alignment;
      group.font = fontBold;
      group.fill = fill;
      group.alignment = alignment;
      group.border = border;

      const sender = worksheet.getCell('C9');
      sender.value = "Người gửi";
      sender.alignment = alignment;
      sender.font = fontBold;
      sender.fill = fill;
      sender.alignment = alignment;
      sender.border = border;

      const receiver = worksheet.getCell('D9');
      receiver.value = "Người nhận";
      receiver.alignment = alignment;
      receiver.font = fontBold;
      receiver.fill = fill;
      receiver.alignment = alignment;
      receiver.border = border;

      worksheet.mergeCells('E8', 'E9');
      const cargoName = worksheet.getCell('E8');
      cargoName.value = "Tên hàng";
      cargoName.alignment = alignment;
      cargoName.font = fontBold;
      cargoName.fill = fill;
      cargoName.alignment = alignment;
      cargoName.border = border;

      worksheet.mergeCells('F8', 'F9');
      const cargoId = worksheet.getCell('F8');
      cargoId.value = "Mã số hàng hóa (nếu có)";
      cargoId.alignment = alignment;
      cargoId.font = fontBold;
      cargoId.fill = fill;
      cargoId.alignment = alignment;
      cargoId.border = border;

      worksheet.mergeCells('G8', 'G9');
      const origin = worksheet.getCell('G8');
      origin.value = "Xuất xứ";
      origin.alignment = alignment;
      origin.font = fontBold;
      origin.fill = fill;
      origin.alignment = alignment;
      origin.border = border;

      worksheet.mergeCells('H8', 'H9');
      const cargoPiece = worksheet.getCell('H8');
      cargoPiece.value = "Số kiện";
      cargoPiece.alignment = alignment;
      cargoPiece.font = fontBold;
      cargoPiece.fill = fill;
      cargoPiece.alignment = alignment;
      cargoPiece.border = border;

      worksheet.mergeCells('I8', 'I9');
      const invoice = worksheet.getCell('I8');
      invoice.value = "Trị giá nguyên tệ";
      invoice.alignment = alignment;
      invoice.font = fontBold;
      invoice.fill = fill;
      invoice.alignment = alignment;
      invoice.border = border;

      worksheet.mergeCells('J8', 'J9');
      const unitInvoice = worksheet.getCell('J8');
      unitInvoice.value = "Đơn giá nguyên tệ";
      unitInvoice.alignment = alignment;
      unitInvoice.font = fontBold;
      unitInvoice.fill = fill;
      unitInvoice.alignment = alignment;
      unitInvoice.border = border;

      worksheet.mergeCells('K8', 'K9');
      const price = worksheet.getCell('K8');
      price.value = "Trị giá (VND)";
      price.alignment = alignment;
      price.font = fontBold;
      price.fill = fill;
      price.alignment = alignment;
      price.border = border;

      worksheet.mergeCells('L8', 'L9');
      const note = worksheet.getCell('L8');
      note.value = "Ghi chú";
      note.alignment = alignment;
      note.font = fontBold;
      note.fill = fill;
      note.alignment = alignment;
      note.border = border;

      worksheet.columns = [
        { key: "no", width: EConfigure.INDEX_5 },
        { key: "HAWBClearance", width: EConfigure.INDEX_40 },
        { key: "sender", width: EConfigure.INDEX_50 },
        { key: "receiver", width: EConfigure.INDEX_50 },
        { key: "cargoName", width: EConfigure.INDEX_20 },
        { key: "cargoId", width: EConfigure.INDEX_20 },
        { key: "origin", width: EConfigure.INDEX_15 },
        { key: "cargoPiece", width: EConfigure.INDEX_20 },
        { key: "invoice", width: EConfigure.INDEX_20 },
        { key: "unitInvoice", width: EConfigure.INDEX_15 },
        { key: "price", width: EConfigure.INDEX_20 },
        { key: "note", width: EConfigure.INDEX_20 },
      ];

      arr.forEach(function (item, _) {
        worksheet.addRow({
          no: item.no,
          HAWBClearance: item.HAWBClearance,
          sender: item.sender,
          receiver: item.receiver,
          cargoName: item.cargoName,
          cargoId: item.cargoId,
          origin: item.origin,
          cargoPiece: item.cargoPiece,
          invoice: item.invoice,
          unitInvoice: item.unitInvoice,
          price: item.price,
          note: item.note,
        });
      });

      const total: number = arr.length;
      // Footer
      const footer = total + EConfigure.INDEX_11;
      worksheet.mergeCells(`A${footer}`, `B${footer}`);
      worksheet.getCell(`A${footer}`).value = "Xác nhận kết quả kiểm tra:";
      worksheet.getCell(`A${footer}`).font = fontBold;

      // Signature Left
      const signatureLeft = total + EConfigure.INDEX_15;
      worksheet.getCell(`B${signatureLeft}`).value = `………. ngày .... tháng .... năm 20....`;
      worksheet.getCell(`B${signatureLeft}`).alignment = alignment,
        worksheet.getCell(`B${signatureLeft}`).font = {
          italic: true
        };
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).value = `CÔNG CHỨC HẢI QUAN`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).alignment = alignment,
        worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).value = `(Ký, đóng dấu công chức)`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).alignment = alignment,
        worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).font = {
          italic: true
        };

      // Signature right
      const signatureRigth = total + EConfigure.INDEX_15;
      worksheet.getCell(`K${signatureRigth}`).value = `………. ngày .... tháng .... năm 20....`;
      worksheet.getCell(`K${signatureRigth}`).alignment = alignment,
        worksheet.getCell(`K${signatureRigth}`).font = {
          italic: true
        };
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).value = `CÔNG TY CHUYỂN PHÁT NHANH`;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).alignment = alignment,
        worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).value = `(Ký, đóng dấu công chức)`;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).alignment = alignment,
        worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).font = {
          italic: true
        };
      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}${filename}`)
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][exportExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async outboundPdfGenerate(value: any, hubs: any, employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      let isBox = false;
      const printType: number = value['printType'];
      const groupBox: boolean = value['groupBox'] != null ? value['groupBox'] : false;
      const isLandscape: boolean = value['isLandscape'] || false;
      const mecOptional: Optional = new Optional();
      mecOptional.setRelation(["exportDetails", "manifest"]);
      mecOptional.setAttributes(["MAWB", "hubId", "declarationNo", "HAWBClearance", "hubId", "cargoPiece", "cargoWeight", "priceVND", "notes", "externalBoxName", "internalBoxName", "exporterFullName", "addressOfExporter"]);
      mecOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      mecOptional.setOrderby([new OrderBy("createdAt", Configure.DESCENDING)]);
      if (value['classify']) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['phase']) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
      }
      if (value['MAWB']) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        isBox = true;
        mecOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (hubs) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      mecOptional.setOrderby([new OrderBy('externalBoxName', EConfigure.DESCENDING)])
      let MECs: IMEC[] = await this.mecRepository.queryAll(mecOptional);
      if (MECs.length > 0) {
        let obj: any = {};
        let hubId: number = 0;
        let mawb: string = '';
        let mawbs: Set<string> = new Set();
        if (MECs.length > 0 && (printType == PrintKey.INVOICE_MEC || printType == PrintKey.INVOICE_MEC_TT29 || printType == PrintKey.DETAIL_INVOICE_MEC_TT29)) {
          hubId = MECs[0]["hubId"]!;
          mawb = MECs[0]["MAWB"]!;
          MECs.forEach((mec: IMEC) => {
            mawbs.add(mec["MAWB"]!);
          });
          if (value['boxes'] && value['boxes'].length > 0) {
            const idaExternalBoxes = _.groupBy(MECs, MIC => MIC['externalBoxName']);
            const idaInternalBoxes = _.groupBy(MECs, MIC => MIC['internalBoxName']);
            value['boxes'].forEach((item: any) => {
              obj[item] = [
                ...idaExternalBoxes[item] ? idaExternalBoxes[item] : [],
                ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []
              ];
            })
          } else {
            MECs = _.shuffle(MECs);
            MECs.forEach((mic: IMEC) => {
              let key: string = 'none';
              if (mic['externalBoxName']) {
                key = mic['externalBoxName'];
              } else if (mic['internalBoxName']) {
                key = mic['internalBoxName'];
              }
              if (obj[key]) {
                obj[key].push(mic);
              } else {
                obj[key] = [mic];
              }
            });
          }
        }
        const now: string = moment().format('DD-MM-YYYY');
        let config: pdf.CreateOptions = {
          "format": "A4",
          "orientation": isLandscape ? "landscape" : "portrait",
          "border": {
            "top": "8px",
            "right": "10px",
            "bottom": "20px",
            "left": "10px"
          },
          timeout: 300000
        }
        let label: String = '';
        let pathTemplate: string = '';
        let filename: string = '';
        let excelName: string = '';
        let subTitle = "";
        if (printType == PrintKey.INVOICE_MEC) {
          let classify: String = 'MEC';
          const name: string = `bangke${classify}-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          excelName = `${name}.xlsx`;
          pathTemplate = `../../../../../template/bangke/bangkemec.ejs`;
          label = `Danh sách hàng hóa xuất khẩu trị giá thấp (${classify}) đã được thông quan`;
          const arr: Record<string, any>[] = [];
          let no = 1;
          let boxNo = 1;
          let totalWeight = 0;
          let totalPiece = 0;
          let totalPrice = 0;
          let nameBox = "";
          for (const key in obj) {
            if (obj[key].length == 0) {
              continue;
            }
            nameBox = key;
            let totalPriceBox = 0;
            let totalPcsBox = 0;
            let totalWeightBox = 0;
            obj[key].forEach((mic: IMEC, _: number) => {
              const itemNameVN: String[] = [];
              if (mic['exportDetails'].length > 0) {
                mic['exportDetails'].forEach((detail: Record<string, any>) => {
                  itemNameVN.push(String(detail['itemNameVN']));
                });
              }
              arr.push({
                "no": no,
                "declarationNo": mic['declarationNo'] != null ? mic['declarationNo'] : "",
                "HAWBClearance": mic['HAWBClearance'],
                "cargoName": itemNameVN.join(','),
                "unitWeight": 'KGM',
                "weight": mic['cargoWeight'],
                "cargoPiece": mic['cargoPiece'],
                "price": Number(mic['priceVND']),
                "note": mic['notes'],
                "boxName": mic['externalBoxName'],
              });
              totalWeight += Number(mic['cargoWeight']);
              totalPiece += Number(mic['cargoPiece']);
              totalPrice += Number(mic['priceVND']);
              totalPcsBox += Number(mic['cargoPiece']);
              totalWeightBox += Number(mic['cargoWeight']);
              totalPriceBox += Number(mic['priceVND']);
              no += 1;
            });
            if (groupBox) {
              arr.push({
                "no": 0,
                "boxNo": boxNo,
                "declarationNo": "",
                "HAWBClearance": "",
                "cargoName": "",
                "unitWeight": "",
                "weight": totalWeightBox,
                "cargoPiece": totalPcsBox,
                "price": totalPriceBox,
                "note": "",
                "boxName": nameBox,
              });
              boxNo += 1;
            }
          }
          this.mecExcel(arr, { totalWeight, totalPiece, totalPrice }, excelName);
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                  <tr>
                      <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                      <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                      <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                  </tr>
              </table>`,
            }
          }
        } else if (printType == PrintKey.INVOICE_MEC_TT29) {
          let classify: String = 'MEC_TT29';
          const name: string = `bangke${classify}-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          excelName = `${name}.xlsx`;
          const arr10: Record<string, any>[] = [];
          const arr8: Record<string, any>[] = [];
          let no10 = 1;
          let totalWeight10 = 0;
          let totalPiece10 = 0;
          let totalPrice10 = 0;
          let no8 = 1;
          let totalWeight8 = 0;
          let totalPiece8 = 0;
          let totalPrice8 = 0;
          for (const key in obj) {
            if (obj[key].length == 0) {
              continue;
            }
            obj[key].forEach((mec: IMEC) => {
              const itemNameVN: String[] = [];
              if (mec['exportDetails']!.length > 0) {
                mec['exportDetails']!.forEach((detail: Record<string, any>) => {
                  itemNameVN.push(String(detail['itemNameVN']));
                });
              }
              const currentEntry = {
                "declarationNo": mec['declarationNo'] != null ? mec['declarationNo'] : "",
                "HAWBClearance": mec['HAWBClearance'],
                "cargoName": itemNameVN.join(','),
                "unitWeight": 'KGM',
                "weight": (mec['cargoWeight']! > 0) ? Number(mec['cargoWeight']).toFixed(2) : 0,
                "cargoPiece": mec['cargoPiece'],
                "price": (mec['priceVND']! > 0) ? Number(mec['priceVND']).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0,
                "note": mec['notes'],
                "boxName": mec['externalBoxName'],
              };
              if (mec['percentTaxPrint'] == null || mec['percentTaxPrint'] === '10') {
                arr10.push({ "no": no10, ...currentEntry });
                totalWeight10 += Number(mec['cargoWeight']);
                totalPiece10 += Number(mec['cargoPiece']);
                totalPrice10 += Number(mec['priceVND']);
                no10 += 1;
              } else {
                arr8.push({ "no": no8, ...currentEntry });
                totalWeight8 += Number(mec['cargoWeight']);
                totalPiece8 += Number(mec['cargoPiece']);
                totalPrice8 += Number(mec['priceVND']);
                no8 += 1;
              }
            });
          }
          this.excelTT29({
            "data8": { "arr8": arr8, "total8": { totalWeight8, totalPiece8, totalPrice8, taxPrice8: totalPrice8 * 0.08 } },
            "data10": { "arr10": arr10, "total10": { totalWeight10, totalPiece10, totalPrice10, taxPrice10: totalPrice10 * 0.1 } }
          }, excelName, "");
          pathTemplate = `../../../../../template/bangke/bangke_mec_tt29.ejs`;
          label = `Danh sách hàng hóa xuất khẩu trị giá thấp`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `
              <table width="100%;" style="font-size: 8px;">
                <tr>
                  <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                  <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                  <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                </tr>
              </table>`,
            }
          }
        }
        if (printType == PrintKey.DETAIL_INVOICE_MEC_TT29) {
          const name: string = `bangkechitiethanghoa_tt29-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          pathTemplate = `../../../../../template/bangke/bangkechitietmec_tt29.ejs`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                <tr>
                  <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                  <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                  <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                </tr>
              </table>`,
            }
          }
        }
        const createHistory = this.historyInvoiceService.create({
          MAWB: mawb,
          employeeId: employeeId,
          printType: printType,
          hubId: hubId,
          isSucess: false,
          dataSearch: value,
          note: value['note'],
        });
        const [historyInvoice, template] = await Promise.all([
          createHistory,
          ejs.renderFile(path.join(__dirname, pathTemplate), { MICs: obj, label, now, printType, isBox: groupBox, subTitle, hasFooter: true, itemLanguage: "vi"  }, { async: true }),
        ]);
        this.pdfCreater({ template, config, filename, historyInvoice, excelName: excelName });
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = historyInvoice;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][pdfPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async micExcel(arr: Record<string, any>, filename: string, subTitle: string): Promise<void> {
    try {
      const data8 = arr.data8;
      const data10 = arr.data10;
      let total: number = 0;
      let total2: number = 0;
      let offset = (data8.arr8.length > 0) ? 5 : 0;
      const alignment = { vertical: 'middle', horizontal: 'center' };
      const fontBold = { bold: true, name: 'Calibri' };
      const fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '96C8FB' },
        bgColor: { argb: '96C8FB' },
      };
      const border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(`sheet ${EConfigure.INDEX_1}`, {
        properties: {
          tabColor: { argb: 'FFC0000' }
        },
      });

      // Title Left
      let titleFirst = worksheet.getCell(`B1`);
      titleFirst.value = `CÔNG TY CỔ PHẦN CHUYỂN PHÁT NHANH`;
      titleFirst.alignment = alignment;
      titleFirst.font = fontBold;

      const titleSecond = worksheet.getCell(`B2`);
      titleSecond.value = `HÀNG HÓA SÀI GÒN`;
      titleSecond.alignment = alignment;
      titleSecond.font = fontBold;

      const titleThird = worksheet.getCell(`B3`);
      titleThird.value = `-------`;
      titleThird.alignment = alignment;
      titleThird.font = fontBold;

      const titleFour = worksheet.getCell(`B4`);
      titleFour.value = `Số: ……………../BK-SCE`;
      titleFour.alignment = alignment;
      titleFour.font = fontBold;

      // Title Right
      worksheet.mergeCells('H1', 'I4');
      const templateCell = worksheet.getCell('H1');
      templateCell.value = "Mẫu số HQ 02-BKTKTGT";
      templateCell.alignment = alignment;
      templateCell.font = fontBold;

      // Title Center
      const title = worksheet.getCell('D5');
      title.value = `BẢNG KÊ ${subTitle}`;
      title.alignment = alignment;
      title.font = fontBold;

      const subtitle = worksheet.getCell('D6');
      subtitle.value = "Danh sách hàng hóa nhập khẩu trị giá thấp (MIC) đã được thông quan";
      subtitle.alignment = alignment;
      subtitle.font = fontBold;

      // Content Data
      if (data8.arr8.length > 0) {
        const stt = worksheet.getCell('A9');
        stt.value = "STT";
        stt.alignment = alignment;
        stt.font = fontBold;
        stt.fill = fill;
        stt.alignment = alignment;
        stt.border = border;
        const noStt = worksheet.getCell('A10');
        noStt.value = "(1)";
        noStt.alignment = alignment;
        noStt.font = fontBold;
        noStt.fill = fill;
        noStt.alignment = alignment;
        noStt.border = border;

        const declarationNo = worksheet.getCell('B9');
        declarationNo.value = "Số tờ khai";
        declarationNo.alignment = alignment;
        declarationNo.font = fontBold;
        declarationNo.fill = fill;
        declarationNo.alignment = alignment;
        declarationNo.border = border;
        const noDeclarationNo = worksheet.getCell('B10');
        noDeclarationNo.value = "(2)";
        noDeclarationNo.alignment = alignment;
        noDeclarationNo.font = fontBold;
        noDeclarationNo.fill = fill;
        noDeclarationNo.alignment = alignment;
        noDeclarationNo.border = border;

        const hawb = worksheet.getCell('C9');
        hawb.value = "Số vận đơn";
        hawb.alignment = alignment;
        hawb.font = fontBold;
        hawb.fill = fill;
        hawb.alignment = alignment;
        hawb.border = border;
        const noHawb = worksheet.getCell('C10');
        noHawb.value = "(3)";
        noHawb.alignment = alignment;
        noHawb.font = fontBold;
        noHawb.fill = fill;
        noHawb.alignment = alignment;
        noHawb.border = border;

        const cargoName = worksheet.getCell('D9');
        cargoName.value = "Tên hàng";
        cargoName.alignment = alignment;
        cargoName.font = fontBold;
        cargoName.fill = fill;
        cargoName.alignment = alignment;
        cargoName.border = border;
        const noCargoName = worksheet.getCell('D10');
        noCargoName.value = "(4)";
        noCargoName.alignment = alignment;
        noCargoName.font = fontBold;
        noCargoName.fill = fill;
        noCargoName.alignment = alignment;
        noCargoName.border = border;

        const cargo = worksheet.getCell('E9');
        cargo.value = "Đơn vị tính";
        cargo.alignment = alignment;
        cargo.font = fontBold;
        cargo.fill = fill;
        cargo.alignment = alignment;
        cargo.border = border;
        const noCargo = worksheet.getCell('E10');
        noCargo.value = "(5)";
        noCargo.alignment = alignment;
        noCargo.font = fontBold;
        noCargo.fill = fill;
        noCargo.alignment = alignment;
        noCargo.border = border;

        const weight = worksheet.getCell('F9');
        weight.value = "Trọng lượng";
        weight.alignment = alignment;
        weight.font = fontBold;
        weight.fill = fill;
        weight.alignment = alignment;
        weight.border = border;
        const noWeight = worksheet.getCell('F10');
        noWeight.value = "(6)";
        noWeight.alignment = alignment;
        noWeight.font = fontBold;
        noWeight.fill = fill;
        noWeight.alignment = alignment;
        noWeight.border = border;

        const cargoPiece = worksheet.getCell('G9');
        cargoPiece.value = "Số kiện";
        cargoPiece.alignment = alignment;
        cargoPiece.font = fontBold;
        cargoPiece.fill = fill;
        cargoPiece.alignment = alignment;
        cargoPiece.border = border;
        const noCargoPiece = worksheet.getCell('G10');
        noCargoPiece.value = "(7)";
        noCargoPiece.alignment = alignment;
        noCargoPiece.font = fontBold;
        noCargoPiece.fill = fill;
        noCargoPiece.alignment = alignment;
        noCargoPiece.border = border;

        const price = worksheet.getCell('H9');
        price.value = "Trị giá (VND)";
        price.alignment = alignment;
        price.font = fontBold;
        price.fill = fill;
        price.alignment = alignment;
        price.border = border;
        const noPrice = worksheet.getCell('H10');
        noPrice.value = "(8)";
        noPrice.alignment = alignment;
        noPrice.font = fontBold;
        noPrice.fill = fill;
        noPrice.alignment = alignment;
        noPrice.border = border;

        const note = worksheet.getCell('I9');
        note.value = "Ghi chú";
        note.alignment = alignment;
        note.font = fontBold;
        note.fill = fill;
        note.alignment = alignment;
        note.border = border;
        const noNote = worksheet.getCell('I10');
        noNote.value = "(9)";
        noNote.alignment = alignment;
        noNote.font = fontBold;
        noNote.fill = fill;
        noNote.alignment = alignment;
        noNote.border = border;

        worksheet.columns = [
          { key: "no", width: EConfigure.INDEX_5 },
          { key: "declarationNo", width: EConfigure.INDEX_50 },
          { key: "HAWBClearance", width: EConfigure.INDEX_40 },
          { key: "cargoName", width: EConfigure.INDEX_20 },
          { key: "unitWeight", width: EConfigure.INDEX_20 },
          { key: "weight", width: EConfigure.INDEX_15 },
          { key: "cargoPiece", width: EConfigure.INDEX_20 },
          { key: "price", width: EConfigure.INDEX_20 },
          { key: "note", width: EConfigure.INDEX_20 },
        ];
        data8.arr8.forEach(function (item: Record<string, any>) {
          worksheet.addRow({
            no: item.no,
            declarationNo: item.declarationNo,
            HAWBClearance: item.HAWBClearance,
            cargoName: item.cargoName,
            unitWeight: item.unitWeight,
            weight: item.weight,
            cargoPiece: item.cargoPiece,
            price: item.price,
            note: item.note,
          });
        });

        total = data8.arr8.length;
        worksheet.mergeCells(`A${total + EConfigure.INDEX_11}`, `E${total + EConfigure.INDEX_11}`);
        const summaryTotal = worksheet.getCell(`A${total + EConfigure.INDEX_11}`);
        summaryTotal.value = "Tổng cộng:";
        summaryTotal.alignment = alignment;
        summaryTotal.font = fontBold;
        summaryTotal.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        summaryTotal.alignment = alignment;
        summaryTotal.border = border;

        const sumWeight = worksheet.getCell(`F${total + EConfigure.INDEX_11}`);
        sumWeight.value = data8.total8['totalWeight8'];
        sumWeight.alignment = alignment;
        sumWeight.font = fontBold;
        sumWeight.alignment = alignment;
        sumWeight.border = border;

        const sumPiece = worksheet.getCell(`G${total + EConfigure.INDEX_11}`);
        sumPiece.value = data8.total8['totalPiece8'];
        sumPiece.alignment = alignment;
        sumPiece.font = fontBold;
        sumPiece.alignment = alignment;
        sumPiece.border = border;

        const sumPrice = worksheet.getCell(`H${total + EConfigure.INDEX_11}`);
        sumPrice.value = data8.total8['totalPrice8'];
        sumPrice.alignment = alignment;
        sumPrice.font = fontBold;
        sumPrice.alignment = alignment;
        sumPrice.border = border;

        ////
        worksheet.mergeCells(`A${total + EConfigure.INDEX_12}`, `E${total + EConfigure.INDEX_12}`);
        const percenTotal = worksheet.getCell(`A${total + EConfigure.INDEX_12}`);
        percenTotal.value = "Thuế xuất thuế GTGT (%):";
        percenTotal.alignment = alignment;
        percenTotal.font = fontBold;
        percenTotal.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        percenTotal.alignment = alignment;
        percenTotal.border = border;
        worksheet.mergeCells(`F${total + EConfigure.INDEX_12}`, `I${total + EConfigure.INDEX_12}`);
        const percenValue = worksheet.getCell(`F${total + EConfigure.INDEX_12}`);
        percenValue.value = "8%";
        percenValue.alignment = alignment;
        percenValue.font = fontBold;
        percenValue.alignment = alignment;
        percenValue.border = border;

        worksheet.mergeCells(`A${total + EConfigure.INDEX_13}`, `E${total + EConfigure.INDEX_13}`);
        const taxTotal = worksheet.getCell(`A${total + EConfigure.INDEX_13}`);
        taxTotal.value = "Số tiền thuế GTGT (%):";
        taxTotal.alignment = alignment;
        taxTotal.font = fontBold;
        taxTotal.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        taxTotal.alignment = alignment;
        taxTotal.border = border;
        worksheet.mergeCells(`F${total + EConfigure.INDEX_13}`, `I${total + EConfigure.INDEX_13}`);
        const taxValue = worksheet.getCell(`F${total + EConfigure.INDEX_13}`);
        taxValue.value = data8.total8['taxPrice8'];
        taxValue.alignment = alignment;
        taxValue.font = fontBold;
        taxValue.alignment = alignment;
        taxValue.border = border;
      }

      // Content Data 10
      if (data10.arr10.length > 0) {
        const stt2 = worksheet.getCell(`A${total + EConfigure.INDEX_13 + offset}`);
        stt2.value = "STT";
        stt2.alignment = alignment;
        stt2.font = fontBold;
        stt2.fill = fill;
        stt2.alignment = alignment;
        stt2.border = border;
        const noStt2 = worksheet.getCell(`A${total + EConfigure.INDEX_14 + offset}`);
        noStt2.value = "(1)";
        noStt2.alignment = alignment;
        noStt2.font = fontBold;
        noStt2.fill = fill;
        noStt2.alignment = alignment;
        noStt2.border = border;

        const declarationNo2 = worksheet.getCell(`B${total + EConfigure.INDEX_13 + offset}`);
        declarationNo2.value = "Số tờ khai";
        declarationNo2.alignment = alignment;
        declarationNo2.font = fontBold;
        declarationNo2.fill = fill;
        declarationNo2.alignment = alignment;
        declarationNo2.border = border;
        const noDeclarationNo2 = worksheet.getCell(`B${total + EConfigure.INDEX_14 + offset}`);
        noDeclarationNo2.value = "(2)";
        noDeclarationNo2.alignment = alignment;
        noDeclarationNo2.font = fontBold;
        noDeclarationNo2.fill = fill;
        noDeclarationNo2.alignment = alignment;
        noDeclarationNo2.border = border;

        const hawb2 = worksheet.getCell(`C${total + EConfigure.INDEX_13 + offset}`);
        hawb2.value = "Số vận đơn";
        hawb2.alignment = alignment;
        hawb2.font = fontBold;
        hawb2.fill = fill;
        hawb2.alignment = alignment;
        hawb2.border = border;
        const noHawb2 = worksheet.getCell(`C${total + EConfigure.INDEX_14 + offset}`);
        noHawb2.value = "(3)";
        noHawb2.alignment = alignment;
        noHawb2.font = fontBold;
        noHawb2.fill = fill;
        noHawb2.alignment = alignment;
        noHawb2.border = border;

        const cargoName2 = worksheet.getCell(`D${total + EConfigure.INDEX_13 + offset}`);
        cargoName2.value = "Tên hàng";
        cargoName2.alignment = alignment;
        cargoName2.font = fontBold;
        cargoName2.fill = fill;
        cargoName2.alignment = alignment;
        cargoName2.border = border;
        const noCargoName2 = worksheet.getCell(`D${total + EConfigure.INDEX_14 + offset}`);
        noCargoName2.value = "(4)";
        noCargoName2.alignment = alignment;
        noCargoName2.font = fontBold;
        noCargoName2.fill = fill;
        noCargoName2.alignment = alignment;
        noCargoName2.border = border;

        const cargo2 = worksheet.getCell(`E${total + EConfigure.INDEX_13 + offset}`);
        cargo2.value = "Đơn vị tính";
        cargo2.alignment = alignment;
        cargo2.font = fontBold;
        cargo2.fill = fill;
        cargo2.alignment = alignment;
        cargo2.border = border;
        const noCargo2 = worksheet.getCell(`E${total + EConfigure.INDEX_14 + offset}`);
        noCargo2.value = "(5)";
        noCargo2.alignment = alignment;
        noCargo2.font = fontBold;
        noCargo2.fill = fill;
        noCargo2.alignment = alignment;
        noCargo2.border = border;

        const weight2 = worksheet.getCell(`F${total + EConfigure.INDEX_13 + offset}`);
        weight2.value = "Trọng lượng";
        weight2.alignment = alignment;
        weight2.font = fontBold;
        weight2.fill = fill;
        weight2.alignment = alignment;
        weight2.border = border;
        const noWeight2 = worksheet.getCell(`F${total + EConfigure.INDEX_14 + offset}`);
        noWeight2.value = "(6)";
        noWeight2.alignment = alignment;
        noWeight2.font = fontBold;
        noWeight2.fill = fill;
        noWeight2.alignment = alignment;
        noWeight2.border = border;

        const cargoPiece2 = worksheet.getCell(`G${total + EConfigure.INDEX_13 + offset}`);
        cargoPiece2.value = "Số kiện";
        cargoPiece2.alignment = alignment;
        cargoPiece2.font = fontBold;
        cargoPiece2.fill = fill;
        cargoPiece2.alignment = alignment;
        cargoPiece2.border = border;
        const noCargoPiece2 = worksheet.getCell(`G${total + EConfigure.INDEX_14 + offset}`);
        noCargoPiece2.value = "(7)";
        noCargoPiece2.alignment = alignment;
        noCargoPiece2.font = fontBold;
        noCargoPiece2.fill = fill;
        noCargoPiece2.alignment = alignment;
        noCargoPiece2.border = border;

        const price2 = worksheet.getCell(`H${total + EConfigure.INDEX_13 + offset}`);
        price2.value = "Trị giá (VND)";
        price2.alignment = alignment;
        price2.font = fontBold;
        price2.fill = fill;
        price2.alignment = alignment;
        price2.border = border;
        const noPrice2 = worksheet.getCell(`H${total + EConfigure.INDEX_14 + offset}`);
        noPrice2.value = "(8)";
        noPrice2.alignment = alignment;
        noPrice2.font = fontBold;
        noPrice2.fill = fill;
        noPrice2.alignment = alignment;
        noPrice2.border = border;

        const note2 = worksheet.getCell(`I${total + EConfigure.INDEX_13 + offset}`);
        note2.value = "Ghi chú";
        note2.alignment = alignment;
        note2.font = fontBold;
        note2.fill = fill;
        note2.alignment = alignment;
        note2.border = border;
        const noNote2 = worksheet.getCell(`I${total + EConfigure.INDEX_14 + offset}`);
        noNote2.value = "(9)";
        noNote2.alignment = alignment;
        noNote2.font = fontBold;
        noNote2.fill = fill;
        noNote2.alignment = alignment;
        noNote2.border = border;

        worksheet.columns = [
          { key: "no", width: EConfigure.INDEX_5 },
          { key: "declarationNo", width: EConfigure.INDEX_50 },
          { key: "HAWBClearance", width: EConfigure.INDEX_40 },
          { key: "cargoName", width: EConfigure.INDEX_20 },
          { key: "unitWeight", width: EConfigure.INDEX_20 },
          { key: "weight", width: EConfigure.INDEX_15 },
          { key: "cargoPiece", width: EConfigure.INDEX_20 },
          { key: "price", width: EConfigure.INDEX_20 },
          { key: "note", width: EConfigure.INDEX_20 },
        ];
        data10.arr10.forEach(function (item: Record<string, any>) {
          worksheet.addRow({
            no: item.no,
            declarationNo: item.declarationNo,
            HAWBClearance: item.HAWBClearance,
            cargoName: item.cargoName,
            unitWeight: item.unitWeight,
            weight: item.weight,
            cargoPiece: item.cargoPiece,
            price: item.price,
            note: item.note,
          });
        });

        total2 = data10.arr10.length;
        worksheet.mergeCells(`A${total + total2 + EConfigure.INDEX_15 + offset}`, `E${total + total2 + EConfigure.INDEX_15 + offset}`);

        const summaryTotal2 = worksheet.getCell(`A${total + total2 + EConfigure.INDEX_15 + offset}`);
        summaryTotal2.value = "Tổng cộng:";
        summaryTotal2.alignment = alignment;
        summaryTotal2.font = fontBold;
        summaryTotal2.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        summaryTotal2.alignment = alignment;
        summaryTotal2.border = border;
        const sumWeight2 = worksheet.getCell(`F${total + total2 + EConfigure.INDEX_15 + offset}`);
        sumWeight2.value = data10.total10['totalWeight10'];
        sumWeight2.alignment = alignment;
        sumWeight2.font = fontBold;
        sumWeight2.alignment = alignment;
        sumWeight2.border = border;

        const sumPiece2 = worksheet.getCell(`G${total + total2 + EConfigure.INDEX_15 + offset}`);
        sumPiece2.value = data10.total10['totalPiece10'];
        sumPiece2.alignment = alignment;
        sumPiece2.font = fontBold;
        sumPiece2.alignment = alignment;
        sumPiece2.border = border;

        const sumPrice2 = worksheet.getCell(`H${total + total2 + EConfigure.INDEX_15 + offset}`);
        sumPrice2.value = data10.total10['totalPrice10'];
        sumPrice2.alignment = alignment;
        sumPrice2.font = fontBold;
        sumPrice2.alignment = alignment;
        sumPrice2.border = border;

        ////
        worksheet.mergeCells(`A${total + total2 + EConfigure.INDEX_16 + offset}`, `E${total + total2 + EConfigure.INDEX_16 + offset}`);
        const percenTotal2 = worksheet.getCell(`A${total + total2 + EConfigure.INDEX_16 + offset}`);
        percenTotal2.value = "Thuế xuất thuế GTGT (%):";
        percenTotal2.alignment = alignment;
        percenTotal2.font = fontBold;
        percenTotal2.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        percenTotal2.alignment = alignment;
        percenTotal2.border = border;
        worksheet.mergeCells(`F${total + total2 + EConfigure.INDEX_16 + offset}`, `I${total + total2 + EConfigure.INDEX_16 + offset}`);
        const percenValue2 = worksheet.getCell(`F${total + total2 + EConfigure.INDEX_16 + offset}`);
        percenValue2.value = "10%";
        percenValue2.alignment = alignment;
        percenValue2.font = fontBold;
        percenValue2.alignment = alignment;
        percenValue2.border = border;

        worksheet.mergeCells(`A${total + total2 + EConfigure.INDEX_17 + offset}`, `E${total + total2 + EConfigure.INDEX_17 + offset}`);
        const taxTotal2 = worksheet.getCell(`A${total + total2 + EConfigure.INDEX_17 + offset}`);
        taxTotal2.value = "Số tiền thuế GTGT (%):";
        taxTotal2.alignment = alignment;
        taxTotal2.font = fontBold;
        taxTotal2.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        taxTotal2.alignment = alignment;
        taxTotal2.border = border;
        worksheet.mergeCells(`F${total + total2 + EConfigure.INDEX_17 + offset}`, `I${total + total2 + EConfigure.INDEX_17 + offset}`);
        const taxValue2 = worksheet.getCell(`F${total + total2 + EConfigure.INDEX_17 + offset}`);
        taxValue2.value = data10.total10['taxPrice10'];
        taxValue2.alignment = alignment;
        taxValue2.font = fontBold;
        taxValue2.alignment = alignment;
        taxValue2.border = border;
      }
      // Signature Left
      const signatureLeft = total + total2 + EConfigure.INDEX_25;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).value = `DOANH NGHIỆP LẬP BẢN KÊ`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).value = `(Ký, đóng dấu)`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).font = {
        italic: true
      };

      const signatureCenter = total + total2 + EConfigure.INDEX_25;
      worksheet.getCell(`F${signatureCenter + EConfigure.INDEX_1}`).value = `Ý kiến của cơ quan hải quan:`;
      worksheet.getCell(`F${signatureCenter + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`F${signatureCenter + EConfigure.INDEX_1}`).font = fontBold;

      // Signature right
      const signatureRigth = total + total2 + EConfigure.INDEX_25;
      worksheet.getCell(`K${signatureRigth}`).value = `TP Hồ Chí Minh, Ngày .... tháng .... năm 20....`;
      worksheet.getCell(`K${signatureRigth}`).alignment = alignment;
      worksheet.getCell(`K${signatureRigth}`).font = {
        italic: true
      };
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).value = `CQHQ XÁC NHẬN HÀNG ĐỦ ĐIỀU KIỆN QUA KHU VỰC GIÁM SÁT`;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).value = `(Ký, đóng dấu công chức)`;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).font = {
        italic: true
      };
      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}${filename}`)
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][micExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async excelTT29(arr: Record<string, any>, filename: string, label: string, subTitle?: string): Promise<void> {
    try {
      const data8 = arr.data8;
      const data10 = arr.data10;
      let total: number = 0;
      let total2: number = 0;
      let offset = (data8.arr8.length > 0) ? 5 : 0;
      const alignment = { vertical: 'middle', horizontal: 'center' };
      const fontBold = { bold: true, name: 'Calibri' };
      const fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '96C8FB' },
        bgColor: { argb: '96C8FB' },
      };
      const border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(`sheet ${EConfigure.INDEX_1}`, {
        properties: {
          tabColor: { argb: 'FFC0000' }
        },
      });

      // Title Left
      let titleFirst = worksheet.getCell(`B1`);
      titleFirst.value = `CÔNG TY CỔ PHẦN CHUYỂN PHÁT NHANH`;
      titleFirst.alignment = alignment;
      titleFirst.font = fontBold;

      const titleSecond = worksheet.getCell(`B2`);
      titleSecond.value = `HÀNG HÓA SÀI GÒN`;
      titleSecond.alignment = alignment;
      titleSecond.font = fontBold;

      const titleThird = worksheet.getCell(`B3`);
      titleThird.value = `-------`;
      titleThird.alignment = alignment;
      titleThird.font = fontBold;

      const titleFour = worksheet.getCell(`B4`);
      titleFour.value = `Số: ……………../BK-SCE`;
      titleFour.alignment = alignment;
      titleFour.font = fontBold;

      // Title Right
      worksheet.mergeCells('H1', 'I4');
      const templateCell = worksheet.getCell('H1');
      templateCell.value = "Mẫu số HQ 02-BKTKTGT";
      templateCell.alignment = alignment;
      templateCell.font = fontBold;

      // Title Center
      const title = worksheet.getCell('D5');
      title.value = `BẢNG KÊ ${subTitle}`;
      title.alignment = alignment;
      title.font = fontBold;

      const labelTitle = worksheet.getCell('D6');
      labelTitle.value = `${label}`;
      labelTitle.alignment = alignment;
      labelTitle.font = fontBold;

      // Content Data
      if (data8.arr8.length > 0) {
        const stt = worksheet.getCell('A9');
        stt.value = "STT";
        stt.alignment = alignment;
        stt.font = fontBold;
        stt.fill = fill;
        stt.alignment = alignment;
        stt.border = border;
        const noStt = worksheet.getCell('A10');
        noStt.value = "(1)";
        noStt.alignment = alignment;
        noStt.font = fontBold;
        noStt.fill = fill;
        noStt.alignment = alignment;
        noStt.border = border;

        const declarationNo = worksheet.getCell('B9');
        declarationNo.value = "Số tờ khai";
        declarationNo.alignment = alignment;
        declarationNo.font = fontBold;
        declarationNo.fill = fill;
        declarationNo.alignment = alignment;
        declarationNo.border = border;
        const noDeclarationNo = worksheet.getCell('B10');
        noDeclarationNo.value = "(2)";
        noDeclarationNo.alignment = alignment;
        noDeclarationNo.font = fontBold;
        noDeclarationNo.fill = fill;
        noDeclarationNo.alignment = alignment;
        noDeclarationNo.border = border;

        const hawb = worksheet.getCell('C9');
        hawb.value = "Số vận đơn";
        hawb.alignment = alignment;
        hawb.font = fontBold;
        hawb.fill = fill;
        hawb.alignment = alignment;
        hawb.border = border;
        const noHawb = worksheet.getCell('C10');
        noHawb.value = "(3)";
        noHawb.alignment = alignment;
        noHawb.font = fontBold;
        noHawb.fill = fill;
        noHawb.alignment = alignment;
        noHawb.border = border;

        const cargoName = worksheet.getCell('D9');
        cargoName.value = "Tên hàng";
        cargoName.alignment = alignment;
        cargoName.font = fontBold;
        cargoName.fill = fill;
        cargoName.alignment = alignment;
        cargoName.border = border;
        const noCargoName = worksheet.getCell('D10');
        noCargoName.value = "(4)";
        noCargoName.alignment = alignment;
        noCargoName.font = fontBold;
        noCargoName.fill = fill;
        noCargoName.alignment = alignment;
        noCargoName.border = border;

        const cargo = worksheet.getCell('E9');
        cargo.value = "Đơn vị tính";
        cargo.alignment = alignment;
        cargo.font = fontBold;
        cargo.fill = fill;
        cargo.alignment = alignment;
        cargo.border = border;
        const noCargo = worksheet.getCell('E10');
        noCargo.value = "(5)";
        noCargo.alignment = alignment;
        noCargo.font = fontBold;
        noCargo.fill = fill;
        noCargo.alignment = alignment;
        noCargo.border = border;

        const weight = worksheet.getCell('F9');
        weight.value = "Số lượng";
        weight.alignment = alignment;
        weight.font = fontBold;
        weight.fill = fill;
        weight.alignment = alignment;
        weight.border = border;
        const noWeight = worksheet.getCell('F10');
        noWeight.value = "(6)";
        noWeight.alignment = alignment;
        noWeight.font = fontBold;
        noWeight.fill = fill;
        noWeight.alignment = alignment;
        noWeight.border = border;

        const cargoPiece = worksheet.getCell('G9');
        cargoPiece.value = "Số kiện";
        cargoPiece.alignment = alignment;
        cargoPiece.font = fontBold;
        cargoPiece.fill = fill;
        cargoPiece.alignment = alignment;
        cargoPiece.border = border;
        const noCargoPiece = worksheet.getCell('G10');
        noCargoPiece.value = "(7)";
        noCargoPiece.alignment = alignment;
        noCargoPiece.font = fontBold;
        noCargoPiece.fill = fill;
        noCargoPiece.alignment = alignment;
        noCargoPiece.border = border;

        const price = worksheet.getCell('H9');
        price.value = "Trị giá (VND)";
        price.alignment = alignment;
        price.font = fontBold;
        price.fill = fill;
        price.alignment = alignment;
        price.border = border;
        const noPrice = worksheet.getCell('H10');
        noPrice.value = "(8)";
        noPrice.alignment = alignment;
        noPrice.font = fontBold;
        noPrice.fill = fill;
        noPrice.alignment = alignment;
        noPrice.border = border;

        const note = worksheet.getCell('I9');
        note.value = "Ghi chú";
        note.alignment = alignment;
        note.font = fontBold;
        note.fill = fill;
        note.alignment = alignment;
        note.border = border;
        const noNote = worksheet.getCell('I10');
        noNote.value = "(9)";
        noNote.alignment = alignment;
        noNote.font = fontBold;
        noNote.fill = fill;
        noNote.alignment = alignment;
        noNote.border = border;

        worksheet.columns = [
          { key: "no", width: EConfigure.INDEX_5 },
          { key: "declarationNo", width: EConfigure.INDEX_50 },
          { key: "HAWBClearance", width: EConfigure.INDEX_40 },
          { key: "cargoName", width: EConfigure.INDEX_20 },
          { key: "unitWeight", width: EConfigure.INDEX_20 },
          { key: "weight", width: EConfigure.INDEX_15 },
          { key: "cargoPiece", width: EConfigure.INDEX_20 },
          { key: "price", width: EConfigure.INDEX_20 },
          { key: "note", width: EConfigure.INDEX_20 },
        ];
        data8.arr8.forEach(function (item: Record<string, any>) {
          worksheet.addRow({
            no: item.no,
            declarationNo: item.declarationNo,
            HAWBClearance: item.HAWBClearance,
            cargoName: item.cargoName,
            unitWeight: item.unitWeight,
            weight: item.weight,
            cargoPiece: item.cargoPiece,
            price: item.price,
            note: item.note,
          });
        });

        total = data8.arr8.length;
        worksheet.mergeCells(`A${total + EConfigure.INDEX_11}`, `E${total + EConfigure.INDEX_11}`);
        const summaryTotal = worksheet.getCell(`A${total + EConfigure.INDEX_11}`);
        summaryTotal.value = "Tổng trị giá tính thuế";
        summaryTotal.alignment = alignment;
        summaryTotal.font = fontBold;
        summaryTotal.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        summaryTotal.alignment = alignment;
        summaryTotal.border = border;

        const sumWeight = worksheet.getCell(`F${total + EConfigure.INDEX_11}`);
        sumWeight.value = data8.total8['totalWeight8'];
        sumWeight.alignment = alignment;
        sumWeight.font = fontBold;
        sumWeight.alignment = alignment;
        sumWeight.border = border;

        const sumPiece = worksheet.getCell(`G${total + EConfigure.INDEX_11}`);
        sumPiece.value = data8.total8['totalPiece8'];
        sumPiece.alignment = alignment;
        sumPiece.font = fontBold;
        sumPiece.alignment = alignment;
        sumPiece.border = border;

        const sumPrice = worksheet.getCell(`H${total + EConfigure.INDEX_11}`);
        sumPrice.value = data8.total8['totalPrice8'];
        sumPrice.alignment = alignment;
        sumPrice.font = fontBold;
        sumPrice.alignment = alignment;
        sumPrice.border = border;

        ////
        worksheet.mergeCells(`A${total + EConfigure.INDEX_12}`, `E${total + EConfigure.INDEX_12}`);
        const percenTotal = worksheet.getCell(`A${total + EConfigure.INDEX_12}`);
        percenTotal.value = "Thuế xuất thuế GTGT (%)";
        percenTotal.alignment = alignment;
        percenTotal.font = fontBold;
        percenTotal.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        percenTotal.alignment = alignment;
        percenTotal.border = border;
        worksheet.mergeCells(`F${total + EConfigure.INDEX_12}`, `I${total + EConfigure.INDEX_12}`);
        const percenValue = worksheet.getCell(`F${total + EConfigure.INDEX_12}`);
        percenValue.value = "8%";
        percenValue.alignment = alignment;
        percenValue.font = fontBold;
        percenValue.alignment = alignment;
        percenValue.border = border;

        worksheet.mergeCells(`A${total + EConfigure.INDEX_13}`, `E${total + EConfigure.INDEX_13}`);
        const taxTotal = worksheet.getCell(`A${total + EConfigure.INDEX_13}`);
        taxTotal.value = "Số tiền thuế GTGT (%)";
        taxTotal.alignment = alignment;
        taxTotal.font = fontBold;
        taxTotal.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        taxTotal.alignment = alignment;
        taxTotal.border = border;
        worksheet.mergeCells(`F${total + EConfigure.INDEX_13}`, `I${total + EConfigure.INDEX_13}`);
        const taxValue = worksheet.getCell(`F${total + EConfigure.INDEX_13}`);
        taxValue.value = data8.total8['taxPrice8'];
        taxValue.alignment = alignment;
        taxValue.font = fontBold;
        taxValue.alignment = alignment;
        taxValue.border = border;
      }

      // Content Data 10
      if (data10.arr10.length > 0) {
        const stt2 = worksheet.getCell(`A${total + EConfigure.INDEX_13 + offset}`);
        stt2.value = "STT";
        stt2.alignment = alignment;
        stt2.font = fontBold;
        stt2.fill = fill;
        stt2.alignment = alignment;
        stt2.border = border;
        const noStt2 = worksheet.getCell(`A${total + EConfigure.INDEX_14 + offset}`);
        noStt2.value = "(1)";
        noStt2.alignment = alignment;
        noStt2.font = fontBold;
        noStt2.fill = fill;
        noStt2.alignment = alignment;
        noStt2.border = border;

        const declarationNo2 = worksheet.getCell(`B${total + EConfigure.INDEX_13 + offset}`);
        declarationNo2.value = "Số tờ khai";
        declarationNo2.alignment = alignment;
        declarationNo2.font = fontBold;
        declarationNo2.fill = fill;
        declarationNo2.alignment = alignment;
        declarationNo2.border = border;
        const noDeclarationNo2 = worksheet.getCell(`B${total + EConfigure.INDEX_14 + offset}`);
        noDeclarationNo2.value = "(2)";
        noDeclarationNo2.alignment = alignment;
        noDeclarationNo2.font = fontBold;
        noDeclarationNo2.fill = fill;
        noDeclarationNo2.alignment = alignment;
        noDeclarationNo2.border = border;

        const hawb2 = worksheet.getCell(`C${total + EConfigure.INDEX_13 + offset}`);
        hawb2.value = "Số vận đơn";
        hawb2.alignment = alignment;
        hawb2.font = fontBold;
        hawb2.fill = fill;
        hawb2.alignment = alignment;
        hawb2.border = border;
        const noHawb2 = worksheet.getCell(`C${total + EConfigure.INDEX_14 + offset}`);
        noHawb2.value = "(3)";
        noHawb2.alignment = alignment;
        noHawb2.font = fontBold;
        noHawb2.fill = fill;
        noHawb2.alignment = alignment;
        noHawb2.border = border;

        const cargoName2 = worksheet.getCell(`D${total + EConfigure.INDEX_13 + offset}`);
        cargoName2.value = "Tên hàng";
        cargoName2.alignment = alignment;
        cargoName2.font = fontBold;
        cargoName2.fill = fill;
        cargoName2.alignment = alignment;
        cargoName2.border = border;
        const noCargoName2 = worksheet.getCell(`D${total + EConfigure.INDEX_14 + offset}`);
        noCargoName2.value = "(4)";
        noCargoName2.alignment = alignment;
        noCargoName2.font = fontBold;
        noCargoName2.fill = fill;
        noCargoName2.alignment = alignment;
        noCargoName2.border = border;

        const cargo2 = worksheet.getCell(`E${total + EConfigure.INDEX_13 + offset}`);
        cargo2.value = "Đơn vị tính";
        cargo2.alignment = alignment;
        cargo2.font = fontBold;
        cargo2.fill = fill;
        cargo2.alignment = alignment;
        cargo2.border = border;
        const noCargo2 = worksheet.getCell(`E${total + EConfigure.INDEX_14 + offset}`);
        noCargo2.value = "(5)";
        noCargo2.alignment = alignment;
        noCargo2.font = fontBold;
        noCargo2.fill = fill;
        noCargo2.alignment = alignment;
        noCargo2.border = border;

        const weight2 = worksheet.getCell(`F${total + EConfigure.INDEX_13 + offset}`);
        weight2.value = "Trọng lượng";
        weight2.alignment = alignment;
        weight2.font = fontBold;
        weight2.fill = fill;
        weight2.alignment = alignment;
        weight2.border = border;
        const noWeight2 = worksheet.getCell(`F${total + EConfigure.INDEX_14 + offset}`);
        noWeight2.value = "(6)";
        noWeight2.alignment = alignment;
        noWeight2.font = fontBold;
        noWeight2.fill = fill;
        noWeight2.alignment = alignment;
        noWeight2.border = border;

        const cargoPiece2 = worksheet.getCell(`G${total + EConfigure.INDEX_13 + offset}`);
        cargoPiece2.value = "Số kiện";
        cargoPiece2.alignment = alignment;
        cargoPiece2.font = fontBold;
        cargoPiece2.fill = fill;
        cargoPiece2.alignment = alignment;
        cargoPiece2.border = border;
        const noCargoPiece2 = worksheet.getCell(`G${total + EConfigure.INDEX_14 + offset}`);
        noCargoPiece2.value = "(7)";
        noCargoPiece2.alignment = alignment;
        noCargoPiece2.font = fontBold;
        noCargoPiece2.fill = fill;
        noCargoPiece2.alignment = alignment;
        noCargoPiece2.border = border;

        const price2 = worksheet.getCell(`H${total + EConfigure.INDEX_13 + offset}`);
        price2.value = "Trị giá (VND)";
        price2.alignment = alignment;
        price2.font = fontBold;
        price2.fill = fill;
        price2.alignment = alignment;
        price2.border = border;
        const noPrice2 = worksheet.getCell(`H${total + EConfigure.INDEX_14 + offset}`);
        noPrice2.value = "(8)";
        noPrice2.alignment = alignment;
        noPrice2.font = fontBold;
        noPrice2.fill = fill;
        noPrice2.alignment = alignment;
        noPrice2.border = border;

        const note2 = worksheet.getCell(`I${total + EConfigure.INDEX_13 + offset}`);
        note2.value = "Ghi chú";
        note2.alignment = alignment;
        note2.font = fontBold;
        note2.fill = fill;
        note2.alignment = alignment;
        note2.border = border;
        const noNote2 = worksheet.getCell(`I${total + EConfigure.INDEX_14 + offset}`);
        noNote2.value = "(9)";
        noNote2.alignment = alignment;
        noNote2.font = fontBold;
        noNote2.fill = fill;
        noNote2.alignment = alignment;
        noNote2.border = border;

        worksheet.columns = [
          { key: "no", width: EConfigure.INDEX_5 },
          { key: "declarationNo", width: EConfigure.INDEX_50 },
          { key: "HAWBClearance", width: EConfigure.INDEX_40 },
          { key: "cargoName", width: EConfigure.INDEX_20 },
          { key: "unitWeight", width: EConfigure.INDEX_20 },
          { key: "weight", width: EConfigure.INDEX_15 },
          { key: "cargoPiece", width: EConfigure.INDEX_20 },
          { key: "price", width: EConfigure.INDEX_20 },
          { key: "note", width: EConfigure.INDEX_20 },
        ];
        data10.arr10.forEach(function (item: Record<string, any>) {
          worksheet.addRow({
            no: item.no,
            declarationNo: item.declarationNo,
            HAWBClearance: item.HAWBClearance,
            cargoName: item.cargoName,
            unitWeight: item.unitWeight,
            weight: item.weight,
            cargoPiece: item.cargoPiece,
            price: item.price,
            note: item.note,
          });
        });

        total2 = data10.arr10.length;
        worksheet.mergeCells(`A${total + total2 + EConfigure.INDEX_15 + offset}`, `E${total + total2 + EConfigure.INDEX_15 + offset}`);

        const summaryTotal2 = worksheet.getCell(`A${total + total2 + EConfigure.INDEX_15 + offset}`);
        summaryTotal2.value = "Tổng trị giá tính thuế";
        summaryTotal2.alignment = alignment;
        summaryTotal2.font = fontBold;
        summaryTotal2.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        summaryTotal2.alignment = alignment;
        summaryTotal2.border = border;
        const sumWeight2 = worksheet.getCell(`F${total + total2 + EConfigure.INDEX_15 + offset}`);
        sumWeight2.value = data10.total10['totalWeight10'];
        sumWeight2.alignment = alignment;
        sumWeight2.font = fontBold;
        sumWeight2.alignment = alignment;
        sumWeight2.border = border;

        const sumPiece2 = worksheet.getCell(`G${total + total2 + EConfigure.INDEX_15 + offset}`);
        sumPiece2.value = data10.total10['totalPiece10'];
        sumPiece2.alignment = alignment;
        sumPiece2.font = fontBold;
        sumPiece2.alignment = alignment;
        sumPiece2.border = border;

        const sumPrice2 = worksheet.getCell(`H${total + total2 + EConfigure.INDEX_15 + offset}`);
        sumPrice2.value = data10.total10['totalPrice10'];
        sumPrice2.alignment = alignment;
        sumPrice2.font = fontBold;
        sumPrice2.alignment = alignment;
        sumPrice2.border = border;

        ////
        worksheet.mergeCells(`A${total + total2 + EConfigure.INDEX_16 + offset}`, `E${total + total2 + EConfigure.INDEX_16 + offset}`);
        const percenTotal2 = worksheet.getCell(`A${total + total2 + EConfigure.INDEX_16 + offset}`);
        percenTotal2.value = "Thuế xuất thuế GTGT (%)";
        percenTotal2.alignment = alignment;
        percenTotal2.font = fontBold;
        percenTotal2.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        percenTotal2.alignment = alignment;
        percenTotal2.border = border;
        worksheet.mergeCells(`F${total + total2 + EConfigure.INDEX_16 + offset}`, `I${total + total2 + EConfigure.INDEX_16 + offset}`);
        const percenValue2 = worksheet.getCell(`F${total + total2 + EConfigure.INDEX_16 + offset}`);
        percenValue2.value = "10%";
        percenValue2.alignment = alignment;
        percenValue2.font = fontBold;
        percenValue2.alignment = alignment;
        percenValue2.border = border;

        worksheet.mergeCells(`A${total + total2 + EConfigure.INDEX_17 + offset}`, `E${total + total2 + EConfigure.INDEX_17 + offset}`);
        const taxTotal2 = worksheet.getCell(`A${total + total2 + EConfigure.INDEX_17 + offset}`);
        taxTotal2.value = "Số tiền thuế GTGT (%)";
        taxTotal2.alignment = alignment;
        taxTotal2.font = fontBold;
        taxTotal2.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D6EAF8' },
          bgColor: { argb: 'D6EAF8' },
        };
        taxTotal2.alignment = alignment;
        taxTotal2.border = border;
        worksheet.mergeCells(`F${total + total2 + EConfigure.INDEX_17 + offset}`, `I${total + total2 + EConfigure.INDEX_17 + offset}`);
        const taxValue2 = worksheet.getCell(`F${total + total2 + EConfigure.INDEX_17 + offset}`);
        taxValue2.value = data10.total10['taxPrice10'];
        taxValue2.alignment = alignment;
        taxValue2.font = fontBold;
        taxValue2.alignment = alignment;
        taxValue2.border = border;
      }
      // Signature Left
      const signatureCenter = total + total2 + EConfigure.INDEX_25;
      worksheet.getCell(`B${signatureCenter + EConfigure.INDEX_1}`).value = `Ý kiến của cơ quan hải quan:`;
      worksheet.getCell(`B${signatureCenter + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`B${signatureCenter + EConfigure.INDEX_1}`).font = fontBold;

      const signatureLeft = total + total2 + EConfigure.INDEX_40;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).value = `DOANH NGHIỆP LẬP BẢN KÊ`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).value = `(Ký, đóng dấu)`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).font = {
        italic: true
      }

      // Signature right
      const signatureRigth = total + total2 + EConfigure.INDEX_40;
      worksheet.getCell(`K${signatureRigth}`).value = `TP Hồ Chí Minh, Ngày .... tháng .... năm 20....`;
      worksheet.getCell(`K${signatureRigth}`).alignment = alignment;
      worksheet.getCell(`K${signatureRigth}`).font = { italic: true }
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).value = `CQHQ XÁC NHẬN HÀNG ĐỦ ĐIỀU KIỆN QUA KHU VỰC GIÁM SÁT`;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).value = `(Ký, đóng dấu công chức)`;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`K${signatureRigth + EConfigure.INDEX_2}`).font = { italic: true }
      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}${filename}`)
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][micExcelTT29]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  private async mecExcel(arr: Record<string, any>[], sumTotal: Record<string, any>, filename: string): Promise<void> {
    try {
      const alignment = { vertical: 'middle', horizontal: 'center' };
      const fontBold = { bold: true, name: 'Calibri' };
      const fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '96C8FB' },
        bgColor: { argb: '96C8FB' },
      };
      const border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(`sheet ${EConfigure.INDEX_1}`, {
        properties: {
          tabColor: { argb: 'FFC0000' }
        },
      });

      // Title Left
      let titleFirst = worksheet.getCell(`B1`);
      titleFirst.value = `CÔNG TY CỔ PHẦN CHUYỂN PHÁT NHANH`;
      titleFirst.alignment = alignment,
        titleFirst.font = fontBold;

      const titleSecond = worksheet.getCell(`B2`);
      titleSecond.value = `HÀNG HÓA SÀI GÒN`;
      titleSecond.alignment = alignment,
        titleSecond.font = fontBold;

      const titleThird = worksheet.getCell(`B3`);
      titleThird.value = `-------`;
      titleThird.alignment = alignment,
        titleThird.font = fontBold;

      const titleFour = worksheet.getCell(`B4`);
      titleFour.value = `Số: ……………../BK-SCE`;
      titleFour.alignment = alignment,
        titleFour.font = fontBold;

      // Title Right
      worksheet.mergeCells('H1', 'I4');
      const templateCell = worksheet.getCell('H1');
      templateCell.value = "Mẫu số HQ 02-BKTKTGT";
      templateCell.alignment = alignment;
      templateCell.font = fontBold;

      // Title Center
      const title = worksheet.getCell('D5');
      title.value = "BẢN LƯỢC KHAI HÀNG HÓA";
      title.alignment = alignment;
      title.font = fontBold;

      const subtitle = worksheet.getCell('D6');
      subtitle.value = "Danh sách hàng hóa xuất khẩu trị giá thấp (MEC) đã được thông quan";
      subtitle.alignment = alignment;
      subtitle.font = fontBold;

      // Content Data
      const stt = worksheet.getCell('A9');
      stt.value = "STT";
      stt.alignment = alignment;
      stt.font = fontBold;
      stt.fill = fill;
      stt.alignment = alignment;
      stt.border = border;
      const noStt = worksheet.getCell('A10');
      noStt.value = "(1)";
      noStt.alignment = alignment;
      noStt.font = fontBold;
      noStt.fill = fill;
      noStt.alignment = alignment;
      noStt.border = border;

      const declarationNo = worksheet.getCell('B9');
      declarationNo.value = "Số tờ khai";
      declarationNo.alignment = alignment;
      declarationNo.font = fontBold;
      declarationNo.fill = fill;
      declarationNo.alignment = alignment;
      declarationNo.border = border;
      const noDeclarationNo = worksheet.getCell('B10');
      noDeclarationNo.value = "(2)";
      noDeclarationNo.alignment = alignment;
      noDeclarationNo.font = fontBold;
      noDeclarationNo.fill = fill;
      noDeclarationNo.alignment = alignment;
      noDeclarationNo.border = border;

      const hawb = worksheet.getCell('C9');
      hawb.value = "Số vận đơn";
      hawb.alignment = alignment;
      hawb.font = fontBold;
      hawb.fill = fill;
      hawb.alignment = alignment;
      hawb.border = border;
      const noHawb = worksheet.getCell('C10');
      noHawb.value = "(3)";
      noHawb.alignment = alignment;
      noHawb.font = fontBold;
      noHawb.fill = fill;
      noHawb.alignment = alignment;
      noHawb.border = border;

      const cargoName = worksheet.getCell('D9');
      cargoName.value = "Tên hàng";
      cargoName.alignment = alignment;
      cargoName.font = fontBold;
      cargoName.fill = fill;
      cargoName.alignment = alignment;
      cargoName.border = border;
      const noCargoName = worksheet.getCell('D10');
      noCargoName.value = "(4)";
      noCargoName.alignment = alignment;
      noCargoName.font = fontBold;
      noCargoName.fill = fill;
      noCargoName.alignment = alignment;
      noCargoName.border = border;

      const cargo = worksheet.getCell('E9');
      cargo.value = "Đơn vị tính";
      cargo.alignment = alignment;
      cargo.font = fontBold;
      cargo.fill = fill;
      cargo.alignment = alignment;
      cargo.border = border;
      const noCargo = worksheet.getCell('E10');
      noCargo.value = "(5)";
      noCargo.alignment = alignment;
      noCargo.font = fontBold;
      noCargo.fill = fill;
      noCargo.alignment = alignment;
      noCargo.border = border;

      const weight = worksheet.getCell('F9');
      weight.value = "Trọng lượng";
      weight.alignment = alignment;
      weight.font = fontBold;
      weight.fill = fill;
      weight.alignment = alignment;
      weight.border = border;
      const noWeight = worksheet.getCell('F10');
      noWeight.value = "(6)";
      noWeight.alignment = alignment;
      noWeight.font = fontBold;
      noWeight.fill = fill;
      noWeight.alignment = alignment;
      noWeight.border = border;

      const cargoPiece = worksheet.getCell('G9');
      cargoPiece.value = "Số kiện";
      cargoPiece.alignment = alignment;
      cargoPiece.font = fontBold;
      cargoPiece.fill = fill;
      cargoPiece.alignment = alignment;
      cargoPiece.border = border;
      const noCargoPiece = worksheet.getCell('G10');
      noCargoPiece.value = "(7)";
      noCargoPiece.alignment = alignment;
      noCargoPiece.font = fontBold;
      noCargoPiece.fill = fill;
      noCargoPiece.alignment = alignment;
      noCargoPiece.border = border;

      const price = worksheet.getCell('H9');
      price.value = "Trị giá (VND)";
      price.alignment = alignment;
      price.font = fontBold;
      price.fill = fill;
      price.alignment = alignment;
      price.border = border;
      const noPrice = worksheet.getCell('H10');
      noPrice.value = "(8)";
      noPrice.alignment = alignment;
      noPrice.font = fontBold;
      noPrice.fill = fill;
      noPrice.alignment = alignment;
      noPrice.border = border;

      const note = worksheet.getCell('I9');
      note.value = "Ghi chú";
      note.alignment = alignment;
      note.font = fontBold;
      note.fill = fill;
      note.alignment = alignment;
      note.border = border;
      const noNote = worksheet.getCell('I10');
      noNote.value = "(9)";
      noNote.alignment = alignment;
      noNote.font = fontBold;
      noNote.fill = fill;
      noNote.alignment = alignment;
      noNote.border = border;

      worksheet.columns = [
        { key: "no", width: EConfigure.INDEX_5 },
        { key: "declarationNo", width: EConfigure.INDEX_50 },
        { key: "HAWBClearance", width: EConfigure.INDEX_40 },
        { key: "cargoName", width: EConfigure.INDEX_20 },
        { key: "unitWeight", width: EConfigure.INDEX_20 },
        { key: "weight", width: EConfigure.INDEX_20 },
        { key: "cargoPiece", width: EConfigure.INDEX_20 },
        { key: "price", width: EConfigure.INDEX_20 },
        { key: "note", width: EConfigure.INDEX_20 },
      ];
      arr.forEach(function (item, index) {
        if (item.no != 0) {
          worksheet.addRow({
            no: item.no,
            declarationNo: item.declarationNo,
            HAWBClearance: item.HAWBClearance,
            cargoName: item.cargoName,
            unitWeight: item.unitWeight,
            weight: item.weight,
            cargoPiece: item.cargoPiece,
            price: item.price,
            note: item.note,
          });
        } else {
          worksheet.mergeCells(`A${index + EConfigure.INDEX_11}`, `E${index + EConfigure.INDEX_11}`);
          const summaryTotal = worksheet.getCell(`A${index + EConfigure.INDEX_11}`);
          summaryTotal.value = `${item.boxName} (${item.boxNo})`;
          summaryTotal.font = fontBold;
          summaryTotal.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'E9ECEF' },
            bgColor: { argb: 'D6EAF8' },
          };
          summaryTotal.border = border;

          const sumWeight = worksheet.getCell(`F${index + EConfigure.INDEX_11}`);
          sumWeight.value = item.weight;
          sumWeight.alignment = alignment;
          sumWeight.font = fontBold;
          sumWeight.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'E9ECEF' },
            bgColor: { argb: 'D6EAF8' },
          };
          sumWeight.border = border;

          const sumPiece = worksheet.getCell(`G${index + EConfigure.INDEX_11}`);
          sumPiece.value = item.cargoPiece;
          sumPiece.alignment = alignment;
          sumPiece.font = fontBold;
          sumPiece.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'E9ECEF' },
            bgColor: { argb: 'D6EAF8' },
          };
          sumPiece.border = border;

          const sumPrice = worksheet.getCell(`H${index + EConfigure.INDEX_11} `);
          sumPrice.value = item.price;
          sumPrice.alignment = alignment;
          sumPrice.font = fontBold;
          sumPrice.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'E9ECEF' },
            bgColor: { argb: 'D6EAF8' },
          };
          sumPrice.border = border;
        }
      });

      const total: number = arr.length;
      worksheet.mergeCells(`A${total + EConfigure.INDEX_11}`, `E${total + EConfigure.INDEX_11}`);
      const summaryTotal = worksheet.getCell(`A${total + EConfigure.INDEX_11}`);
      summaryTotal.value = "Tổng cộng:";
      summaryTotal.alignment = alignment;
      summaryTotal.font = fontBold;
      summaryTotal.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D6EAF8' },
        bgColor: { argb: 'D6EAF8' },
      };
      summaryTotal.alignment = alignment;
      summaryTotal.border = border;

      const sumWeight = worksheet.getCell(`F${total + EConfigure.INDEX_11}`);
      sumWeight.value = sumTotal['totalWeight'];
      sumWeight.alignment = alignment;
      sumWeight.font = fontBold;
      sumWeight.alignment = alignment;
      sumWeight.border = border;

      const sumPiece = worksheet.getCell(`G${total + EConfigure.INDEX_11}`);
      sumPiece.value = sumTotal['totalPiece'];
      sumPiece.alignment = alignment;
      sumPiece.font = fontBold;
      sumPiece.alignment = alignment;
      sumPiece.border = border;

      const sumPrice = worksheet.getCell(`H${total + EConfigure.INDEX_11}`);
      sumPrice.value = sumTotal['totalPrice'];
      sumPrice.alignment = alignment;
      sumPrice.font = fontBold;
      sumPrice.alignment = alignment;
      sumPrice.border = border;

      // Footer
      const footer = total + EConfigure.INDEX_12 + 5;
      worksheet.mergeCells(`A${footer}`, `B${footer}`);
      worksheet.getCell(`A${footer}`).value = "Ý kiến của cơ quan hải quan:";
      worksheet.getCell(`A${footer}`).font = fontBold;

      // Signature Left
      const signatureLeft = total + EConfigure.INDEX_15;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).value = `DOANH NGHIỆP LẬP BẢN KÊ`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).value = `(Ký, đóng dấu)`;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`B${signatureLeft + EConfigure.INDEX_2}`).font = {
        italic: true
      };

      // Signature right
      const signatureRigth = total + EConfigure.INDEX_15;
      worksheet.getCell(`H${signatureRigth}`).value = `TP Hồ Chí Minh, Ngày .... tháng .... năm 20....`;
      worksheet.getCell(`H${signatureRigth}`).alignment = alignment;
      worksheet.getCell(`H${signatureRigth}`).font = {
        italic: true
      };
      worksheet.getCell(`H${signatureRigth + EConfigure.INDEX_1}`).value = `CQHQ XÁC NHẬN HÀNG ĐỦ ĐIỀU KIỆN QUA KHU VỰC GIÁM SÁT`;
      worksheet.getCell(`H${signatureRigth + EConfigure.INDEX_1}`).alignment = alignment;
      worksheet.getCell(`H${signatureRigth + EConfigure.INDEX_1}`).font = fontBold;
      worksheet.getCell(`H${signatureRigth + EConfigure.INDEX_2}`).value = `(Ký, đóng dấu công chức)`;
      worksheet.getCell(`H${signatureRigth + EConfigure.INDEX_2}`).alignment = alignment;
      worksheet.getCell(`H${signatureRigth + EConfigure.INDEX_2}`).font = {
        italic: true
      };
      await workbook.xlsx.writeFile(`${EConfigure.FOLDER_INVOICE}${filename}`)
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][mecExcel]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public async eOutboundPdfGenerate(value: any, hubs: any, employeeId: number): Promise<IResponze> {
    try {
      let reponze: IResponze = {
        'status': true,
        'message': {
          'message': EMessage.NOT_FOUND,
          'data': null,
          'error': []
        }
      }
      let isBox = false;
      const printType: number = value['printType'];
      const groupBox: boolean = value['groupBox'] != null ? value['groupBox'] : false;
      const isLandscape: boolean = value['isLandscape'] || false;
      const mecOptional: Optional = new Optional();
      mecOptional.setRelation(["exportDetails", "manifest"]);
      mecOptional.setAttributes(["MAWB", "hubId", "declarationNo", "HAWBClearance", "hubId", "cargoPiece", "cargoWeightGross", "priceVND", "notes", "externalBoxName", "internalBoxName", "exporterFullName", "addressOfExporter"]);
      mecOptional.setWhere([
        new Where(EConfigure.AND, 'isHold', EConfigure.EQUAL, false),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false),
      ]);
      mecOptional.setOrderby([new OrderBy("createdAt", Configure.DESCENDING)]);
      if (value['classify']) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'classify', EConfigure.EQUAL, value['classify'])
        );
      }
      if (value['phase']) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'phase', EConfigure.EQUAL, value['phase'])
        );
      }
      if (value['MAWB']) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'MAWB', EConfigure.EQUAL, value['MAWB'])
        );
      }
      if (value['boxes'] && value['boxes'].length > 0) {
        isBox = true;
        mecOptional.getWhere().push(
          new Where(EConfigure.OR, 'externalBoxName', EConfigure.IN, value['boxes'].join()),
          new Where(EConfigure.OR, 'internalBoxName', EConfigure.IN, value['boxes'].join())
        );
      }
      if (hubs) {
        mecOptional.getWhere().push(
          new Where(EConfigure.AND, 'hubId', EConfigure.IN, hubs.split(',').join())
        );
      }
      mecOptional.setOrderby([new OrderBy('externalBoxName', EConfigure.DESCENDING)])

      let MECs: eIMEC[] = await this.eMECRepository.queryAll(mecOptional);
      if (MECs.length > 0) {
        let obj: any = {};
        let hubId: number = 0;
        let mawb: string = '';
        let mawbs: Set<string> = new Set();
        if (MECs.length > 0 && (printType == PrintKey.INVOICE_MEC_TT29 || printType == PrintKey.DETAIL_INVOICE_MEC_TT29)) {
          hubId = MECs[0]["hubId"]!;
          mawb = MECs[0]["MAWB"]!;
          MECs.forEach((mec: eIMEC) => {
            mawbs.add(mec["MAWB"]!);
          });
          if (value['boxes'] && value['boxes'].length > 0) {
            const idaExternalBoxes = _.groupBy(MECs, MIC => MIC['externalBoxName']);
            const idaInternalBoxes = _.groupBy(MECs, MIC => MIC['internalBoxName']);
            value['boxes'].forEach((item: any) => {
              obj[item] = [
                ...idaExternalBoxes[item] ? idaExternalBoxes[item] : [],
                ...idaInternalBoxes[item] ? idaInternalBoxes[item] : []
              ];
            })
          } else {
            MECs = _.shuffle(MECs);
            MECs.forEach((mec: eIMEC) => {
              let key: string = 'none';
              if (mec['externalBoxName']) {
                key = mec['externalBoxName'];
              } else if (mec['internalBoxName']) {
                key = mec['internalBoxName'];
              }
              if (obj[key]) {
                obj[key].push(mec);
              } else {
                obj[key] = [mec];
              }
            });
          }
        }
        const now: string = moment().format('DD-MM-YYYY');
        const config: pdf.CreateOptions = {
          "format": "A4",
          "orientation": isLandscape ? "landscape" : "portrait",
          "border": {
            "top": "8px",
            "right": "10px",
            "bottom": "20px",
            "left": "10px"
          },
          timeout: 300000
        }
        let label: String = '';
        let pathTemplate: string = '';
        let filename: string = '';
        let excelName: string = '';
        if (printType == PrintKey.INVOICE_MEC) {
          let classify: String = 'MEC';
          const name: string = `bangke${classify}-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          excelName = `${name}.xlsx`;
          pathTemplate = `../../../../../template/bangke/bangkemec.ejs`;
          label = `Danh sách hàng hóa xuất khẩu trị giá thấp (${classify}) đã được thông quan`;
          const arr: Record<string, any>[] = [];
          let no = 1;
          let boxNo = 1;
          let totalWeight = 0;
          let totalPiece = 0;
          let totalPrice = 0;
          let nameBox = "";
          for (const key in obj) {
            if (obj[key].length == 0) {
              continue;
            }
            nameBox = key;
            let totalPriceBox = 0;
            let totalPcsBox = 0;
            let totalWeightBox = 0;
            obj[key].forEach((mic: IMEC, index: number) => {
              const itemNameVN: String[] = [];
              if (mic['exportDetails'].length > 0) {
                mic['exportDetails'].forEach((detail: Record<string, any>) => {
                  itemNameVN.push(String(detail['itemNameVN']));
                });
              }
              arr.push({
                "no": no,
                "declarationNo": mic['declarationNo'] != null ? mic['declarationNo'] : "",
                "HAWBClearance": mic['HAWBClearance'],
                "cargoName": itemNameVN.join(','),
                "unitWeight": 'KGM',
                "weight": mic['cargoWeight'],
                "cargoPiece": mic['cargoPiece'],
                "price": Number(mic['priceVND']),
                "note": mic['notes'],
                "boxName": mic['externalBoxName'],
              });
              totalWeight += Number(mic['cargoWeight']);
              totalPiece += Number(mic['cargoPiece']);
              totalPrice += Number(mic['priceVND']);
              totalPcsBox += Number(mic['cargoPiece']);
              totalWeightBox += Number(mic['cargoWeight']);
              totalPriceBox += Number(mic['priceVND']);
              no += 1;
            });
            if (groupBox) {
              arr.push({
                "no": 0,
                "boxNo": boxNo,
                "declarationNo": "",
                "HAWBClearance": "",
                "cargoName": "",
                "unitWeight": "",
                "weight": totalWeightBox,
                "cargoPiece": totalPcsBox,
                "price": totalPriceBox,
                "note": "",
                "boxName": nameBox,
              });
              boxNo += 1;
            }
          }
          this.mecExcel(arr, { totalWeight, totalPiece, totalPrice }, excelName);
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                  <tr>
                      <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                      <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                      <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                  </tr>
              </table>`, // fallback value
            }
          }
        }
        else if (printType == PrintKey.INVOICE_MEC_TT29) {
          let classify: String = 'MEC_TT29';
          const name: string = `bangke${classify}-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          excelName = `${name}.xlsx`;
          const arr10: Record<string, any>[] = [];
          const arr8: Record<string, any>[] = [];
          let no10 = 1;
          let totalWeight10 = 0;
          let totalPiece10 = 0;
          let totalPrice10 = 0;
          let no8 = 1;
          let totalWeight8 = 0;
          let totalPiece8 = 0;
          let totalPrice8 = 0;
          for (const key in obj) {
            if (obj[key].length == 0) {
              continue;
            }
            obj[key].forEach((mec: eIMEC) => {
              const itemNameVN: String[] = [];
              if (mec['exportDetails']!.length > 0) {
                mec['exportDetails']!.forEach((detail: Record<string, any>) => {
                  itemNameVN.push(String(detail['itemNameVN']));
                });
              }
              const currentEntry = {
                "declarationNo": mec['declarationNo'] != null ? mec['declarationNo'] : "",
                "HAWBClearance": mec['HAWBClearance'],
                "cargoName": itemNameVN.join(','),
                "unitWeight": 'KGM',
                "weight": (mec['cargoWeightGross']! > 0) ? Number(mec['cargoWeightGross']).toFixed(2) : 0,
                "cargoPiece": mec['cargoPiece'],
                "price": (mec['priceVND']! > 0) ? Number(mec['priceVND']).toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,") : 0,
                "note": mec['notes'],
                "boxName": mec['externalBoxName'],
              };
              if (mec['percentTaxPrint'] == null || mec['percentTaxPrint'] === '10') {
                arr10.push({ "no": no10, ...currentEntry });
                totalWeight10 += Number(mec['cargoWeightGross']);
                totalPiece10 += Number(mec['cargoPiece']);
                totalPrice10 += Number(mec['priceVND']);
                no10 += 1;
              } else {
                arr8.push({ "no": no8, ...currentEntry });
                totalWeight8 += Number(mec['cargoWeightGross']);
                totalPiece8 += Number(mec['cargoPiece']);
                totalPrice8 += Number(mec['priceVND']);
                no8 += 1;
              }
            });
          }
          this.excelTT29({
            "data8": { "arr8": arr8, "total8": { totalWeight8, totalPiece8, totalPrice8, taxPrice8: totalPrice8 * 0.08 } },
            "data10": { "arr10": arr10, "total10": { totalWeight10, totalPiece10, totalPrice10, taxPrice10: totalPrice10 * 0.1 } }
          }, excelName, "");
          pathTemplate = `../../../../../template/bangke/bangke_emec_tt29.ejs`;
          label = `Danh sách hàng hóa xuất khẩu trị giá thấp`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `
              <table width="100%;" style="font-size: 8px;">
                <tr>
                  <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                  <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                  <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                </tr>
              </table>`,
            }
          }
        }
        if (printType == PrintKey.DETAIL_INVOICE_MEC_TT29) {
          const name: string = `bangkechitiethanghoa_tt29-${mawb}_${moment().format("X")}`;
          filename = `${name}.pdf`;
          pathTemplate = `../../../../../template/bangke/bangkechitietmec_tt29.ejs`;
          config['footer'] = {
            "height": "12mm",
            "contents": {
              default: `<table width="100%;" style="font-size: 8px;">
                <tr>
                  <td width="10%;" style="padding:0;text-align: left;">Ngày: ${now}</td>
                  <td width="10%;" style="padding:0;text-align: center;">Master awb: ${[...mawbs].join(', ')}</td>
                  <td width="10%;" style="padding:0;text-align: right;">Trang {{page}}/{{pages}}</td>
                </tr>
              </table>`,
            }
          }
        }
        const createHistory = this.historyInvoiceService.create({
          MAWB: mawb,
          employeeId: employeeId,
          printType: printType,
          hubId: hubId,
          isSucess: false,
          dataSearch: value,
          note: value['note'],
        });
        const [historyInvoice, template] = await Promise.all([
          createHistory,
          ejs.renderFile(path.join(__dirname, pathTemplate), { MICs: obj, label, now, printType, isBox: groupBox, subTitle: "", hasFooter: true, itemLanguage: 'vi' }, { async: true }),
        ]);
        this.pdfCreater({ template, config, filename, historyInvoice, excelName: excelName });
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.SUCCESS;
        reponze['message']['data'] = historyInvoice;
      }
      return reponze;
    } catch (error) {
      Utilities.sendDiscordErr('[service][PdfGenerate][pdfPrint]', (error as Error).message);
      throw new Error(error as any);
    }
  }
}

export default PdfGenerate;