'use strict';

import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import EMessage from '../../../emuns/messages';
import { Database } from '../../../database/index';
import { IResponze } from "../../../https/responze";
import MICRepository from "../importClearance/MICClearance.reporsitory";
import IDARepository from "../importClearance/IDAClearance.repository";
import LogPrintRepository from "./logPrint.reporsitory";
import { MIC } from '../../../models/mic.model';
import { ILogPrint, IDA, MEC, EDA } from '../../../models/index.model';
import { PrintN<PERSON>, PrintKey } from '../../../emuns/print';
import MECRepository from '../exportClearance/MECClearance.reporsitory';
import EDARepository from '../exportClearance/EDAClearance.reporsitory';


class LogPrintService extends BaseService {
  private micRepository: MICRepository;
  private mecRepository: MECRepository;
  private idaRepository: IDARepository;
  private edaRepository: EDARepository;
  private logPrintRepository: LogPrintRepository;
  private _sequelize: any;
  constructor () {
    super(new LogPrintRepository());
    this.micRepository = new MICRepository();
    this.idaRepository = new IDARepository();
    this.mecRepository = new MECRepository();
    this.edaRepository = new EDARepository();
    this.logPrintRepository = new LogPrintRepository();
    this._sequelize = Database.Sequelize;
  }

  public async getAllIDA(optional: Optional): Promise<any> {
    try {
      const objData = await this.idaRepository.getAll(optional);
      return objData;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async edaPrintCount(HAWBs: string[], employeeId: number, type: string): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
      ]);
      const EDAs: EDA[] = await this.edaRepository.queryAll(optional);
      if(EDAs.length > 0) {
        transaction = await this._sequelize.transaction();
        await Promise.all(EDAs.map(async (EDA: EDA) => {
          const createLog: ILogPrint = {
            'HAWB': EDA['HAWB'],
            'employeeId': employeeId,
          }
          const typePrint = this.handeTypePrint(EDA, createLog, type);
          await typePrint.dataUpdate.save(transaction);
          await this.logPrintRepository.createDataTrx(typePrint.dataCreate, transaction);
        }));
        
        await transaction.commit();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      } else {
        reponze['message']['message'] = EMessage.NOT_FOUND;
      }
      return reponze;
    } catch (error) {
      await transaction.rollback();
      throw new Error(error as any);
    }
  }

  public async mecPrintCount(HAWBs: string[], employeeId: number, type: string): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
      ]);
      const MECs: MEC[] = await this.mecRepository.queryAll(optional);
      if(MECs.length > 0) {
        transaction = await this._sequelize.transaction();
        await Promise.all(MECs.map(async (MEC: MEC) => {
          const createLog: ILogPrint = {
            'HAWB': MEC['HAWB'],
            'employeeId': employeeId,
          }
          const typePrint = this.handeTypePrint(MEC, createLog, type);
          await typePrint.dataUpdate.save(transaction);
          await this.logPrintRepository.createDataTrx(typePrint.dataCreate, transaction);
        }));
        
        await transaction.commit();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      } else {
        reponze['message']['message'] = EMessage.NOT_FOUND;
      }
      return reponze;
    } catch (error) {
      await transaction.rollback();
      throw new Error(error as any);
    }
  }

  public async micPrintCount(HAWBs: string[], employeeId: number, type: string): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
      ]);
      const MICs: MIC[] = await this.micRepository.queryAll(optional);
      if(MICs.length > 0) {
        transaction = await this._sequelize.transaction();
        await Promise.all(MICs.map(async (MIC: MIC) => {
          const createLog: ILogPrint = {
            'HAWB': MIC['HAWB'],
            'employeeId': employeeId,
          }
          const typePrint = this.handeTypePrint(MIC, createLog, type);
          await typePrint.dataUpdate.save(transaction);
          await this.logPrintRepository.createDataTrx(typePrint.dataCreate, transaction);
        }));
        
        await transaction.commit();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      } else {
        reponze['message']['message'] = EMessage.NOT_FOUND;
      }
      return reponze;
    } catch (error) {
      await transaction.rollback();
      throw new Error(error as any);
    }
  }

  public async idaPrintCount(HAWBs: string[], employeeId: number, type: string): Promise<any> {
    let transaction: any;
    try {
      let reponze: IResponze = {
        'status': false,
        'message': {
          'message': EMessage.UPDATE_FAIL,
          'data': []
        }
      }
      const optional: Optional = new Optional();
      optional.setWhere([
        new Where(EConfigure.AND, EConfigure.HAWB_FIELD, EConfigure.IN, HAWBs.join())
      ]);
      const IDAs: IDA[] = await this.idaRepository.queryAll(optional);
      if(IDAs.length > 0) {
        transaction = await this._sequelize.transaction();
        await Promise.all(IDAs.map(async (IDA: IDA) => {
          const createLog: ILogPrint = {
            'HAWB': IDA['HAWB'],
            'employeeId': employeeId,
          }
          const typePrint = this.handeTypePrint(IDA, createLog, type);
          await typePrint.dataUpdate.save(transaction);
          await this.logPrintRepository.createDataTrx(typePrint.dataCreate, transaction);
        }));
        
        await transaction.commit();
        reponze['status'] = true;
        reponze['message']['message'] = EMessage.UPDATE_SUCCESS;
      } else {
        reponze['message']['message'] = EMessage.NOT_FOUND;
      }
      return reponze;
    } catch (error) {
      await transaction.rollback();
      throw new Error(error as any);
    }
  }

  private handeTypePrint(dataUpdate: any, dataCreate: any, type: string) {
    if(type === PrintName.get(PrintKey.CLEARANCED)) {
      dataCreate['isClearancedPrint'] = true;
      dataUpdate['clearancedTimes'] = (dataUpdate['clearancedTimes'] === null)?1:Number(dataUpdate['clearancedTimes']) + 1;
    }
    if(type === PrintName.get(PrintKey.DECLARATION)) {
      dataCreate['isClearanceDeclarationPrint'] = true;
      dataUpdate['clearanceDeclarationTimes'] = (dataUpdate['clearanceDeclarationTimes'] === null)?1:Number(dataUpdate['clearanceDeclarationTimes']) + 1;
    }
    if(type === PrintName.get(PrintKey.INSPECTION)) {
      dataCreate['isInspectionKindPrint'] = true;
      dataUpdate['inspectionKindTimes'] = (dataUpdate['inspectionKindTimes'] === null)?1:Number(dataUpdate['inspectionKindTimes']) + 1;
    }
    return {
      dataUpdate,
      dataCreate
    }
  }
}

export default LogPrintService;