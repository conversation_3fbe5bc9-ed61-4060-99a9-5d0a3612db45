'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';
import Utilities from '../../../util/utilities';
import { ClassifyName } from '../../../emuns/classify';
import ImportClearanceDetailValidate from '../importClearanceDetail/importClearanceDetail.validate';
import { PrintName } from '../../../emuns/print';
import { WeightName } from '../../../emuns/weight';

class LogPrintValidate {

  static create: any = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).required().messages({
      "any.required": `HAWBs ${EMessage.REQUIRED}`,
      "array.base": `HAWBs ${EMessage.ARRAY}`,
      "array.min": `HAWBs ${EMessage.LEAST_ITEM_1}`,
    }),
    type: Utilities.stringValid(PrintName).max(3).trim().messages({
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
  });

  

  static update: any = Joi.object({
    items: Joi.array().min(1).items(ImportClearanceDetailValidate.updateIDAName).messages({
      "array.base": `items ${EMessage.ARRAY}`,
      "array.min": `items ${EMessage.LEAST_ITEM_1}`,
    }),
    HAWB: Joi.string().trim().max(20).messages({
      "string.empty": `HAWB ${EMessage.EMPTYED}`,
      "string.max": `HAWB ${EMessage.MAX_20}`,
    }),
    customsOffice: Joi.string().trim().max(8).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_8}`,
    }),
    customsSubSection: Joi.string().trim().max(4).messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_4}`,
    }),

    departurePlannedDate: Joi.string().max(20).messages({
      "string.empty": `departurePlannedDate ${EMessage.EMPTYED}`,
      "string.max": `departurePlannedDate ${EMessage.MAX_20}`,
    }),
    exporterCode: Joi.string().trim().max(15).messages({
      "string.empty": `exporterCode ${EMessage.EMPTYED}`,
      "string.max": `exporterCode ${EMessage.MAX_15}`,
    }),
    exporterName: Joi.string().trim().max(255).messages({
      "string.empty": `exporterName ${EMessage.EMPTYED}`,
      "string.max": `exporterName ${EMessage.MAX_255}`,
    }),
    addressOfExporter: Joi.string().trim().required().messages({
      "any.required": `addressOfExporter ${EMessage.REQUIRED}`,
      "string.empty": `addressOfExporter ${EMessage.EMPTYED}`,
    }),
    telephoneNumber: Joi.string().trim().max(15).messages({
      "string.empty": `telephoneNumber ${EMessage.EMPTYED}`,
      "string.max": `telephoneNumber ${EMessage.MAX_15}`,
    }),
    consigneeName: Joi.string().trim().max(255).messages({
      "string.empty": `consigneeName ${EMessage.EMPTYED}`,
      "string.max": `consigneeName ${EMessage.MAX_255}`,
    }),

    address1: Joi.string().messages({
      "string.empty": `address1 ${EMessage.EMPTYED}`,
    }),
    countryCode: Joi.string().trim().max(2).required().messages({
      "any.required": `countryCode ${EMessage.REQUIRED}`,
      "string.empty": `countryCode ${EMessage.EMPTYED}`,
      "string.max": `countryCode ${EMessage.MAX_2}`,
    }),
    cargoPiece: Joi.number().integer().min(1).required().messages({
      "any.required": `cargoPiece ${EMessage.REQUIRED}`,
      "number.base": `cargoPiece ${EMessage.NUMBER}`,
      "number.integer": `cargoPiece ${EMessage.INTEGER}`,
      "number.min": `cargoPiece ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    cargoWeightGross: Joi.number().min(0).greater(0).required().messages({
      "any.required": `cargoWeightGross ${EMessage.REQUIRED}`,
      "number.base": `cargoWeightGross ${EMessage.NUMBER}`,
      "number.min": `cargoWeightGross ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `cargoWeightGross ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    invoiceCurrencyCode: Joi.string().trim().max(3).required().messages({
      "any.required": `invoiceCurrencyCode ${EMessage.REQUIRED}`,
      "string.empty": `invoiceCurrencyCode ${EMessage.EMPTYED}`,
      "string.max": `invoiceCurrencyCode ${EMessage.MAX_3}`,
    }),
    totalInvoicePrice: Joi.number().min(0).greater(0).required().messages({
      "any.required": `totalInvoicePrice ${EMessage.REQUIRED}`,
      "number.base": `totalInvoicePrice ${EMessage.NUMBER}`,
      "number.min": `totalInvoicePrice ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `totalInvoicePrice ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    valueClearanceVND: Joi.number().min(0).greater(0).required().messages({
      "any.required": `valueClearanceVND ${EMessage.REQUIRED}`,
      "number.base": `valueClearanceVND ${EMessage.NUMBER}`,
      "number.min": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
      "number.greater": `valueClearanceVND ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    unitOfMass: Utilities.stringValid(WeightName).max(3).trim().messages({
      "string.empty": `unitOfMass ${EMessage.EMPTYED}`,
      "string.max": `unitOfMass ${EMessage.MAX_3}`,
    }),
    ///
    serviceId: Joi.number().integer().required().min(1).messages({
      "any.required": `serviceId ${EMessage.REQUIRED}`,
      "number.base": `serviceId ${EMessage.NUMBER}`,
      "number.integer": `serviceId ${EMessage.INTEGER}`,
      "number.min": `serviceId ${EMessage.GREATE_EQUAL_THAN_1}`,
    }),
    classify: Utilities.stringValid(ClassifyName).required().trim().max(3).messages({
      "any.required": `classify ${EMessage.REQUIRED}`,
      "string.empty": `classify ${EMessage.EMPTYED}`,
      "string.max": `classify ${EMessage.MAX_3}`,
    }),
    customerBusinessId: Joi.string().trim().messages({
      "string.empty": `customerBusinessId ${EMessage.EMPTYED}`,
    }),
    customerPersonalId: Joi.string().trim().messages({
      "string.empty": `customerPersonalId ${EMessage.EMPTYED}`,
    }),
  });

  static arrUpdate = Joi.array().min(1).max(300).items(LogPrintValidate.update).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`,
    "array.max": `data ${EMessage.MAX_ITEM_50}`,
  });

  static master = Joi.object({
    HAWBs: Joi.array().min(1).items(Joi.string().trim()).required().messages({
      "any.required": `HAWBs ${EMessage.REQUIRED}`,
      "array.base": `HAWBs ${EMessage.ARRAY}`,
      "array.min": `HAWBs ${EMessage.LEAST_ITEM_1}`,
    }),
    MAWB: Joi.string().trim().required().max(50).messages({
      "any.required": `MAWB ${EMessage.REQUIRED}`,
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
      "string.max": `MAWB ${EMessage.MAX_50}`,
    }),
    shipmentID: Joi.string().max(50).trim().required().messages({
      "any.required": `shipmentID ${EMessage.REQUIRED}`,
      "string.empty": `shipmentID ${EMessage.EMPTYED}`,
      "string.max": `shipmentID ${EMessage.MAX_50}`,
    }),
    flightNo: Joi.string().max(50).trim().required().messages({
      "any.required": `flightCode ${EMessage.REQUIRED}`,
      "string.empty": `flightCode ${EMessage.EMPTYED}`,
      "string.max": `flightCode ${EMessage.MAX_50}`,
    }),
  })

  static arrMaster = Joi.array().min(1).max(300).items(LogPrintValidate.master).required().messages({
    "any.required": `data ${EMessage.REQUIRED}`,
    "array.base": `data ${EMessage.ARRAY}`,
    "array.min": `data ${EMessage.LEAST_ITEM_1}`,
    "array.max": `data ${EMessage.MAX_ITEM_50}`,
  });
}

export default LogPrintValidate;
