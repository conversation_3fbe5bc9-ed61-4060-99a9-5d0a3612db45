'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import Middlewares from '../../../middleware/middlewares';
import ParserUrl from '../../../parser/url';
import LogPrintService from './logPrint.service';
import EConfigure from '../../../emuns/configures';
import Optional from '../../../parser/optional';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import Meta from "../../../https/meta";
import HttpData from "../../../https/data";
import ErrorValidate from "../../../util/error.validate";
import LogPrintValidate from "./logPrint.validate";
import { IResponze } from "../../../https/responze";
import RedisPromise from "../../../util/redis.promise";
import UserService from "../user/user.service";

class LogPrintController {
  public path = '/log_prints';
  public router = express.Router();
  constructor() {
    const middlewares = new Middlewares();
    this.router.get(this.path, this.getAll);
    
    this.router.post(`${this.path}/mic`, this.createLogMIC);
    this.router.post(`${this.path}/ida`, this.createLogIDA);

    this.router.post(`${this.path}/mec`, this.createLogMEC);
    this.router.post(`${this.path}/eda`, this.createLogEDA);
  }

  private async createLogEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } =  LogPrintValidate.create.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: any = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const logPrintService =  new LogPrintService();
      const { HAWBs, type } = value;
      const data: IResponze = await logPrintService.edaPrintCount(HAWBs, employeeId, type);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][log_print_ida][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async createLogMEC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } =  LogPrintValidate.create.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: any = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const logPrintService =  new LogPrintService();
      const { HAWBs, type } = value;
      const data: IResponze = await logPrintService.mecPrintCount(HAWBs, employeeId, type);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][log_print_mec][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async createLogMIC(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } =  LogPrintValidate.create.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: any = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const logPrintService =  new LogPrintService();
      const { HAWBs, type } = value;
      const data: IResponze = await logPrintService.micPrintCount(HAWBs, employeeId, type);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][log_print_mic][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async createLogIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } =  LogPrintValidate.create.validate(req.body, { abortEarly: false });
      if(error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string  = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { identity } = res['locals'];
      let employeeId: any = 0;
      try {
        const profile: any = await RedisPromise.getData(identity);
        employeeId = profile['employeeId'];
      } catch (error) {
        const userService = new UserService();
        const userInfo = await userService.getUserByIdentity(identity);
        employeeId = userInfo['employeeId'];
      }
      const logPrintService =  new LogPrintService();
      const { HAWBs, type } = value;
      const data: IResponze = await logPrintService.idaPrintCount(HAWBs, employeeId, type);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][log_print_ida][create]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }


  private async getAll(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();
      const logPrintService = new LogPrintService();
      const [data, total] = await logPrintService.getAll(optional);
      const meta = new Meta(optional.getLimit(), optional.getOffset(), total, optional.getOrderby());
      return HttpResponse.sendMetaData(res, httpStatus.OK, data, meta);
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][import][get_all]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  
}

export default LogPrintController;