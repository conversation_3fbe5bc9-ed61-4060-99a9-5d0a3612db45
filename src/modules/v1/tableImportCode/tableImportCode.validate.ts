'user strict'

import Joi from '@hapi/joi';
import EMessage from '../../../emuns/messages';

class HSCodeValidate {
  static create: any = Joi.object({
    code: Joi.string().trim().required().max(50).messages({
      "any.required": `code ${EMessage.REQUIRED}`,
      "string.empty": `code ${EMessage.EMPTYED}`,
      "string.max": `code ${EMessage.MAX_50}`,
    }),
    name: Joi.string().trim().messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
    }),
  });

  static arrCreate: any = Joi.array().items(HSCodeValidate.create).unique().required();
}

export default HSCodeValidate;
