'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import httpStatus = require("http-status");
import moment = require('moment');
import HttpException from "../../../https/exception";
import Meta from "../../../https/meta";
import HttpResponse from "../../../https/response";
import Optional from "../../../parser/optional";
import ParserUrl from "../../../parser/url";
import ImportClearanceService from '../importClearance/importClearance.service';
import EConfigure from '../../../emuns/configures';
import { IResponze } from "../../../https/responze";
import HttpData from "../../../https/data";
import ImportClearanceValidate from "../importClearance/importClearance.validate";
import ErrorValidate from "../../../util/error.validate";
import ExportClearanceService from "../exportClearance/exportClearance.service";
import ExportClearanceValidate from "../exportClearance/exportClearance.validate";


class NonAuthenController {
  public path = '/nonAuthen';
  public router = express.Router();

  constructor() {
    this.router.get(`${this.path}/imports/:HAWB`, this.getOneMICIDA);
    this.router.get(`${this.path}/exports/:HAWB`, this.getOneMECEDA);
    this.router.get(`${this.path}/printAWB/:HAWB`, this.printAWB);

    this.router.post(`${this.path}/importMAWB`, this.importMAWB);
    this.router.post(`${this.path}/exportMAWB`, this.exportMAWB);
    this.router.post(`${this.path}/boxMAWB`, this.boxMAWB);
    this.router.post(`${this.path}/exportLane`, this.exportLane);
  }

  private async boxMAWB(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.boxMAWB.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { externalBoxName } = value;
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.boxMAWB(externalBoxName);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']['data']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][masterLimit]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async exportMAWB(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.masterLimit.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { hubId } = value;
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.masterLimit(hubId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']['data']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][outbound][masterLimit]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async importMAWB(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ImportClearanceValidate.masterLimit.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { hubId } = value;
      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.masterLimit(hubId);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']['data']));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][inbound][masterLimit]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneMICIDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { HAWB } = req.params;
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();

      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getOneMICIDA(HAWB, optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][nonAuthenController][import][all_MIC_IDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async getOneMECEDA(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { HAWB } = req.params;
      const parseUrl: ParserUrl = new ParserUrl(req.query);
      const optional: Optional = parseUrl.handleParameter();

      const importClearanceService = new ImportClearanceService();
      const data: IResponze = await importClearanceService.getOneMECEDA(HAWB, optional);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][nonAuthenController][import][all_MEC_EDA]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async printAWB(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { HAWB } = req.params;
      const exportService = new ExportClearanceService();
      const data: IResponze = await exportService.printAWB(HAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']));
    } catch (error) {
      console.log(' \t --- [%s] [error][nonAuthenController][import][printAWB]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }

  private async exportLane(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { error, value } = ExportClearanceValidate.HAWB.validate(req.body, { abortEarly: false, stripUnknown: true });
      if (error) {
        let errorValidate: ErrorValidate = new ErrorValidate(error);
        let message: string = errorValidate.handleError();
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, message));
      }
      const { HAWB } = value;
      const exportClearanceService = new ExportClearanceService();
      const data: IResponze = await exportClearanceService.exportLane(HAWB);
      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(data['status'], data['message']['data']));
    } catch (error) {
      console.log(' \t --- [%s] [error][nonAuthenController][export][exportLane]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default NonAuthenController;