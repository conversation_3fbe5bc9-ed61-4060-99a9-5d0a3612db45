'use strict';

import * as express from "express";
import { Request, Response, NextFunction } from "express";
import Middlewares from '../../../middleware/middlewares';
import EConfigure from '../../../emuns/configures';
import moment = require('moment');
import HttpResponse from '../../../https/response';
import httpStatus = require('http-status');
import HttpException from '../../../https/exception';
import HttpData from '../../../https/data';
import { ActionKey, ActionName } from "../../../emuns/action";

class ActionController {
  public path = '/actions';
  public router = express.Router();

  constructor() {
    const middlewares = new Middlewares();
    this.router.get(this.path, this.action);
  }

  private async action(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      let micClearance: any = {};
      let idaClearance: any = {};
      const micClearanceAction: number[] = [
        ActionKey.CREATE,
        ActionKey.UPDATE_CARGO_NAME,
        
        // ActionKey.SUBMIT_CUSTOM_CLEARANCE,
        // ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE,

        // ActionKey.INSPECTION_KIND,
        ActionKey.ACCEPT_CLEARANCE,
        ActionKey.RETURN_CARGO_DELIVERY,
      ];
      ActionName.forEach(function(value, key){
        if(micClearanceAction.includes(key)){
          micClearance[key] = value;
        }
      });

      const idaClearanceAction: number[] = [
        ActionKey.CREATE, 
        ActionKey.UPDATE_CARGO_NAME,
        
        // ActionKey.SUBMIT_INFOMATION, // IDA / EDA
        ActionKey.SENT_INFOMATION,

        // ActionKey.SUBMIT_CUSTOM_CLEARANCE,
        // ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE,
        
        // ActionKey.INSPECTION_KIND,
        ActionKey.ACCEPT_CLEARANCE,
        ActionKey.RETURN_CARGO_DELIVERY,
      ];
      ActionName.forEach(function(value, key){
        if(idaClearanceAction.includes(key)){
          idaClearance[key] = value;
        }
      });


      return HttpResponse.sendData(res, httpStatus.OK, new HttpData(true, 
        {
          'micClearance': micClearance,
          'idaClearance': idaClearance,
        }
      ));
    } catch (error) {
      console.log(' \t --- [%s] [error][controller][constant][currency]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.sendMessage(res, httpStatus.INTERNAL_SERVER_ERROR, new HttpException(false, (error as any).message));
    }
  }
}

export default ActionController;