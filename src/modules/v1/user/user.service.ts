'use strict';

import BaseService from '../../index.service';
import Optional from "../../../parser/optional";
import Where from "../../../parser/where";
import EConfigure from '../../../emuns/configures';
import UserRepository from './user.reporsitory';

class UserService extends BaseService {
  private userRepository: UserRepository;
  constructor () {
    super(new UserRepository());
    this.userRepository = new UserRepository();
  }

  public async getUserByIdentity(identity: string): Promise<any> {
    try {
      const optional = new Optional();
      optional.setWhere([new Where(EConfigure.AND, 'identity', EConfigure.EQUAL, identity), 
        new Where(EConfigure.AND, 'isActivated', EConfigure.EQUAL, true),
        new Where(EConfigure.AND, 'isDeleted', EConfigure.EQUAL, false)
      ]);
      // optional.setRelation(['groupPermission', 'employee', 'client']);
      optional.setRelation(['employee']);
      const user: any = await this.userRepository.getOneOptional(optional);
      let userInfo: any = {}
      if(user) {
        if(user['groupPermission']){
          userInfo['permissions'] = user['groupPermission']['permissions'].map((element: any) => {
            return element['key'];
          });
        }
        userInfo['clients'] = user['clientIds'];
        userInfo['stations'] = user['stationIds'];
        userInfo['hubs'] = user['hubIds'];
        userInfo['employeeId'] = user['employeeId'];
        userInfo['role'] = user['roleId'];
        userInfo['odooDriverId'] = user['odooDriverId'];
        userInfo['userId'] = user['userId'];
        userInfo['clientId'] = user['clientId'];
        if(user['client']){
          userInfo['clientCode'] = user['client']['code'];
        }
      }
      return userInfo;
    } catch (error) {
      throw new Error(error as any);
    }
  }
}

export default UserService;