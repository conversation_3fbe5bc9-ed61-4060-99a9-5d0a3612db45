'user strict'

import Jo<PERSON> from '@hapi/joi';
import EMessage from '../../../emuns/messages';

class HSCodeValidate {
  static create: any = Joi.object({
    code: Joi.string().trim().required().max(50).messages({
      "any.required": `code ${EMessage.REQUIRED}`,
      "string.empty": `code ${EMessage.EMPTYED}`,
      "string.max": `code ${EMessage.MAX_50}`,
    }),
    name: Joi.string().trim().messages({
      "string.empty": `MAWB ${EMessage.EMPTYED}`,
    }),
    percentValue: Joi.number().min(0).messages({
      "number.base": `percentValue ${EMessage.NUMBER}`,
      "number.min": `percentValue ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
    position: Joi.number().min(0).messages({
      "number.base": `position ${EMessage.NUMBER}`,
      "number.min": `position ${EMessage.GREATE_EQUAL_THAN_0}`,
    }),
  });

  static arrCreate: any = Joi.array().items(HSCodeValidate.create).unique().required();
}

export default HSCodeValidate;
