'use strict'

import Optional from "../parser/optional";

class BaseService {
  private repo: any;
  constructor (repository: any) {
    this.repo = repository;
  }

  public async getAll(optional: Optional): Promise<any> {
    try {
      const objData = await this.repo.getAll(optional);
      return objData;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async getOne(id: number): Promise<any> {
    try {
      const objData = await this.repo.getOne(id);
      return objData;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async getOneOptional(id: number, optional: Optional, fields: string[] = []): Promise<any> {
    try {
      const objData = await this.repo.getOneById(id, optional, fields);
      return objData;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async update(data: any, optional: Optional): Promise<any> {
    try {
      const objUpdated = this.repo.updateData(data, optional);
      return objUpdated;
    } catch (error) {
      throw new Error(error as any)
    }
  }

  public async create(data: object): Promise<any> {
    try {
      const objCreated = await this.repo.createData(data);
      return objCreated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async createBulk(data: Array<any>): Promise<any> {
    try {
      const objCreated = await this.repo.createBulk(data);
      return objCreated;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async deleteOne(id: number): Promise<any> {
    try {
      const objDeleted = await this.repo.updateDeleteOne(id);
      return objDeleted;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async delete(optional: Optional): Promise<any> {
    try {
      const objDeleted = await this.repo.updateDelete(optional);
      return objDeleted;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async remove(optional: Optional): Promise<any> {
    try {
      const objDeleted = await this.repo.destroyData(optional);
      return objDeleted;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public async removeOne(id: number): Promise<any> {
    try {
      const objDeleted = await this.repo.destroyOne(id);
      return objDeleted;
    } catch (error) {
      throw new Error(error as any);
    }
  }
}

export default BaseService;