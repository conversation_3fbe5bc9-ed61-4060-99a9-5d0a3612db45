
import { modelsLoader, } from '../models/index.model';
import DatabaseConfig from '../config/index';

export class Database {
  public static readonly modelCtors = modelsLoader();
  private static _sequelize: any = null;
  constructor() {
  }

  public static init(): void {
    // get database config
    this._sequelize = new DatabaseConfig().sequelize;

    // init every model
    Object.keys(this.modelCtors).forEach(modelName => {
      this.modelCtors[modelName].prepareInit(this._sequelize);
    });

    // call (create) associations for each model
    Object.keys(this.modelCtors).forEach(modelName => {
      this.modelCtors[modelName].setAssociations(this.modelCtors);
    });
  }

  /**
   * connect & authenticate with the database
   * */
  public static async authenticate(): Promise<any> {
    try {
      // return await to catch error if thrown
      if(!this._sequelize) {
        Database.init();
      }
      return await this._sequelize.authenticate();
      // do not sync otherwise current data in database will be emptied out (Dropping all tables and recreating them)
      // return await this._sequelize.sync();
    } catch (e) {
      return Promise.reject(e);
    }
  }

  /**
   * export the sequelize object
   * */
  public static get Sequelize(): any {
    if(!this._sequelize) {
      Database.init();
    }
    return this._sequelize;
  }
}

// export const getDatabase = async () => {
//   const database = new Database();
//   await database.prepare();
//   return database;
// };