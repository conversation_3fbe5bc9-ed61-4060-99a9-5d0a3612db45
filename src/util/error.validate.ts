'use strict'

import { ValidationError } from '@hapi/joi';
// import HttpException from '../exceptions/HttpException';

class ErrorValidate {
  private error: ValidationError;
  constructor(error: ValidationError) {
    this.error = error;
  }

  public handleError(): any {
    try {
      const { details } = this.error;
      const messages = details.map((detail: any) => {
        if(detail.type == 'any.only') {
          return `${detail.message} ${detail.context.valids}`;
        }
        return detail.message;
      });
      return messages;
    } catch (error) {
      throw Error(error);
    }
  }

  public handleErrorExcel(): any {
    try {
      const { details } = this.error;
      const myMap: any = {};
      let arrMessage: any[];
      details.forEach(detail => {
        const path = detail['path'];
        const positionData: any = path[0];
        const fieldName: any = path[1];
        const positionItem: any = path[2];
        const fieldNameItem = path[3];
        const ojbMessage: any = {};
        if(myMap[positionData]){
          arrMessage = myMap[positionData];
        } else {
          arrMessage = [];
        }
        ojbMessage['key'] = fieldName;
        ojbMessage['field'] = fieldName;
        ojbMessage['msg'] = detail['message'];
        if(fieldName === 'items' && positionItem !== null) {
          ojbMessage['field'] = fieldNameItem?fieldNameItem:fieldName;
          ojbMessage['position'] = positionItem;
        } 
        arrMessage.push(ojbMessage);
        myMap[positionData] = arrMessage;
      });
      return myMap;
    } catch (error) {
      throw Error(error);
    }
  }
}

export default ErrorValidate;