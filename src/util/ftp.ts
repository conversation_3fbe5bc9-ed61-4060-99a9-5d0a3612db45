import { AccessOptions, Client } from 'basic-ftp';
import dotenv from 'dotenv';
import _ from 'lodash';
import moment from 'moment';
import Configure from '../emuns/configures';
import Utilities from './utilities';

dotenv.config();

export class Ftp {
  public client = new Client();

  private subFolderSend = Configure.CLEARANCE_FTP_FOLDER_SUB_SEND;
  private subFolderReceive = Configure.CLEARANCE_FTP_FOLDER_SUB_RECEIVE;

  private ftpConnectOpt: AccessOptions = {
    host: process.env.FTP_HOST || '',
    user: process.env.FTP_USERNAME || '',
    password: process.env.FTP_PASSWORD || '',
    secure: true,
    secureOptions: { rejectUnauthorized: false }
  };

  constructor(host: string, user: string, pass: string, port?: number, isTLS?: boolean) {
    this.ftpConnectOpt = {
      host: host,
      user: user,
      password: pass,
      secure: isTLS,
      secureOptions: isTLS == true ? { rejectUnauthorized: false } : undefined,
      port: port ? port : undefined
    };
  }

  async connect() {
    try {
      await this.client.access(this.ftpConnectOpt);
    } catch (error) {
      throw error;
    }
  }

  close() {
    try {
      this.client.close();
    } catch (error) {
      throw error;
    }
  }

  async uploadFile(pathHost: string, pathLocal: string, fileName: string, subFolderSend?: string) {
    let subFolder = subFolderSend ? subFolderSend : this.subFolderSend;
    try {
      return await this.client.uploadFrom(`${pathLocal}/${fileName}`, `${pathHost}/${subFolder}/${fileName}`);
    } catch (err) {
      console.log(`[UPLOAD_FILE_FTP] PathHost: ${pathHost}/${subFolder}/${fileName}`);
      throw err;
    }
  }

  async downloadToDir(pathHost: string, pathLocal: string, subFolderReceive?: string) {
    let subFolder = subFolderReceive ? subFolderReceive : this.subFolderReceive;
    try {
      return await this.client.downloadToDir(`${pathLocal}`, `${pathHost}/${subFolder}`);
    } catch (err) {
      throw err;
    }
  }

  async downloadFile(pathHost: string, pathLocal: string, fileName: string) {
    try {
      return await this.client.downloadTo(`${pathLocal}/${fileName}`, `${pathHost}/${fileName}`);
    } catch (err) {
      throw err;
    }
  }

  async listFile(pathHost: string, subFoler: string) {
    try {
      return await this.client.list(`${pathHost}/${subFoler}`);
    } catch (err) {
      throw err;
    }
  }

  async createFolder(folder: string) {
    try {
      return await this.client.send(`mkd ${folder}`);
    } catch (err) {
      throw err;
    }
  }

  async checkFile(path: string, fileName: string) {
    try {
      let list = await this.client.list(`${path}/${fileName}`);

      if (list.length > 0) {
        let file = list.find((o) => o.name.toUpperCase() == fileName.toUpperCase());
        if (file) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  async move(pathStart: string, pathEnd: string, fileName: string) {
    try {
      return await this.client.rename(`${pathStart}/${fileName}`, `${pathEnd}/${moment().format('HHmmss')}-${fileName}`);
    } catch (err) {
      throw err;
    }
  }

  async remove(path: string, file: string) {
    try {
      return await this.client.remove(`${path}/${file}`);
    } catch (err) {
      throw err;
    }
  }

  async removeAllFile(path: string, fileNames: string[]) {
    try {
      for (let idx = 0; idx < fileNames.length; idx++) {
        const file = fileNames[idx];
        await this.client.remove(`${path}/${file}`);
      }

      return true;
    } catch (err) {
      throw err;
    }
  }

  async downloadAndBackup(pathHost: string, subFolder: string, pathLocal: string) {
    try {
      await new Promise((f) => setTimeout(f, 50));

      let listFiles = await this.listFile(pathHost, subFolder);

      if (listFiles.length > 0) {
        let backupFolder = `BACKUP_TMS/${moment().format('YYYY/MM/DD')}`;

        try {
          await this.createFolder(`${pathHost}/${backupFolder}`);
        } catch (error) {}

        let listFile100 = _.sampleSize(listFiles, 100);

        for (let idx = 0; idx < listFile100.length; idx++) {
          const file = listFile100[idx];
          let fileName = file.name;

          try {
            let file = await this.checkFile(`${pathHost}/${subFolder}`, fileName);
            if (file) {
              let download = await this.downloadFile(`${pathHost}/${subFolder}`, pathLocal, fileName);

              if (download.code == 226) {
                await this.move(`${pathHost}/${subFolder}`, `${pathHost}/${backupFolder}`, fileName);
              }
            }
          } catch (error) {
            Utilities.sendTelegramNoti('DOWNLOAD_BACKUP', `${fileName}: ${error.message}`);
            console.log(' --- [%s] [DOWNLOAD_BACKUP]: %o', moment().format(Configure.FULL_TIME), error);
          }
        }
      }
    } catch (error) {
      Utilities.sendTelegramNoti('DOWNLOAD_BACKUP_FAILD', `${error.message}`);
    }
  }
}
