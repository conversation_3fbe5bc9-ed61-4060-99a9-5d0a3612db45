import multer from 'multer';
import { Request } from "express";

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, './public')
  },
  filename: (req, file, cb) => {
    const fileExtension = file['originalname'].split('.')[1];
    const fieldname = file['originalname'].split('.')[0];
    cb(null, `${fieldname}-${Date.now()}.${fileExtension}`)
  },
});


const fileFilter = (request: Request, file: any, cb: any): any => {
  // accept image only
  if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
    return cb(new Error('Only image files are allowed!'), false);
  }
  cb(null, true);
};

export { fileFilter, storage }
