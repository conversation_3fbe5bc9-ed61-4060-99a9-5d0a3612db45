'use strict'

import { config } from "dotenv";

class Config {
  constructor() {
    config();
  }
  public getRedis(): any {
    const HOST: string = process.env.REDIS_HOST || '127.0.0.1';
    const PORT: number = Number(process.env.REDIS_PORT) || 6379;
    const PASSWORD: string = process.env.REDIS_PASSWORD || 'abc@123';
    const DB: number = Number(process.env.REDIS_DB) || 2;
    return {
      'host': HOST,
      'port': PORT,
      'password': PASSWORD,
      'db': DB
    }
  }

  public secretKey(): string {
    return process.env.SECRET_AUTHEN_KEY || '5Bpg#s12c@2oS0@7MS-o1125';
  }

  public secretHRKey(): string {
    return process.env.SECRET_HR_AUTHEN_KEY || 'hR5Bpg#s3712eT@2oS0';
  }

  public internalSecretKey(): string {
    return process.env.INTERNAL_AUTHEN_KEY || 'iN73rn4l!5Bpg#s12c@2oS0@7MS-o1125';
  }

  public odooDomain(): string {
    return process.env.ODOO_API || 'https://devodoo.globex.vn';
  }

  public odooDB(): string {
    return process.env.ODOO_DB || 'sbp_test';
  }

  public odooLogin(): string {
    return process.env.ODOO_LOGIN || 'admin_it';
  }

  public odooPassword(): string {
    return process.env.ODOO_PASSWORD || 'q7Fvt5R*Vt%+ag9L';
  }

  public ommAPI(): string {
    return process.env.OMM_URL || 'https://devapi.globex.vn/omm/api/v1';
  }

  public clearanceDomain(): string {
    return process.env.CLEARANCE_DOMAIN || 'https://devapi.globex.vn/clearance';
  }
}

export default Config;