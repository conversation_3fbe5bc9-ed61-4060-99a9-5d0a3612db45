import * as Minio from 'minio';
import { IResponze } from '../https/responze';
import Utilities from './utilities';

export class MiniOUpload {
  private minioClient: Minio.Client;

  constructor() {
    this.minioClient = new Minio.Client({
      endPoint: process.env.MINIO_ENDPOINT || '10.0.2.12',
      port: 9000,
      useSSL: false,
      accessKey: process.env.MINIO_ACCESSKEY || 'ecusv5',
      secretKey: process.env.MINIO_SECRETKEY || 'r2Lv2WwIQTXk9cp1WmcFB5Fyv'
    });
  }

  private metaData(file: any): Minio.ItemBucketMetadata {
    return { 'Content-Type': file.mimetype };
  }

  async makeBucket(bucketName: string): Promise<IResponze> {
    try {
      let bucketExists = await this.minioClient.bucketExists(bucketName);

      if (bucketExists == false) {
        await this.minioClient.makeBucket(bucketName, '');
      }

      return { status: true, message: {} };
    } catch (error) {
      Utilities.sendTelegramNoti('MINIO_MAKE_BUCKET', (error as Error).message);
      return { status: false, message: { error: (error as Error).message } };
    }
  }

  async uploadBuffer(bucketName: string, pathFileMinio: string, file: any): Promise<IResponze> {
    try {
      await this.minioClient.putObject(bucketName, pathFileMinio, file.buffer, this.metaData(file));

      return { status: true, message: {} };
    } catch (error) {
      Utilities.sendTelegramNoti('MINIO_UPLOAD_BUFFER', (error as Error).message);
      return { status: false, message: { error: (error as Error).message } };
    }
  }
}
