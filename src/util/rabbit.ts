import rabbitMq, { Options, Connection, Channel, ConsumeMessage } from 'amqplib';
import { ClearanceRabbit } from '../xmlClearance/enum';
import { Clearance } from '../xmlClearance/clearance';
import Configure from '../emuns/configures';
import moment from 'moment';
import ImportClearanceService from '../modules/v1/importClearance/importClearance.service';
import ExportClearanceService from '../modules/v1/exportClearance/exportClearance.service';
import { AutoV5 } from '../modules/v1/autoV5/autoV5.util';

let connection: Connection;
let connectionCreate: Connection;
let connectionInternal: Connection;

class Rabbit {
  private connectOpt: Options.Connect = {
    protocol: 'amqp',
    hostname: process.env.RABBIT_HOST || 'localhost',
    port: ((process.env.RABBIT_PORT as unknown) as number) || 5672,
    username: process.env.RABBIT_USERNAME || 'rabbit',
    password: process.env.RABBIT_PASSWORD || 'rabbit',
    vhost: process.env.RABBIT_VHOST || '/ommapi',
    heartbeat: 60
  };

  private connectCreateOpts: Options.Connect = {
    protocol: 'amqp',
    hostname: process.env.RABBIT_HOST || 'localhost',
    port: ((process.env.RABBIT_PORT as unknown) as number) || 5672,
    username: process.env.RABBIT_USERNAME || 'rabbit',
    password: process.env.RABBIT_PASSWORD || 'rabbit',
    vhost: process.env.RABBIT_VHOST_PROD || 'clearance',
    heartbeat: 60
  };

  private connectInternalOpts: Options.Connect = {
    protocol: 'amqp',
    hostname: process.env.RABBIT_HOST || 'localhost',
    port: ((process.env.RABBIT_PORT as unknown) as number) || 5672,
    username: process.env.RABBIT_USERNAME || 'rabbit',
    password: process.env.RABBIT_PASSWORD || 'rabbit',
    vhost: process.env.RABBIT_VHOST_INTERNAL || 'tms_messaging_bus_dev',
    heartbeat: 60
  };

  async connect() {
    if (!connection) {
      connection = await rabbitMq.connect(this.connectOpt);
    }

    connection.on('blocked', async () => {
      await connection.close().bind(connection);
      console.log(` --- [%s] [rabbit][connection][block]`, moment().format(Configure.FULL_TIME));
      await this.connect();
    });

    return connection;
  }

  async connectInternal() {
    if (!connectionInternal) {
      connectionInternal = await rabbitMq.connect(this.connectInternalOpts);
    }

    connectionInternal.on('blocked', async () => {
      await connectionInternal.close().bind(connectionInternal);
      console.log(` --- [%s] [rabbit][connectionInternal][block]`, moment().format(Configure.FULL_TIME));
      await this.connectInternal();
    });

    return connectionInternal;
  }

  async connectCreate() {
    if (!connectionCreate) {
      connectionCreate = await rabbitMq.connect(this.connectCreateOpts);
    }

    connectionCreate.on('blocked', async () => {
      await connectionCreate.close().bind(connectionCreate);
      console.log(` --- [%s] [rabbit][connectionCreate][block]`, moment().format(Configure.FULL_TIME));
      await this.connectCreate();
    });

    return connectionCreate;
  }
}

export class RabbitSendInternal extends Rabbit {
  private channel!: Channel;

  // opt { durable: true, autoDelete: false }
  private async toQueue(queueName: string, data: any, opt?: Options.AssertQueue) {
    await this.channel.assertQueue(queueName, opt);

    for (const idx in data) {
      let el = data[idx];
      let content: string;

      try {
        if (typeof el == 'object') {
          content = JSON.stringify(el);
        } else {
          content = el;
        }

        this.channel.sendToQueue(queueName, Buffer.from(content, 'utf8'));
      } catch (error) {
        console.log(error);
      }
    }
  }

  /**
   * Gửi message lên queue
   *
   * @param {string} queueName Tên queue
   * @param {any[]} data dữ liệu cần gửi nằm trong mảng data
   * @param {Options.AssertQueue} [opt] tùy chọn opt { durable: true, autoDelete: false }
   * @memberof RabbitSend
   */
  async send(queueName: string, data: any, opt?: Options.AssertQueue) {
    try {
      let connection = await this.connectInternal();
      this.channel = await connection.createChannel();

      await this.toQueue(queueName, data, opt);

      setTimeout(() => {
        this.channel.close();
        console.log(` --- [%s] [rabbit][queue][${queueName}] close channel (${data.length})`, moment().format(Configure.FULL_TIME));
      }, 2000);
    } catch (error) {
      setTimeout(() => {
        this.send(queueName, data, opt);
      }, 1000);
    }
  }

  private async publish(exchange: string, queueName: string, data: any, delay: number) {
    for (const idx in data) {
      let el = data[idx];
      let content: string;

      try {
        if (typeof el == 'object') {
          content = JSON.stringify(el);
        } else {
          content = el;
        }

        this.channel.publish(exchange, queueName, Buffer.from(content, 'utf8'), { headers: { 'x-delay': delay } });
      } catch (error) {
        console.log(error);
      }
    }
  }

  // opt { durable: true, autoDelete: false, passive: true,  arguments: {'x-delayed-type':  "direct"}}
  /**
   * Gửi message delay 1 khoảng thời gian
   *
   * @param {string} queueName
   * @param {*} data
   * @param {Options.AssertQueue} [opt]
   * @memberof RabbitSend
   */
  async delayMs(queueName: string, data: any[], delayTime: number, opt?: Options.AssertQueue) {
    try {
      let exchange = ClearanceRabbit.ExchangeDelay;

      let connection = await this.connectInternal();
      this.channel = await connection.createChannel();

      let exchangeOpt = Object.assign({}, opt);
      exchangeOpt['arguments'] = { 'x-delayed-type': 'direct' };

      await this.channel.assertExchange(exchange, 'x-delayed-message', exchangeOpt);
      await this.channel.assertQueue(queueName, opt);
      await this.channel.bindQueue(queueName, exchange, queueName);

      await this.publish(exchange, queueName, data, delayTime * 1000);

      setTimeout(() => {
        this.channel.close();
        console.log(` --- [%s] [rabbit][delay exchange][${queueName}] close channel (${data.length})`, moment().format(Configure.FULL_TIME));
      }, 2000);
    } catch (error) {
      setTimeout(() => {
        this.delayMs(queueName, data, delayTime, opt);
      }, 1000);
    }
  }
}

export class RabbitSend extends Rabbit {
  private channel!: Channel;

  // opt { durable: true, autoDelete: false }
  private async toQueue(queueName: string, data: any, opt?: Options.AssertQueue, optMs?: Options.Publish) {
    await this.channel.assertQueue(queueName, opt);

    for (const idx in data) {
      let el = data[idx];
      let content: string;

      try {
        if (typeof el == 'object') {
          content = JSON.stringify(el);
        } else {
          content = el;
        }

        this.channel.sendToQueue(queueName, Buffer.from(content, 'utf8'), optMs);
      } catch (error) {
        console.log(error);
      }
    }
  }

  /**
   * Gửi message lên queue
   *
   * @param {string} queueName Tên queue
   * @param {any[]} data dữ liệu cần gửi nằm trong mảng data
   * @param {Options.AssertQueue} [opt] tùy chọn opt { durable: true, autoDelete: false }
   * @memberof RabbitSend
   */
  async send(queueName: string, data: any[], opt?: Options.AssertQueue, optMs?: Options.Publish) {
    try {
      let connection = await this.connect();
      this.channel = await connection.createChannel();

      await this.toQueue(queueName, data, opt, optMs);

      setTimeout(() => {
        this.channel.close();
        console.log(` --- [%s] [rabbit][queue][${queueName}] close channel (${data.length})`, moment().format(Configure.FULL_TIME));
      }, 2000);
    } catch (error) {
      setTimeout(() => {
        this.send(queueName, data, opt);
      }, 1000);
    }
  }

  private async publish(exchange: string, queueName: string, data: any, delay: number) {
    for (const idx in data) {
      let el = data[idx];
      let content: string;

      try {
        if (typeof el == 'object') {
          content = JSON.stringify(el);
        } else {
          content = el;
        }

        this.channel.publish(exchange, queueName, Buffer.from(content, 'utf8'), { headers: { 'x-delay': delay } });
      } catch (error) {
        console.log(error);
      }
    }
  }

  // opt { durable: true, autoDelete: false, passive: true,  arguments: {'x-delayed-type':  "direct"}}
  /**
   * Gửi message delay 1 khoảng thời gian
   *
   * @param {string} queueName
   * @param {*} data
   * @param {Options.AssertQueue} [opt]
   * @memberof RabbitSend
   */
  async delayMs(queueName: string, data: any[], delayTime: number, opt?: Options.AssertQueue) {
    try {
      let exchange = ClearanceRabbit.ExchangeDelay;

      let connection = await this.connect();
      this.channel = await connection.createChannel();

      let exchangeOpt = Object.assign({}, opt);
      exchangeOpt['arguments'] = { 'x-delayed-type': 'direct' };

      await this.channel.assertExchange(exchange, 'x-delayed-message', exchangeOpt);
      await this.channel.assertQueue(queueName, opt);
      await this.channel.bindQueue(queueName, exchange, queueName);

      await this.publish(exchange, queueName, data, delayTime * 1000);

      setTimeout(() => {
        this.channel.close();
        console.log(` --- [%s] [rabbit][delay exchange][${queueName}] close channel (${data.length})`, moment().format(Configure.FULL_TIME));
      }, 2000);
    } catch (error) {
      setTimeout(() => {
        this.delayMs(queueName, data, delayTime, opt);
      }, 1000);
    }
  }
}

export class RabbitReceive extends Rabbit {
  async clearancePush() {
    try {
      let connection = await this.connect();
      let channel = await connection.createChannel();
      let queueName: string = ClearanceRabbit.Push;

      let controller = new Clearance();

      await channel.assertQueue(queueName, { durable: true, autoDelete: false });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              console.log(` --- [%s] [rabbit_cleareance_push]: ${msg.content.toString()}`, moment().format(Configure.FULL_TIME));

              let dataObj = msg ? msg.content.toString() : '{}';
              let data = JSON.parse(dataObj);

              await controller.handling(data);

              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.ack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async clearancePullTerminal() {
    try {
      let connection = await this.connect();
      let channel = await connection.createChannel();
      let queueName: string = ClearanceRabbit.Pull_terminal;

      let controller = new Clearance();

      await channel.assertQueue(queueName, { durable: true, autoDelete: false });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              console.log(` --- [%s] [rabbit_cleareance_pull_terminal]: ${msg.content.toString()}`, moment().format(Configure.FULL_TIME));

              let dataObj = msg ? msg.content.toString() : '{}';
              let data = JSON.parse(dataObj);

              await controller.pullTerminal(data);

              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.ack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' \t --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async clearancePull() {
    try {
      let connection = await this.connect();
      let channel = await connection.createChannel();
      let queueName: string = ClearanceRabbit.Pull;

      let controller = new Clearance();

      await channel.assertQueue(queueName, { durable: true, autoDelete: false });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              console.log(` --- [%s] [rabbit_cleareance_pull]: ${msg.content.toString()}`, moment().format(Configure.FULL_TIME));

              await controller.saveFile(msg.content.toString());

              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.ack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' \t --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async clearanceValidPush() {
    try {
      let connection = await this.connect();
      let channel = await connection.createChannel();
      let queueName: string = ClearanceRabbit.ValidPush;

      let controller = new Clearance();

      await channel.assertQueue(queueName, { durable: true, autoDelete: false });
      channel.prefetch(1);

      channel.consume(
        queueName,
        (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              setTimeout(async function () {
                console.log(` --- [%s] [rabbit_cleareance_validPush]: ${msg.content.toString()}`, moment().format(Configure.FULL_TIME));

                let dataObj = msg ? msg.content.toString() : '{}';
                let data = JSON.parse(dataObj);

                await controller.updateNoReply(data);

                channel.ack(msg);
              }, 700);
            } catch (error) {
              console.log(error);
              channel.ack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' \t --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async inboundOnlyClearance() {
    try {
      let connection = await this.connectCreate();
      let channel = await connection.createChannel();
      let queueName: string = Configure.INBOUND_ONLY_CLEARANCE;

      if (process.env.NODE_ENV === 'local') {
        queueName = `${queueName}_local`;
      }

      await channel.assertQueue(queueName, { durable: true, autoDelete: false });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              let controller = new ImportClearanceService();

              console.log(` --- [%s] [only_clearance_pull_inbound]: ${msg.content.toString()}`, moment().format(Configure.FULL_TIME));

              let dataObj = msg ? msg.content.toString() : '{}';
              let data = JSON.parse(dataObj);

              await controller.createImportExcel(data);

              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.ack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' \t --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async outboundOnlyClearance() {
    try {
      let connection = await this.connectCreate();
      let channel = await connection.createChannel();
      let queueName: string = Configure.OUTBOUND_ONLY_CLEARANCE;

      if (process.env.NODE_ENV === 'local') {
        queueName = `${queueName}_local`;
      }

      await channel.assertQueue(queueName, { durable: true, autoDelete: false });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              let controller = new ExportClearanceService();

              console.log(` --- [%s] [only_clearance_pull_outbound]: ${msg.content.toString()}`, moment().format(Configure.FULL_TIME));

              let dataObj = msg ? msg.content.toString() : '{}';
              let data = JSON.parse(dataObj);

              await controller.createImportExcel(data);

              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.ack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' \t --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async updateTaxCodeNumber() {
    try {
      let connection = await this.connect();
      let channel = await connection.createChannel();
      let queueName: string = ClearanceRabbit.UPDATE_TAX_CODE_NUMBER;

      if (process.env.NODE_ENV === 'local') {
        queueName = `${queueName}_local`;
      }

      await channel.assertQueue(queueName, { durable: true, autoDelete: false });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              console.log(` --- [%s] [${queueName}]`, moment().format(Configure.FULL_TIME));

              await new ImportClearanceService().updateTaxCodeNumber(msg.content.toString());
              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.nack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' \t --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async updateMicTaxCodeNumber() {
    try {
      let connection = await this.connect();
      let channel = await connection.createChannel();
      let queueName: string = ClearanceRabbit.UPDATE_MIC_TAX_CODE_NUMBER;

      if (process.env.NODE_ENV === 'local') {
        queueName = `${queueName}_local`;
      }

      await channel.assertQueue(queueName, { durable: true, autoDelete: false });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              console.log(` --- [%s] [${queueName}]`, moment().format(Configure.FULL_TIME));

              await new ImportClearanceService().updateTaxCodeNumber(msg.content.toString(), 'MIC');
              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.nack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' \t --- [%s] [error][UPDATE_MIC_TAX_CODE_NUMBER]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async sendFileV5() {
    try {
      let connection = await this.connect();
      let channel = await connection.createChannel();
      let queueName: string = Configure.RABBIT_SEND_V5;

      if (process.env.NODE_ENV === 'local') {
        queueName = `${queueName}_local`;
      }

      await channel.assertQueue(queueName, { durable: false, autoDelete: true });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              let ulti = new AutoV5();

              console.log(` --- [%s] [SendFileV5]: ${msg.content.toString()}`, moment().format(Configure.FULL_TIME));

              let dataObj = msg ? msg.content.toString() : '{}';
              let data = JSON.parse(dataObj);

              await ulti.send(data);

              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.ack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }

  async receiveFileV5() {
    try {
      let connection = await this.connect();
      let channel = await connection.createChannel();
      let queueName: string = Configure.RABBIT_RECEIVE_V5;

      if (process.env.NODE_ENV === 'local') {
        queueName = `${queueName}_local`;
      }

      await channel.assertQueue(queueName, { durable: false, autoDelete: true });
      channel.prefetch(1);

      channel.consume(
        queueName,
        async (msg: ConsumeMessage | null) => {
          if (msg != null) {
            try {
              let ulti = new AutoV5();

              console.log(` --- [%s] [ReceiveFileV5]: ${msg.content.toString()}`, moment().format(Configure.FULL_TIME));

              let dataObj = msg ? msg.content.toString() : '{}';
              let data = JSON.parse(dataObj);

              await ulti.save(data);

              channel.ack(msg);
            } catch (error) {
              console.log(error);
              channel.ack(msg);
            }
          }
        },
        { noAck: false }
      );
    } catch (error) {
      console.log(' --- [%s] [error][rabbit]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }
}
