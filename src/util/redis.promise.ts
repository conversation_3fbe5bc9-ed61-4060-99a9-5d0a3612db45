'use strict'

import redis from 'redis';
import EConfigure from '../emuns/configures';

class RedisPromise {
  private static client: any;
  constructor () {}

  public static async initializeRedis(): Promise<void> {
    return new Promise((resolve, reject)=> {
    if(!RedisPromise.client || RedisPromise.client.connected === false) {
        RedisPromise.client = redis.createClient({
          'host': process.env.REDIS_HOST || '127.0.0.1', 
          'port': Number(process.env.REDIS_PORT) || 6379,
          'retry_strategy': (options) => {
            if (options.error && options.error.code === "ECONNREFUSED") {
              // End reconnecting on a specific error and flush all commands with
              // a individual error
              return new Error("The server refused the connection");
            }
            if (options.total_retry_time > 1000 * 60 * 60) {
              // End reconnecting after a specific timeout and flush all commands
              // with a individual error
              return new Error("Retry time exhausted");
            }
            if (options.attempt > 10) {
              // End reconnecting with built in error
              return 0;
            }
            // reconnect after
            return Math.min(options.attempt * 100, 3000);
          }
        });
        // RedisPromise.client.auth(process.env.REDIS_PASSWORD || 'abc@123');
        RedisPromise.client.on('error',(error: any) => {
          RedisPromise.client = null;
          reject(error);
        }).on('connect', () => {
          resolve();
        }).on('reconnecting', function() {
          reject({'message': `${EConfigure.ERROR_STATUS} when connect to Redis` });
        });
      }
    });
  }
  

  public static checkConnection(): boolean {
    let status: boolean = false;
    if(RedisPromise.client && RedisPromise.client.connected) {
      status = true;
    }
    return status;
  }

  private static selectDB(db: number = Number(process.env.REDIS_DB) || 2): Promise<any> {
    return new Promise((resolve, reject)=> {
      RedisPromise.client.select(db, (err: any) => {
        if(err) {
          console.log('---- Select DB Redis error: %o', err);
          reject(EConfigure.ERROR_STATUS);
        } else {
          resolve();
        }
      });
    });
  }

  public static setData(key: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if(!RedisPromise.client || RedisPromise.client.connected === false){
        RedisPromise.initializeRedis().then(()=>{
          console.log('---- Reconnect setData Redis successfull');
        }).catch((err) => {
          console.log('---- Reconnect setData Redis error: %o', err);
          reject(EConfigure.ERROR_STATUS);
        });
      }
      if(RedisPromise.client && RedisPromise.client.connected) {
        RedisPromise.selectDB().then(()=> {
          RedisPromise.client.set(key, data, (err: any, data: any)=> {
            if(err) {
              console.log('---- setData Redis error: %o', err);
              reject(EConfigure.ERROR_STATUS);
            } else {
              resolve();
            }
          });
        }).catch((error: any)=> {
          console.log('---- Select DB Redis when setData error: %o', error);
          reject(EConfigure.ERROR_STATUS);
        });
      } else {
        console.log('---- Check connect Redis setData error');
        reject(EConfigure.ERROR_STATUS);
      }
    });
  }

  public static setDataExpire(key: string, data: any, secondTime: number): Promise<any> {
    return new Promise((resolve, reject) => {
      if(!RedisPromise.client || RedisPromise.client.connected === false){
        RedisPromise.initializeRedis().then(()=>{
          console.log('---- Reconnect setDataExpire Redis successfull');
        }).catch((err) => {
          console.log('---- Reconnect setDataExpire Redis error: %o', err);
          reject(EConfigure.ERROR_STATUS);
        });
      }
      if(RedisPromise.client && RedisPromise.client.connected) {
        RedisPromise.selectDB().then(()=> {
          RedisPromise.client.set(key, data, 'EX', secondTime,(err: any, data: any)=> {
            if(err) {
              console.log('---- setDataExpire Redis error: %o', err);
              reject(EConfigure.ERROR_STATUS);
            } else {
              resolve();
            }
          });
        }).catch((error: any)=> {
          console.log('---- Select DB Redis when setDataExpire error: %o', error);
          reject(EConfigure.ERROR_STATUS);
        });
      } else {
        console.log('---- Check connect Redis setDataExpire error');
        reject(EConfigure.ERROR_STATUS);
      }
    });
  }

  public static getData(key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      // Reconnect for next time using
      if(!RedisPromise.client || RedisPromise.client.connected === false){
        RedisPromise.initializeRedis().then(()=>{
          console.log('---- Reconnect getData Redis successfull');
        }).catch((err) => {
          console.log('---- Reconnect getData Redis error: %o', err);
        });
      }
      if(RedisPromise.client && RedisPromise.client.connected) {
        RedisPromise.selectDB().then(() => {
          RedisPromise.client.get(key, RedisPromise.callback(resolve, reject));
        }).catch((error: any) => {
          console.log('---- Select DB Redis when getData error: %o', error);
          reject(EConfigure.ERROR_STATUS);
        });
      } else {
        console.log('---- Check connect Redis getData error');
        reject(EConfigure.ERROR_STATUS);
      }
      
    });
  }

  public static keyData(key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if(!RedisPromise.client || RedisPromise.client.connected === false){
        RedisPromise.initializeRedis().then(()=>{
          console.log('---- Reconnect keyData Redis successfull');
        }).catch((err) => {
          console.log('---- Reconnect keyData Redis error: %o', err);
          reject(EConfigure.ERROR_STATUS);
        });
      }
      if(RedisPromise.client && RedisPromise.client.connected) {
        RedisPromise.selectDB().then(()=> {
          RedisPromise.client.keys(key, async (err: any, keys: any[]) => {
            if(err) {
              console.log('---- keyData Redis error: %o', err);
              reject(err);
            } else {
              try {
                let value: any[] = [];
                if(keys.length > 0) {
                  value = await Promise.all(keys.map((key: any)=>{
                    return RedisPromise.getData(key);
                  }));
                } 
                resolve(value);
              } catch (error) {
                reject(EConfigure.ERROR_STATUS);
              }
              
            }
          });
        }).catch((error: any)=> {
          console.log('---- Select DB Redis when keyData error: %o', error);
          reject(EConfigure.ERROR_STATUS);
        });
      } else {
        console.log('---- Check connect Redis keyData error');
        reject(EConfigure.ERROR_STATUS);
      }
      
    });
  }

  public static deleteData(key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if(!RedisPromise.client || RedisPromise.client.connected === false){
        RedisPromise.initializeRedis().then(()=>{
          console.log('---- Reconnect deleteData Redis successfull');
        }).catch((err) => {
          console.log('---- Reconnect deleteData Redis error: %o', err);
          reject(EConfigure.ERROR_STATUS);
        });
      }
      if(RedisPromise.client && RedisPromise.client.connected) {
        RedisPromise.selectDB().then(()=> {
          RedisPromise.client.del(key, RedisPromise.callback(resolve, reject));
        }).catch((error: any)=> {
          console.log('---- Select DB Redis when deleteData error: %o', error);
          reject(EConfigure.ERROR_STATUS);
        });
      } else {
        console.log('---- Check connect Redis deleteData error');
        reject(EConfigure.ERROR_STATUS);
      }
      
    });
  }

  public static deleteDatas(prefixKey: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if(!RedisPromise.client || RedisPromise.client.connected === false){
        RedisPromise.initializeRedis().then(()=>{
          console.log('---- Reconnect deleteDatas Redis successfull');
        }).catch((err) => {
          console.log('---- Reconnect deleteDatas Redis error: %o', err);
          reject(EConfigure.ERROR_STATUS);
        });
      }
      if(RedisPromise.client && RedisPromise.client.connected) {
        RedisPromise.selectDB().then(()=> {
          RedisPromise.client.keys(prefixKey, async (err: any, keys: any[]) => {
            if(err) {
              console.log('---- deleteDatas Redis error: %o', err);
              reject(err);
            } else {
              try {
                let value: any[] = [];
                if(keys.length > 0) {
                  value = await Promise.all(keys.map((key: any)=>{
                    return RedisPromise.deleteData(key);
                  }));
                } 
                resolve(value);
              } catch (error) {
                reject(EConfigure.ERROR_STATUS);
              }
            }
          });
        }).catch((error: any)=> {
          console.log('---- Select DB Redis when deleteDatas error: %o', error);
          reject(EConfigure.ERROR_STATUS);
        });
      } else {
        console.log('---- Check connect Redis deleteDatas error');
        reject(EConfigure.ERROR_STATUS);
      }
      
    });
  }

  private static callback(resolve: any, reject: any) {
    return (err: any, data: any) => {
      if(err) {
        console.log('---- Callback data Redis error: %o', err);
        reject(err);
      } else {
        if(data) {
          resolve((typeof data==='string')?JSON.parse(data):data);
        } else {
          resolve(null)
        }
        
      }
    }
  }
}

export default RedisPromise;