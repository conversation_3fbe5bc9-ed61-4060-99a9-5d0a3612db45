'use strict';

import RedisPromise from './redis.promise';
import EConfigure from '../emuns/configures';
import axios from 'axios';
import fs from 'fs';
import Config from '../util/configure';
import moment from 'moment';
import <PERSON><PERSON> from '@hapi/joi';
import WarehouseRepository from '../modules/v1/warehouse/warehouse.reporsitory';
import { IWarehouse } from '../models/warehouse.model';
import CountryRepository from '../modules/v1/country/country.reporsitory';
import { ClearanceCreateLog, Hold, ICountry } from '../models/index.model';
import { WeightName, WeightKey } from '../emuns/weight';
import HSCodeRepository from '../modules/v1/hscodeDetail/hscodeDetail.reporsitory';
import TelegramBot from 'node-telegram-bot-api';
import ClearanceCreateLogRepository from '../modules/v1/clearanceCreateLog/clearanceCreateLog.reporsitory';
import Configure from '../emuns/configures';
import ParameterRepository from '../modules/v1/parameter/parameter.repository';
import Optional from '../parser/optional';
import Where from '../parser/where';

interface ISession {
  session: string;
  expire: number;
}

class Utilities {
  public static removeVietnameseTones(str: string): string {
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
    str = str.replace(/Đ/g, "D");
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, " ");
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, " ");
    return str;
  }
  public static randomString(length: number): string {
    const characters: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const charactersLength: number = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  public static padWithZeroes(number: number, length: number) {
    let stringNumber = '' + number;
    while (stringNumber.length < length) {
      stringNumber = '0' + stringNumber;
    }
    return stringNumber;
  }

  public static removeSpace(data: string, replace = ''): string {
    if (data) {
      return data.split(' ').join(replace);
    }
    return '';
  }

  public static addDays(dateObj: Date, numDays: number) {
    dateObj.setDate(dateObj.getDate() + numDays);
    return dateObj;
  }

  public static addMinutes(dateObj: Date, numMins: number) {
    dateObj.setMinutes(dateObj.getMinutes() + numMins);
    return dateObj;
  }

  public static async callAPINonAuth(url: string, method: string = EConfigure.GET_METHOD, data: any = []): Promise<any> {
    try {
      let response: any;
      if (method === EConfigure.GET_METHOD) {
        response = await axios.get(url);
      }
      if (method === EConfigure.DELETE_METHOD) {
        response = await axios.delete(url);
      }
      if (method === EConfigure.PUT_METHOD) {
        response = await axios.put(url, data);
      }
      if (method === EConfigure.POST_METHOD) {
        response = await axios.post(url, data);
      }
      if (response && response['data']) {
        return response['data'];
      }
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static async callAPI(url: string, method: string = EConfigure.GET_METHOD, data: any = []): Promise<any> {
    try {
      const config = new Config();
      let response: any;
      const axiosConfig = {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          iauthorization: `Bearer ${config.internalSecretKey()}`
        }
      };
      if (method === EConfigure.GET_METHOD) {
        response = await axios.get(url, axiosConfig);
      }
      if (method === EConfigure.DELETE_METHOD) {
        response = await axios.delete(url, axiosConfig);
      }
      if (method === EConfigure.PUT_METHOD) {
        response = await axios.put(url, data, axiosConfig);
      }
      if (method === EConfigure.POST_METHOD) {
        response = await axios.post(url, data, axiosConfig);
      }
      if (response && response['data']) {
        return response['data'];
      }
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static async removeFile(path: string): Promise<any> {
    try {
      if (fs.existsSync(path)) {
        fs.unlinkSync(path);
      }
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static stringValid(EName: any) {
    let joiString = Joi.string();
    EName.forEach((value: string, key: number) => {
      joiString = joiString.valid(value);
    });
    return joiString;
  }

  public static async getWarehouse(id: number): Promise<any> {
    const keyCache: string = `WAREHOUSE_${id}}`;
    const warehouseRepository: WarehouseRepository = new WarehouseRepository();
    try {
      let data: any = null;
      const objQuery: any = { id: id, orderTypeId: EConfigure.INDEX_0, isActivated: true, isDeleted: false };
      // try {
      // const dataCached: IWarehouse = await RedisPromise.getData(keyCache);
      // if(dataCached) {
      //   return dataCached;
      // } else {
      const objData: IWarehouse = await warehouseRepository.getOneObj(objQuery);
      if (objData) {
        //   await RedisPromise.setData(keyCache, JSON.stringify(objData));
        data = objData;
      }
      return data;
      // }
      // } catch (error) {
      //   const objData: IWarehouse = await warehouseRepository.getOneObj(objQuery);
      //   data = objData;
      //   return data;
      // }
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static async getWarehouseByHub(hubId: number, orderTypeId: number): Promise<any> {
    const keyCache: string = `WAREHOUSE_HUB_${hubId}}`;
    const warehouseRepository: WarehouseRepository = new WarehouseRepository();
    try {
      let data: any = null;
      const objQuery: any = { hubId, orderTypeId, isActivated: true, isDeleted: false };
      // try {
      // const dataCached: IWarehouse = await RedisPromise.getData(keyCache);
      // if(dataCached) {
      //   return dataCached;
      // } else {
      const objData: IWarehouse = await warehouseRepository.getOneObj(objQuery);
      if (objData) {
        //   await RedisPromise.setData(keyCache, JSON.stringify(objData));
        data = objData;
      }
      return data;
      // }
      // } catch (error) {
      //   const objData: IWarehouse = await warehouseRepository.getOneObj(objQuery);
      //   data = objData;
      //   return data;
      // }
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static async getCountryByCode(code: string): Promise<any> {
    const keyCache: string = EConfigure.KEY_COUNTRY_CODE;
    const countryRepository: CountryRepository = new CountryRepository();
    try {
      const objCache: any = {};
      let country = null;
      const objQuery = { code: code };
      try {
        const countrys: any = await RedisPromise.getData(keyCache);
        if (countrys && countrys[code]) {
          return countrys[code];
        } else {
          const objData: ICountry = await countryRepository.getOneObj(objQuery);

          if (objData) {
            country = objData;
            objCache[code] = country;
            await RedisPromise.setData(keyCache, JSON.stringify(objCache));
            return objCache[code];
          }
        }
      } catch (error) {
        country = await countryRepository.getOneObj(objQuery);
      }
      return country;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static convertKG(value: number, unitOfMass: string): number {
    const itemWeight: number = Number(value);
    let weightKG: number = 0;
    switch (unitOfMass) {
      case WeightName.get(WeightKey.GRAM):
        weightKG = itemWeight * 0.001;
        break;
      case WeightName.get(WeightKey.POUND):
        weightKG = Number(itemWeight * 0.45359);
        break;
      default:
        weightKG = Number(itemWeight);
        break;
    }
    return weightKG;
  }

  public static async getHSCode(itemName: string): Promise<any> {
    const keyCache: string = `HSCode_${itemName}`;
    const hscodeRepository: HSCodeRepository = new HSCodeRepository();
    try {
      let hscode: any = null;
      const objQuery = { name: itemName };
      try {
        hscode = await RedisPromise.getData(keyCache);
        if (hscode) {
          return hscode;
        } else {
          hscode = await hscodeRepository.getOneObj(objQuery);
          if (hscode) {
            await RedisPromise.setData(keyCache, JSON.stringify(hscode));
            return hscode;
          }
        }
      } catch (error) {
        hscode = await hscodeRepository.getOneObj(objQuery);
      }
      return hscode;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static flightCode(flightNo: string, dateStr: string) {
    const arrivalDate = moment(dateStr).format('DDMMM');
    return `${flightNo.toUpperCase()}/${arrivalDate.toUpperCase()}`;
  }

  public static taxAndCollection(dataUpdate: any, hscode: any): any {
    if (hscode) {
      let priceVND: any = dataUpdate['priceVND'] ? Number(dataUpdate['priceVND']) : 0;
      // Xử lý thành tiền từ biểu thuế NK
      dataUpdate['importTax'] = Number(hscode['importTaxValue']);
      dataUpdate['importTaxCode'] = hscode['importTaxCode'];
      dataUpdate['importPrice'] = 0;
      if (!dataUpdate['importTaxFree']) {
        if (priceVND > 0 && hscode['importTaxValue'] > 0) {
          dataUpdate['importPrice'] = +(priceVND * (Number(hscode['importTaxValue']) / 100)).toFixed(0);
        }
      }
      priceVND = priceVND + dataUpdate['importPrice'];
      dataUpdate['priceAfterImportTax'] = priceVND;
      // Xử lý thành tiền từ biểu thuế BVMT
      dataUpdate['environmentTax'] = Number(hscode['environmentTaxPrice']);
      dataUpdate['environmentTaxCode'] = hscode['environmentTaxCode'] ? hscode['environmentTaxCode'] : null;
      dataUpdate['environmentPrice'] = 0;
      if (!dataUpdate['environmentTaxFree']) {
        if (hscode['environmentTaxPrice'] > 0) {
          dataUpdate['environmentPrice'] = +(dataUpdate['quantity1'] * hscode['environmentTaxPrice']).toFixed(0);
        }
      }
      // Xử lý thành tiền từ biểu thuế TTDB
      dataUpdate['specialConsumptionTax'] = Number(hscode['specialConsumptionTaxValue']);
      dataUpdate['specialConsumptionTaxCode'] = hscode['specialConsumptionTaxCode'] ? hscode['specialConsumptionTaxCode'] : null;
      dataUpdate['specialConsumptionPrice'] = 0;
      if (!dataUpdate['specialConsumptionTaxFree']) {
        if (hscode['specialConsumptionTaxValue'] > 0) {
          dataUpdate['specialConsumptionPrice'] = +(priceVND * (Number(hscode['specialConsumptionTaxValue']) / 100)).toFixed(0);
        }
      }
      // Xử lý thành tiền từ biểu thuế VAT
      const totalVAT: number = priceVND + Number(dataUpdate['environmentPrice']) + Number(dataUpdate['specialConsumptionPrice']);
      dataUpdate['priceBeforeVATTax'] = totalVAT;
      dataUpdate['VATTax'] = Number(hscode['VATValue']);
      dataUpdate['VATTaxCode'] = hscode['VATCode'];
      dataUpdate['VATPrice'] = 0;
      if (!dataUpdate['VATTaxFree']) {
        if (hscode['VATValue'] > 0) {
          dataUpdate['VATPrice'] = +(totalVAT * (Number(hscode['VATValue']) / 100)).toFixed(0);
        }
      }
    } else {
      //NK
      dataUpdate['importTax'] = 0;
      dataUpdate['importTaxCode'] = null;
      dataUpdate['importTaxCode'] = null;
      dataUpdate['importPrice'] = 0;
      // BVMT
      dataUpdate['environmentTax'] = 0;
      dataUpdate['environmentTaxCode'] = null;
      dataUpdate['environmentTaxFree'] = null;
      dataUpdate['environmentPrice'] = 0;
      // TTDB
      dataUpdate['specialConsumptionTax'] = 0;
      dataUpdate['specialConsumptionTaxCode'] = null;
      dataUpdate['specialConsumptionTaxFree'] = null;
      dataUpdate['specialConsumptionPrice'] = 0;
      // VAT
      dataUpdate['VATTax'] = 0;
      dataUpdate['VATTaxCode'] = null;
      dataUpdate['VATTaxFree'] = null;
      dataUpdate['VATPrice'] = 0;
    }
    return dataUpdate;
  }

  public static taxAndCollectionExport(dataUpdate: any, hscode: any): any {
    if (hscode) {
      let priceVND: any = dataUpdate['priceVND'] ? Number(dataUpdate['priceVND']) : 0;
      // Xử lý thành tiền từ biểu thuế XK
      dataUpdate['exportTax'] = Number(hscode['importTaxValue']);
      dataUpdate['exportTaxCode'] = hscode['importTaxCode'];
      dataUpdate['exportPrice'] = 0;
      if (!dataUpdate['exportTaxFree']) {
        if (priceVND > 0 && hscode['importTaxValue'] > 0) {
          dataUpdate['exportPrice'] = +(priceVND * (Number(hscode['importTaxValue']) / 100)).toFixed(0);
        }
      }
      priceVND = priceVND + dataUpdate['exportPrice'];
      dataUpdate['priceAfterImportTax'] = priceVND;
      // Xử lý thành tiền từ biểu thuế BVMT
      dataUpdate['environmentTax'] = Number(hscode['environmentTaxPrice']);
      dataUpdate['environmentTaxCode'] = hscode['environmentTaxCode'];
      dataUpdate['environmentPrice'] = 0;
      if (!dataUpdate['environmentTaxFree']) {
        if (hscode['environmentTaxPrice'] > 0) {
          dataUpdate['environmentPrice'] = +(dataUpdate['quantity1'] * hscode['environmentTaxPrice']).toFixed(0);
        }
      }
      // Xử lý thành tiền từ biểu thuế TTDB
      dataUpdate['specialConsumptionTax'] = Number(hscode['specialConsumptionTaxValue']);
      dataUpdate['specialConsumptionTaxCode'] = hscode['specialConsumptionTaxCode'];
      dataUpdate['specialConsumptionPrice'] = 0;
      if (!dataUpdate['specialConsumptionTaxFree']) {
        if (hscode['specialConsumptionTaxValue'] > 0) {
          dataUpdate['specialConsumptionPrice'] = +(priceVND * (Number(hscode['specialConsumptionTaxValue']) / 100)).toFixed(0);
        }
      }
      // Xử lý thành tiền từ biểu thuế VAT
      const totalVAT: number = priceVND + Number(dataUpdate['environmentPrice']) + Number(dataUpdate['specialConsumptionPrice']);
      dataUpdate['priceBeforeVATTax'] = totalVAT;
      dataUpdate['VATTax'] = Number(hscode['VATValue']);
      dataUpdate['VATTaxCode'] = hscode['VATCode'];
      dataUpdate['VATPrice'] = 0;
      if (!dataUpdate['VATTaxFree']) {
        if (hscode['VATValue'] > 0) {
          dataUpdate['VATPrice'] = +(totalVAT * (Number(hscode['VATValue']) / 100)).toFixed(0);
        }
      }
    } else {
      //NK
      dataUpdate['exportTax'] = 0;
      dataUpdate['exportTaxCode'] = null;
      dataUpdate['exportTaxCode'] = null;
      dataUpdate['exportPrice'] = 0;
      // BVMT
      dataUpdate['environmentTax'] = 0;
      dataUpdate['environmentTaxCode'] = null;
      dataUpdate['environmentTaxFree'] = null;
      dataUpdate['environmentPrice'] = 0;
      // TTDB
      dataUpdate['specialConsumptionTax'] = 0;
      dataUpdate['specialConsumptionTaxCode'] = null;
      dataUpdate['specialConsumptionTaxFree'] = null;
      dataUpdate['specialConsumptionPrice'] = 0;
      // VAT
      dataUpdate['VATTax'] = 0;
      dataUpdate['VATTaxCode'] = null;
      dataUpdate['VATTaxFree'] = null;
      dataUpdate['VATPrice'] = 0;
    }
    return dataUpdate;
  }

  public static convertNull(value: any): any {
    if (Array.isArray(value)) {
      const dataConvert = value.map((element: any) => {
        for (const [key, data] of Object.entries(element)) {
          if (data === '') {
            element[key] = null;
          }
        }
        return element;
      });
      return dataConvert;
    } else {
      for (const [key, data] of Object.entries(value)) {
        if (data === '') {
          value[key] = null;
        }
      }
    }
    return value;
  }

  public static handleAddress(address: string): any {
    let store: any = null;
    if (address) {
      let temp: any = null;
      let times: number = 1;
      store = {};
      const removeSpace: string = address
        //Replace double space with single
        .replace(/  +/g, ' ')
        //Replace any amount of whitespace before or after a `.` to nothing
        .replace(/\s*\.\s*/gi, '.')
        //Replace any amount of whitespace before or after a `,` to nothing
        .replace(/\s*,\s*/gi, ' ')
        .trim();
      const parts: string[] = removeSpace.split(' ');
      parts.forEach((part: string, index: number) => {
        if (part) {
          if (part.length > 0 && part.length < 50) {
            if (!temp) {
              temp = part;
              if (index === parts.length - 1) {
                store[times] = temp;
                times++;
                temp = null;
              }
            } else {
              if (temp.length + part.length < 50) {
                temp = `${temp} ${part}`;
                if (index === parts.length - 1) {
                  store[times] = temp;
                  times++;
                }
              } else {
                store[times] = temp;
                temp = part;
                times++;
              }
            }
          } else {
            store[times] = part;
            times++;
          }
        }
      });
    }
    return store;
  }

  public static handleRemoveSpaceComma(address: string): any {
    let noSpace: any = null;
    if (address) {
      noSpace = address
        //Replace double space with single
        .replace(/  +/g, ' ')
        //Replace any amount of whitespace before or after a `.` to nothing
        // .replace(/\s*\.\s*/ig, '.')
        //Replace any amount of whitespace before or after a `,` to nothing
        .replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|{|}|\||\\/g, '')
        .replace(/\s*,\s*/gi, ',')
        .trim();
    }
    return noSpace;
  }

  public static removeSpeialCharacter(str: string): string {
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, ' ');
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    // str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,"");
    str = str.replace(/!|\^|\*|\+|\=|\<|\>|\?|\'|\"|\[|\]|~|`|\||\\/g, '');
    return str;
  }

  public static removeGeneralToken(str: string): string {
    str = str.replace(/\t|\n|\r|\0+/g, '');
    return str;
  }

  public static subString(data: any, start: number, end: number): any {
    let strSub = null;
    if (data !== null && data !== '') {
      strSub = String(data).substring(start, end);
    }
    return strSub;
  }

  public static async updateDeclaration(HAWB: string[]): Promise<any> {
    try {
      const tmmUrl = process.env.TMM_URL || 'https://devapi.globex.vn/tmm/api/v1/';
      console.log(`${tmmUrl}manifests/declarationClearance`);
      const res: any = await Utilities.callAPI(`${tmmUrl}manifests/declarationClearance`, EConfigure.PUT_METHOD, { HAWBs: HAWB });
      return res;
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static async updatePartnerStatusHAWB(HAWB: string[], statusId: number, employeeId: any = null, reasonDetailId: any = null): Promise<any> {
    const config = new Config();
    try {
      if (HAWB) {
        const url: string = `${config.ommAPI()}/partner/status`;
        console.log('\n --- [%s] Cập nhật partner status OMM: %s', moment().format(moment().format(EConfigure.FULL_TIME)), url);
        const dataUpdate: any = {
          hawbs: HAWB,
          statusId: statusId,
          employeeId: employeeId,
          createdAt: moment().format(EConfigure.FULL_TIME),
          reasonDetailId: reasonDetailId
        };
        Utilities.callAPI(url, 'POST', dataUpdate);
      }
    } catch (error) {
      console.log(`Cập nhật partner status HAWB OMM thất bại: %o`, error);
    }
  }

  public static sendTelegramNoti(functionName: string, errMessage: string) {
    try {
      const token: string = Configure.TELEGRAM_CLEARANCE_TOKEN || '';
      const chatId: string = Configure.TELEGRAM_CLEARANCE_CHATID || '';

      const message: string = `<b>[${functionName}]</b>: ${errMessage}`;

      const client = new TelegramBot(token);
      client.sendMessage(chatId, message, { parse_mode: EConfigure.TELEGRAM_PARSE_HTML });
    } catch (error) {
      console.log(error);
      console.log('\n --- [%s] Gửi telegram lỗi: %j', moment().format(EConfigure.FULL_TIME), error);
    }
  }

  public static sendTelegramError(functionName: string, errMessage: string) {
    try {
      const token: string = process.env.TELEGRAM_ERROR_TOKEN || '';
      const chatId: string = process.env.TELEGRAM_ERROR_CHAT_ID || '';
      const enviroment: string = process.env.NODE_ENV || '';
      const domain: string = process.env.DOMAIN || '';
      const message: string = `Resource <b>${domain} ${enviroment}</b> - <b>${functionName}</b> - ${errMessage}`;
      const client = new TelegramBot(token);
      client.sendMessage(chatId, message, { parse_mode: EConfigure.TELEGRAM_PARSE_HTML });
    } catch (error) {
      console.log(error);
      console.log('\n --- [%s] Gửi telegram lỗi: %j', moment().format(EConfigure.FULL_TIME), error);
    }
  }

  public static sendDiscordErr(functionName: string, errMessage: string) {
    try {
      const webhookUrl: string = 'https://discordapp.com/api/webhooks/1376771048745205790/kEmYCBYp37rY5hslgFIZhGunEZ9zIyLBnM-BhQIj_29mCD2q0n_R9vpGrYJPQIYLrdYq';
      const enviroment: string = process.env.NODE_ENV || '';
      const domain: string = process.env.DOMAIN || '';
      const message: string = `Resource **${domain} ${enviroment}** - **${functionName}** - ${errMessage}`;
      const data = { 'content': message };
      axios.post(webhookUrl, data)
        .then(_ => {
          console.log('\n --- [%s] Gửi thông báo lỗi Discord thành công', moment().format(EConfigure.FULL_TIME));
        })
        .catch(error => {
          console.error('\n --- [%s] Lỗi khi gửi thông báo lỗi Discord: %j', moment().format(EConfigure.FULL_TIME), error);
        });
    } catch (error) {
      console.log(error)
      console.log('\n --- [%s] Gửi discord lỗi: %j', moment().format(EConfigure.FULL_TIME), error);
    }
  }

  public static async updateCreateLog(id: number, status: boolean): Promise<any> {
    const clearanceCreateLogRepository: ClearanceCreateLogRepository = new ClearanceCreateLogRepository();
    try {
      const clearanceCreateLog: ClearanceCreateLog = await clearanceCreateLogRepository.getOne(id);
      clearanceCreateLog['pending'] = clearanceCreateLog['pending'] > 0 ? clearanceCreateLog['pending'] - 1 : 0;
      if (status) {
        clearanceCreateLog['success'] = clearanceCreateLog['success'] + 1;
      } else {
        clearanceCreateLog['fail'] = clearanceCreateLog['fail'] + 1;
      }

      await clearanceCreateLog.save();
    } catch (error) {
      throw new Error(error as any);
    }
  }

  public static async callTMMApi(endPoint: string, method: string, data: any): Promise<any> {
    try {
      const tmmUrl = process.env.TMM_URL || 'https://devapi.globex.vn/tmm/api/v1/';
      console.log(`${tmmUrl}${endPoint}`);
      const res: any = await Utilities.callAPI(`${tmmUrl}${endPoint}`, method, data);
      return res;
    } catch (error) {
      Utilities.sendDiscordErr('[Utilities]][callTMMApi]', (error as Error).message);
      throw new Error(error as any);
    }
  }

  public static roundHalf(value: number, roundValue: number): number {
    const remainder: number = value % roundValue;
    if (remainder > 0) value = value - remainder + roundValue;
    return value;
  }

  public static async getCurrentSequence(field: string): Promise<number> {
    try {
      const parameterRepository = new ParameterRepository;
      const whereHAWB = { 'name': field };
      const sequenceHAWB = await parameterRepository.getOneObj(whereHAWB);
      if (sequenceHAWB) {
        const currentNo: number = sequenceHAWB['value'] as number;
        return currentNo;
      } else {
        const dataUpdate = {
          'name': field,
          'value': 0
        }
        await parameterRepository.createData(dataUpdate);
        return 0;
      }
    } catch (error) {

      throw new Error(error as any);
    }
  }

  public static async addOnSequence(no: number, distant: number, field: string) {
    try {
      const parameterRepository = new ParameterRepository;
      const optional = new Optional();
      optional.setWhere([new Where(EConfigure.AND, 'name', EConfigure.EQUAL, field)]);
      const updateData = {
        'value': Number(no) + Number(distant)
      }
      await parameterRepository.updateData(updateData, optional);
    } catch (error) {

      throw new Error(error as any);
    }
  }

  public static halfCeil(value: number, roundDecimal: number = 1): number {
    let converted: number = 0;
    if (value) {
      let round: number = parseFloat('1'.padEnd(parseFloat(String(roundDecimal)) + 1, '0'));
      converted = Math.ceil(round * value) / round;
    }
    return converted;
  }
}

export default Utilities;
