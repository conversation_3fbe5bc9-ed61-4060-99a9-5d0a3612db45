require('dotenv').config();

module.exports = {
  'local': {
    'username': process.env.DB_USERNAME,
    'password': process.env.DB_PASSWORD,
    'database': process.env.DB_NAME,
    'host': process.env.DB_HOST,
    'timezone': 'UTC',
    'dialect': 'postgres'
  },
  'development': {
    'username': process.env.DB_USERNAME,
    'password': process.env.DB_PASSWORD,
    'database': process.env.DB_NAME,
    'host': process.env.DB_HOST,
    'timezone': 'UTC',
    'dialect': 'postgres'
  },
  'production': {
    'username': 'nd5mJ65R',
    'password': 'dMurLt93FQ2VKxxa',
    'database': 'ommapi_prod',
    'host': '**********',
    'timezone': 'UTC',
    'dialect': 'postgres'
  }
}