import { LanguageMessages } from '@hapi/joi';

export const errorsJoi: LanguageMessages = {
  'any.invalid': '{{#label}} không hợp lệ',
  'any.only': '{{#label}} không hợp lệ',
  'any.required': '{{#label}} không được để trống',
  'any.unknown': '{{#label}} không hợp lệ',
  'array.min': '{{#label}} phải có ít nhất {{#limit}} dữ liệu',
  'boolean.base': '{{#label}} sai định dạng',
  'date.format': '{{#label}} sai định dạng {{#format}}',
  'number.base': '{{#label}} sai định dạng',
  'number.min': '{{#label}} phải lớn hơn hoặc bằng {{#limit}}',
  'number.positive': '{{#label}} phải lớn hơn 0',
  'string.base': '{{#label}} sai định dạng',
  'string.guid': '{{#label}} sai định dạng',
  'string.max': '{{#label}} không được vượt quá {{#limit}} ký tự',
  'string.uri': '{{#label}} sai định dạng',
  invalid: '{{#label}} không hợp lệ',
  'array.max': '{{#label}} không được vượt quá {{#limit}} dữ liệu',

  'any.empty': '{{#label}} không được để trống!',
  'any.allowOnly': '{{#label}} không chính xác!',
  'any.default': '{{#label}} threw an error when running default method',

  'alternatives.base': '{{#label}} not matching any of the allowed alternatives',

  'array.base': '{{#label}} không đúng định dạng!',
  'array.includes': '{{#label}} at position {{#pos}} does not match any of the allowed types',
  'array.includesSingle': '{{#label}} single value of "{{!label}}" does not match any of the allowed types',
  'array.includesOne': '{{#label}} at position {{#pos}} fails because {{#reason}}',
  'array.includesOneSingle': '{{#label}} single value of "{{!label}}" fails because {{#reason}}',
  'array.includesRequiredUnknowns': '{{#label}} does not contain {{#unknownMisses}} required value(s)',
  'array.includesRequiredKnowns': '{{#label}} does not contain {{#knownMisses}}',
  'array.includesRequiredBoth':
    '{{#label}} does not contain {{#knownMisses}} and {{#unknownMisses}} other required value(s)',
  'array.excludes': '{{#label}} at position {{#pos}} contains an excluded value',
  'array.excludesSingle': '{{#label}} single value of "{{!label}}" contains an excluded value',

  'array.length': '{{#label}} chỉ bao gồm {{#limit}} giá trị!',
  'array.ordered': '{{#label}} at position {{#pos}} fails because {{#reason}}',
  'array.orderedLength': '{{#label}} at position {{#pos}} fails because array must contain at most {{#limit}} items',
  'array.ref': '{{#label}} references "{{ref}}" which is not a positive integer',
  'array.sparse': '{{#label}} must not be a sparse array',
  'array.unique': '{{#label}} có giá trị trùng lặp!',

  'binary.base': '{{#label}} must be a buffer or a string',
  'binary.min': '{{#label}} must be at least {{#limit}} bytes',
  'binary.max': '{{#label}} must be less than or equal to {{#limit}} bytes',
  'binary.length': '{{#label}} must be {{#limit}} bytes',

  'date.base': '{{#label}} không đúng định dạng!',

  'date.strict': '{{#label}} must be a valid date',
  'date.min': '{{#label}} must be larger than or equal to "{{limit}}"',
  'date.max': '{{#label}} must be less than or equal to "{{limit}}"',

  'date.isoDate': '{{#label}} must be a valid ISO 8601 date',
  'date.timestamp.javascript': '{{#label}} must be a valid timestamp or number of milliseconds',
  'date.timestamp.unix': '{{#label}} must be a valid timestamp or number of seconds',
  'date.ref': '{{#label}} references "{{ref}}" which is not a date',

  'function.base': '{{#label}} must be a Function',
  'function.arity': '{{#label}} must have an arity of {{#n}}',
  'function.minArity': '{{#label}} must have an arity greater or equal to {{#n}}',
  'function.maxArity': '{{#label}} must have an arity lesser or equal to {{#n}}',
  'function.ref': '{{#label}} must be a Joi reference',
  'function.class': '{{#label}} must be a class',

  'lazy.base': '{{#label}} !!schema error: lazy schema must be set',
  'lazy.schema': '{{#label}} !!schema error: lazy schema function must return a schema',

  'object.unknown': 'Trường {{#label}} không hợp lệ!',
  'object.base': '{{#label}} không đúng định dạng!',
  'object.child': '{{#label}} !!child "{{!child}}" fails because {{#reason}}',
  'object.min': '{{#label}} must have at least {{#limit}} children',
  'object.max': '{{#label}} must have less than or equal to {{#limit}} children',
  'object.length': '{{#label}} must have {{#limit}} children',
  'object.allowUnknown': '{{#label}} !!"{{!child}}" không được phép!',
  'object.with': '{{#label}} !!"{{mainWithLabel}}" và "{{peerWithLabel}}" là trường bắt buộc!',
  'object.without': '{{#label}} !!"{{mainWithLabel}}" conflict with forbidden peer "{{peerWithLabel}}"',
  'object.missing': '{{#label}} must contain at least one of {{#peersWithLabels}}',
  'object.xor': '{{#label}} contains a conflict between exclusive peers {{#peersWithLabels}}',
  'object.or': '{{#label}} must contain at least one of {{#peersWithLabels}}',
  'object.and': '{{#label}} contains {{#presentWithLabels}} without its required peers {{#missingWithLabels}}',
  'object.nand': '{{#label}} !!"{{mainWithLabel}}" must not exist simultaneously with {{#peersWithLabels}}',
  'object.assert': '{{#label}} !!"{{ref}}" validation failed because "{{ref}}" failed to {{#message}}',
  'rename.multiple':
    'cannot rename child "{{from}}" because multiple renames are disabled and another key was already renamed to "{{to}}"',
  'rename.override':
    '{{#label}} cannot rename child "{{from}}" because override is disabled and target "{{to}}" exists',
  'rename.regex.multiple':
    'cannot rename children {{#from}} because multiple renames are disabled and another key was already renamed to "{{to}}"',
  'rename.regex.override':
    '{{#label}} cannot rename children {{#from}} because override is disabled and target "{{to}}" exists',

  'object.type': '{{#label}} must be an instance of "{{type}}"',
  'object.schema': '{{#label}} must be a Joi instance',

  'number.max': '{{#label}} must be less than or equal to {{#limit}}',
  'number.less': '{{#label}} must be less than {{#limit}}',
  'number.greater': '{{#label}} must be greater than {{#limit}}',
  'number.float': '{{#label}} must be a float or double',
  'number.integer': '{{#label}} phải là số nguyên dương!',
  'number.negative': '{{#label}} must be a negative number',

  'number.precision': '{{#label}} must have no more than {{#limit}} decimal places',
  'number.ref': '{{#label}} references "{{ref}}" which is not a number',
  'number.multiple': '{{#label}} must be a multiple of {{#multiple}}',
  'number.multiple.result': '{{#label}} có giá trị không chính xác!',

  'string.min': '{{#label}} phải có ít nhất {{#limit}} ký tự!',

  'string.length': '{{#label}} có độ dài {{#limit}} ký tự',
  'string.alphanum': '{{#label}} must only contain alpha-numeric characters',
  'string.token': '{{#label}} must only contain alpha-numeric and underscore characters',
  'string.empty': '{{#label}} không được để trống!',

  'string.pattern.base': '{{#label}} có chứa ký tự không hợp lệ!',
  'string.pattern.name': '{{#label}} có chứa ký tự không hợp lệ! Các ký tự hợp lệ bao gồm: {{#name}}',

  'string.regex.invert.base': '{{#label}} with value "{{!value}}" matches the inverted pattern: {{#pattern}}!',
  'string.regex.invert.name': '{{#label}} with value "{{!value}}" matches the inverted {{#name}} pattern',

  'string.email': '{{#label}} không hợp lệ!',

  'string.uriRelativeOnly': '{{#label}} must be a valid relative uri',
  'string.uriCustomScheme': '{{#label}} must be a valid uri with a scheme matching the {{#scheme}} pattern',
  'string.isoDate': '{{#label}} must be a valid ISO 8601 date',

  'string.hex': '{{#label}} must only contain hexadecimal characters',
  'string.base64': '{{#label}} must be a valid base64 string',
  'string.hostname': '{{#label}} must be a valid hostname',
  'string.normalize': '{{#label}} must be unicode normalized in the {{#form}} form',
  'string.lowercase': '{{#label}} must only contain lowercase characters',
  'string.uppercase': '{{#label}} must only contain uppercase characters',
  'string.trim': '{{#label}} must not have leading or trailing whitespace',
  'string.creditCard': '{{#label}} must be a credit card',
  'string.ref': '{{#label}} references "{{ref}}" which is not a number',
  'string.ip': '{{#label}} must be a valid ip address with a {{#cidr}} CIDR',
  'string.ipVersion':
    '{{#label}} must be a valid ip address of one of the following versions {{#version}} with a {{#cidr}} CIDR',

  'phone.invalid': '{{#label}} không hợp lệ!',
  'phone.incorrect': '{{#label}} không chính xác!'
};
