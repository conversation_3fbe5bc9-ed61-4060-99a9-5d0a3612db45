import mongoose from 'mongoose';
import dotenv from 'dotenv';
import moment from 'moment';
import Configure from '../emuns/configures';

dotenv.config();

export class MongoDatabase {
  private connection = {
    host: process.env.DB_MONGO_HOST || '**********',
    port: (process.env.DB_MONGO_PORT || 27017) as number,
    username: process.env.DB_MONGO_USERNAME || 'globex',
    password: process.env.DB_MONGO_PASSWORD || 'Gl0b3x2019',
    name: process.env.DB_MONGO_NAME || 'ommapi',
    collection: process.env.DB_MONGO_COLLECTION || 'clearancelogger',
    authSource: process.env.DB_MONGO_AUTHSOURCE || ''
  };

  public get uriConnect(): string {
    return `mongodb://${this.connection.username}:${this.connection.password}@${this.connection.host}:${this.connection.port}/${this.connection.name}?${this.connection.authSource}`;
  }

  public get collect(): string {
    return this.connection.collection;
  }

  connect() {
    // let connectString = `mongodb://${this.connection.username}:${this.connection.password}@${this.connection.host}:${this.connection.port}`;

    mongoose.connect(this.uriConnect, {
      dbName: this.connection.name,
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false
    });

    let dbMongo = mongoose.connection;

    dbMongo.on('error', async (error: any) => {
      console.log(' --- [%s] [error][mongo]: %o', moment().format(Configure.FULL_TIME), error);
      setTimeout(() => {
        this.connect();
      }, 2000);
    });

    dbMongo.once('open', () => {
      console.log(' --- [%s] [Mongo]', moment().format(Configure.FULL_TIME));
    });
  }
}
