import { Sequelize, Options } from 'sequelize';
import dotenv from 'dotenv';

class ConfigDatabase {
  public sequelize: Sequelize;
  constructor() {
    dotenv.config();
    const options: Options = {
      host: process.env.DB_HOST || 'db',
      username: process.env.DB_USERNAME || 'api',
      password: process.env.DB_PASSWORD || 'api',
      database: process.env.DB_NAME || 'api',

      // host: '**********' || 'db',
      // username: 'nd5mJ65R' || 'api',
      // password: 'dMurLt93FQ2VKxxa' || 'api',
      // database: 'ommapi_prod' || 'api',

      // host: 'data-warehouse.sbp.cloud' || 'db',
      // username: 'nd5mJ65R' || 'api',
      // password: 'dMurLt93FQ2VKxxa' || 'api',
      // database: 'ommapi_prod' || 'api',

      timezone: 'UTC',
      dialect: 'postgres',
      pool: {
        max: 5,
        min: 0,
        idle: 10 * 1000,
        acquire: 80000
      },
    };
    this.sequelize = new Sequelize(options);
  }
}

export default ConfigDatabase;

// import Hub from '../modules/v1/hub/hub.model';
// import Partner from '../modules/v1/partner/partner.model';
// import HubPartner from '../modules/v1/hubPartner/hubPartner.model';

// Partner.belongsToMany(Hub, { as: 'hubs', through: HubPartner, foreignKey: 'partnerId' });
// Hub.belongsToMany(Partner, { as: 'partners', through: HubPartner, foreignKey: 'hubId' });