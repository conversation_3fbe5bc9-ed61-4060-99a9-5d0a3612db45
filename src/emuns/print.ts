export enum PrintKey {
  CLEARANCED,
  INSPECTION,
  DECLARAT<PERSON>,

  INVOICE_MIC,
  INVOICE_IDA,
  INVOICE_CARGO,

  MANAGEMENT_PDF,
  MANAGEMENT_EXCEL,

  PICK_UP_1,
  PICK_UP_2,

  REPORT_CUSTOM_IMPORT,
  REPORT_CUSTOM_EXPORT,

  INVOICE_CARGO_EXCEL,

  INVOICE_MEC,

  DETAIL_INVOICE,

  IBCExport,
  INVOICE_MIC_TT29,
  INVOICE_MEC_TT29,

  DETAIL_INVOICE_MIC_TT29,
  DETAIL_INVOICE_MEC_TT29,
}

export const PrintName = new Map<number, string>([
  [PrintKey.CLEARANCED, 'CLE'],
  [PrintKey.INSPECTION, 'INS'],
  [PrintKey.DECLARATION, 'DEC'],

  [PrintKey.INVOICE_MIC, '<PERSON>ảng kê MIC'],
  [PrintKey.INVOICE_IDA, '<PERSON><PERSON><PERSON> kê IDA'],
  [PrintKey.INVOICE_CARGO, '<PERSON><PERSON><PERSON> kê hàng hóa'],
  [PrintKey.INVOICE_CARGO_EXCEL, '<PERSON><PERSON>ng kê hàng hóa - excel'],

  [PrintKey.MANAGEMENT_PDF, 'Bảng kê pdf'],
  [PrintKey.MANAGEMENT_EXCEL, 'Bảng kê excel'],
  [PrintKey.PICK_UP_1, 'Bảng kê lấy hàng (1)'],
  [PrintKey.PICK_UP_2, 'Bảng kê lấy hàng (2)'],

  [PrintKey.REPORT_CUSTOM_IMPORT, 'Báo cáo hải quan hàng Nhập'],
  [PrintKey.REPORT_CUSTOM_EXPORT, 'Báo cáo hải quan hàng Xuất'],

  [PrintKey.INVOICE_MEC, 'Bảng kê MEC'],
  [PrintKey.DETAIL_INVOICE, 'Bảng kê chi tiết hàng hóa'],
  [PrintKey.IBCExport, 'Xuất dữ liệu IBC'],

  [PrintKey.INVOICE_MIC_TT29, 'Bảng kê MIC (TT29)'],
  [PrintKey.INVOICE_MEC_TT29, 'Bảng kê MEC (TT29)'],
  [PrintKey.DETAIL_INVOICE_MIC_TT29, 'Bảng kê chi tiết hàng hóa - Nhập (TT29)'],
  [PrintKey.DETAIL_INVOICE_MEC_TT29, 'Bảng kê chi tiết hàng hóa - Xuất (TT29)'],
]);