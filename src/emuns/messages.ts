enum ValidateMessage {
  HEADER_MISSING = 'Request thiếu header',
  AUTHORIZATION_WRONG_TYPE = 'Authorization sai định kiểu',
  INTERNAL_AUTHORIZATION_WRONG_TOKEN = 'Internal authorization sai mật khẩu',
  TOKEN_EXPIRED_TIME = 'Token hết hạn',
  UNAUTHORIZED = 'Bị từ chối truy cập vào tài nguyên',
  LOGIN_TIMEOUT = 'Phiên đăng nhập đã hết hạn',
  PERMISSION_DENIED = 'Quyền truy cập vào chức năng bị từ chối',
  //
  BOOLEAN = 'phải là boolean',
  ARRAY = 'phải là mảng',
  NUMBER = 'phải là số',
  INTEGER = 'phải là số nguyên',
  REQUIRED = 'không được thiếu',
  ONLY = 'phải là một trong số',
  EMPTYED = 'không được để rỗng',
  MAX_2 = 'không được dài hơn 2 kí tự',
  MAX_3 = 'không được dài hơn 3 kí tự',
  MAX_4 = 'không được dài hơn 4 kí tự',
  MAX_5 = 'không được dài hơn 5 kí tự',
  MAX_6 = 'không được dài hơn 6 kí tự',
  MAX_7 = 'không được dài hơn 7 kí tự',
  MAX_8 = 'không được dài hơn 8 kí tự',
  MAX_10 = 'không được dài hơn 10 kí tự',
  MAX_15 = 'không được dài hơn 15 kí tự',
  MAX_20 = 'không được dài hơn 20 kí tự',
  MAX_30 = 'không được dài hơn 30 kí tự',
  MAX_33 = 'không được dài hơn 33 kí tự',
  MAX_50 = 'không được dài hơn 50 kí tự',
  MAX_200 = 'không được dài hơn 200 kí tự',
  MAX_100 = 'không được dài hơn 100 kí tự',
  MAX_250 = 'không được dài hơn 250 kí tự',
  MAX_255 = 'không được dài hơn 255 kí tự',
  MAX_400 = 'không được dài hơn 400 kí tự',
  MAX_1000 = 'không được dài hơn 1000 kí tự',
  MAX_5000 = 'không được dài hơn 5000 kí tự',
  GREATE_EQUAL_THAN_0 = 'phải lớn hơn 0',
  GREATE_EQUAL_THAN_1 = 'phải lớn hơn hoặc bằng 1',
  GREATER_THAN_0 = 'phải lớn hơn 0',

  MIN_7 = 'không được ít hơn 7 kí tự',

  LEAST_ITEM_1 = 'phải có it nhất 1 phần tử',
  MAX_ITEM_50 = 'không được lớn hơn 50 phần tử',
  MAX_ITEM_300 = 'không được lớn hơn 300 phần tử',

  SAME_DATA = 'có giá trị bị trùng',
  UNKNOWN = 'không hợp lệ',

  PARAMETER_WRONG_TYPE = 'Tham số sai định dạng',

  WAIT_CUSTOMER_CONFIRM = 'Đang chờ xác nhận của khách hàng',
  PRICE_THAN_1M = 'Giá trị hàng hóa lớn hơn 1.000.000 VNĐ',
  //
  FOUND = 'Dữ liệu',
  SUCCESS = 'Thành công',
  FAIL = 'Thất bại',
  NOT_FOUND = 'Không tìm thấy dữ liệu',
  VALIDATE_ERROR = 'Kiểm tra dữ liệu đầu vào lỗi',
  // UPDATE_FAIL = 'Cập nhật thất bại',
  DELETE_SUCCESS = 'Xóa thành công',
  DELETE_FAIL = 'Xóa thất bại',
  SERVICE_NOT_SAME = 'Có tồn tại Manifest không cùng một dịch vụ',
  STATION_NOT_SAME = 'Có tồn tại Manifest không cùng một station',
  MANIFEST_NOT_CREATE = 'Manifest có trạng thái khác tạo mới',
  MANIFEST_NOT_CONSOLIDATE = 'Manifest có trạng thái khác Consolidate',
  MANIFEST_NOT_ASSIGN_STATION = 'Manifest chưa được gán trạm',
  MANIFEST_NOT_CHECKIN = 'Manifest có trạng thái chưa checkin hoặc có trạng thái chưa checkout khỏi trạm',
  MANIFEST_CHECKING_IN = 'Manifest có trạng thái đã checkin',
  MANIFEST_DELIVERIED = 'Manifest có trạng thái đã được giao hàng',
  DISPATCH_DELIVERY = 'Manifest có trạng thái điều phối giao hàng',
  MANIFEST_BAGGING = 'Manifest có trạng thái đang đóng gói',
  MANIFEST_SHIPMENT = 'Manifest có trạng thái đang vận chuyển',
  MANIFEST_CHECK_IN_SUCSESS = 'Checkin Manifest thành công',
  MANIFEST_BAGGING_SUCSESS = 'Đóng gói Manifest thành công',
  MANIFEST_SEND_DISPATCH_SUCSESS = 'Manifest sang bộ phận chuyển phát thành công',
  MANIFEST_CHECKOUT_SUCSESS = 'Checkout Manifest thành công',
  MANIFEST_IS_CANCELED = 'Có tần tại Manifest bị cancel',
  MANIFEST_NOT_ASSIGN_OR_NOT_CHECK_IN = 'Tồn tại Manifest chưa được gán đúng trạm, Manifest chưa chekin hoặc đã bagging',
  MANIFEST_NOT_CHECKOUT = 'Tồn tại manifest chưa checkout khỏi trạm',
  MANIFEST_NOT_BAGGING = 'Có tần tại Manifest có trạng thái chưa đóng gói',
  BOX_CHECKOUT_SUCSESS = 'Checkout Box thành công',
  BOX_STATION_NOT_SAME = 'Có tồn tại Box không cùng một station',
  MANIFEST_NOT_UPDATE = 'Manifest của bạn đã được checkin không thể cập nhật được dữ liệu',
  MANIFEST_ASSIGNED_COURIER = 'Manifest đã được gán cho courier',
  MANIFEST_NOT_CHECKIN_WAREHOUSE = 'Manifest chưa checkin lưu kho',
  //
  MANIFEST_DIFFERENCE_SERVICE = 'Manifest khác dịch vụ',
  NOT_EXIST = 'Không tồn tại',
  //
  FAIL_URL_HANDLE = 'Xảy ra lỗi khi phân tích đối sổ url',

  MIN_6 = 'phải dài hơn 6 kí tự',
  USER_PASS_MISSING = 'Username và password không được để trống',
  EXCEPTION_MESSAGE = 'Xảy ra lỗi! Vui lòng thử lại sau',
  USER_NOT_FOUND = 'Tài khoản không tồn tại',
  PASSWORD_WRONG = 'Mật khẩu không đúng',
  USERNAME_PASSWORD_INVALID = 'Tên đăng nhập hoặc mật khẩu không đúng',

  // validate model
  NAME_EXISTED = 'Tên đã tồn tại',
  CODE_EXISTED = 'Mã đã tồn tại',
  ORDER_EXISTED = 'Mã đơn hàng đã tồn tại',
  OMM_NOT_FOUND = 'Không tìm thấy dữ liệu từ OMM',
  HAWB_DOMESTIC = 'HAWB sử dụng dịch vụ domestic',

  // thông điệp trả về
  CREATE_FAIL = 'Tạo mới dữ liệu thất bại',
  CREATED_DATA = 'Tạo mới dữ liệu thành công',
  UPDATE_FAIL = 'Cập nhật dữ liệu thất bại',
  UPDATE_SUCCESS = 'Cập nhật thành công',
  UPDATE_CLASSIFY_SUCCESS = 'Chuyển loại hình thành công',
  UPDATE_CLASSIFY_FAIL = 'Chuyển loại hình thất bại',
  MIC_TO_IDA_SUCCESS = 'Chuyển trạng thái MIC thành IDA thành công',
  MIC_TO_IDA_FAIL = 'Chuyển trạng thái MIC thành IDA thất bại',
  //
  IDA_TO_MIC_SUCCESS = 'Chuyển trạng thái IDA thành MIC thành công',
  IDA_TO_MIC_FAIL = 'Chuyển trạng thái IDA thành MIC thất bại',
  READY_CODE_TAX = 'HAWB đã có mã số thuế',
}

export default ValidateMessage;
