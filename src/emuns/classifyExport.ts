export enum ClassifyKey {
  DOC,
  PAR,
  COM, //commerce
}

export const ClassifyName = new Map<number, string>([
  [ClassifyKey.DOC, 'DOC'],
  [ClassifyKey.PAR, 'PAR'],
  [ClassifyKey.COM, 'COM'],
]);

export enum ClassifyValidateKey {
  DOC,
  MEC,
  EDA,
  COM, //commerce
}

export const ClassifyValidateName = new Map<number, string>([
  [ClassifyValidateKey.DOC, 'DOC'],
  [ClassifyValidateKey.MEC, 'MEC'],
  [ClassifyValidateKey.EDA, 'EDA'],
  [ClassifyValidateKey.COM, 'COM'],
]);