export enum ClassifyKey {
  DOC,
  PAR,
  COM, //commerce
}

export const ClassifyName = new Map<number, string>([
  [ClassifyKey.DOC, 'DOC'],
  [ClassifyKey.PAR, 'PAR'],
  [ClassifyKey.COM, 'COM'],
]);

export enum ClassifyValidateKey {
  DOC,
  MIC,
  IDA,
  COM, //commerce
}

export const ClassifyValidateName = new Map<number, string>([
  [ClassifyValidateKey.DOC, 'DOC'],
  [ClassifyValidateKey.MIC, 'MIC'],
  [ClassifyValidateKey.IDA, 'IDA'],
  [ClassifyValidateKey.COM, 'COM'],
]);