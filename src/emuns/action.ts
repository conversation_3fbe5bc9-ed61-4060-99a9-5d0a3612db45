export enum ActionKey {
  CREATE,
  //
  UPDATE_CARGO_NAME,
  // K<PERSON>I TAM
  SUBMIT_INFOMATION, // IDA / EDA
  SENT_INFOMATION,

  // KHAI CHINH THUC
  SUBMIT_CUSTOM_CLEARANCE, // MIC / MEC + IDC / EDC
  // CHINH SUA
  SUBMIT_EDIT_CUSTOM_CLEARANCE, // MIE / MEE + IDE /EDE

  INSPECTION_KIND,
  ACCEPT_CLEARANCE,

  CHANGE_CLASSIFY = 50,
  UPDATE_MASTER,
  MESSAGE_TAX,
  RESET_MIC,
  RESET_IDA,
  UPDATE_MIC,
  UPDATE_IDA,
  SEND_ECUS,
  RECEIVE_ECUS,
  HOLD,
  UN_HOLD,
  SAVE_WAREHOUSE,
  DELETE_WAREHOUSE,
  NOTE_HOLD,
  RETURN_CARGO_DELIVERY,

  ASSIGN_UPDATE_CARGO_NAME,
  RE_CREATE,
  DELETE_HAWB,
  UPDATE_EXCHANGE_RATE,
  TRANSLATE_PRODUCT_NAME
}

export const ActionName = new Map<number, any>([
  [ActionKey.CREATE, { vi: '<PERSON><PERSON><PERSON> khai báo tên hàng', ui: 'Chưa khai báo tên hàng', en: 'Created' }],
  [ActionKey.UPDATE_CARGO_NAME, { vi: 'Chưa truyền tờ khai', ui: 'Chưa truyền tờ khai', en: 'Changed cargo name' }],

  [ActionKey.SUBMIT_INFOMATION, { vi: 'Đăng ký khai thông tin tạm', ui: 'Đăng ký khai thông tin tạm', en: 'Submit infomation' }],
  [ActionKey.SENT_INFOMATION, { vi: 'Đã khai thông tin', ui: 'Đã khai trước thông tin (IDA - EDA)', en: 'Sent infomation' }],

  [ActionKey.SUBMIT_CUSTOM_CLEARANCE, { vi: 'Gửi thông tin chính thức', ui: 'Gửi thông tin chính thức', en: 'Submit custom clearance' }],
  [ActionKey.SUBMIT_EDIT_CUSTOM_CLEARANCE, { vi: 'Gửi thông tin chỉnh sửa', ui: 'Gửi thông tin chỉnh sửa', en: 'Submit edit custom clearance' }],

  [ActionKey.INSPECTION_KIND, { vi: 'Đã phân luồng', ui: 'Chưa thông quan', en: 'Inspection kind' }],
  [ActionKey.ACCEPT_CLEARANCE, { vi: 'Đã thông quan', ui: 'Đã thông quan', en: 'Clearanced' }],

  [ActionKey.CHANGE_CLASSIFY, { vi: 'Chuyển luồng', en: 'Change type classify' }],
  [ActionKey.UPDATE_MASTER, { vi: 'Cập nhật số master', en: 'Update master' }],
  [ActionKey.MESSAGE_TAX, { vi: 'Gửi thuế', en: 'Message tax' }],
  [ActionKey.RESET_MIC, { vi: 'Thiết lập lại thông tin MIC', en: 'Reset MIC' }],
  [ActionKey.RESET_IDA, { vi: 'Thiết lập lại thông tin IDA', en: 'Reset IDA' }],
  [ActionKey.UPDATE_MIC, { vi: 'Cập nhât dữ liệu MIC', en: 'Update data MIC' }],
  [ActionKey.UPDATE_IDA, { vi: 'Cập nhât dữ liệu IDA', en: 'Update data IDA' }],
  [ActionKey.SEND_ECUS, { vi: 'Gửi thông tin đến ECUS', en: 'Send data to ECUS' }],
  [ActionKey.RECEIVE_ECUS, { vi: 'Nhận thông tin ECUS', en: 'Receive data ECUS' }],
  [ActionKey.HOLD, { vi: 'Hold', en: 'Hold' }],
  [ActionKey.UN_HOLD, { vi: 'Bỏ hold', en: 'Unhold' }],
  [ActionKey.SAVE_WAREHOUSE, { vi: 'Lưu kho', en: 'Save warehourse' }],
  [ActionKey.DELETE_WAREHOUSE, { vi: 'Bỏ lưu kho', en: 'Delete warehourse' }],
  [ActionKey.NOTE_HOLD, { vi: 'Ghi chú hold', en: 'Note hold' }],
  [ActionKey.RETURN_CARGO_DELIVERY, { vi: 'Trả hàng', ui: 'Trả hàng', en: 'Return cargo' }],
  [ActionKey.ASSIGN_UPDATE_CARGO_NAME, { vi: 'Xử lý cập nhật tên hàng hoá', en: 'Process update cargo name' }],
  [ActionKey.RE_CREATE, { vi: 'Tạo lại manifest', en: 'Recreate manifest' }],
  [ActionKey.DELETE_HAWB, { vi: 'Xóa manifest', en: 'Destroy manifest' }],
  [ActionKey.UPDATE_EXCHANGE_RATE, { vi: 'Cập nhật tỉ giá', en: 'Update exchange rate' }],
  [ActionKey.TRANSLATE_PRODUCT_NAME, { vi: 'Cập nhật tên hàng', en: 'Translate product name' }],
]);


