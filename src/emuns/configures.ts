import dotenv from 'dotenv';
dotenv.config();

enum Configure {
  DAY_TIME_MONOLITHIC = 'YYMMDD',
  DAY_TIME = 'YYYY-MM-DD',
  VIEW_DAY = 'DD-MM-YYYY',
  FULL_TIME = 'YYYY-MM-DD HH:mm:ss',
  VIEW_FULL_TIME = 'HH:mm:ss DD-MM-YYYY',
  EXPORT_DATE = 'DD/MM/YYYY',
  TIME = 'HH:mm:ss',
  // token expired time
  EXPIRED_TIME_CODE = 419,
  // login again
  LOGIN_TIMEOUT_CODE = 440,
  LIMIT_DEFAULT = 10,
  // url symbol
  COMMA = ',',
  STAND = '|',
  // clause where
  AND = 'and',
  OR = 'or',
  // operation where
  EQUAL = 'eq',
  NOT_EQUAL = 'neq',
  EQUAL_NUMBER = 'eqn',
  NOT_EQUAL_NUMBER = 'neqn',
  GREATER_THAN = 'gt',
  GREATER_THAN_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_EQUAL = 'lte',
  LIKE = 'like',
  ILIKE = 'ilike',
  ILIKE_START = 'ilike_start',
  ILIKE_END = 'ilike_end',
  IN = 'in',
  NOT_IN = 'notIn',
  IS = 'is',
  NOT = 'not',
  BETWEEN = 'between',
  NOT_BETWEEN = 'notBetween',
  CONTAINS = 'contains',
  ANY = 'any',
  SPLIT_PART_3 = 'split_part_3',
  //
  ARRAY_LOWER = 'array_lower',
  // url query
  ATTRIBUTES = 'attributes',
  LIMIT = 'limit',
  OFFSET = 'offset',
  SORT = 'sort',
  RELATION = 'relation',
  WHERE = 'where',
  GROUP = 'group',
  TRANSACTION = 'transaction',
  // order by
  ORDER_BY = 'order',
  SCOPE = 'scope',
  ASCENDING = 'ASC',
  DESCENDING = 'DESC',
  // raw query
  RAW = 'raw',

  //amount zero
  AMOUNT_HAWB_NO = 13,
  AMOUNT_BOX_NO = 8,

  //field name
  HAWB_FIELD = 'HAWB',
  HAWB_CLEARANCE_FIELD = 'HAWBClearance',
  BOX_FIELD = 'BOX',
  SHIPMENT_FIELD = 'SHIPMENT',
  V5_INVOICE = 'V5_INVOICE',
  QUANTITY_FIELD = 'quantity',
  CLIENT_CODE_FIELD = 'clientCode',
  HAWB_ITEMS = 'items',
  PHASE_FIELD = 'phase',
  STATION_ID_FIELD = 'stationId',
  ORDER_ID_FIELD = 'orderId',
  ORDER_NUMBER_FIELD = 'orderNumber',
  ORDER_NUMBER_CLIENT_FIELD = 'orderNumberClient',
  STATION_CREATED_FIELD = 'createdAtStationId',
  PRODUCT_ID_FIELD = 'productId',
  PRODUCT_NAME_FIELD = 'productName',
  CLASSIFY_FIELD = 'classify',
  CLIENT_ID_FIELD = 'clientId',
  CURRENCY_FIELD = 'currency',
  SUB_SERVICE_IDS_FIELD = 'subServiceIds',
  CANCELED_FIELD = 'isCanceled',
  DELETED_FIELD = 'isDeleted',
  // index
  INDEX_0 = 0,
  INDEX_1 = 1,
  INDEX_2 = 2,
  INDEX_4 = 4,
  INDEX_5 = 5,
  INDEX_3 = 3,
  INDEX_7 = 7,
  INDEX_9 = 9,

  INDEX_11 = 11,
  INDEX_12 = 12,
  INDEX_13 = 13,
  INDEX_14 = 14,
  INDEX_15 = 15,
  INDEX_16 = 16,
  INDEX_17 = 17,
  INDEX_18 = 18,
  INDEX_19 = 19,
  INDEX_20 = 20,
  INDEX_25 = 25,
  INDEX_30 = 30,
  INDEX_50 = 50,
  INDEX_40 = 40,
  INDEX_1000000 = 1000000,
  // key cache
  KEY_STATION_CODE = 'STATION_CODE',
  KEY_HUB_CODE = 'HUB_CODE',
  KEY_COUNTRY_CODE = 'COUNTRY_CODE',
  KEY_CLIENT_CODE = 'CLIENT_CODE',

  // method call order API
  POST_METHOD = 'POST',
  GET_METHOD = 'GET',
  DELETE_METHOD = 'DELETE',
  PUT_METHOD = 'PUT',
  // currency
  CURRENCY_VND = 'VND',

  // count
  COUNT = 'count',
  //
  ERROR_STATUS = 'error',

  //import constant
  IMPORTER_CODE = 9999999999998,
  IMPORTER_NAME = 'Cá nhân - Tổ chức không có mã số thuế',
  H11 = 'H11',
  H21 = 'H21',
  A11 = 'A11',
  MIC = 'MIC',
  MEC = 'MEC',
  IDA = 'IDA',
  EDA = 'EDA',
  COM = 'MẬU DỊCH',
  DOC = 'TÀI LIỆU',
  PAR = 'PAR',
  TYPE_A = 'A',
  TYPE_D = 'D',
  TRANSPORT_FLIGHT = 1,
  INDIVIDUAL_ORGANIZATION = 4,
  OTHER_CODE = 'ZZZ',
  OTHER_NAME = 'OTHER',
  PACKAGE_CODE = 'PK',
  CARGO_PIECE = 1,
  IMPORT_TAX_INCENTIVES = 'B01',
  VAT_TAX_INCENTIVES_VB901 = 'VB901',
  VAT_TAX_INCENTIVES_VB195 = 'VB195',
  VAT_TAX_INCENTIVES_VB205 = 'VB205',
  VAT_TAX_INCENTIVES_VB215 = 'VB215',
  PIECE = 'PCE',
  CIF = 'CIF',
  VN = 'VN',
  TAX_PAYER = 2,
  KHONGTT = 'KHONGTT',
  KC = 'KC',
  TTR = 'TTR',
  MO = 'MO',
  DDP = 'DDP',
  G = 'G',
  HOLD = 'Hold',
  _H11 = 'Bảng kê H11',
  _MIC = 'Bảng kê MIC',
  _H21 = 'Bảng kê H21',
  _MEC = 'Bảng kê MEC',

  // inspection kind
  ACCEPTED_CLEARANCE = 1,

  // CLEARANCE
  CLEARANCE_FOLDER_SEND = './public/clearance/send/',
  CLEARANCE_FOLDER_RECEIVE = './public/clearance/receive/',
  FOLDER_V5_SEND = './public/clearance/v5_send',
  FOLDER_V5_RECEIVE = './public/clearance/v5_receive/',
  FOLDER_INVOICE = './public/clearance/invoice/',
  FOLDER_MONITOR = './public/clearance/monitor/',
  FOLDER_REPORT_CUSTOM = './public/clearance/report/',

  CLEARANCE_FTP_FOLDER_SUB_SEND = 'CPN_ECUS5',
  CLEARANCE_FTP_FOLDER_SUB_RECEIVE = 'ECUS5_CPN',

  TMM_API_IAUTHORIZATION = 'Bearer iN73rn4l!5Bpg#s12c@2oS0@7MS-o1125',

  //
  NUMBER_ONE = 1,
  // telegram parse
  TELEGRAM_PARSE_HTML = 'HTML',
  //
  COMMIT = 'commit',
  // QUEUE NAME
  INBOUND_ONLY_CLEARANCE = 'inboundOnlyClearance',
  OUTBOUND_ONLY_CLEARANCE = 'outboundOnlyClearance',
  PUSH_WEBHOOK_PARTNER = 'pushWebHookPartner',
  UPDATE_LASTEST_MANIFEST_STATUS = 'updateLastestManifestStatus',
  RABBIT_SEND_V5 = 'sendFileV5',
  RABBIT_RECEIVE_V5 = 'receiveFileV5',

  // ACTION TMM/OMM
  CLEARANCED = 28,
  HOLD_CLEANCE = 202,
  UNHOLD_CLEANCE = 203,

  // TELEGRAM
  TELEGRAM_CLEARANCE_TOKEN = '**********************************************',
  TELEGRAM_CLEARANCE_CHATID = '-749600870',

  // MINIO UPLOAD
  BUCKET_ECUS_V5 = 'ecusv5',

  //
  OUTBOUND_SERVICE = 2,
  INBOUND_SERVICE = 3,
}

export default Configure;
