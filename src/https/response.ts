'use strict'

import httpStatus from 'http-status';
import { Response } from 'express';
import HttpException from '../https/exception';
import Data from '../https/data';
import Meta from '../https/meta';
import Message from '../emuns/messages';
import Duplicate from './duplicate';
import { IResponze } from './responze';
import EMessage from '../emuns/messages';

class HttpResponse {
  static sendMessage(res: Response, statusCode: number, message: HttpException): any {
    try {
      return res.status(statusCode).json(message);
    } catch (error) {
      this.exceptionError(res, (error as any).message);
    }
  }

  static sendValidate(res: Response, statusCode: number, data: any) {
    const responze: IResponze = {
      'status': false,
      'message': {
        'message' : EMessage.VALIDATE_ERROR,
        'error': data
      }
    }
    return res.status(statusCode).json(responze);
  }

  static sendData(res: Response, statusCode: number, data: Data): any {
    return res.status(statusCode).json(data);
  }

  static sendMetaData(res: Response, statusCode: number, data: any, meta: Meta): any {
    return res.status(statusCode).json({ data, meta });
  }

  static sendDuplicate(res: Response, statusCode: number, data: Duplicate): any {
    return res.status(statusCode).json(data);
  }

  static exceptionError (res: Response, message: string = Message.EXCEPTION_MESSAGE): any {
    return res.status(httpStatus.INTERNAL_SERVER_ERROR).json(new HttpException(false, message));
  }
}

export default HttpResponse;