'use strict';

import 'reflect-metadata';
import express, { Request, Response, NextFunction } from 'express';
import moment from 'moment';
import bodyParser from 'body-parser';
import cors from 'cors';
import httpStatus from 'http-status';
import Middlewares from './middleware/middlewares';
import routesV1 from './modules/v1/index.routes';
import HttpResponse from './https/response';
import HttpException from './https/exception';
import EConfigure from './emuns/configures';
import { config } from 'dotenv';
import RedisPromise from './util/redis.promise';
import { Database } from './database';
import { RabbitReceive } from './util/rabbit';
import { MongoDatabase } from './config/mongo.database';
import { CronJob } from 'cron';
import { Clearance } from './xmlClearance/clearance';
import dotenv from 'dotenv';
import { MongoDB } from 'winston-mongodb';
import winston from 'winston';
import expressWinston from 'express-winston';
import routesGateway from './modules/v1/index.gateway';
import nonAuthenRoutes from './modules/v1/index.nonAuthenRoutes';
import { AutoV5 } from './modules/v1/autoV5/autoV5.util';

dotenv.config();

class App {
  public app: express.Application;
  private path: string = '/api';
  private middlewares: Middlewares;
  constructor() {
    config();
    this.middlewares = new Middlewares();
    // run the express instance and store in app
    this.app = express();
    // middleware config
    this.middlewareConfig();
    // check connect database
    this.connectPostgre();
    // check connect redis
    this.connectRedis();
    // connect mongo database
    this.connectMongo();
    // initialize Rabbit receiver
    this.initializeRabbit();
    // initialize Cron
    this.initializeCron();
  }
  private middlewareConfig(): void {
    // enable cors by adding cors middleware
    this.app.use(cors());
    // support application/json type post data
    this.app.use(bodyParser.json({ limit: '50mb' }));
    // support application/x-www-form-urlencoded post data
    this.app.use(bodyParser.urlencoded({ limit: '50mb', extended: false }));

    // static router
    this.app.use(express.static('public'));
    // this.app.use(
    //   expressWinston.logger({
    //     transports: [
    //       new MongoDB({
    //         db: new MongoDatabase().uriConnect,
    //         options: { useNewUrlParser: true, useUnifiedTopology: true },
    //         collection: new MongoDatabase().collect,
    //         capped: false,
    //         metaKey: 'meta'
    //       })
    //     ],
    //     statusLevels: false,
    //     level: function (req, res) {
    //       let level = 'info';
    //       if (res.statusCode >= 400) {
    //         level = 'warn';
    //       }
    //       if (res.statusCode >= 500) {
    //         level = 'error';
    //       }
    //       return level;
    //     },
    //     dynamicMeta: function (req, res) {
    //       let data: any = {
    //         http: {
    //           method: req.method,
    //           statusCode: res.statusCode
    //         },
    //         agent: {
    //           request: req
    //         },
    //         user: req.headers.secret
    //       };
    //       return data;
    //     },
    //     requestFilter: function (req, propName) {
    //       return req[propName];
    //     },
    //     responseFilter: function (res, propName) {
    //       return res[propName];
    //     },
    //     format: winston.format.combine(winston.format.json()),
    //     meta: true,
    //     msg: `{{req.method}} {{req.url}}`,
    //     requestWhitelist: ['query', 'body'],
    //     responseWhitelist: ['body']
    //   })
    // );
    this.initializeControllers(this.path, nonAuthenRoutes.controllers);
    // check username/password header middleware
    this.app.use(this.path, this.middlewares.CheckToken);
    this.initializeMiddlewayControllers('/gateway', this.middlewares.CheckTokenGateway, routesGateway.controllers);
    // add routes v1
    this.initializeControllers(this.path, routesV1.controllers);
    // catch not found endpoint
    this.app.use(function (req: Request, res: Response, next: NextFunction) {
      return HttpResponse.sendMessage(res, httpStatus.NOT_FOUND, new HttpException(false, 'Not found endpoint API'));
    });
  }

  private initializeControllers(versionUrl: string, controllers: Array<any>) {
    controllers.forEach((controller) => {
      this.app.use(versionUrl, controller.router);
    });
  }

  private initializeMiddlewayControllers(versionUrl: string, middleware: any, controllers: Array<any>) {
    controllers.forEach((controller) => {
      this.app.use(versionUrl, middleware, controller.router);
    });
  }

  private async connectRedis() {
    try {
      await RedisPromise.initializeRedis();
      console.log('Connection has been established to REDIS successfully.');
    } catch (error) {
      console.log(' \t --- [%s] [error][app][connect_redis]: %s', moment().format(EConfigure.FULL_TIME), (error as any).message);
    }
  }

  private async connectPostgre() {
    try {
      await Database.authenticate();
      console.log('Connection has been established to DATABASE successfully.');
    } catch (error) {
      console.log(' \t --- [%s] [error][app][connect_database]: %o', moment().format(EConfigure.FULL_TIME), error);
    }
  }

  private async connectMongo() {
    new MongoDatabase().connect();
  }

  private async initializeRabbit() {
    if (process.env.IS_CRON === '1') {
      await new RabbitReceive().clearancePush();

      await new RabbitReceive().clearancePullTerminal();

      await new RabbitReceive().clearancePull();
      await new RabbitReceive().clearancePull();
      await new RabbitReceive().clearancePull();

      await new RabbitReceive().clearanceValidPush();

      await new RabbitReceive().updateTaxCodeNumber();
      await new RabbitReceive().updateMicTaxCodeNumber();

      await new RabbitReceive().sendFileV5();
      await new RabbitReceive().receiveFileV5();

      await new RabbitReceive().inboundOnlyClearance();
      await new RabbitReceive().outboundOnlyClearance();
    }
  }

  private initializeCron() {
    if (process.env.IS_CRON === '1') {
      let job2 = new CronJob('*/10 * * * * *', () => {
        console.log(' --- [%s] [CRON_ECUS_DOWNLOAD]', moment().format(EConfigure.FULL_TIME));
        new Clearance().getTerminal();
        new Clearance().getDownloadFile();
      });
      job2.start();

      let job = new CronJob('*/30 * * * * *', () => {
        console.log(' --- [%s] [CRON_V5_DOWNLOAD]', moment().format(EConfigure.FULL_TIME));
        new AutoV5().dowload();
      });
      job.start();
    }
  }
}

export default new App().app;
