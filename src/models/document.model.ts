'use strict';

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import moment from 'moment';
import EConfigure from '../emuns/configures';
import { IDA } from './ida.model';
import { DocumentStatusName } from '../modules/v1/autoV5/autoV5.enum';

export class Documents extends BaseModel {
  public static readonly ModelName: string = 'Documents';
  public static readonly TableName: string = 'clearance_documents';
  public static readonly DefaultScope: FindOptions = {
    attributes: {
      exclude: ['deletedAt', 'isDeleted']
    }
  };

  public id!: number;
  public HAWB!: string;
  public HAWBClearance!: string;

  public documentTypeId!: number;
  public url!: string;

  public SoTN!: string;
  public NgayTN!: string;

  public sendTimes!: string;
  public successTimes!: number;
  public errorTimes!: number;

  public messageError!: string;
  public statusId!: number;

  public statusName!: number;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: { type: DataTypes.INTEGER, primaryKey: true, unique: true, autoIncrement: true, autoIncrementIdentity: true },
        HAWB: { type: DataTypes.STRING },
        HAWBClearance: { type: DataTypes.STRING },

        documentTypeId: { type: DataTypes.SMALLINT },

        url: { type: DataTypes.STRING },

        SoTN: { type: DataTypes.STRING },
        NgayTN: { type: DataTypes.DATEONLY },

        sendTimes: { type: DataTypes.SMALLINT, defaultValue: 0 },
        successTimes: { type: DataTypes.SMALLINT, defaultValue: 0 },
        errorTimes: { type: DataTypes.SMALLINT, defaultValue: 0 },

        messageError: { type: DataTypes.STRING },

        statusId: DataTypes.SMALLINT,
        statusName: {
          type: DataTypes.VIRTUAL,
          get() {
            let statusId = this.getDataValue('statusId');
            return DocumentStatusName.get(statusId) || '';
          }
        },

        createdAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('createdAt')) {
              return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('createdAt');
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('updatedAt')) {
              return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('updatedAt');
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('deletedAt')) {
              return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('deletedAt');
          }
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        timestamps: true,
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        scopes: {}
      }
    );
  }

  public static setAssociations() {
    // place to set model associations
    Documents.hasMany(IDA, { as: 'documents', foreignKey: 'HAWB', sourceKey: 'HAWB' });
  }
}
