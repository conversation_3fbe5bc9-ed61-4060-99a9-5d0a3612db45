'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IExportDetailValidate {
  HSCode: string;
  itemName: string;
  itemNameVN: string;
  url: string;
  currencyCode: string;  // currency
  quantity: number;
  invoiceValue: number;
  invoiceUnitPrice: number;
  productId: string;
  weight: number;
  unitOfMass: string;
}

export interface IExportDetailCreate {
  id?: number;
  HAWB?: string;
  HSCode?: string;
  itemName?: string;
  itemNameVN?: string;
  url?: string;
  unitPriceCurrencyCode?: string;
  invoiceUnitPrice?: number;
  invoiceValue?: number;
  placeOfOriginCode?: string;
  quantity1?: number;
  quantityUnitCode1?: string;
  valueClearanceVND?: number;
  priceVND ?: number;
  productId?: string;
  weightKG?: number;
  priceQuantityUnit?: string;
  position?: number;
  quantity2?: any;
  quantityUnitCode2?: any;
}

export interface IExportDetail {
  id?: number
  HAWB?: string;
  HSCode?: string;
  itemName?: string;
  itemNameVN?: string;
  placeOfOriginCode?: string;
  quantity1?: number;
  quantityUnitCode1?: string;
  unitPriceCurrencyCode?: string;
  invoiceUnitPrice?: number;
  invoiceValue?: number;
  valueClearanceVND?: number;
  priceVND? : number;
  url?: string;
  productId?: string;
  weightKG?: number;

  priceQuantityUnit?: string;
  customsValueCurrencyCode?: string;
  taxValueM?: number;
  customsValueUnitPrice?: number;
  customsUnitPriceCurrency?: string;
  quantityUnitOfCustoms?: string;
  customsValueS?: number;
  exportTaxAmount?: number;
  exportTaxAmountCurrencyCode?: string;
  emportTaxRate?: string;
  otherLawCode?: string;

  exportTax?: number
  exportTaxCode?: string;
  exportTaxFree?: string;
  exportTaxFreePrice?: number;
  exportPrice?: number
  priceAfterImportTax?: number;
  priceBeforeVATTax?: number;
  VATTax?: number;
  VATTaxCode?: string;
  VATTaxFree?: string;
  VATTaxFreePrice?: number;
  VATPrice?: number;
  environmentTax?: number;
  environmentTaxCode?: string;
  environmentTaxFree?: string;
  environmentTaxFreePrice?: number;
  environmentPrice?: number;
  specialConsumptionTax?: number;
  specialConsumptionTaxCode?: string;
  specialConsumptionTaxFree?: string;
  specialConsumptionTaxFreePrice?: number;
  specialConsumptionPrice?: number;

  quantity2?: number;
  quantityUnitCode2?: string;
  position?: number;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;

}

export class ExportDetail extends BaseModel implements IExportDetail {
  public static readonly ModelName: string = 'ExportDetail';
  public static readonly TableName: string = 'clearance_export_details';
  public static readonly DefaultScope: FindOptions = {};

  public id?: number
  public HAWB?: string;
  public HSCode?: string;
  public itemName?: string;
  public itemNameVN?: string;
  public placeOfOriginCode?: string;
  public quantity1?: number;
  public quantityUnitCode1?: string;
  public unitPriceCurrencyCode?: string;
  public invoiceUnitPrice?: number;
  public invoiceValue?: number;
  public valueClearanceVND?: number;
  public priceVND? : number;
  public url?: string;
  public productId?: string;
  public weightKG?: number;

  public priceQuantityUnit?: string;
  public customsValueCurrencyCode?: string;
  public taxValueM?: number;
  public customsValueUnitPrice?: number;
  public customsUnitPriceCurrency?: string;
  public quantityUnitOfCustoms?: string;
  public customsValueS?: number;
  public exportTaxAmount?: number;
  public exportTaxAmountCurrencyCode?: string;
  public emportTaxRate?: string;
  public otherLawCode?: string;

  public exportTax?: number
  public exportTaxCode?: string;
  public exportTaxFree?: string;
  public exportTaxFreePrice?: number;
  public exportPrice?: number
  public priceAfterImportTax?: number;
  public priceBeforeVATTax?: number;
  public VATTax?: number;
  public VATTaxCode?: string;
  public VATTaxFree?: string;
  public VATTaxFreePrice?: number;
  public VATPrice?: number;
  public environmentTax?: number;
  public environmentTaxCode?: string;
  public environmentTaxFree?: string;
  public environmentTaxFreePrice?: number;
  public environmentPrice?: number;
  public specialConsumptionTax?: number;
  public specialConsumptionTaxCode?: string;
  public specialConsumptionTaxFree?: string;
  public specialConsumptionTaxFreePrice?: number;
  public specialConsumptionPrice?: number;

  public quantity2!: number;
  public quantityUnitCode2!: string;
  public position?: number;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          unique : true,
          primaryKey: true,
          autoIncrement: true
        },
        HAWB: {
          type: DataTypes.STRING(30),
          allowNull : true
        },
        HSCode: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        itemName: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        itemNameVN: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        placeOfOriginCode: {
          type: DataTypes.STRING(6),
          allowNull : true
        },
        quantity1: {
          type: DataTypes.SMALLINT,
          allowNull : true
        },
        quantityUnitCode1: {
          type: DataTypes.STRING(6),
          allowNull : true
        },
        quantity2: {
          type: DataTypes.DECIMAL(20, 6),
          allowNull: true,
          defaultValue: 0
        },
        quantityUnitCode2: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        unitPriceCurrencyCode: {
          type: DataTypes.STRING(4),
          allowNull : true
        },
        priceQuantityUnit: {
          type: DataTypes.STRING(6),
          allowNull : true
        },
        customsValueS: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull : true
        },
        customsValueCurrencyCode: {
          type: DataTypes.STRING(6),
          allowNull : true
        },
        taxValueM: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull : true
        },
        customsValueUnitPrice: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull : true
        },
        customsUnitPriceCurrency: {
          type: DataTypes.STRING(6),
          allowNull : true
        },
        quantityUnitOfCustoms: {
          type: DataTypes.STRING(6),
          allowNull : true
        },
        emportTaxRate: {
          type: DataTypes.STRING(10),
          allowNull : true
        },
        exportTaxAmount: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull : true
        },
        exportTaxAmountCurrencyCode: {
          type: DataTypes.STRING(6),
          allowNull : true
        },
  
        invoiceUnitPrice: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull : true
        },
        invoiceValue: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull : true
        },
        valueClearanceVND: { // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull : true
        },
        priceVND: { // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull : true
        },
        url: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        productId: {
          type: DataTypes.STRING(255),
          allowNull : true,
        },
        weightKG: {
          type: DataTypes.DECIMAL(12, 4),
          allowNull : true,
        },
        otherLawCode: {
          type: DataTypes.STRING(6),
          allowNull : true
        },
        exportTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        exportTaxCode: {
          type: DataTypes.STRING(20),
          allowNull : true
        },
        exportTaxFree: {
          type: DataTypes.STRING(20),
          allowNull : true
        },
        exportTaxFreePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull : true,
          defaultValue: 0
        },
        exportPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        priceAfterImportTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull : true,
          defaultValue: 0
        },
        priceBeforeVATTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull : true,
          defaultValue: 0
        },
        VATTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        VATTaxCode: {
          type: DataTypes.STRING(20),
          allowNull : true
        },
        VATTaxFree: {
          type: DataTypes.STRING(20),
          allowNull : true
        },
        VATTaxFreePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull : true,
          defaultValue: 0
        },
        VATPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        environmentTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        environmentTaxCode: {
          type: DataTypes.STRING(20),
          allowNull : true
        },
        environmentTaxFree: {
          type: DataTypes.STRING(20),
          allowNull : true
        },
        environmentTaxFreePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull : true,
          defaultValue: 0
        },
        environmentPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        specialConsumptionTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        specialConsumptionTaxCode: {
          type: DataTypes.STRING(20),
          allowNull : true
        },
        specialConsumptionTaxFree: {
          type: DataTypes.STRING(20),
          allowNull : true
        },
        specialConsumptionTaxFreePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull : true,
          defaultValue: 0
        },
        specialConsumptionPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        position: {
          type: DataTypes.INTEGER,
          allowNull : true,
        },
        // create information
        createdAt: {
          type: DataTypes.DATE,
        },
        updatedAt: {
          type: DataTypes.DATE,
        },
        deletedAt: {
          type: DataTypes.DATE,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

