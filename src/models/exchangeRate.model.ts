'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IExchangeRate {
  id?: number
  currency?: string;
  effectiveDate?: string;
  valueClearance?: number;
  valueMarket?: number;
}

export class ExchangeRate extends BaseModel implements IExchangeRate {
  public static readonly ModelName: string = 'ExchangeRate';
  public static readonly TableName: string = 'exchange_rates';
  public static readonly DefaultScope: FindOptions = {};

  public id?: number
  public currency?: string;
  public effectiveDate?: string;
  public valueClearance?: number;
  public valueMarket?: number;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      currency: {
        type: DataTypes.STRING(5),
        allowNull : false,
      },
      effectiveDate: {
        type: DataTypes.DATEONLY,
        allowNull : true,
      },
      valueClearance: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull : true,
        get() {      
          const that: any = this;
          if(that.getDataValue('valueClearance')!== null){
            return parseFloat(that.getDataValue('valueClearance'));
          }
        }
      },
      valueMarket: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull : true,
        get() {      
          const that: any = this;
          if(that.getDataValue('valueMarket')!== null){
            return parseFloat(that.getDataValue('valueMarket'));
          }
        }
      },
    },
    {
      sequelize: sequelize,
      tableName: this.TableName,
      name: {
        singular: this.ModelName,
      },
      defaultScope: this.DefaultScope,
      timestamps: false,
      comment: 'Model for the public accessible data of an import',
    },
  );}

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

