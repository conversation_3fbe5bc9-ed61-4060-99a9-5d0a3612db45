'use strict';

import { Model, DataTypes, Sequelize, FindOptions, ModelCtor } from 'sequelize';
import moment from 'moment';
import { BaseModel } from './base.model';
import { Box, Carrier, EDA, Hub, IDA, MEC, Partner, Station } from './index.model';
import { MIC } from './mic.model';

export interface IShipment {
  id?: number;
  name?: string;
  weightTotal?: string;
  weightCharge?: string;
  MAWB?: string;
  dateFrom?: string;
  dateTo?: string;
  flightCode?: string;
  carrierId?: number;
  partnerId?: number;
  stationFrom?: number;
  stationTo?: number;
  hubTo?: number;
  portOfLoading?: string;
  portOfDischarge?: string;
  description?: string;
  isBoxed?: boolean;
  isExternal?: boolean;
  refNo?: string;
  totalHAWB?: number;
  HAWBs?: any;
  isPOD?: boolean;
  boxIds?: any;
  isAccepted17Track?: boolean;
  dataAccept17Track?: any;
  dataError17Track?: any;
  dateAccepted17Track?: string;
  meanOfTransportationCode?: number;
  totalOrder?: number;
  orders?: string;
  isSendDeclaration?: boolean;
  isNWS?: boolean;
  dateNWS?: string;
  clientName?: string;
  isUpdateMaster?: boolean;
  nameFrom?: string;
  addressFrom?: string;
  telephoneFrom?: string;
  faxFrom?: string;
  emailFrom?: string;
  nameTo?: string;
  addressTo?: string;
  telephoneTo?: string;
  faxTo?: string;
  emailTo?: string;
  phase?: number;
  //
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export class Shipment extends BaseModel implements IShipment {
  public static readonly ModelName: string = 'Shipment';
  public static readonly TableName: string = 'shipments';
  public static readonly DefaultScope: FindOptions = {};

  id?: number;
  name?: string;
  weightTotal?: string;
  weightCharge?: string;
  MAWB?: string;
  dateFrom?: string;
  dateTo?: string;
  flightCode?: string;
  carrierId?: number;
  partnerId?: number;
  stationFrom?: number;
  stationTo?: number;
  hubTo?: number;
  portOfLoading?: string;
  portOfDischarge?: string;
  description?: string;
  isBoxed?: boolean;
  isExternal?: boolean;
  refNo?: string;
  totalHAWB?: number;
  HAWBs?: any;
  isPOD?: boolean;
  boxIds?: any;
  isAccepted17Track?: boolean;
  dataAccept17Track?: any;
  dataError17Track?: any;
  dateAccepted17Track?: string;
  meanOfTransportationCode?: number;
  totalOrder?: number;
  orders?: string;
  isSendDeclaration?: boolean;
  isNWS?: boolean;
  dateNWS?: string;
  clientName?: string;
  isUpdateMaster?: boolean;
  nameFrom?: string;
  addressFrom?: string;
  telephoneFrom?: string;
  faxFrom?: string;
  emailFrom?: string;
  nameTo?: string;
  addressTo?: string;
  telephoneTo?: string;
  faxTo?: string;
  emailTo?: string;
  phase?: number;
  //
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: DataTypes.STRING(33),
          unique: true,
          allowNull: false
        },
        weightTotal: {
          //nặng
          type: DataTypes.STRING(11),
          allowNull: true
        },
        weightCharge: {
          // cân nặng tính phí
          type: DataTypes.STRING(11),
          allowNull: true
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        boxIds: {
          type: DataTypes.ARRAY(DataTypes.INTEGER),
          allowNull: true
        },
        refNo: {
          // khach hang nhap
          type: DataTypes.STRING(50),
          allowNull: true
        },
        dateFrom: {
          type: DataTypes.DATEONLY,
          allowNull: false
        },
        dateTo: {
          type: DataTypes.DATEONLY,
          allowNull: false
        },
        flightCode: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        carrierId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        partnerId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        externalTransitId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        stationFrom: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        stationTo: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        hubTo: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        portOfLoading: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        portOfDischarge: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        totalHAWB: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        HAWBs: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        isPOD: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: true
        },
        meanOfTransportationCode: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        isUpdateMaster: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        nameFrom: {
          type: DataTypes.STRING(2000),
          allowNull: true
        },
        addressFrom: {
          type: DataTypes.STRING(2000),
          allowNull: true
        },
        telephoneFrom: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        faxFrom: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        emailFrom: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        nameTo: {
          type: DataTypes.STRING(2000),
          allowNull: true
        },
        addressTo: {
          type: DataTypes.STRING(2000),
          allowNull: true
        },
        telephoneTo: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        faxTo: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        emailTo: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        phase: {
          type: DataTypes.INTEGER,
          allowNull: true,
          defaultValue: 0
        },
        // create information
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        createdAt: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE
        },
        deletedAt: {
          type: DataTypes.DATE
        },
        isBoxed: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isExternal: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isAccepted17Track: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        dataAccept17Track: {
          type: DataTypes.JSON,
          allowNull: true
        },
        dataError17Track: {
          type: DataTypes.JSON,
          allowNull: true
        },
        dateAccepted17Track: {
          type: DataTypes.DATE,
          allowNull: true
        },
        totalOrder: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        orders: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        isSendDeclaration: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isNWS: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        dateNWS: {
          type: DataTypes.DATE
        },
        clientName: {
          type: DataTypes.STRING(255),
          allowNull: true
        }
      },
      {
        sequelize: sequelize,
        timestamps: false,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          carrier: {
            include: [
              { 
                model: Carrier, 
                as: 'carrier'
              }
            ]
          },
          stationOriginal: {
            include: [
              { 
                model: Station, 
                as: 'stationOriginal'
              }
            ]
          },
          stationDestination: {
            include: [
              { 
                model: Station, 
                as: 'stationDestination'
              }
            ]
          },
          hubDestination: {
            include: [
              { 
                model: Hub, 
                as: 'hubDestination'
              }
            ]
          },
          partner: {
            include: [
              { 
                model: Partner, 
                as: 'partner'
              }
            ]
          },
          boxes: {
            include: [
              { 
                model: Box, 
                as: 'boxes'
              }
            ]
          }
        }
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
    Shipment.belongsTo(Carrier, { as: 'carrier', foreignKey: 'carrierId' });
    Shipment.belongsTo(Station, { as: 'stationDestination', foreignKey: 'stationTo' });
    Shipment.belongsTo(Station, { as: 'stationOriginal', foreignKey: 'stationFrom' });
    Shipment.belongsTo(Hub, { as: 'hubDestination', foreignKey: 'hubTo' });
    Shipment.belongsTo(Partner, { as: 'partner', foreignKey: 'partnerId' });
    Shipment.hasMany(Box, { as: 'boxes', foreignKey: 'shipmentId' });
  }
}
