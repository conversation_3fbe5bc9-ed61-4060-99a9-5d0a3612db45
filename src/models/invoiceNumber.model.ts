'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IInvoiceNumber {
  id: number;
  numberCode?: string;
  hubId?: number;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class InvoiceNumber extends BaseModel implements IInvoiceNumber {
  public static readonly ModelName: string = 'InvoiceNumber';
  public static readonly TableName: string = 'clearance_invoice_numbers';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public numberCode!: string;
  public hubId!: number;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          unique : true,
          primaryKey: true,
          autoIncrement: true
        },
        numberCode: {
          type: DataTypes.STRING(6),
          allowNull : false
        },
        hubId: {
          type: DataTypes.INTEGER,
          allowNull : false
        },
        // create information
        createdAt: {
          type: DataTypes.DATE,
        },
        updatedAt: {
          type: DataTypes.DATE,
        },
        deletedAt: {
          type: DataTypes.DATE,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

