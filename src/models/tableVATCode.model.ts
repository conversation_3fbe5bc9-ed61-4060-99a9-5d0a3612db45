'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';


export interface IVatCode {
  code?: string;
  name?: string;
  percentValue?: number;
  position?: number;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  isDeleted?: string;
}

export class VatCode extends BaseModel implements IVatCode {
  public static readonly ModelName: string = 'VatCode';
  public static readonly TableName: string = 'clearance_vat_codes';
  public static readonly DefaultScope: FindOptions = {
    'attributes': {
      'exclude': ['deletedAt', 'isDeleted']
    }
  };

  public code!: string;
  public name!: string;
  public percentValue!: number;
  public position!: number;

  public createdAt!: string;
  public updatedAt!: string;
  public deletedAt!: string;
  public isDeleted!: string;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      //header
      code: {
        type: DataTypes.STRING(50),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      name: {
        type: DataTypes.TEXT,
        allowNull : true
      },
      percentValue: {
        type: DataTypes.DECIMAL(12, 4),
        allowNull : true
      },
      position: {
        type: DataTypes.INTEGER,
        allowNull : true
      },
      createdAt: {
        type: DataTypes.DATE,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE,
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      sequelize: sequelize,
      tableName: this.TableName,
      name: {
        singular: this.ModelName,
      },
      defaultScope: this.DefaultScope,
      comment: 'Model for the public accessible data of an import',
    },
  );}

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

