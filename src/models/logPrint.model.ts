'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import moment from 'moment';
import EConfigure from '../emuns/configures';
import { Employee } from './employee.model';
import { User } from './user.model';

export interface ILogPrint {
  id?: number
  HAWB?: string;
  employeeId?: number;
  isInspectionKindPrint?: boolean;
  isClearanceDeclarationPrint?: boolean;
  isClearancedPrint?: boolean;
  createdAt?:string;
  updatedAt?:string;
  deletedAt?:string;
  isDeleted?:boolean;
  employee?: Employee;
}

export class LogPrint extends BaseModel implements ILogPrint {
  public static readonly ModelName: string = 'LogPrint';
  public static readonly TableName: string = 'clearance_log_print';
  public static readonly DefaultScope: FindOptions = {};

  public id?: number
  public HAWB?: string;
  public employeeId?: number;
  public isInspectionKindPrint?: boolean;
  public isClearanceDeclarationPrint?: boolean;
  public isClearancedPrint?: boolean;
  public createdAt?:string;
  public updatedAt?:string;
  public deletedAt?:string;
  public isDeleted?:boolean;
  public employee?: Employee;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      id: {
        type: DataTypes.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      HAWB: {
        type: DataTypes.STRING(50),
        allowNull : false
      },
      employeeId: {
        type: DataTypes.INTEGER,
        allowNull : false
      },
      isInspectionKindPrint: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isClearanceDeclarationPrint: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isClearancedPrint: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        get() {
          const that: any = this;
          if(that.getDataValue('createdAt')){
            return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
          }
        }
      },
      updatedAt: {
        type: DataTypes.DATE,
        get() {
          const that: any = this;
          if(that.getDataValue('updatedAt')){
            return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
          }
        }
      },
      deletedAt: {
        type: DataTypes.DATE,
        get() {
          const that: any = this;
          if(that.getDataValue('deletedAt')){
            return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
          }
        }
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      sequelize: sequelize,
      tableName: this.TableName,
      name: {
        singular: this.ModelName,
      },
      defaultScope: this.DefaultScope,
      comment: 'Model for the public accessible data of an import',
      scopes: {
        employee: {
          include: [
            { 
              model: Employee, 
              as: 'employee',
              include: [
                {
                  model: User,
                  as: 'user',
                  attributes: {
                    exclude: ['password', 'salt'],
                  },
                },
              ]
            }
          ]
        },
      }
    },
  );}

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
    LogPrint.belongsTo(Employee, {as: 'employee' , foreignKey: 'employeeId', targetKey: 'id'})
  }
}

