'use strict';

import moment from 'moment';
import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { Employee, Hold, Station, User } from './index.model';
import EConfigure from '../emuns/configures';
import { ActionName } from '../emuns/action';

export interface ICreateAction {
  HAWB: string;
  action: number;
  classify: string;
  currentClassify: string;
  data: any;
  newData: any;
  stationId: number;
}

export interface IUpdateAction {
  HAWB: string;
  action: number;
  classify: string;
  currentClassify: string;
  data: any;
  newData: any;
  employeeId: number;
}

export interface IChangeClassify {
  HAWB: string;
  action: number;
  classify: string;
  employeeId: number;
  currentClassify: string;
  changeClassify: string;
}

export interface IMessageTax {
  HAWB: string;
  action: number;
  employeeId: number;
  isMessageTax: boolean;
  
}

export interface IMaster {
  HAWB: string;
  action: number;
  MAWB: string;
  hubId: any;
}

export interface IReset {
  HAWB: string;
  action: number;
  classify: string;
  currentClassify: string;
  employeeId: number;
  data: any;
  newData: any;
  
}

export interface IRegister {
  HAWB: string;
  action: number;
  classify: string;
  currentClassify: string;
  employeeId: number;
  typeAction: string;
}

export interface IUpdateNameVN {
  HAWB: string;
  action: number;
  detailId: number;
  employeeId: number;
  newData: any;
}

export interface IExchangeRateTransaction {
  HAWB: string;
  action: number;
  newData?: any;
  employeeId: number;
}

export interface IHoldTransaction {
  HAWB: string;
  action: number;
  holdId?: any;
  employeeId: number;
}

export interface INoteHold {
  HAWB: string;
  action: number;
  employeeId: number;
  noteHold: any;
  
}

export interface IWarehouseTransaction {
  HAWB: string;
  action: number;
  employeeId: number;
  warehouseAddress?: string;
}

export interface IReturnCargo {
  HAWB: string;
  action: number;
  employeeId: number;
  
}

export interface IAssignCargo {
  HAWB: string;
  action: number;
  employeeId: number;
}

export interface IImportTransaction {
  id?: number;
  HAWB?: string;
  detailId?: number;
  action?: number;
  MAWB?: string;
  declarationNo?: string;
  inspectionKindClassification?: number;
  employeeId?: number;
  isError?: boolean;
  isECUSError?: boolean;
  isMessageTax?: boolean;
  messageErrorName?: string;
  messageError?: string;
  typeAction?: string;
  classify?: string;
  data?: any;
  newData?: any;
  currentClassify?: string;
  changeClassify?: string;
  holdId?: number;
  warehouseAddress?: string;
  stationId?: number;
  hubId?: number;
  phase_name?: any;
  noteHold?: string;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class ImportTransaction extends BaseModel implements IImportTransaction {
  public static readonly ModelName: string = 'ImportTransaction';
  public static readonly TableName: string = 'clearance_import_transactions';
  public static readonly DefaultScope: FindOptions = {};

  public id?: number;
  public HAWB?: string;
  public detailId?: number;
  public MAWB?: string;
  public declarationNo?: string;
  public inspectionKindClassification?: number;
  public action?: number;
  public employeeId?: number;
  public isError?: boolean;
  public isECUSError?: boolean;
  public isMessageTax?: boolean;
  public messageErrorName?: string;
  public messageError?: string;
  public typeAction?: string;
  public classify?: string;
  public data?: any;
  public newData?: any;
  public currentClassify?: string;
  public changeClassify?: string;
  public holdId?: number;
  public warehouseAddress?: string;
  public stationId?: number;
  public hubId?: number;
  public phase_name?: any;
  public noteHold?: string;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          unique : true,
          primaryKey: true,
          autoIncrement: true
        },
        HAWB: {
          type: DataTypes.STRING(30),
          allowNull : false
        },
        detailId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        declarationNo: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        inspectionKindClassification: { // Phan luong
          type: DataTypes.SMALLINT,
          allowNull : true
        },
        action: {
          type: DataTypes.SMALLINT,
          allowNull : true
        },
        employeeId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        isError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        isECUSError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        isMessageTax: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        messageErrorName: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        messageError: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        typeAction: {
          type: DataTypes.STRING(10),
          allowNull : true
        },
        classify: {
          type: DataTypes.STRING(10),
          allowNull : true
        },
        data: {
          type: DataTypes.JSON,
          allowNull : true
        },
        newData: {
          type: DataTypes.JSON,
          allowNull : true
        },
        currentClassify: {
          type: DataTypes.STRING(10),
          allowNull : true,
        },
        changeClassify: {
          type: DataTypes.STRING(10),
          allowNull : true,
        },
        holdId: {
          type: DataTypes.INTEGER,
          allowNull : true,
        },
        warehouseAddress: {
          type: DataTypes.STRING(255),
          allowNull : true,
        },
        stationId: {
          type: DataTypes.INTEGER,
          allowNull : true,
        },
        hubId: {
          type: DataTypes.INTEGER,
          allowNull : true,
        },
        action_name: {
          type: DataTypes.VIRTUAL,
          allowNull : true,
          get() {      
            const that: any = this;
            if(that.getDataValue('action')!== null){
              return ActionName.get(that.getDataValue('action'));
            } 
            return that.getDataValue('action');
          }
        },
        noteHold: {
          type: DataTypes.TEXT,
          allowNull : true,
        },
        // create information
        createdAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if(that.getDataValue('createdAt')){
              return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('createdAt');
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if(that.getDataValue('updatedAt')){
              return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('updatedAt');
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if(that.getDataValue('deletedAt')){
              return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('deletedAt');
          }
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          hold: {
            include: [
              {
                model: Hold,
                as: 'hold',
                attributes: ['name'],
              }
            ]
          },
          station: {
            include: [
              { 
                model: Station, 
                as: 'station'
              }
            ]
          },
          employee: {
            include: [
              { 
                model: Employee, 
                as: 'employee',
                include: [{
                  model: User, 
                  as: 'user',
                  attributes: ['firstname', 'lastname']
                }]
              }
            ]
          },
        }
      },
      
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
    ImportTransaction.belongsTo(Hold, { as: 'hold', foreignKey: 'holdId' });
    ImportTransaction.belongsTo(Station, { as: 'station', foreignKey: 'stationId' });
    ImportTransaction.belongsTo(Employee, { as: 'employee', foreignKey: 'employeeId' });
  }
}
