'use strict'

import { DataTypes, FindOptions, Model, Model<PERSON><PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IStation {
  id: number;
  name: string;
  nameCompany: string;
  logo: string;
  code: string;
  address: string;
  email: string;
  fax: string;
  phone: string;
  countryId: number;
  website: string;
  currency: string
  isInternal: boolean;
  unitOfLength: string;
  unitOfMass: string;
  position: number;
  serviceId: any;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class Station extends BaseModel implements IStation {
  public static readonly ModelName: string = 'Station';
  public static readonly TableName: string = 'stations';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public name!: string;
  public nameCompany!: string;
  public logo!: string;
  public code!: string;
  public address!: string;
  public email!: string;
  public fax!: string;
  public phone!: string;
  public countryId!: number;
  public website!: string;
  public currency!: string
  public isInternal!: boolean;
  public unitOfLength!: string;
  public unitOfMass!: string;
  public position!: number;
  public serviceId!: any;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          unique: true,
        },
        nameCompany: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        logo: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        code: {
          type: DataTypes.STRING,
          unique: true,
        },
        address: {
          type: DataTypes.STRING,
          allowNull : true
        },
        email: {
          type: DataTypes.SMALLINT,
          allowNull : true
        },
        fax: {
          type: DataTypes.STRING,
          allowNull : true
        },
        phone: {
          type: DataTypes.STRING,
          allowNull : true
        },
        countryId: {
          type: DataTypes.INTEGER,
          allowNull : true,
        },
        website: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        currency: {
          type: DataTypes.STRING(5),
          allowNull : true
        },
        isInternal: {
          type: DataTypes.BOOLEAN,
          defaultValue : true
        },
        unitOfLength: {
          type: DataTypes.STRING(5),
          allowNull : true,
        },
        unitOfMass: {
          type: DataTypes.STRING(5),
          allowNull : true,
        },
        position: {
          type: DataTypes.INTEGER,
          allowNull : true,
        },
        serviceId: {
          type: DataTypes.ARRAY(DataTypes.INTEGER),
          allowNull : true
        },
        //
        createdAt: {
          type: DataTypes.DATE,
        },
        updatedAt: {
          type: DataTypes.DATE,
        },
        deletedAt: {
          type: DataTypes.DATE,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

