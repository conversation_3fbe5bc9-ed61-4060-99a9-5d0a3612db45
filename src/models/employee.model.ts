'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { User } from './user.model';

export interface IEmployee {
  id?: number
  no?: string;
  identityCard?: string;
  birthDate?: string;
  userId?: number;
  roleId?: number;
  odooTeamId?: number;
  odooStationId?: number;
  companyId?: number;
  user?: User
}

export class Employee extends BaseModel implements IEmployee {
  public static readonly ModelName: string = 'Employee';
  public static readonly TableName: string = 'employees';
  public static readonly DefaultScope: FindOptions = {};

  public id?: number
  public no?: string;
  public identityCard?: string;
  public birthDate?: string;
  public userId?: number;
  public roleId?: number;
  public odooTeamId?: number;
  public odooStationId?: number;
  public companyId?: number;
  public user?: User

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      no: {
        type: DataTypes.STRING(15),
        allowNull : true
      },
      identityCard: {
        type: DataTypes.STRING(12),
        allowNull : true
      },
      birthDate: {
        type: DataTypes.DATEONLY,
        allowNull : true
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull : true,
        references: {
          model: 'users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      roleId: {
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      odooTeamId: {
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      odooStationId: {
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      companyId: {
        type: DataTypes.INTEGER,
        allowNull : true,
      },
    },
    {
      sequelize: sequelize,
      timestamps: false,
      tableName: this.TableName,
      name: {
        singular: this.ModelName,
      },
      defaultScope: this.DefaultScope,
      comment: 'Model for the public accessible data of an import',
      scopes: {
        user: {
          include: [
            { 
              model: User, 
              as: 'user',
              attributes: {
                exclude: ['password', 'salt'],
              },
            }
          ]
        },
      }
    },
  );}

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
    Employee.belongsTo(User, {as: 'user' , foreignKey: 'userId', targetKey: 'id'})
  }
}