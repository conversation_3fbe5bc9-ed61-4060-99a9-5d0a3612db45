'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface ICarrier {
  id?: number;
  name?: string;
  code?: string;
  description?: string;
  isNotSendClearance?: boolean;
  isActivated?: boolean;
  isDeleted?: boolean;
  carrierCode17Track?: string;
  isOutBoundTracking?: boolean;
  readonly createdAt?: Date;
  readonly updatedAt?: Date;
  readonly deletedAt?: Date;
}

export class Carrier extends BaseModel implements ICarrier {
  public static readonly ModelName: string = 'Carrier';
  public static readonly TableName: string = 'carriers';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public name!: string;
  public code!: string;
  public description!: string;
  public isNotSendClearance!: boolean;
  public isActivated!: boolean;
  public isDeleted!: boolean;
  public carrierCode17Track!: string;
  public isOutBoundTracking!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public readonly deletedAt!: Date;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true
        },
        name: {
          type: DataTypes.STRING,
          unique: true
        },
        code: {
          type: DataTypes.STRING
        },
        carrierCode17Track: {
          type: DataTypes.STRING(10)
        },
        isOutBoundTracking: {
          type: DataTypes.BOOLEAN
        },
        isNotSendClearance: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updatedAt: {
          type: DataTypes.DATE,
        },
        deletedAt: {
          type: DataTypes.DATE,
        }
      },
      {
        sequelize: sequelize,
        timestamps: false,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}