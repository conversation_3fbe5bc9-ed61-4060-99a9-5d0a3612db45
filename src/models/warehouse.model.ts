'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { ITerminal, Terminal } from './terminal.model';
import { Company } from './company.model';
import { Station } from './station.model';

export interface IWarehouse {
  id?: number;
  companyId?: number;
  stationId?: number;
  code?: string;
  customsOffice?: string;
  customsSubSection?: string;
  terminalIds?: ITerminal[];
  feePrice?: number;
  isActivated?: boolean;
  loadingLocationCode?: string;
  unloadingPortCode?: string;
  agencyCode?: string;
  individualOrganization?: number;
  name?: string;
  hubId?: number;
  orderTypeId?: number;
  kindleCode?: string;
  isGenerateInvoice?: boolean;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class Warehouse extends BaseModel implements IWarehouse {
  public static readonly ModelName: string = 'Warehouse';
  public static readonly TableName: string = 'clearance_warehouses';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public companyId!: number;
  public stationId!: number;
  public code!: string;
  public customsOffice!: string;
  public customsSubSection!: string;
  public terminalIds!: ITerminal[];
  public feePrice!: number;
  public isActivated!: boolean;
  public loadingLocationCode!: string;
  public unloadingPortCode!: string;
  public agencyCode!: string;
  public individualOrganization!: number;
  public name?: string;
  public hubId?: number;
  public isGenerateInvoice?: boolean;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          unique: true,
          primaryKey: true,
          autoIncrement: true
        },
        companyId: {
          type: DataTypes.INTEGER,
          allowNull: false
        },
        stationId: {
          type: DataTypes.INTEGER,
          allowNull: false
        },
        code: {
          type: DataTypes.STRING(50),
          allowNull: false
        },
        customsOffice: {
          type: DataTypes.STRING(10),
          allowNull: false
        },
        customsSubSection: {
          type: DataTypes.STRING(8),
          allowNull: false
        },
        terminalIds: {
          type: DataTypes.ARRAY(DataTypes.INTEGER),
          allowNull: false
        },
        feePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        isActivated: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        loadingLocationCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        unloadingPortCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        agencyCode: {
          type: DataTypes.STRING(10),
          allowNull: true,
        },
        individualOrganization: {
          type: DataTypes.SMALLINT,
          allowNull: true,
        },
        name: {
          type: DataTypes.STRING(100),
          allowNull: true,
        },
        hubId: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        kindleCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        orderTypeId: {
          type: DataTypes.INTEGER,
          allowNull: true,
          defaultValue: 0,
        },
        isGenerateInvoice: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        // create information
        createdAt: {
          type: DataTypes.DATE,
        },
        updatedAt: {
          type: DataTypes.DATE,
        },
        deletedAt: {
          type: DataTypes.DATE,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          company: {
            include: [
              {
                model: Company,
                as: 'company'
              }
            ]
          },
          station: {
            include: [
              {
                model: Station,
                as: 'station'
              }
            ]
          },
          terminals: {
            include: [
              {
                model: Terminal,
                as: 'terminals',
                on: Sequelize.literal('"terminals"."id" = any("Warehouse"."terminalIds")')
              }
            ]
          },
        },
      },
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model>; }) {
    // place to set model associations
    Warehouse.belongsTo(Company, { as: 'company' });
    Warehouse.belongsTo(Station, { as: 'station' });
    Warehouse.hasMany(Terminal, { sourceKey: 'terminalIds', as: 'terminals', constraints: false, foreignKey: 'id' });
  }
}

