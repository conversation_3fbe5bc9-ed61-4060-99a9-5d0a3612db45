'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IInvoiceCondition {
  code?: string;
  name?: string;
  insuranceCode?: string;
  position?: number;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class InvoiceCondition extends BaseModel implements IInvoiceCondition {
  public static readonly ModelName: string = 'InvoiceCondition';
  public static readonly TableName: string = 'clearance_invoice_conditions';
  public static readonly DefaultScope: FindOptions = {};

  public code!: string;
  public name!: string;
  public insuranceCode!: string;
  public position!: number;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        code: {
          type: DataTypes.STRING(50),
          unique : true,
          primaryKey: true,
          allowNull : true
        },
        name: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        insuranceCode: {
          type: DataTypes.STRING(4),
          allowNull : true
        },
        position: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        // create information
        createdAt: {
          type: DataTypes.DATE,
        },
        updatedAt: {
          type: DataTypes.DATE,
        },
        deletedAt: {
          type: DataTypes.DATE,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

