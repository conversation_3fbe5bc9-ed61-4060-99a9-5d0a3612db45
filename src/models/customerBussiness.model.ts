'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize, UUIDV4 } from 'sequelize';
import { BaseModel } from './base.model';

export interface ICustomerBusiness {
  customerId?: string;
  customerTaxId?: string;
  customerPassword?: string;
  customerSecret?: string;
  customerShortName?: string;
  customerFullName?: string;
  customerPhone?: string;
  customerAddress?: string;
  customerEmail?: string;
  customerAvatarPath?: string;
  customerContactFullName?: string;
  customerContactPhone?: string;
  customerContactEmail?: string;
  customerDeposit?: number;
  customerTransactionDebt?: number;
  customerTransactionLimit?: number;
  unitCurrency?: string;
  customerStatus?: number;
  isChangePass?: boolean;
  isAgency?: boolean;
  countryId?: number;
  customerActiveDate?: Date;
  customerExpiryDate?: Date;
  customerTsv?: string;
  customerTypeId?: number;
  saleId?: number;
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export class CustomerBusiness extends BaseModel implements ICustomerBusiness {
  public static readonly ModelName: string = 'CustomerBusiness';
  public static readonly TableName: string = 'customer_businesses';
  public static readonly DefaultScope: FindOptions = {};

  public customerId?: string;
  public customerTaxId?: string;
  public customerPassword?: string;
  public customerSecret?: string;
  public customerShortName?: string;
  public customerFullName?: string;
  public customerPhone?: string;
  public customerAddress?: string;
  public customerEmail?: string;
  public customerAvatarPath?: string;
  public customerContactFullName?: string;
  public customerContactPhone?: string;
  public customerContactEmail?: string;
  public customerDeposit?: number;
  public customerTransactionDebt?: number;
  public customerTransactionLimit?: number;
  public unitCurrency?: string;
  public customerStatus?: number;
  public isChangePass?: boolean;
  public isAgency?: boolean;
  public countryId?: number;
  public customerActiveDate?: Date;
  public customerExpiryDate?: Date;
  public customerTsv?: string;
  public customerTypeId?: number;
  public saleId?: number;
  readonly isDeleted?: boolean;
  readonly createdAt?: Date;
  readonly updatedAt?: Date;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        customerId: {
          type: DataTypes.UUID,
          primaryKey: true,
          defaultValue: UUIDV4
        },
        customerTaxId: { 
          type: DataTypes.STRING(15)
        },
        customerPassword:  { type: DataTypes.STRING },
        customerSecret:  { type: DataTypes.STRING(15) },
        customerShortName: { 
          type: DataTypes.STRING(5)
        },
        customerFullName:  { type: DataTypes.STRING(200) },
        customerPhone: DataTypes.STRING(20),
        customerAddress: DataTypes.STRING,
        customerEmail: DataTypes.STRING(100),
        customerAvatarPath: DataTypes.STRING,
        customerContactFullName: DataTypes.STRING(200),
        customerContactPhone: DataTypes.STRING(15),
        customerContactEmail: DataTypes.STRING(100),
        customerDeposit: { type: DataTypes.DECIMAL(12, 0), defaultValue: 0 },
        customerTransactionDebt: { type: DataTypes.DECIMAL(12, 0), defaultValue: 0 },
        customerTransactionLimit: { type: DataTypes.DECIMAL(12, 0), defaultValue: 0 },
        unitCurrency: DataTypes.STRING(10),
        customerStatus: { type: DataTypes.SMALLINT, defaultValue: 0 },
        isChangePass: { type: DataTypes.BOOLEAN, defaultValue: false },
        isAgency: { type: DataTypes.BOOLEAN, defaultValue: false },
        customerActiveDate: {
          type: DataTypes.DATEONLY,
        },
        customerExpiryDate: {
          type: DataTypes.DATEONLY,
        },
        customerTsv: { type: 'tsvector' },
        saleId: DataTypes.INTEGER,
        countryId: DataTypes.INTEGER,
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        createdAt: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updatedAt: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        }   
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

