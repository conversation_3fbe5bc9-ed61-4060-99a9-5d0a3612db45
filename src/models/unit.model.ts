'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IUnit {
  code?: string;
  name?: string;
  nameVN?: string;
  position?: number;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class Unit extends BaseModel implements IUnit {
  public static readonly ModelName: string = 'Unit';
  public static readonly TableName: string = 'clearance_units';
  public static readonly DefaultScope: FindOptions = {};

  public code!: string;
  public name!: string;
  public nameVN!: string;
  public position!: number;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        code: {
          type: DataTypes.STRING(50),
          unique : true,
          primaryKey: true,
          allowNull : true
        },
        name: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        nameVN: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        position: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        // create information
        createdAt: {
          type: DataTypes.DATE,
        },
        updatedAt: {
          type: DataTypes.DATE,
        },
        deletedAt: {
          type: DataTypes.DATE,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

