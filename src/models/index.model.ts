import * as fs from 'fs';
import * as path from 'path';
import { ModelCtor } from 'sequelize/types/lib/model';
import { BaseModel } from './base.model';

// region exporting all models from nested folders

export { MIC, IMIC, IMICMasterValidate, IMICMaster } from './mic.model';
export { IDA, IIDA } from './ida.model';
export { ImportDetail, IDetailValidate, } from './importDetail.model';
export { Country, ICountry } from './country.model';
export { Warehouse, IWarehouse } from './warehouse.model';
export { Terminal, ITerminal } from './terminal.model';
export { Company, ICompany } from './company.model';
export { Station, IStation } from './station.model';
export { HSCode, IHSCode } from './hscode.model';
export { HSCodeDetail, IHSCodeDetail } from './hscodeDetail.model';

export { ImportCode, IImportCode } from './tableImportCode.model';
export { VatCode, IVatCode } from './tableVATCode.model';
export { EnvironmentCode, IEnvironmentCode } from './tableEnvironmentCode.model';
export { SpecialConsumptionCode, ISpecialConsumptionCode } from './tableSpecialConsumptionCode.model';
export { TaxFreeCode, ITaxFreeCode } from './tableTaxFreeCode.model';

export { Unit, IUnit } from './unit.model';
export { Packeage, IPackeage } from './package.model';
export { InvoiceCondition, IInvoiceCondition } from './invoiceCondition.model';

export { CustomerBusiness, ICustomerBusiness } from './customerBussiness.model';
export { CustomerPersonal, ICustomerPersonal } from './customerPersonal.model';

export { Employee, IEmployee } from './employee.model';
export { LogPrint, ILogPrint } from './logPrint.model';
export { User, IUser } from './user.model';

export { TaxCode, ITaxCode } from './taxCode.model';

export { ImportTransaction, IImportTransaction } from './importTransaction.model';

export { MEC, IMEC, IMECCreate, IExportValidate, IMECMaster, IMasterValidate } from './mec.model';
export { EDA, IEDA, IEDACreate, IEDAMaster } from './eda.model';
export { ExportDetail, IExportDetail, IExportDetailValidate, IExportDetailCreate } from './exportDetail.model';


export { IManifest, Manifest } from './manifest.model';
export { ISortLabel, SortLabel } from './sortLabe.model';

export { IHold, Hold } from './hold.model';
export { IInvoiceNumber, InvoiceNumber } from './invoiceNumber.model';
export { IHub, Hub } from './hub.model';
export { IOrder, Order } from './order.model';

export { IClearanceCreateLog, ClearanceCreateLog } from './clearanceCreateLog.model';
export { IClient, Client } from './client.model';
export { IParameter, Parameter } from './parameter.model';
export { IShipment, Shipment } from './shipment.model';

export { ICarrier, Carrier } from './carrier.model';
export { IBox, Box } from './box.model';
export { IPartner, Partner } from './partner.model';

export { IHistoryInvoice, HistoryInvoice } from './historyInvoice.model';
export { IMasterAnalyzer, MasterAnalyzer } from './masterAnalyzer.model';

export { IHistoryMonitorGate, HistoryMonitorGate } from './historyMonitorGate.model';

export { IManifestArchive, ManifestArchive } from './manifestArchive.model';
export { IOrderArchive, OrderArchive } from './orderArchive.model';
export { ExportDetailArchive, IExportDetailArchive } from './exportDetailArchive.model';
export { MECArchive, IMECArchive } from './mecArchive.model';

export { ImportDetailArchive, IImportDetailArchive } from './importDetailArchive.model';
export { MICArchive, IMICArchive } from './micArchive.model';
export { IDAArchive, IIDAArchive } from './idaArchive.model';

export { eImportDetail, eIImportDetail, eIDetailValidate, eIImportDetailCreate } from './eimportDetail.model';
export { eMIC, eIMIC, eIMICMaster, eIMICCreate } from './emic.model';

export { eMEC, eIMEC, eIMECCreate, eIMECMaster } from './emec.model';
export { eExportDetail, eIExportDetail, eIExportDetailValidate, eIExportDetailCreate } from './eexportDetail.model';

// endregion

// region loader function for all models in this folder or subfolders

/**
 * exports all Model classes of files that end with '.model.ts'
 * */
export const modelsLoader = () => {
  const array = getFilesRecursively(__dirname)
    .filter(fileName => fileName.endsWith('model.js'))
    .map(fileName => {
      // tslint:disable-next-line:non-literal-require
      const modelClass = require(fileName);
      let models: any = {};
      Object.keys(modelClass).forEach(key => {
        if (
          modelClass[key].constructor != null &&
          modelClass[key].ModelName != null
        ) {
          models[modelClass[key].ModelName] = modelClass[key];
        }
      });
      return models;
    })
    .reduce<{ [key: string]: any }[]>(
      (arr: any[], val: any[]) => arr.concat(val),
      [],
    );
  let obj = {};
  array.forEach((item: any) => {
    obj = {
      ...obj,
      ...item,
    };
  });
  return obj as {
    [modelName: string]: typeof BaseModel & ModelCtor<BaseModel>;
  };
};

// region helper functions
const getAllSubFolders = (baseFolder: string, folderList: string[] = []) => {
  const folders: string[] = fs
    .readdirSync(baseFolder)
    .filter(file => fs.statSync(path.join(baseFolder, file)).isDirectory());
  folders.forEach(folder => {
    folderList.push(path.join(baseFolder, folder));
    getAllSubFolders(path.join(baseFolder, folder), folderList);
  });
  return folderList;
};
const getFilesInFolder = (rootPath: string) => {
  return fs
    .readdirSync(rootPath)
    .filter(
      filePath => !fs.statSync(path.join(rootPath, filePath)).isDirectory(),
    )
    .map(filePath => path.normalize(path.join(rootPath, filePath)));
};
const getFilesRecursively = (rootPath: string): string[] => {
  const rootFiles: any = getFilesInFolder(rootPath);
  const subFolders: any = getAllSubFolders(rootPath);
  const allFiles: any = subFolders.map(getFilesInFolder);
  return [].concat.apply([...rootFiles], allFiles);
};

// endregion

// endregion