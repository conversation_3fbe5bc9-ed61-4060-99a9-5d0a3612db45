'use strict';

import { DataTypes, FindOptions, Model, Model<PERSON><PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface ITerminal {
  id: number;
  host: string;
  username: string;
  password: string;
  userId: string;
  folerName: string;
  isLive: boolean;
  isPrioritize: boolean;
  ratePush: number;
  isTLS: boolean;
  handleNumber: number;
  port: number;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class Terminal extends BaseModel implements ITerminal {
  public static readonly ModelName: string = 'Terminal';
  public static readonly TableName: string = 'clearance_terminals';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public host!: string;
  public username!: string;
  public password!: string;
  public userId!: string;
  public folerName!: string;
  public isLive!: boolean;
  public isPrioritize!: boolean;
  public ratePush!: number;
  public isTLS!: boolean;
  public handleNumber!: number;
  public port!: number;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          unique: true,
          primaryKey: true,
          autoIncrement: true
        },
        host: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        username: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        password: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        userId: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        folerName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        isLive: {
          type: DataTypes.BOOLEAN,
          allowNull: true,
          defaultValue: true
        },
        isPrioritize: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: true
        },
        ratePush: {
          type: DataTypes.SMALLINT,
          defaultValue: 1
        },
        isTLS: {
          type: DataTypes.BOOLEAN,
          defaultValue: true
        },
        handleNumber: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        port: { type: DataTypes.DECIMAL(10, 0) },
        // create information
        createdAt: {
          type: DataTypes.DATE
        },
        updatedAt: {
          type: DataTypes.DATE
        },
        deletedAt: {
          type: DataTypes.DATE
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import'
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
  }
}
