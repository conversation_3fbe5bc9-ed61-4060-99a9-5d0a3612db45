'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';


export interface IEnvironmentCode {
  code?: string;
  name?: string;
  unit?: string;
  unitPriceValue?: number;
  position?: number;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  isDeleted?: string;
}

export class EnvironmentCode extends BaseModel implements IEnvironmentCode {
  public static readonly ModelName: string = 'EnvironmentCode';
  public static readonly TableName: string = 'clearance_environment_codes';
  public static readonly DefaultScope: FindOptions = {
    'attributes': {
      'exclude': ['deletedAt', 'isDeleted']
    }
  };

  public code!: string;
  public name!: string;
  public unit!: string;
  public unitPriceValue!: number;
  public position!: number;

  public createdAt!: string;
  public updatedAt!: string;
  public deletedAt!: string;
  public isDeleted!: string;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      //header
      code: {
        type: DataTypes.STRING(50),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      name: {
        type: DataTypes.TEXT,
        allowNull : true
      },
      unit: {
        type: DataTypes.STRING(10),
        allowNull : true
      },
      unitPriceValue: {
        type: DataTypes.DECIMAL(20, 4),
        allowNull : true
      },
      position: {
        type: DataTypes.INTEGER,
        allowNull : true
      },
      createdAt: {
        type: DataTypes.DATE,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE,
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      sequelize: sequelize,
      tableName: this.TableName,
      name: {
        singular: this.ModelName,
      },
      defaultScope: this.DefaultScope,
      comment: 'Model for the public accessible data of an import',
    },
  );}

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

