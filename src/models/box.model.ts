'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { Hub, Shipment } from './index.model';
import { Station } from './station.model';

export interface IBox {
  id?: number; 
  name?: string; 
  weightManifest?: number;
  weight?: number; 
  long?: number; 
  width?: number; 
  height?: number;
  stationId?: number; 
  hubId?: number;
  shipmentId?: number;
  amountHAWB?: number;
  HAWBs?: any;
  phase?: number;
  status?: number;
  description?: string;
  unitOfMass?: string;
  unitOfLength?: string;
  logHAWBs?: any;
  shipment?: Shipment;
  boxNumber?: string;
  amountOrder?: number;
  logOrders?: string;
  isOrder?: boolean;
  createdAtStationId?: number;
  createdAtStation?: Station;
  hub?: Hub;
  logOrderNumbers?: string;
  //
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export class Box extends BaseModel implements IBox {
  public static readonly ModelName: string = 'Box';
  public static readonly TableName: string = 'boxes';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number; 
  public name!: string; 
  public weightManifest!: number;
  public weight!: number; 
  public long!: number; 
  public width!: number; 
  public height!: number;
  public stationId!: number; 
  public hubId!: number;
  public shipmentId!: number;
  public amountHAWB!: number;
  public HAWBs!: any;
  public phase!: number;
  public status!: number;
  public description!: string;
  public unitOfMass!: string;
  public unitOfLength!: string;
  public logHAWBs!: any;
  public shipment!: Shipment;
  public boxNumber!: string;
  public amountOrder!: number;
  public logOrders!: string;
  public isOrder!: boolean;
  public createdAtStationId!: number;
  public createdAtStation!: Station;
  public hub!:Hub;
  public logOrderNumbers!: string;
  //
  isDeleted!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public readonly deletedAt!: Date;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: DataTypes.STRING(30),
        unique: true,
        allowNull : false
      },
      weightManifest: { //sum cân nặng từng manifest
        type: DataTypes.STRING(11),
        allowNull : true
      },
      weight: { //nặng
        type: DataTypes.STRING(11),
        allowNull : true
      },
      long: { // chiều dài
        type: DataTypes.STRING(11),
        allowNull : true
      },
      width: { // chiều rông
        type: DataTypes.STRING(11),
        allowNull : true
      },
      height: { // chiều cao
        type: DataTypes.STRING(11),
        allowNull : true
      },
      stationId: {
        type: DataTypes.INTEGER,
        allowNull : false
      },
      hubId: {
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      shipmentId: { 
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      amountHAWB: {
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      HAWBs: {
        type: DataTypes.TEXT,
        allowNull : true,
      },
      phase: {
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      phase_name: {
        type: DataTypes.VIRTUAL,
        allowNull : true,
      },
      unitOfMass: {
        type: DataTypes.STRING(5),
        allowNull : true,
      },
      unitOfLength: {
        type: DataTypes.STRING(5),
        allowNull : true,
      },
      status: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      logHAWBs: {
        type: DataTypes.STRING,
        allowNull : true,
      },
      boxNumber: {
        type: DataTypes.TEXT,
        allowNull : true,
      },
      amountOrder:{
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      logOrders: {
        type: DataTypes.TEXT,
        allowNull : true,
      },
      isOrder: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      createdAtStationId: {
        type: DataTypes.INTEGER,
        allowNull : true,
      },
      logOrderNumbers: {
        type: DataTypes.TEXT,
        allowNull : true,
      },
      // create information
      createdAt: {
        type: DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      deletedAt: {
        type: DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      }
      },
      {
        sequelize: sequelize,
        timestamps: false,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}