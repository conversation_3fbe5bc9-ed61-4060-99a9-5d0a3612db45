'use strict'

import moment from 'moment';
import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import Configure from '../emuns/configures';
import { PrintName } from '../emuns/print';
import { BaseModel } from './base.model';
import { Employee, User } from './index.model';

export interface IHistoryInvoice {
  id?: number;
  MAWB?: string;
  employeeId?: number;
  printType?: number;
  successDate?: string;
  hubId?: number;
  isSucess?: boolean;
  dataSearch?: any;
  print_type_name?: string;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
  link?: string;
  note?: string;
  excelLink?: string;
}

export class HistoryInvoice extends BaseModel implements IHistoryInvoice {
  public static readonly ModelName: string = 'HistoryInvoice';
  public static readonly TableName: string = 'clearance_history_invoice_print';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public MAWB!: string;
  public employeeId!: number;
  public printType!: number;
  public successDate!: string;
  public hubId!: number;
  public isSucess!: boolean;
  public dataSearch!: any;
  public print_type_name!: string;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;
  public link?: string;
  public note?: string;
  public excelLink?: string;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: { 
          type: DataTypes.INTEGER,
          primaryKey: true,
          unique: true,
          autoIncrement: true,
          autoIncrementIdentity: true 
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull : false
        },
        employeeId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        printType: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        successDate: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('successDate')) {
              return moment(that.getDataValue('successDate')).format(Configure.FULL_TIME);
            }
            return that.getDataValue('successDate');
          }
        },
        hubId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        isSucess: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        dataSearch: {
          type: DataTypes.JSON,
          allowNull : true,
        },
        print_type_name: {
          type: DataTypes.VIRTUAL,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('printType') !== null) {
              return PrintName.get(that.getDataValue('printType'));
            }
            return that.getDataValue('printType');
          }
        },
        link: {
          type: DataTypes.STRING(255),
          allowNull : true
        },
        excelLink: {
          type: DataTypes.STRING(250),
          allowNull : true
        },
        note: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        // create information
        createdAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('createdAt')) {
              return moment(that.getDataValue('createdAt')).format(Configure.FULL_TIME);
            }
            return that.getDataValue('createdAt');
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('updatedAt')) {
              return moment(that.getDataValue('updatedAt')).format(Configure.FULL_TIME);
            }
            return that.getDataValue('updatedAt');
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('deletedAt')) {
              return moment(that.getDataValue('deletedAt')).format(Configure.FULL_TIME);
            }
            return that.getDataValue('deletedAt');
          }
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          employee: {
            include: [
              {
                model: Employee,
                as: 'employee',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['firstname', 'lastname']
                  }
                ]
              }
            ]
          },
        }
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
    HistoryInvoice.belongsTo(Employee, { as: 'employee', foreignKey: 'employeeId' });
  }
}

