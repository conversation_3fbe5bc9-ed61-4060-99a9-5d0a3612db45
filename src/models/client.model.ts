'use strict'

import { DataTypes, FindOptions, Model, Model<PERSON><PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IClient {
  id?: number;
  name?: string;
  code?: string;
  userId?: number;
  webHookEndpoint?: string;
  webHookToken?: string;
  customerId?: string;
  isDeleted?: boolean;
}

export class Client extends BaseModel implements IClient {
  public static readonly ModelName: string = 'Client';
  public static readonly TableName: string = 'clients';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public name!: string;
  public code!: string;
  public userId!: number;
  public webHookEndpoint!: string;
  public webHookToken!: string;
  public customerId!: string;
  public isDeleted!: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
        },
        code: {    
          type: DataTypes.STRING,
          unique: true,
        },
        userId: {
          type: DataTypes.INTEGER,
          allowNull : true,
        },
        customerId: {
          type: DataTypes.UUID,
          allowNull : false
        },
        webHookEndpoint: {
          type: DataTypes.TEXT,
          allowNull : true,
        },
        webHookToken: {
          type: DataTypes.TEXT,
          allowNull : true,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize: sequelize,
        timestamps: false,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

