'use strict';

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import moment from 'moment';
import EConfigure from '../emuns/configures';
import { Client, Country, Employee, ExportDetail, Hold, Manifest, Shipment, SortLabel, Station, User } from './index.model';
import { CustomerBusiness } from './index.model';
import { CustomerPersonal } from './index.model';
import { ActionName } from '../emuns/action';
import { Hub } from './hub.model';

export interface IEDAMaster {
  MAWB?: string;
  departurePlannedDate?: string;
  loadingPlannedVesselName?: string;
  address4?: string;
  theFinalDestinationCode?: string;
  theFinalDestinationName?: string;
  countryCode?: string;
  customsWarehouseCode?: string;
  plannedDeclarantCode?: string;
  loadingPortCode?: string;
  loadingPortName?: string;
  customsOffice?: string;
  customsSubSection?: string;
  meansOfTransportationCode?: number;
  terminalName?: string;
  ftpId?: number;
  hubId?: number;
}

export interface IEDACreate {
  HAWB: string;
  HAWBClearance?: string;
  declarationNo?: string;
  stationId: number;
  phase: number;
  cargoNo: string;
  serviceId: number;
  startDate?: string;
  arrivalDateOfTransport?: string;

  MAWB?: string;
  declarationKindCode?: string;
  meansOfTransportationCode?: string;
  customsOffice?: string;
  customsSubSection?: string;
  exporterCode?: string;
  exporterName?: string;
  exporterFullName?: string;
  postCode?: string;
  addressOfExporter?: string;
  telephoneNumber?: string;
  consigneeCode?: string;
  consigneeName?: string;
  consigneeTelephoneNumber?: string;
  postCodeIdentification?: string;
  address1?: string;
  address2?: string;
  address3?: string;
  address4?: string;
  countryCode?: string;
  plannedDeclarantCode?: string;
  cargoPiece?: number;
  pieceUnitCode?: string;
  cargoWeightGross?: number;
  weightUnitCodeGross?: string;
  customsWarehouseCode?: string;
  destinationLocationForBondedTransport?: string;
  theFinalDestinationCode?: string;
  theFinalDestinationName?: string;
  loadingPortCode?: string;
  loadingPortName?: string;
  loadingPlannedVesselName?: string;
  departurePlannedDate?: string;
  termOfPayment?: string;
  invoicePriceConditionCode?: string;
  invoiceCurrencyCode?: string;
  totalInvoicePrice?: number;
  invoicePriceKindCode?: string;
  currencyCodeOfTaxValue?: string;
  totalOfTaxValue?: number;
  taxPayer?: number;
  codeOfExtendingDueDate?: string;
  notes?: string;
  valueClearanceVND?: number;
  priceVND?: number;
  customerBusinessId?: string;
  customerPersonalId?: string;
  invoiceClassificationCode?: string;
  classify?: string;
  clientId?: any;
  hubId?: number;
  terminalName?: string;
  ftpId?: number;
  inspectionKindClassification?: number;
  dateCheckin?: string;
  dateClearanced?: string;
  dateCheckout?: string;
}

export interface IEDA {
  HAWB?: string;
  MAWB?: string;

  declarationNo?: string;
  inspectionKindClassification?: string;
  stationId?: number;
  declarationKindCode?: string;
  meansOfTransportationCode?: string;
  cargoClassificationCode?: string;
  customsOffice?: string;
  customsSubSection?: string;
  exporterCode?: string;
  exporterName?: string;
  exporterFullName?: string;
  postCode?: string;
  addressOfExporter?: string;
  telephoneNumber?: string;
  exporterContractorCode?: string;
  exporterContractorName?: string;
  consigneeCode?: string;
  consigneeName?: string;
  consigneeTelephoneNumber?: string;
  postCodeIdentification?: string;
  address1?: string;
  address2?: string;
  address3?: string;
  address4?: string;
  countryCode?: string;
  plannedDeclarantCode?: string;
  cargoNo?: string;
  cargoPiece?: number;
  pieceUnitCode?: string;
  cargoWeightGross?: number;
  weightUnitCodeGross?: string;
  customsWarehouseCode?: string;
  customsClearanceWarehouseName?: string;
  theFinalDestinationCode?: string;
  theFinalDestinationName?: string;
  loadingPortCode?: string;
  loadingPortName?: string;
  loadingPlannedVesselName?: string;
  departurePlannedDate?: string;
  invoiceClassificationCode?: string;
  termOfPayment?: string;
  invoicePriceConditionCode?: string;
  invoiceCurrencyCode?: string;
  totalInvoicePrice?: number;
  invoicePriceKindCode?: string;
  currencyCodeOfTaxValue?: string;
  totalOfTaxValue?: number;
  taxPayer?: number;
  codeOfExtendingDueDate?: string;
  startDate?: string;
  arrivalDateOfTransport?: string;
  destinationLocationForBondedTransport?: string;
  totalBasicPriceInput?: string;
  paymentClassification?: string;
  numberOfDeclaration?: number;
  structure?: number;
  notes?: string;
  // create information
  isError?: boolean;
  isECUSError?: boolean;
  messageErrorName?: string;
  messageError?: string;
  valueClearanceVND?: number;
  priceVND?: number;
  serviceId?: number;
  customerBusinessId?: string;
  customerPersonalId?: string;
  classify?: string;
  dateCheckin?: string;
  dateClearanced?: string;
  dateCheckout?: string;
  phase?: number;
  phase_name?: any;
  times?: number;
  tempTimes?: number;
  originalOrderNumberClient?: string;
  terminalName?: string;
  ftpId?: number;
  dateAction?: string;
  typeAction?: string;
  customerBusiness?: CustomerBusiness;
  customerPersonal?: CustomerPersonal;
  isEDCed?: boolean;
  isEditProcessing?: boolean;
  isIEDAed?: boolean;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  isDeleted?: boolean;

  representativeTaxCode?: string;
  customsOfficeName?: string;
  declarationPlannedDate?: string;
  registeredTime?: string;
  managementNumberUser?: string;
  dateOfCompletionOfInspection?: string;
  timeOfCompletionOfInspection?: string;
  dateOfPermit?: string;
  timeOfPermit?: string;
  nameOfHeadCustomsOffice?: string;

  subjectExportCode?: string;
  numberExport?: number;
  totalExportTax?: number;
  subjectVATCode?: string;
  numberVAT?: number;
  totalVATTax?: number;
  subjectEnvironmentCode?: string;
  numberEnvironment?: number;
  totalEnvironmentTax?: number;
  subjectSpecialConsumptionCode?: string;
  numberSpecialConsumption?: number;
  totalSpecialConsumptionTax?: number;
  totalTax?: number;
  hubId?: number;
  HAWBClearance?: string;
  exportDetails: ExportDetail[];
  invoiceNo?: string;
  invoiceDate?: string;
  clientId?: number;
  employeeUpdateCargoName?: number;
  employeeUpdateCargo?: Employee;
  isHold?: boolean;
  reasonIds?: number[];
  noteHold?: string;
  hub?: Hub;
  country?: Country;

  station?: Station;
  exportDetailItems?: ExportDetail[];
  manifest?: Manifest;
  holds?: Hold[];
  declarationNoCustomer?: string;
  isPrioritize?: boolean;
  externalBoxName?: string;
  internalBoxName?: string;
  isHellmannSuccess?: boolean;
  hellmannSuccessDate?: string;
  warehouseAddress?: string;
  warehouseCheckin?: string;
  warehouseCheckout?: string;
  isSortLane?: boolean;
}

export class EDA extends BaseModel implements IEDA {
  public static readonly ModelName: string = 'EDA';
  public static readonly TableName: string = 'clearance_edas';
  public static readonly DefaultScope: FindOptions = {
    attributes: {
      exclude: ['deletedAt', 'isDeleted']
    }
  };

  public HAWB!: string;
  public MAWB!: string;

  public declarationNo!: string;
  public inspectionKindClassification!: string;
  public stationId!: number;
  public declarationKindCode!: string;
  public meansOfTransportationCode!: string;
  public cargoClassificationCode!: string;
  public customsOffice!: string;
  public customsSubSection!: string;
  public exporterCode!: string;
  public exporterName!: string;
  public exporterFullName!: string;
  public postCode!: string;
  public addressOfExporter!: string;
  public telephoneNumber!: string;
  public exporterContractorCode!: string;
  public exporterContractorName!: string;
  public consigneeCode!: string;
  public consigneeName!: string;
  public consigneeTelephoneNumber!: string;
  public postCodeIdentification!: string;
  public address1!: string;
  public address2!: string;
  public address3!: string;
  public address4!: string;
  public countryCode!: string;
  public plannedDeclarantCode!: string;
  public cargoNo!: string;
  public cargoPiece!: number;
  public pieceUnitCode!: string;
  public cargoWeightGross!: number;
  public weightUnitCodeGross!: string;
  public customsWarehouseCode!: string;
  public customsClearanceWarehouseName!: string;
  public theFinalDestinationCode!: string;
  public theFinalDestinationName!: string;
  public loadingPortCode!: string;
  public loadingPortName!: string;
  public loadingPlannedVesselName!: string;
  public departurePlannedDate!: string;
  public invoiceClassificationCode!: string;
  public termOfPayment!: string;
  public invoicePriceConditionCode!: string;
  public invoiceCurrencyCode!: string;
  public totalInvoicePrice!: number;
  public invoicePriceKindCode!: string;
  public currencyCodeOfTaxValue!: string;
  public totalOfTaxValue!: number;
  public taxPayer!: number;
  public codeOfExtendingDueDate!: string;
  public startDate!: string;
  public arrivalDateOfTransport!: string;
  public destinationLocationForBondedTransport!: string;
  public totalBasicPriceInput!: string;
  public paymentClassification!: string;
  public numberOfDeclaration!: number;
  public structure!: number;
  public notes!: string;
  // create information
  public isError!: boolean;
  public isECUSError!: boolean;
  public messageErrorName!: string;
  public messageError!: string;
  public valueClearanceVND!: number;
  public priceVND!: number;
  public serviceId!: number;
  public customerBusinessId!: string;
  public customerPersonalId!: string;
  public classify!: string;
  public dateCheckin!: string;
  public dateClearanced!: string;
  public dateCheckout!: string;
  public phase!: number;
  public phase_name!: any;
  public times!: number;
  public tempTimes!: number;
  public originalOrderNumberClient!: string;
  public terminalName!: string;
  public ftpId!: number;
  public dateAction!: string;
  public typeAction!: string;
  public customerBusiness!: CustomerBusiness;
  public customerPersonal!: CustomerPersonal;
  public isEDCed!: boolean;
  public isEditProcessing!: boolean;
  public isIEDAed!: boolean;
  public createdAt!: string;
  public updatedAt!: string;
  public deletedAt!: string;
  public isDeleted!: boolean;
  public representativeTaxCode!: string;
  public customsOfficeName!: string;
  public declarationPlannedDate!: string;
  public registeredTime!: string;
  public managementNumberUser!: string;

  public dateOfCompletionOfInspection!: string;
  public timeOfCompletionOfInspection!: string;
  public dateOfPermit!: string;
  public timeOfPermit!: string;
  public nameOfHeadCustomsOffice!: string;

  public subjectExportCode!: string;
  public numberExport!: number;
  public totalExportTax!: number;
  public subjectVATCode!: string;
  public numberVAT!: number;
  public totalVATTax!: number;
  public subjectEnvironmentCode!: string;
  public numberEnvironment!: number;
  public totalEnvironmentTax!: number;
  public subjectSpecialConsumptionCode!: string;
  public numberSpecialConsumption!: number;
  public totalSpecialConsumptionTax!: number;
  public totalTax!: number;
  public hubId!: number;
  public HAWBClearance!: string;
  public exportDetails!: ExportDetail[];
  public invoiceNo!: string;
  public invoiceDate!: string;
  public clientId!: number;
  public isSortLane!: boolean;

  public employeeUpdateCargoName!: number;
  public employeeUpdateCargo!: Employee;
  public isHold!: boolean;
  public reasonIds!: number[];
  public noteHold!: string;
  public country!: Country;
  public exportDetailItems!: ExportDetail[];
  public inspectionKindTimes!: number;
  public clearanceDeclarationTimes!: number;
  public hub!: Hub;
  public station!: Station;
  public manifest!: Manifest;
  public holds!: Hold[];
  public declarationNoCustomer!: string;
  public isPrioritize?: boolean;
  public externalBoxName?: string;
  public internalBoxName?: string;
  public isHellmannSuccess?: boolean;
  public hellmannSuccessDate?: string;
  public warehouseAddress?: string;
  public warehouseCheckin?: string;
  public warehouseCheckout?: string;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        HAWB: {
          type: DataTypes.STRING(50),
          unique: true,
          primaryKey: true,
          allowNull: true
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        declarationNo: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        inspectionKindClassification: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        stationId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        declarationKindCode: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        meansOfTransportationCode: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        declarationPlannedDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        registeredTime: {
          type: DataTypes.TIME,
          allowNull: true
        },
        managementNumberUser: {
          type: DataTypes.STRING(8),
          allowNull: true
        },
        representativeTaxCode: {
          type: DataTypes.STRING(8),
          allowNull: true
        },
        cargoClassificationCode: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        customsOffice: {
          type: DataTypes.STRING(8),
          allowNull: true
        },
        customsSubSection: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        customsOfficeName: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        exporterCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        exporterName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        exporterFullName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        postCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        addressOfExporter: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        telephoneNumber: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        exporterContractorCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        exporterContractorName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        consigneeCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        consigneeName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        consigneeTelephoneNumber: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        postCodeIdentification: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        address1: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        address2: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        address3: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        address4: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        countryCode: {
          type: DataTypes.STRING(5),
          allowNull: true
        },
        plannedDeclarantCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        agentName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        cargoNo: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        cargoPiece: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        pieceUnitCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        cargoWeightGross: {
          type: DataTypes.DECIMAL(12, 3),
          allowNull: true
        },
        weightUnitCodeGross: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        customsWarehouseCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        customsClearanceWarehouseName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        theFinalDestinationCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        theFinalDestinationName: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        loadingPortCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        loadingPortName: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        loadingPlannedVesselName: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        departurePlannedDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        invoiceClassificationCode: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        termOfPayment: {
          type: DataTypes.STRING(7),
          allowNull: true
        },
        invoicePriceConditionCode: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        invoiceCurrencyCode: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        totalInvoicePrice: {
          type: DataTypes.DECIMAL(12, 3),
          allowNull: true
        },
        invoicePriceKindCode: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        currencyCodeOfTaxValue: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        totalOfTaxValue: {
          type: DataTypes.DECIMAL(12, 3),
          allowNull: true
        },
        taxPayer: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        codeOfExtendingDueDate: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        startDate: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        arrivalDateOfTransport: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        destinationLocationForBondedTransport: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        totalBasicPriceInput: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        paymentClassification: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        numberOfDeclaration: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        structure: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        notes: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        // create information
        isError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isECUSError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        messageErrorName: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        messageError: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        valueClearanceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        priceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        serviceId: {
          type: DataTypes.INTEGER,
          allowNull: false
        },
        customerBusinessId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        customerPersonalId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        classify: {
          // phân loại DOCUMENT, PARCEL
          type: DataTypes.STRING(4),
          allowNull: true,
          defaultValue: 'PAR'
        },
        dateCheckin: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCheckin')) {
              return moment(that.getDataValue('dateCheckin')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCheckin');
          }
        },
        dateClearanced: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateClearanced')) {
              return moment(that.getDataValue('dateClearanced')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateClearanced');
          }
        },
        dateCheckout: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCheckout')) {
              return moment(that.getDataValue('dateCheckout')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCheckout');
          }
        },
        phase: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        phase_name: {
          type: DataTypes.VIRTUAL,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('phase') !== null) {
              return ActionName.get(that.getDataValue('phase'));
            }
            return that.getDataValue('phase');
          }
        },
        times: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        tempTimes: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        originalOrderNumberClient: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        terminalName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        ftpId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        dateAction: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateAction')) {
              return moment(that.getDataValue('dateAction')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateAction');
          }
        },
        typeAction: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        inspectionKindTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        clearanceDeclarationTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        clearancedTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        isEDCed: {
          // đánh dấu EDA đã gửi EDC
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isEditProcessing: {
          // đánh dấu EDA0x phải chờ ECUS xử lý xong mới được EDA0x tiếp
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isIEDAed: {
          // đánh đấu EDA đã EDA0x rồi mới được EDE
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        dateOfCompletionOfInspection: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        timeOfCompletionOfInspection: {
          type: DataTypes.TIME,
          allowNull: true
        },
        dateOfPermit: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        timeOfPermit: {
          type: DataTypes.TIME,
          allowNull: true
        },
        nameOfHeadCustomsOffice: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        subjectExportCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        numberExport: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalExportTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        subjectVATCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        numberVAT: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalVATTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        subjectEnvironmentCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        numberEnvironment: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalEnvironmentTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        subjectSpecialConsumptionCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        numberSpecialConsumption: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalSpecialConsumptionTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        totalTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        hubId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        HAWBClearance: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        invoiceNo: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        invoiceDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        employeeUpdateCargoName: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        isHold: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: true
        },
        reasonIds: {
          type: DataTypes.ARRAY(DataTypes.INTEGER),
          allowNull: true
        },
        noteHold: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        declarationNoCustomer: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        isPrioritize: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        clientId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        externalBoxName: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        internalBoxName: {
          type: DataTypes.STRING(33),
          allowNull : true
        },
        isHellmannSuccess: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        hellmannSuccessDate: {
          type: DataTypes.DATE,
          defaultValue: null
        },
        warehouseAddress: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        warehouseCheckin: {
          type: DataTypes.DATE,
          allowNull: true
        },
        warehouseCheckout: {
          type: DataTypes.DATE,
          allowNull: true
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        createdAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('createdAt')) {
              return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('createdAt');
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('updatedAt')) {
              return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('updatedAt');
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('deletedAt')) {
              return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('deletedAt');
          }
        },
        isSortLane: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
      },
      {
        timestamps: true,
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          exportDetails: {
            include: [
              {
                model: ExportDetail,
                as: 'exportDetails'
              }
            ]
          },
          country: {
            include: [
              {
                model: Country,
                as: 'country'
              }
            ]
          },
          customerBusiness: {
            include: [
              {
                model: CustomerBusiness,
                as: 'customerBusiness'
              }
            ]
          },
          customerPersonal: {
            include: [
              {
                model: CustomerPersonal,
                as: 'customerPersonal'
              }
            ]
          },
          manifest: {
            include: [
              {
                model: Manifest,
                as: 'manifest',
                include: [
                  {
                    model: SortLabel,
                    as: 'sortLabel'
                  }
                ]
              }
            ]
          },
          employeeUpdateCargo: {
            include: [
              {
                model: Employee,
                as: 'employeeUpdateCargo',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['firstname', 'lastname']
                  }
                ]
              }
            ]
          },
          exportDetailItems: {
            include: [
              {
                model: ExportDetail,
                as: 'exportDetailItems',
                attributes: ['itemName', 'itemNameVN', 'productId']
              }
            ]
          },
          station: {
            include: [
              {
                model: Station,
                as: 'station'
              }
            ]
          },
          hub: {
            include: [
              {
                model: Hub,
                as: 'hub'
              }
            ]
          },
          holds: {
            include: [
              {
                model: Hold,
                as: 'holds',
                attributes: ['name'],
                on: Sequelize.literal('"holds"."id" = any("EDA"."reasonIds")')
              }
            ]
          },
          client: {
            include: [
              {
                model: Client,
                as: 'client'
              }
            ]
          },
          shipmentWeight: {
            include: [
              {
                model: Shipment,
                as: 'shipmentWeight',
                attributes: ['weightTotal', 'boxIds']
              }
            ]
          }
        }
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
    EDA.hasMany(ExportDetail, { as: 'exportDetails', foreignKey: 'HAWB', sourceKey: 'HAWB' });
    EDA.belongsTo(Country, { as: 'country', foreignKey: 'countryCode', targetKey: 'code' });
    EDA.belongsTo(CustomerPersonal, { as: 'customerPersonal', foreignKey: 'customerPersonalId' });
    EDA.belongsTo(CustomerBusiness, { as: 'customerBusiness', foreignKey: 'customerBusinessId' });
    EDA.belongsTo(Manifest, { as: 'manifest', foreignKey: 'HAWB' });
    EDA.belongsTo(Employee, { as: 'employeeUpdateCargo', foreignKey: 'employeeUpdateCargoName' });
    EDA.hasMany(ExportDetail, { as: 'exportDetailItems', foreignKey: 'HAWB', sourceKey: 'HAWB' });
    EDA.belongsTo(Station, { as: 'station', foreignKey: 'stationId' });
    EDA.belongsTo(Hub, { as: 'hub', foreignKey: 'hubId' });
    EDA.hasMany(Hold, { as: 'holds', constraints: false, foreignKey: 'reasonIds' });
    EDA.belongsTo(Client, { as: 'client', foreignKey: 'clientId', targetKey: 'id' });
    EDA.belongsTo(Shipment, { as: 'shipmentWeight', foreignKey: 'MAWB', targetKey: 'MAWB' });
  }
}
