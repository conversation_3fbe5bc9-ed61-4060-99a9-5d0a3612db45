'use strict'

import { DataTypes, FindOptions, Model, Model<PERSON><PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { Employee } from './index.model';

export interface IUser {
  id?: number;
  firstname?: string;
  lastname?: string;
  username?: string;
  password?: string;
  salt?: string;
  identity?: string;
  secret?: string;
  email?: string;
  phone?: string;
  roleId?: number;
  environments?: string;
  isActivated?: boolean;
  clientId?: number;
  userTypeId?: number;
  employeeId?: number;
  groupId?: number;
  resourceId?: number;
  //
  isDeleted?: string;
  readonly createdAt?: Date;
  readonly updatedAt?: Date;
  readonly deletedAt?: Date;

  odooDriverId?: number;
  odooEmployeeId?: number;
  companyId?: number;
  companyName?: string;
  licenseMark?: string;
  licenseExpire?: string;
  licenseNumber?: string;
  shippingTeam?: string;
  odooStationId?: number;
  odooTeamId?: number;
  odooStationName?: string;
  expiredWarning?: boolean;
  expiredDays?: number;
  stationIds?: string;
  hubIds?: string;
  clientIds?: string;
  isManagementUpdateCargoName?: boolean;
}

export class User extends BaseModel implements IUser {
  public static readonly ModelName: string = 'User';
  public static readonly TableName: string = 'users';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public firstname!: string;
  public lastname!: string;
  public username!: string;
  public password!: string;
  public salt!: string;
  public identity!: string;
  public secret!: string;
  public email!: string;
  public phone!: string;
  public roleId!: number;
  public environments!: string;
  public isActivated!: boolean;
  public clientId!: number;
  public userTypeId!: number;
  public employeeId!: number;
  public groupId!: number;
  public resourceId!: number;
  //
  public isDeleted!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public readonly deletedAt!: Date;

  public odooDriverId!: number;
  public odooEmployeeId!: number;
  public companyId!: number;
  public companyName!: string;
  public licenseMark!: string;
  public licenseExpire!: string;
  public licenseNumber!: string;
  public shippingTeam!: string;
  public odooStationId!: number;
  public odooTeamId!: number;
  public odooStationName!: string;
  public expiredWarning!: boolean;
  public expiredDays!: number;
  public stationIds!: string;
  public hubIds!: string;
  public clientIds!: string;
  public isManagementUpdateCargoName!: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      firstname: {
        type: DataTypes.STRING(50),
        allowNull : true
      },
      lastname: {
        type: DataTypes.STRING(50),
        allowNull : false
      },
      username: {
        type: DataTypes.STRING(50),
        unique : true,
        allowNull : false,
      },
      password: {
        type: DataTypes.STRING(60),
        allowNull : true
      },
      salt: {
        type: DataTypes.STRING(29),
        allowNull : true
      },
      identity: {
        type: DataTypes.STRING(15),
        unique : true, 
        allowNull : false
      },
      secret: {
        type: DataTypes.STRING(15),
        unique : true,
        allowNull : false
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull : true
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull : true
      },
      environments: {
        type: DataTypes.STRING(50),
        allowNull : false
      },
      isActivated: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      roleId: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      userTypeId: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      employeeId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      groupId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'Group',
          key: 'id'
        },
      },
      resourceId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Resource',
          key: 'id'
        },
      },
    
      odooDriverId: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      odooEmployeeId: {
        type: DataTypes.INTEGER,
        allowNull : true
      },
      companyId: {
        type: DataTypes.SMALLINT,
        allowNull : true
      },
      companyName: {
        type: DataTypes.STRING(255),
        allowNull : true
      },
      licenseMark: {
        type: DataTypes.STRING(5),
        allowNull : true
      },
      licenseExpire: {
        type: DataTypes.STRING(22),
        allowNull : true
      },
      licenseNumber: {
        type: DataTypes.STRING(14),
        allowNull : true
      },
      shippingTeam: {
        type: DataTypes.STRING(20),
        allowNull : true
      },
      odooStationId: {
        type: DataTypes.INTEGER,
        allowNull : true
      },
      odooStationName: {
        type: DataTypes.STRING(255),
        allowNull : true
      },
      expiredWarning: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      expiredDays: {
        type: DataTypes.INTEGER,
        allowNull : true
      },
      stationIds: {
        type: DataTypes.STRING(50),
        allowNull : true
      },
      clientIds: {
        type: DataTypes.STRING(50),
        allowNull : true
      },
      hubIds: {
        type: DataTypes.STRING(50),
        allowNull : true
      },
      isManagementUpdateCargoName: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      sequelize: sequelize,
      tableName: this.TableName,
      name: {
        singular: this.ModelName,
      },
      defaultScope: this.DefaultScope,
      comment: 'Model for the public accessible data of an import',
      scopes: {
        employee: {
          include: [
            { 
              model: Employee, 
              as: 'employee',
              include: [{
                model: User, 
                as: 'user',
                attributes: ['firstname', 'lastname']
              }]
            }
          ]
        },
      }
    },
  );}

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
    User.belongsTo(Employee, { as: 'employee', foreignKey: 'employeeId' });
  }
}