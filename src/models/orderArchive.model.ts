'use strict'

import { DataTypes, FindOptions, Model, Model<PERSON><PERSON>, Sequelize, UUIDV4 } from 'sequelize';
import { BaseModel } from './base.model';

export interface IOrderArchive {
	orderId?: string;
  orderNumber?: string;
  orderNumberClient?: string;
	totalAmount?: number;
	unitAmount?: string;
	receiverData?: any;
}

export class OrderArchive extends BaseModel implements IOrderArchive {
	public static readonly ModelName: string = 'OrderArchive';
	public static readonly TableName: string = 'orders_archive';
	public static readonly DefaultScope: FindOptions = {
		'attributes': {
			'exclude': ['deletedAt', 'isDeleted']
		}
	};

	orderId?: string;
  orderNumber?: string;
  orderNumberClient?: string;
	totalAmount?: number;
	unitAmount?: string;
	receiverData?: any;


	// region Static
	public static prepareInit(sequelize: Sequelize) {
		this.init({
			//header
			orderId: {
				type: DataTypes.UUID,
				defaultValue: UUIDV4,
				primaryKey: true,
				allowNull: false
			},
	
			orderNumber: { 
				type: DataTypes.STRING(50), 
				unique: true 
			},
			orderNumberClient: { 
				type: DataTypes.STRING(50) 
			},
			totalAmount: {
				type: DataTypes.DECIMAL(15, 3)
			},
    		unitAmount: {
				type: DataTypes.STRING(10),
			},
			receiverData: {
				type: DataTypes.JSON,
			},
		},
		{
			timestamps: false,
			sequelize: sequelize,
			tableName: this.TableName,
			name: {
				singular: this.ModelName,
			},
			defaultScope: this.DefaultScope,
			comment: 'Model for the public accessible data of an import',
			scopes: {
				
			}
		},
	);
}

	public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
		// place to set model associations
	}
}

