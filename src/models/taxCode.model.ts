'use strict';

import { DataTypes, FindOptions, Model, Model<PERSON><PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface ITaxCode {
  id: number;
  name_vn?: string;
  code?: string;
  address?: string;
  name?: string;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class TaxCode extends BaseModel implements ITaxCode {
  public static readonly ModelName: string = 'TaxCode';
  public static readonly TableName: string = 'clearance_tax_codes';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public name_vn!: string;
  public code!: string;
  public address!: string;
  public name!: string;
  public isClearanced?: boolean;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          unique: true,
          primaryKey: true,
          autoIncrement: true
        },
        name_vn: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        code: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        address: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        name: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        isClearanced: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        // create information
        createdAt: {
          type: DataTypes.DATE
        },
        updatedAt: {
          type: DataTypes.DATE
        },
        deletedAt: {
          type: DataTypes.DATE
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import'
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
  }
}
