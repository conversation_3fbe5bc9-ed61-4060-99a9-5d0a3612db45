'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IHSCodeCreate {
  hsCode?: string;
  name?: string;
  nameVN?: string;
  unitCode?: string;
  unitName?: string;
  importTaxValue?: number;
  importTaxCode?: string;
  VATValue?: number;
  VATCode?: string;
  specialConsumptionTaxValue?: number;
  specialConsumptionTaxCode?: string;
  environmentTaxPrice?: number;
  environmentTaxCode?: string;
}

export interface IHSCodeDetail {
  id: number;
  hsCode: string;
  name: string;
  nameVN: string;
  unitCode: string;
  unitName: string;
  importTaxValue: number;
  importTaxCode: string;
  VATValue: number;
  VATCode: string;
  specialConsumptionTaxValue: number;
  specialConsumptionTaxCode: string;
  environmentTaxPrice: number;
  environmentTaxCode: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string;
  isDeleted: string;
}

export class HSCodeDetail extends BaseModel implements IHSCodeDetail {
  public static readonly ModelName: string = 'HSCodeDetail';
  public static readonly TableName: string = 'clearance_hscode_details';
  public static readonly DefaultScope: FindOptions = {
    'attributes': {
      'exclude': ['deletedAt', 'isDeleted']
    }
  };

  public id!: number;
  public hsCode!: string;
  public name!: string;
  public nameVN!: string;
  public unitCode!: string;
  public unitName!: string;
  public importTaxValue!: number;
  public importTaxCode!: string;
  public VATValue!: number;
  public VATCode!: string;
  public specialConsumptionTaxValue!: number;
  public specialConsumptionTaxCode!: string;
  public environmentTaxPrice!: number;
  public environmentTaxCode!: string;
  public createdAt!: string;
  public updatedAt!: string;
  public deletedAt!: string;
  public isDeleted!: string;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      //header
      id: {
        type: DataTypes.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      hsCode: {
        type: DataTypes.STRING(50),
        allowNull : true
      },
      name: {
        type: DataTypes.TEXT,
        allowNull : true
      },
      nameVN: {
        type: DataTypes.TEXT,
        allowNull : true
      },
      unitCode: {
        type: DataTypes.STRING(10),
        allowNull : true
      },
      unitName: {
        type: DataTypes.STRING(20),
        allowNull : true
      },
      importTaxValue: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull : true
      },
      importTaxCode: {
        type: DataTypes.STRING(10),
        allowNull : true
      },
      VATValue: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull : true
      },
      VATCode: {
        type: DataTypes.STRING(10),
        allowNull : true
      },
      specialConsumptionTaxValue: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull : true
      },
      specialConsumptionTaxCode: {
        type: DataTypes.STRING(10),
        allowNull : true
      },
      environmentTaxPrice: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull : true
      },
      environmentTaxCode: {
        type: DataTypes.STRING(10),
        allowNull : true
      },
      createdAt: {
        type: DataTypes.DATE,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE,
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      sequelize: sequelize,
      tableName: this.TableName,
      name: {
        singular: this.ModelName,
      },
      defaultScope: this.DefaultScope,
      comment: 'Model for the public accessible data of an import',
    },
  );}

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

