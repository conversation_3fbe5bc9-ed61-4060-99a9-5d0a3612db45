'use strict';

import { DataTypes, FindOptions, Model, Model<PERSON><PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IEcusFolder {
  id: number;

  MAWB: string;
  HAWBs: string[];
  folder: string;
  isActive: boolean;

  // create information

  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class EcusFolder extends BaseModel implements IEcusFolder {
  public static readonly ModelName: string = 'EcusFolder';
  public static readonly TableName: string = 'clearance_ecus_folder';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public MAWB!: string;
  public HAWBs!: string[];
  public folder!: string;
  public isActive!: boolean;

  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          unique: true,
          primaryKey: true,
          autoIncrement: true
        },
        MAWB: {
          type: DataTypes.STRING(50)
        },
        HAWBs: {
          type: DataTypes.ARRAY(DataTypes.STRING)
        },
        folder: {
          type: DataTypes.STRING(200)
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true
        },
        // create information
        createdAt: {
          type: DataTypes.DATE
        },
        updatedAt: {
          type: DataTypes.DATE
        },
        deletedAt: {
          type: DataTypes.DATE
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import'
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
  }
}
