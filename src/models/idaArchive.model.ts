'use strict';

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import moment from 'moment';
import EConfigure from '../emuns/configures';
import { ActionName } from '../emuns/action';
import { Client, Country, Employee, Hold, ImportDetailArchive, ManifestArchive, OrderArchive, SortLabel, Station, User } from './index.model';
import { CustomerPersonal } from './index.model';
import { CustomerBusiness } from './index.model';
import { Hub } from './hub.model';
import { Shipment } from './shipment.model';

export interface IIDAArchive {
  HAWB?: string;
  MAWB?: string;
  declarationNo?: string;
  stationId?: number;
  declarationKindCode?: string;
  firstDeclarationNo?: string;
  cargoClassificationCode?: string;
  meansOfTransportationCode?: number;
  classificationOfIndividualOrganization?: number;
  inspectionKindClassification?: string;
  representativeTaxCode?: string;
  customsOffice?: string;
  customsOfficeName?: string;
  customsSubSection?: string;
  declarationPlannedDate?: string;
  registeredTime?: string;
  registeredDateOfCorrection?: string;
  registeredTimeOfCorrection?: string;
  importerCode?: string;
  importerName?: string;
  postCode?: string;
  addressOfImporter?: string;
  telephoneNumberOfImporter?: string;
  importContractorCode?: string;
  importContractorName?: string;
  consignorCode?: string;
  consignorName?: string;
  postCodeIdentification?: string;
  address1?: string;
  address2?: string;
  address3?: string;
  address4?: string;
  countryCode?: string;
  exportConsignerName?: string;
  plannedDeclarantCode?: string;
  agentCode?: string;
  agentName?: string;
  customsBrokerCode?: string;
  nameHeadCustoms?: string;
  cargoNo?: string;
  cargoPiece?: number;
  pieceUnitCode?: string;
  cargoWeightGross?: number;
  weightUnitCodeGross?: string;
  customsWarehouseCode?: string;
  customsClearanceWarehouseName?: string;
  loadingVesselAircraftName?: string;
  arrivalDate?: string;
  unloadingPortCode?: string;
  unloadingPortName?: string;
  loadingLocationCode?: string;
  loadingLocationName?: string;
  invoiceClassificationCode?: string;

  invoicePriceKindCode?: string;
  invoicePriceConditionCode?: string;
  invoiceCurrencyCode?: string;
  totalInvoicePrice?: number;
  valuationDeclarationDemarcationCode?: number;
  totalOfTaxValue?: number;
  totalOfProportional?: number;
  totalBasicPrice?: string;
  totalOfProportionalDistributionOnTaxValue?: number;
  taxPayer?: number;
  codeOfExtendingDueDateForPayment?: string;
  paymentClassification?: string;
  structure?: number;
  numberOfDeclarationColumn?: number;

  freightDemarcationCode?: string;
  freightCurrencyCode?: any;
  freight?: any;
  freightExchangeRate?: any;
  insuranceDemarcationCode?: string;
  managementNumberUser?: string;

  dateOfPermit?: string;
  timeOfPermit?: string;
  dateCompletionInspection?: string;
  timeCompletionInspection?: string;
  isError?: boolean;
  isECUSError?: boolean;
  messageErrorName?: string;
  messageError?: string;
  isMessageTax?: boolean;
  feeClearance?: number;
  // notes
  notes?: string;
  paymentStatementNo?: string;
  numberOfDeferrableDays?: string;
  deferrableDurationStartDate?: Date;
  treasuryAccountNo?: string;
  treasuryName?: string;
  arrearagesRate?: string;
  dateOfIssuedStatement?: Date;

  subjectImportCode?: string;
  numberImport?: number;
  totalImportTax?: number;
  subjectVATCode?: string;
  numberVAT?: number;
  totalVATTax?: number;
  subjectEnvironmentCode?: string;
  numberEnvironment?: number;
  totalEnvironmentTax?: number;
  subjectSpecialConsumptionCode?: string;
  numberSpecialConsumption?: number;
  totalSpecialConsumptionTax?: number;

  totalTax?: number;
  termOfPayment?: string;

  otherLawCode?: string;
  otherLawCode2?: string;
  otherLawCode3?: string;
  // create information
  valueClearanceVND?: number;
  priceVND?: number;
  serviceId?: number;
  customerBusinessId?: string;
  customerPersonalId?: string;
  classify?: string;
  dateCustomClearance?: string;
  dateCheckin?: string;
  dateClearanced?: string;
  dateCheckout?: string;
  phase?: number;
  phase_name?: any;
  times?: number;
  originalOrderNumberClient?: string;
  importerFullName?: string;
  tempTimes?: number;
  inspectionKindTimes?: number; //số lần in tờ khai phân luồng
  clearanceDeclarationTimes?: number; // sỗ lần in bảng kê
  clearancedTimes?: number; // số lần in tờ khai thông quan
  dateAction?: string;
  typeAction?: string;
  terminalName?: string;
  ftpId?: number;
  // clearanceMsg?: string;

  electronicInvoiceReceiptNo?: string;
  releaseBeforePermitRequestReasonCode?: string;
  isIDCed?: boolean;
  isEditProcessing?: boolean;
  isIIDAed?: boolean;

  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  isDeleted?: boolean;
  importDetails?: ImportDetailArchive[];
  importDetailItems?: ImportDetailArchive[];
  country?: Country;
  customerBusiness?: CustomerBusiness;
  customerPersonal?: CustomerPersonal;
  hubId?: number;
  HAWBClearance?: string;

  holds?: Hold[];
  reasonIds?: any;
  isHold?: boolean;
  declarationNoCustomer?: string;
  invoiceNo?: string;
  invoiceDate?: string;
  manifest?: ManifestArchive;
  warehouseAddress?: string;
  warehouseCheckin?: string;
  warehouseCheckout?: string;
  permitLicenses?: any;
  clientId?: number;
  noteHold?: string;
  employeeUpdateCargoName?: number;
  employeeUpdateCargo?: Employee;
  hub?: Hub;
  taxCodeNumber?: string;
  orderId?: string;
  order?: OrderArchive;
  dateTaxCodeNumber?: string;

  originalPrice?: number;
  currencyOriginalPrice?: string;
  internalBoxName?: string;
  externalBoxName?: string;
  dataType?: string;
  orderNumber?: string;
  currentStation?: number;
  childOrderNumber?: string;
  invoiceCustomer?: string;
  labelCustomer?: string;
  printDate?: string;
  totalPrint?: number;

  actionSendV5?: number;
  identity?: string;
  threadCode?: string;
  threadName?: string;
  threadColor?: string;
  threadUrl?: string;
  orderTypeId?: number;
  isPrioritize?: boolean;
  HAWBs?: any;
  client?: Client;
}

export class IDAArchive extends BaseModel implements IIDAArchive {
  public static readonly ModelName: string = 'IDAArchive';
  public static readonly TableName: string = 'clearance_idas_archive';
  public static readonly DefaultScope: FindOptions = {
    attributes: {
      exclude: ['deletedAt', 'isDeleted']
    }
  };

  public HAWB!: string;
  public MAWB!: string;
  public declarationNo!: string;
  public stationId!: number;
  public declarationKindCode!: string;
  public firstDeclarationNo!: string;
  public cargoClassificationCode!: string;
  public meansOfTransportationCode!: number;
  public classificationOfIndividualOrganization!: number;
  public inspectionKindClassification!: string;
  public representativeTaxCode!: string;
  public customsOffice!: string;
  public customsOfficeName!: string;
  public customsSubSection!: string;
  public declarationPlannedDate!: string;
  public registeredTime!: string;
  public registeredDateOfCorrection!: string;
  public registeredTimeOfCorrection!: string;
  public importerCode!: string;
  public importerName!: string;
  public postCode!: string;
  public addressOfImporter!: string;
  public telephoneNumberOfImporter!: string;
  public importContractorCode!: string;
  public importContractorName!: string;
  public consignorCode!: string;
  public consignorName!: string;
  public postCodeIdentification!: string;
  public address1!: string;
  public address2!: string;
  public address3!: string;
  public address4!: string;
  public countryCode!: string;
  public exportConsignerName!: string;
  public plannedDeclarantCode!: string;
  public agentCode!: string;
  public agentName!: string;
  public customsBrokerCode!: string;
  public nameHeadCustoms!: string;
  public cargoNo!: string;
  public cargoPiece!: number;
  public pieceUnitCode!: string;
  public cargoWeightGross!: number;
  public weightUnitCodeGross!: string;
  public customsWarehouseCode!: string;
  public customsClearanceWarehouseName!: string;
  public loadingVesselAircraftName!: string;
  public arrivalDate!: string;
  public unloadingPortCode!: string;
  public unloadingPortName!: string;
  public loadingLocationCode!: string;
  public loadingLocationName!: string;
  public invoiceClassificationCode!: string;

  public invoicePriceKindCode!: string;
  public invoicePriceConditionCode!: string;
  public invoiceCurrencyCode!: string;
  public totalInvoicePrice!: number;
  public valuationDeclarationDemarcationCode!: number;
  public totalOfTaxValue!: number;
  public totalOfProportional!: number;
  public totalBasicPrice!: string;
  public totalOfProportionalDistributionOnTaxValue!: number;
  public taxPayer!: number;
  public codeOfExtendingDueDateForPayment!: string;
  public paymentClassification!: string;
  public structure!: number;
  public numberOfDeclarationColumn!: number;

  public freightDemarcationCode!: string;
  public freightCurrencyCode!: string;
  public freight!: number;
  public freightExchangeRate!: number;
  public insuranceDemarcationCode!: string;
  public managementNumberUser!: string;

  public dateOfPermit!: string;
  public timeOfPermit!: string;
  public dateCompletionInspection!: string;
  public timeCompletionInspection!: string;
  public isError!: boolean;
  public isECUSError!: boolean;
  public messageErrorName!: string;
  public messageError!: string;
  public isMessageTax!: boolean;
  public feeClearance!: number;
  // notes
  public notes!: string;
  public paymentStatementNo!: string;
  public numberOfDeferrableDays!: string;
  public deferrableDurationStartDate!: Date;
  public treasuryAccountNo!: string;
  public treasuryName!: string;
  public arrearagesRate!: string;
  public dateOfIssuedStatement!: Date;
  public subjectImportCode!: string;
  public numberImport!: number;
  public totalImportTax!: number;
  public subjectVATCode!: string;
  public numberVAT!: number;
  public totalVATTax!: number;
  public subjectEnvironmentCode!: string;
  public numberEnvironment!: number;
  public totalEnvironmentTax!: number;
  public subjectSpecialConsumptionCode!: string;
  public numberSpecialConsumption!: number;
  public totalSpecialConsumptionTax!: number;
  public totalTax!: number;
  public termOfPayment!: string;
  public otherLawCode!: string;
  public otherLawCode2!: string;
  public otherLawCode3!: string;
  // create information
  public valueClearanceVND!: number;
  public priceVND!: number;
  public serviceId!: number;
  public customerBusinessId!: string;
  public customerPersonalId!: string;
  public classify!: string;
  public dateCustomClearance!: string;
  public dateCheckin!: string;
  public dateClearanced!: string;
  public dateCheckout!: string;
  public phase!: number;
  public phase_name!: any;
  public times!: number;
  public tempTimes!: number;
  public originalOrderNumberClient!: string;
  public importerFullName!: string;
  public inspectionKindTimes!: number;
  public clearanceDeclarationTimes!: number;
  public clearancedTimes!: number;
  public dateAction!: string;
  public typeAction!: string;
  public terminalName!: string;
  public ftpId!: number;
  // public clearanceMsg!: string;

  public electronicInvoiceReceiptNo!: string;
  public releaseBeforePermitRequestReasonCode!: string;
  public isIDCed!: boolean;
  public isEditProcessing?: boolean;
  public isIIDAed?: boolean;

  public createdAt!: string;
  public updatedAt!: string;
  public deletedAt!: string;
  public isDeleted!: boolean;
  public importDetails?: ImportDetailArchive[];
  public country?: Country;
  public customerBusiness?: CustomerBusiness;
  public customerPersonal?: CustomerPersonal;
  public hubId?: number;
  public HAWBClearance!: string;
  public reasonIds?: number[];
  public isHold?: boolean;
  public declarationNoCustomer?: string;
  public invoiceNo?: string;
  public invoiceDate?: string;
  public manifest!: ManifestArchive;
  public station!: Station;
  public warehouseAddress?: string;
  public warehouseCheckin?: string;
  public warehouseCheckout?: string;
  public holds?: Hold[];
  public importDetailItems!: ImportDetailArchive[];
  public permitLicenses!: any;
  public clientId?: number;
  public noteHold?: string;
  public employeeUpdateCargoName?: number;
  public employeeUpdateCargo?: Employee;
  public hub!: Hub;
  public taxCodeNumber!: string;
  public orderId!: string;
  public order!: OrderArchive;
  public dateTaxCodeNumber!: string;

  public originalPrice!: number;
  public currencyOriginalPrice!: string;
  public internalBoxName!: string;
  public externalBoxName!: string;

  public dataType!: string;
  public orderNumber!: string;
  public currentStation!: number;
  public childOrderNumber!: string;
  public invoiceCustomer!: string;
  public labelCustomer!: string;
  public printDate!: string;
  public totalPrint!: number;

  public actionSendV5?: number;
  public identity?: string;
  public threadCode?: string;
  public threadName?: string;
  public threadColor?: string;
  public threadUrl?: string;
  public orderTypeId?: number;
  public isPrioritize?: boolean;
  public HAWBs?: any;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        //header
        HAWB: {
          type: DataTypes.STRING(50),
          unique: true,
          primaryKey: true,
          allowNull: true
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        declarationNo: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        stationId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        //
        declarationKindCode: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        firstDeclarationNo: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        cargoClassificationCode: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        meansOfTransportationCode: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        classificationOfIndividualOrganization: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        inspectionKindClassification: {
          // Phan luong
          type: DataTypes.STRING(3),
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('inspectionKindClassification')) {
              return String(that.getDataValue('inspectionKindClassification')).substring(0, 1);
            }
            return that.getDataValue('inspectionKindClassification');
          }
        },
        representativeTaxCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        customsOffice: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        customsOfficeName: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        customsSubSection: {
          type: DataTypes.STRING(4),
          allowNull: true
        },

        declarationPlannedDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        registeredTime: {
          type: DataTypes.TIME,
          allowNull: true
        },
        registeredDateOfCorrection: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        registeredTimeOfCorrection: {
          type: DataTypes.TIME,
          allowNull: true
        },
        importerCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        importerName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        postCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        addressOfImporter: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        telephoneNumberOfImporter: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        importContractorCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        importContractorName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        consignorCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        consignorName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        postCodeIdentification: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        address1: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        address2: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        address3: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        address4: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        countryCode: {
          type: DataTypes.STRING(5),
          allowNull: true
        },
        exportConsignerName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        plannedDeclarantCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        agentCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        agentName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        customsBrokerCode: {
          // Mã nhân viên hải quan
          type: DataTypes.STRING(255),
          allowNull: true
        },
        nameHeadCustoms: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        cargoNo: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        cargoPiece: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        pieceUnitCode: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        cargoWeightGross: {
          type: DataTypes.DECIMAL(12, 4),
          allowNull: true
        },
        weightUnitCodeGross: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        customsWarehouseCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        customsClearanceWarehouseName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        loadingVesselAircraftName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        arrivalDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        unloadingPortCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        unloadingPortName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        loadingLocationCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        loadingLocationName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        invoiceClassificationCode: {
          type: DataTypes.STRING(2),
          allowNull: true
        },

        // invoice price
        invoicePriceKindCode: {
          // phân loại giá hóa đơn
          type: DataTypes.STRING(2),
          allowNull: true
        },
        invoicePriceConditionCode: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        invoiceCurrencyCode: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        totalInvoicePrice: {
          type: DataTypes.DECIMAL(26, 6),
          allowNull: true
        },
        valuationDeclarationDemarcationCode: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalOfTaxValue: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        totalOfProportional: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        totalBasicPrice: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        totalOfProportionalDistributionOnTaxValue: {
          type: DataTypes.DECIMAL(26, 6),
          allowNull: true
        },
        taxPayer: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        codeOfExtendingDueDateForPayment: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        paymentClassification: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        structure: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        numberOfDeclarationColumn: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        //bao hiểm
        freightDemarcationCode: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        freightCurrencyCode: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        freight: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        freightExchangeRate: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        insuranceDemarcationCode: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        managementNumberUser: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        // end bao hiểm
        dateOfPermit: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        timeOfPermit: {
          type: DataTypes.TIME,
          allowNull: true
        },
        dateCompletionInspection: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        timeCompletionInspection: {
          type: DataTypes.TIME,
          allowNull: true
        },
        isError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isECUSError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        messageErrorName: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        messageError: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        isMessageTax: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        feeClearance: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        // notes
        notes: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        paymentStatementNo: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        numberOfDeferrableDays: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        deferrableDurationStartDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        treasuryAccountNo: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        treasuryName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        arrearagesRate: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        dateOfIssuedStatement: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        subjectImportCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        numberImport: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalImportTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        subjectVATCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        numberVAT: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalVATTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        subjectEnvironmentCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        numberEnvironment: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalEnvironmentTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        subjectSpecialConsumptionCode: {
          type: DataTypes.STRING(3),
          allowNull: true
        },
        numberSpecialConsumption: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        totalSpecialConsumptionTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        totalTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0,
          get() {
            const that: any = this;
            if (that.getDataValue('totalTax') !== null) {
              return parseFloat(that.getDataValue('totalTax'));
            }
            return that.getDataValue('totalTax');
          }
        },
        termOfPayment: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        otherLawCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        otherLawCode2: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        otherLawCode3: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        // create information
        valueClearanceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        priceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        serviceId: {
          type: DataTypes.INTEGER,
          allowNull: false
        },
        customerBusinessId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        customerPersonalId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        classify: {
          // phân loại DOCUMENT, PARCEL
          type: DataTypes.STRING(4),
          allowNull: true,
          defaultValue: 'PAR'
        },
        dateCustomClearance: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCustomClearance')) {
              return moment(that.getDataValue('dateCustomClearance')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCustomClearance');
          }
        },
        dateCheckin: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCheckin')) {
              return moment(that.getDataValue('dateCheckin')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCheckin');
          }
        },
        dateClearanced: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateClearanced')) {
              return moment(that.getDataValue('dateClearanced')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateClearanced');
          }
        },
        dateCheckout: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCheckout')) {
              return moment(that.getDataValue('dateCheckout')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCheckout');
          }
        },
        dateTaxCodeNumber: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateTaxCodeNumber')) {
              return moment(that.getDataValue('dateTaxCodeNumber')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateTaxCodeNumber');
          }
        },
        phase: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        phase_name: {
          type: DataTypes.VIRTUAL,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('phase') !== null) {
              return ActionName.get(that.getDataValue('phase'));
            }
            return that.getDataValue('phase');
          }
        },
        times: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        tempTimes: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        originalOrderNumberClient: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        importerFullName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        inspectionKindTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        clearanceDeclarationTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        clearancedTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        terminalName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        ftpId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        // clearanceMsg: {
        //   type: DataTypes.STRING, allowNull: true
        // },
        dateAction: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateAction')) {
              return moment(that.getDataValue('dateAction')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateAction');
          }
        },
        typeAction: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        electronicInvoiceReceiptNo: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        releaseBeforePermitRequestReasonCode: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        isIDCed: {
          // đánh dấu IDA đã gửi IDC
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isEditProcessing: {
          // đánh dấu IDA0x phải chờ ECUS xử lý xong mới được IDA0x tiếp
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isIIDAed: {
          // đánh đấu IDA đã IDA0x rồi mới được IDE
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        hubId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        HAWBClearance: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        reasonIds: {
          type: DataTypes.ARRAY(DataTypes.INTEGER),
          allowNull: true
        },
        isHold: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: true
        },
        declarationNoCustomer: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        invoiceNo: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        invoiceDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        warehouseAddress: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        clientId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        warehouseCheckin: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('warehouseCheckin')) {
              return moment(that.getDataValue('warehouseCheckin')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('warehouseCheckin');
          }
        },
        warehouseCheckout: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('warehouseCheckout')) {
              return moment(that.getDataValue('warehouseCheckout')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('warehouseCheckout');
          }
        },
        permitLicenses: {
          type: DataTypes.ARRAY(DataTypes.JSON),
          allowNull: true
        },
        noteHold: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        employeeUpdateCargoName: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        taxCodeNumber: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        orderId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        originalPrice: {
          type: DataTypes.DECIMAL(20, 3),
          allowNull: true
        },
        currencyOriginalPrice: {
          type: DataTypes.STRING(5),
          allowNull: true
        },
        internalBoxName: {
          type: DataTypes.STRING(33),
          allowNull: true
        },
        externalBoxName: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        currentStation: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        orderNumber: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        childOrderNumber: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        invoiceCustomer: {
          type: DataTypes.STRING(5000),
          allowNull: true
        },
        labelCustomer: {
          type: DataTypes.STRING(5000),
          allowNull: true
        },
        printDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        actionSendV5: {
          type: DataTypes.SMALLINT
        },
        orderTypeId: {
          type: DataTypes.INTEGER,
          allowNull: true,
          defaultValue: 0,
        },
        createdAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('createdAt')) {
              return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('createdAt');
          }
        },
        totalPrint: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        identity: {
          type: DataTypes.STRING(1000),
          allowNull: true
        },
        threadCode: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        threadName: {
          type: DataTypes.STRING(200),
          allowNull: true
        },
        threadColor: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        threadUrl: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        isPrioritize: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('updatedAt')) {
              return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('updatedAt');
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('deletedAt')) {
              return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('deletedAt');
          }
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        timestamps: true,
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          country: {
            include: [
              {
                model: Country,
                as: 'country'
              }
            ]
          },
          importDetails: {
            include: [
              {
                model: ImportDetailArchive,
                as: 'importDetails',
                order: [['createdAt', 'ASC']]
              }
            ]
          },
          importDetailItems: {
            include: [
              {
                model: ImportDetailArchive,
                as: 'importDetailItems',
                attributes: ['itemName', 'itemNameVN', 'productId', 'url', 'originalUrl', 'originalProductName', 'HSCode']
              }
            ]
          },
          customerBusiness: {
            include: [
              {
                model: CustomerBusiness,
                as: 'customerBusiness'
              }
            ]
          },
          customerPersonal: {
            include: [
              {
                model: CustomerPersonal,
                as: 'customerPersonal'
              }
            ]
          },
          manifest: {
            include: [
              {
                model: ManifestArchive,
                as: 'manifest',
                include: [
                  {
                    model: SortLabel,
                    as: 'sortLabel'
                  }
                ]
              }
            ]
          },
          holds: {
            include: [
              {
                model: Hold,
                as: 'holds',
                attributes: ['name'],
                on: Sequelize.literal('"holds"."id" = any("IDAArchive"."reasonIds")')
              }
            ]
          },
          station: {
            include: [
              {
                model: Station,
                as: 'station'
              }
            ]
          },
          employeeUpdateCargo: {
            include: [
              {
                model: Employee,
                as: 'employeeUpdateCargo',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['firstname', 'lastname']
                  }
                ]
              }
            ]
          },
          hub: {
            include: [
              {
                model: Hub,
                as: 'hub'
              }
            ]
          },
          order: {
            include: [
              {
                model: OrderArchive,
                as: 'order'
              }
            ]
          },
          client: {
            include: [
              {
                model: Client,
                as: 'client'
              }
            ]
          },
          shipment: {
            include: [
              {
                model: Shipment,
                as: 'shipment'
              }
            ]
          }
        }
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
    IDAArchive.hasMany(ImportDetailArchive, { as: 'importDetails', foreignKey: 'HAWB', sourceKey: 'HAWB' });
    IDAArchive.hasMany(ImportDetailArchive, { as: 'importDetailItems', foreignKey: 'HAWB', sourceKey: 'HAWB' });
    IDAArchive.belongsTo(ManifestArchive, { as: 'manifest', foreignKey: 'HAWB' });
    IDAArchive.belongsTo(Country, { as: 'country', foreignKey: 'countryCode', targetKey: 'code' });
    IDAArchive.belongsTo(CustomerPersonal, { as: 'customerPersonal', foreignKey: 'customerPersonalId' });
    IDAArchive.belongsTo(CustomerBusiness, { as: 'customerBusiness', foreignKey: 'customerBusinessId' });
    
    IDAArchive.hasMany(Hold, { as: 'holds', constraints: false, foreignKey: 'reasonIds' });
    IDAArchive.belongsTo(Station, { as: 'station', foreignKey: 'stationId' });
    IDAArchive.belongsTo(Employee, { as: 'employeeUpdateCargo', foreignKey: 'employeeUpdateCargoName' });
    IDAArchive.belongsTo(Hub, { as: 'hub', foreignKey: 'hubId' });

    IDAArchive.belongsTo(OrderArchive, { as: 'order', foreignKey: 'orderId' });
    IDAArchive.belongsTo(Client, { as: 'client', foreignKey: 'clientId' });

    IDAArchive.belongsTo(Shipment, { as: 'shipment', foreignKey: 'MAWB', targetKey: 'MAWB' });
  }
}
