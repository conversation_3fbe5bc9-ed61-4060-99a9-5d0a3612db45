'use strict';

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import moment from 'moment';
import EConfigure from '../emuns/configures';
import { ActionName } from '../emuns/action';
import { Country, SortLabel, Station, Employee, User, Client, Shipment, ImportDetailArchive, ManifestArchive } from './index.model';
import { CustomerBusiness } from './index.model';
import { CustomerPersonal } from './index.model';
import { Hold } from './hold.model';
import { Hub } from './hub.model';
import { Order } from './order.model';

export interface IMICArchive {
  HAWB?: string;
  MAWB?: string;
  flightNo?: string;
  declarationNo?: string;
  classificationIndividualOrganization?: number;
  classificationOfIndividualOrganization?: number;
  inspectionKindClassification?: string;
  customsOffice?: string;
  customsOfficeName?: string;
  customsSubSection?: string;
  stationId?: number;
  declarationPlannedDate?: string;
  registeredTime?: string;
  registeredDateCorrection?: string;
  registeredTimeCorrection?: string;
  importerCode?: string;
  importerName?: string;
  postCode?: string;
  addressOfImporter?: string;
  telephoneNumberOfImporter?: string;
  consignorCode?: string;
  consignorName?: string;
  postCodeIdentification?: string;
  address1?: string;
  address2?: string;
  address3?: string;
  address4?: string;
  countryCode?: string;
  agentCode?: string;
  agentName?: string;
  customsBrokerCode?: string;
  nameHeadCustomsOffice?: string;
  cargoPiece?: number;
  cargoWeight?: number;
  customsWarehouseCode?: string;
  customsClearanceName?: string;
  arrivalDate?: string;
  unloadingPort?: string;
  portOfDischarge?: string;
  portOfDischargeName?: string;
  loadingLocationCode?: string;
  loadingLocationName?: string;
  currencyCode?: string;
  currencyExchangeRate?: number;
  invoicePriceKind?: string;
  invoicePriceCondition?: string;
  invoiceCurrencyCode?: string;
  totalInvoicePrice?: number;
  freightDemarcation?: string;
  freightCurrency?: string;
  freight?: number;
  insuranceDemarcation?: string;
  insuranceCurrency?: string;
  insuranceAmount?: number;
  dateOfPermit?: string;
  timeOfPermit?: string;
  dateOfCompletion?: string;
  timeCompletion?: string;
  isError?: boolean;
  isECUSError?: boolean;
  messageErrorName?: string;
  messageError?: string;
  notes?: string;
  paymentStatementNo?: string;
  treasuryAccountNo?: string;
  treasuryName?: string;
  dateIssuedStatement?: Date;
  valueClearanceVND?: number;
  priceVND?: number;
  serviceId?: number;
  customerBusinessId?: string;
  customerPersonalId?: string;
  classify?: string;
  dateCustomClearance?: string;
  dateCheckin?: string;
  dateClearanced?: string;
  dateCheckout?: string;
  phase?: number;
  phase_name?: any;
  times?: number;
  originalOrderNumberClient?: string;
  importerFullName?: string;
  inspectionKindTimes?: number;
  clearanceDeclarationTimes?: number;
  clearancedTimes?: number;
  dateAction?: string;
  typeAction?: string;
  terminalName?: string;
  ftpId?: number;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  isDeleted?: boolean;
  importDetails?: ImportDetailArchive[];
  importDetailItems?: ImportDetailArchive[];
  country?: Country;
  customerBusiness?: CustomerBusiness;
  customerPersonal?: CustomerPersonal;
  hubId?: number;
  HAWBClearance?: string;
  reasonIds?: number[];
  isHold?: boolean;
  holds?: Hold[];
  manifest?: ManifestArchive;
  warehouseAddress?: string;
  warehouseCheckin?: string;
  warehouseCheckout?: string;
  clientId?: number;
  noteHold?: string;
  employeeUpdateCargoName?: number;
  employeeUpdateCargo?: Employee;
  hub?: Hub;
  orderId?: string;
  order?: Order;
  originalPrice?: number;
  currencyOriginalPrice?: string;
  internalBoxName?: string;
  externalBoxName?: string;
  dataType?: string;
  orderNumber?: string;
  currentStation?: number;
  childOrderNumber?: string;
  invoiceCustomer?: string;
  labelCustomer?: string;
  identity?: string;
  threadCode?: string;
  threadName?: string;
  threadColor?: string;
  threadUrl?: string;
  orderTypeId?: number;
  isPrioritize?: boolean;
  client?: Client;
}

export class MICArchive extends BaseModel implements IMICArchive {
  public static readonly ModelName: string = 'MICArchive';
  public static readonly TableName: string = 'clearance_mics_archive';
  public static readonly DefaultScope: FindOptions = {
    attributes: {
      exclude: ['deletedAt', 'isDeleted']
    }
  };

  public HAWB!: string;
  public MAWB!: string;
  public flightNo!: string;
  public declarationNo!: string;
  public classificationIndividualOrganization!: number;
  public classificationOfIndividualOrganization!: number;
  public inspectionKindClassification!: string;
  public customsOffice!: string;
  public customsOfficeName!: string;
  public customsSubSection!: string;
  public stationId!: number;
  public declarationPlannedDate!: string;
  public registeredTime!: string;
  public registeredDateCorrection!: string;
  public registeredTimeCorrection!: string;
  public importerCode!: string;
  public importerName!: string;
  public postCode!: string;
  public addressOfImporter!: string;
  public telephoneNumberOfImporter!: string;
  public consignorCode!: string;
  public consignorName!: string;
  public postCodeIdentification!: string;
  public address1!: string;
  public address2!: string;
  public address3!: string;
  public address4!: string;
  public countryCode!: string;
  public agentCode!: string;
  public agentName!: string;
  public customsBrokerCode!: string;
  public nameHeadCustomsOffice!: string;
  public cargoPiece!: number;
  public cargoWeight!: number;
  public customsWarehouseCode!: string;
  public customsClearanceName!: string;
  public arrivalDate!: string;
  public unloadingPort!: string;
  public portOfDischarge!: string;
  public portOfDischargeName!: string;
  public loadingLocationCode!: string;
  public loadingLocationName!: string;
  public currencyCode!: string;
  public currencyExchangeRate!: number;
  public invoicePriceKind!: string;
  public invoicePriceCondition!: string;
  public invoiceCurrencyCode!: string;
  public totalInvoicePrice!: number;
  public freightDemarcation!: string;
  public freightCurrency!: string;
  public freight!: number;
  public insuranceDemarcation!: string;
  public insuranceCurrency!: string;
  public insuranceAmount!: number;
  public dateOfPermit!: string;
  public timeOfPermit!: string;
  public dateOfCompletion!: string;
  public timeCompletion!: string;
  public isError!: boolean;
  public isECUSError!: boolean;
  public messageErrorName!: string;
  public messageError!: string;
  public notes!: string;
  public paymentStatementNo!: string;
  public treasuryAccountNo!: string;
  public treasuryName!: string;
  public dateIssuedStatement!: Date;
  public valueClearanceVND!: number;
  public priceVND!: number;
  public serviceId!: number;
  public customerBusinessId!: string;
  public customerPersonalId!: string;
  public classify!: string;
  public dateCustomClearance!: string;
  public dateCheckin!: string;
  public dateClearanced!: string;
  public dateCheckout!: string;
  public phase!: number;
  public phase_name!: any;
  public times!: number;
  public originalOrderNumberClient!: string;
  public importerFullName!: string;
  public inspectionKindTimes!: number;
  public clearanceDeclarationTimes!: number;
  public clearancedTimes!: number;
  public dateAction!: string;
  public typeAction!: string;
  public terminalName!: string;
  public ftpId!: number;
  public createdAt!: string;
  public updatedAt!: string;
  public deletedAt!: string;
  public isDeleted!: boolean;
  public importDetails?: ImportDetailArchive[];
  public country?: Country;
  public customerBusiness?: CustomerBusiness;
  public customerPersonal?: CustomerPersonal;
  public hubId?: number;
  public HAWBClearance?: string;
  public reasonIds?: number[];
  public isHold?: boolean;
  public holds?: Hold[];
  public manifest!: ManifestArchive;
  public warehouseAddress?: string;
  public warehouseCheckin?: string;
  public warehouseCheckout?: string;
  public importDetailItems!: ImportDetailArchive[];
  public station!: Station;
  public clientId?: number;
  public noteHold?: string;
  public employeeUpdateCargoName?: number;
  public employeeUpdateCargo?: Employee;
  public hub!: Hub;
  public orderId!: string;
  public order!: Order;

  public originalPrice!: number;
  public currencyOriginalPrice!: string;
  public internalBoxName!: string;
  public externalBoxName!: string;
  public dataType!: string;

  public orderNumber!: string;
  public currentStation!: number;
  public childOrderNumber!: string;
  public invoiceCustomer!: string;
  public labelCustomer!: string;
  public identity?: string;
  public threadCode?: string;
  public threadName?: string;
  public threadColor?: string;
  public threadUrl?: string;
  public isPrioritize?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        //header
        HAWB: {
          type: DataTypes.STRING(50),
          unique: true,
          primaryKey: true,
          allowNull: true
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        flightNo: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        declarationNo: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        //
        classificationIndividualOrganization: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        classificationOfIndividualOrganization: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        inspectionKindClassification: {
          type: DataTypes.STRING(1),
          allowNull: true
        },
        customsOffice: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        customsOfficeName: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        customsSubSection: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        stationId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        declarationPlannedDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        registeredTime: {
          type: DataTypes.TIME,
          allowNull: true
        },
        registeredDateCorrection: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        registeredTimeCorrection: {
          type: DataTypes.TIME,
          allowNull: true
        },
        importerCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        importerName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        postCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        addressOfImporter: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        telephoneNumberOfImporter: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        consignorCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        consignorName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        postCodeIdentification: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        address1: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        address2: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        address3: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        address4: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        countryCode: {
          type: DataTypes.STRING(5),
          allowNull: true
        },
        agentCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        agentName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        customsBrokerCode: {
          // Mã nhân viên hải quan
          type: DataTypes.STRING(255),
          allowNull: true
        },
        nameHeadCustomsOffice: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        //MIC Cargo
        cargoPiece: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        cargoWeight: {
          type: DataTypes.DECIMAL(12, 3),
          allowNull: true
        },
        customsWarehouseCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        customsClearanceName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        arrivalDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        unloadingPort: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        portOfDischarge: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        portOfDischargeName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        loadingLocationCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        loadingLocationName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        currencyCode: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        currencyExchangeRate: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        // invoice price
        invoicePriceKind: {
          // phân loại giá hóa đơn
          type: DataTypes.STRING(2),
          allowNull: true
        },
        invoicePriceCondition: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        invoiceCurrencyCode: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        totalInvoicePrice: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        // end invoice price
        //bao hiểm
        freightDemarcation: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        freightCurrency: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        freight: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        // end bao hiểm
        insuranceDemarcation: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        insuranceCurrency: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        insuranceAmount: {
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        dateOfPermit: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        timeOfPermit: {
          type: DataTypes.TIME,
          allowNull: true
        },
        dateOfCompletion: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        timeCompletion: {
          type: DataTypes.TIME,
          allowNull: true
        },
        isError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isECUSError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        messageErrorName: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        messageError: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        // notes
        notes: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        paymentStatementNo: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        treasuryAccountNo: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        treasuryName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        dateIssuedStatement: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        // create information
        valueClearanceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        priceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        serviceId: {
          type: DataTypes.INTEGER,
          allowNull: false
        },
        customerBusinessId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        customerPersonalId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        classify: {
          // phân loại DOCUMENT, PARCEL
          type: DataTypes.STRING(4),
          allowNull: true,
          defaultValue: 'PAR'
        },
        dateCustomClearance: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCustomClearance')) {
              return moment(that.getDataValue('dateCustomClearance')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCustomClearance');
          }
        },
        dateCheckin: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCheckin')) {
              return moment(that.getDataValue('dateCheckin')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCheckin');
          }
        },
        dateClearanced: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateClearanced')) {
              return moment(that.getDataValue('dateClearanced')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateClearanced');
          }
        },
        dateCheckout: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCheckout')) {
              return moment(that.getDataValue('dateCheckout')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCheckout');
          }
        },
        phase: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        phase_name: {
          type: DataTypes.VIRTUAL,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('phase') !== null) {
              return ActionName.get(that.getDataValue('phase'));
            }
            return that.getDataValue('phase');
          }
        },
        times: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        originalOrderNumberClient: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        importerFullName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        inspectionKindTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        clearanceDeclarationTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        clearancedTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        terminalName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        ftpId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        dateAction: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateAction')) {
              return moment(that.getDataValue('dateAction')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateAction');
          }
        },
        typeAction: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        hubId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        HAWBClearance: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        reasonIds: {
          type: DataTypes.ARRAY(DataTypes.INTEGER),
          allowNull: true
        },
        isHold: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: true
        },
        warehouseAddress: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        clientId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        warehouseCheckin: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('warehouseCheckin')) {
              return moment(that.getDataValue('warehouseCheckin')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('warehouseCheckin');
          }
        },
        warehouseCheckout: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('warehouseCheckout')) {
              return moment(that.getDataValue('warehouseCheckout')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('warehouseCheckout');
          }
        },
        noteHold: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        employeeUpdateCargoName: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        orderId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        originalPrice: { 
          type: DataTypes.DECIMAL(20, 3),
          allowNull : true
        },
        currencyOriginalPrice: {
          type: DataTypes.STRING(5),
          allowNull : true
        },
        internalBoxName: {
          type: DataTypes.STRING(33),
          allowNull : true
        },
        externalBoxName: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        currentStation: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        orderNumber: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        childOrderNumber: {
          type: DataTypes.STRING(30),
          allowNull : true
        },
        invoiceCustomer: {
          type: DataTypes.STRING(5000),
          allowNull : true
        },
        labelCustomer: {
          type: DataTypes.STRING(5000),
          allowNull : true,
        },
        identity: {
          type: DataTypes.STRING(1000),
          allowNull : true
        },
        threadCode: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        threadName: {
          type: DataTypes.STRING(200),
          allowNull : true
        },
        threadColor: {
          type: DataTypes.STRING(50),
          allowNull : true
        },
        threadUrl:  {
          type: DataTypes.STRING(255),
          allowNull : true
        },
        orderTypeId: {
          type: DataTypes.INTEGER,
          allowNull : true,
          defaultValue: 0,
        },
        isPrioritize: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        createdAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('createdAt')) {
              return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('createdAt');
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('updatedAt')) {
              return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('updatedAt');
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('deletedAt')) {
              return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('deletedAt');
          }
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        timestamps: true,
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          importDetails: {
            include: [
              {
                model: ImportDetailArchive,
                as: 'importDetails'
              }
            ]
          },
          importDetailItems: {
            include: [
              {
                model: ImportDetailArchive,
                as: 'importDetailItems',
                attributes: ['itemName', 'itemNameVN', 'productId', 'url', 'originalUrl', 'originalProductName', 'HSCode']
              }
            ]
          },
          country: {
            include: [
              {
                model: Country,
                as: 'country'
              }
            ]
          },
          customerBusiness: {
            include: [
              {
                model: CustomerBusiness,
                as: 'customerBusiness'
              }
            ]
          },
          customerPersonal: {
            include: [
              {
                model: CustomerPersonal,
                as: 'customerPersonal'
              }
            ]
          },
          manifest: {
            include: [
              {
                model: ManifestArchive,
                as: 'manifest',
                include: [
                  {
                    model: SortLabel,
                    as: 'sortLabel'
                  }
                ]
              }
            ]
          },
          holds: {
            include: [
              {
                model: Hold,
                as: 'holds',
                attributes: ['name'],
                on: Sequelize.literal('"holds"."id" = any("MICArchive"."reasonIds")')
              }
            ]
          },
          station: {
            include: [
              {
                model: Station,
                as: 'station'
              }
            ]
          },
          employeeUpdateCargo: {
            include: [
              {
                model: Employee,
                as: 'employeeUpdateCargo',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['firstname', 'lastname']
                  }
                ]
              }
            ]
          },
          hub: {
            include: [
              {
                model: Hub,
                as: 'hub'
              }
            ]
          },
          order: {
            include: [
              {
                model: Order,
                as: 'order'
              }
            ]
          },
          client: {
            include: [
              {
                model: Client,
                as: 'client'
              }
            ]
          },
          shipment: {
            include: [
              {
                model: Shipment,
                as: 'shipment'
              }
            ]
          }
        }
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
    MICArchive.hasMany(ImportDetailArchive, { as: 'importDetails', foreignKey: 'HAWB', sourceKey: 'HAWB' });
    MICArchive.hasMany(ImportDetailArchive, { as: 'importDetailItems', foreignKey: 'HAWB', sourceKey: 'HAWB' });
    MICArchive.belongsTo(ManifestArchive, { as: 'manifest', foreignKey: 'HAWB' });

    MICArchive.belongsTo(Country, { as: 'country', foreignKey: 'countryCode', targetKey: 'code' });
    MICArchive.belongsTo(CustomerPersonal, { as: 'customerPersonal', foreignKey: 'customerPersonalId' });
    MICArchive.belongsTo(CustomerBusiness, { as: 'customerBusiness', foreignKey: 'customerBusinessId' });
    MICArchive.hasMany(Hold, { as: 'holds', constraints: false, foreignKey: 'reasonIds' });
    MICArchive.belongsTo(Station, { as: 'station', foreignKey: 'stationId' });
    MICArchive.belongsTo(Employee, { as: 'employeeUpdateCargo', foreignKey: 'employeeUpdateCargoName' });
    MICArchive.belongsTo(Hub, { as: 'hub', foreignKey: 'hubId' });
    MICArchive.belongsTo(Order, { as: 'order', foreignKey: 'orderId' });
    MICArchive.belongsTo(Client, { as: 'client', foreignKey: 'clientId' });
    MICArchive.belongsTo(Shipment, { as: 'shipment', foreignKey: 'MAWB', targetKey: 'MAWB' });
  }
}
