'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface ISortLabel {
	id?: number;
  name?: string;
  code?: string;
  sortWeightId?: number;
  sortHAWBId?: number;
  url?: string;
  isDeleted?: boolean;
  readonly createdAt?: Date;
  readonly updatedAt?: Date;
  readonly deletedAt?: Date;
}

export class SortLabel extends BaseModel implements ISortLabel {
	public static readonly ModelName: string = 'SortLabel';
	public static readonly TableName: string = 'sort_labels';
	public static readonly DefaultScope: FindOptions = {
		'attributes': {
			'exclude': ['deletedAt', 'isDeleted']
		}
	};

	public id?: number;
  public name?: string;
  public code?: string;
  public sortWeightId?: number;
  public sortHAWBId?: number;
  public url?: string;
  public isDeleted?: boolean;
  public readonly createdAt?: Date;
  public readonly updatedAt?: Date;
  public readonly deletedAt?: Date;

	// region Static
	public static prepareInit(sequelize: Sequelize) {
		this.init({
			//header
			id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true
			},
			name: {
				type: DataTypes.STRING(255),
				allowNull : false
			},
			sortWeightId: {
				type: DataTypes.SMALLINT,
				allowNull : false
			},
			sortHAWBId: {
				type: DataTypes.SMALLINT,
				allowNull : false,
			},
			url: {
				type: DataTypes.STRING(255),
				allowNull : true,
			},
			// create information
			isDeleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			createdAt: {
				type: DataTypes.DATE,
			},
			updatedAt: {
				type: DataTypes.DATE,
			},
			deletedAt: {
				type: DataTypes.DATE,
			}
		},
		{
			timestamps: true,
			sequelize: sequelize,
			tableName: this.TableName,
			name: {
				singular: this.ModelName,
			},
			defaultScope: this.DefaultScope,
			comment: 'Model for the public accessible data of an import',
			scopes: {
			}
		},
	);
}

	public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
		// place to set model associations
	}
}

