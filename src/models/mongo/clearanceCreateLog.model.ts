
import { model, Schema, Model, Document } from 'mongoose';

export interface IClearanceCreateLog extends Document {
  clearanceCreateLogId?: number;
  data?: any;
  message?: string;
  cause?: string[];
  processResult?: any;
  handleData?: any;
  classify?: string;
  type?: number;
  isSuccess?: boolean;
}

export interface ClearanceCreateLogModel extends Model<IClearanceCreateLog> {
  createLog(data: any): any;
  getAll(data: any): any;
};

const ClearanceCreateLogSchema: Schema = new Schema({
  clearanceCreateLogId: { type: Number, required: true },
  data: { type: Schema.Types.Mixed, required: true },
  message: { type: String, required: true },
  cause: { type: [String] },
  processResult: { type: Schema.Types.Mixed, required: true },
  handleData: { type: Schema.Types.Mixed },
  classify: { type: String },
  type: { type: Number },
  isSuccess: { type: Boolean },
}, 
{ timestamps: true }
);

ClearanceCreateLogSchema.statics.createLog = function (data: any) {
  return this.create(data);
};

ClearanceCreateLogSchema.statics.getAll = async function (data: any) {
  const cursor =  this.find(data).sort({'createdAt': -1}).cursor();
  const logs: any[] = [];
  for (let log = await cursor.next(); log != null; log = await cursor.next()) {
    logs.push(log);
  }
  return logs
};

const ClearanceCreateLogMongo: ClearanceCreateLogModel  = model<IClearanceCreateLog, ClearanceCreateLogModel>('clearance_create_logs', ClearanceCreateLogSchema);
export default ClearanceCreateLogMongo;