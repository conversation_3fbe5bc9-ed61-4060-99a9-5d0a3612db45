'use strict'

import moment from 'moment';
import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { Country } from './index.model';
import EConfigure from '../emuns/configures';

export interface IHub {
  id?: number;
  name?: string;
  nameCompany?: string;
  code?: string;
  address?: string;
  email?: number;
  fax?: string;
  phone?: string;
  countryId?: number;
  stationId?: number;
  url?: string;
  isInternal?: boolean;
  isDeleted?: boolean;
  country?: Country;
  clearanceWarehouseId?: number;
  taxNumber?: string;
  micTaxNumber?: string;
  fullname?: string;
  emicTaxNumber?: string;
  emecTaxNumber?: string;
  readonly createdAt?: Date;
  readonly updatedAt?: Date;
  readonly deletedAt?: Date;
}

export class Hub extends BaseModel implements IHub {
  public static readonly ModelName: string = 'Hub';
  public static readonly TableName: string = 'hubs';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public name!: string;
  public nameCompany!: string;
  public code!: string;
  public address!: string;
  public email!: number;
  public fax!: string;
  public phone!: string;
  public countryId!: number;
  public stationId!: number;
  public url!: string;
  public isInternal!: boolean;
  public isDeleted!: boolean;
  public country!: Country;
  public clearanceWarehouseId!: number;
  public taxNumber!: string;
  public micTaxNumber!: string;
  public fullname!: string;
  public emicTaxNumber!: string;
  public emecTaxNumber!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public readonly deletedAt!: Date;

  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: true
        },
        nameCompany: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        code: {
          type: DataTypes.STRING
        },
        address: {
          type: DataTypes.STRING,
          allowNull: true
        },
        email: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        fax: {
          type: DataTypes.STRING,
          allowNull: true
        },
        phone: {
          type: DataTypes.STRING,
          allowNull: true
        },
        countryId: {
          type: DataTypes.INTEGER,
        },
        isInternal: {
          type: DataTypes.BOOLEAN,
          defaultValue: true
        },
        url: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        stationId: {
          type: DataTypes.SMALLINT,
          allowNull: true,
        },
        clearanceWarehouseId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        taxNumber: {
          type: DataTypes.STRING(255),
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('taxNumber')) {
              return Number(that.getDataValue('taxNumber'));
            }
          }
        },
        micTaxNumber: {
          type: DataTypes.STRING(200),
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('micTaxNumber')) {
              return Number(that.getDataValue('micTaxNumber'));
            }
          }
        },
        emicTaxNumber: {
          type: DataTypes.STRING(200),
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('emicTaxNumber')) {
              return Number(that.getDataValue('emicTaxNumber'));
            }
          }
        },
        emecTaxNumber: {
          type: DataTypes.STRING(200),
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('emecTaxNumber')) {
              return Number(that.getDataValue('emecTaxNumber'));
            }
          }
        },
        fullname: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        createdAt: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          get() {
            const that: any = this;
            if (that.getDataValue('createdAt'))
              return moment(that.getDataValue('createdAt')).format(EConfigure.VIEW_FULL_TIME);
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('updatedAt'))
              return moment(that.getDataValue('updatedAt')).format(EConfigure.VIEW_FULL_TIME);
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('deletedAt'))
              return moment(that.getDataValue('deletedAt')).format(EConfigure.VIEW_FULL_TIME);
          }
        }
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model>; }) {
    // place to set model associations
  }

}
