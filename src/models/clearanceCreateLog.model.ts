'use strict'

import { DataTypes, Sequelize, FindOptions, ModelC<PERSON>, Model } from 'sequelize';
import moment from 'moment';
import EConfigure from '../emuns/configures';
import { ClearanceCreateName } from '../emuns/clearanceCreate';
import { BaseModel } from './base.model';

export interface IClearanceCreateLog {
  id?: number;
  MAWB?: string;
  employeeId?: number;
  clientId?: number;
  serviceId?: number;
  total?: number;
  pending?: number;
  success?: number;
  fail?: number;
  shipmentId?: number;
  type?: number;
  type_name?: string;
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export class ClearanceCreateLog extends BaseModel implements IClearanceCreateLog {
  public static readonly ModelName: string = 'ClearanceCreateLog';
  public static readonly TableName: string = 'clearance_create_logs';
  public static readonly DefaultScope: FindOptions = {};
  
  id!: number;
  MAWB!: string;
  employeeId!: number;
  clientId!: number;
  serviceId!: number;
  total!: number;
  pending!: number;
  success!: number;
  fail!: number;
  shipmentId!: number;
  type!: number;
  type_name!: string;
  isDeleted!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public readonly deletedAt!: Date;
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        // Model attributes are defined here
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull : true,
        },
        employeeId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        clientId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        serviceId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        total: {
          type: DataTypes.INTEGER,
          allowNull : true,
          defaultValue: 0,
        },
        pending: {
          type: DataTypes.INTEGER,
          allowNull : true,
          defaultValue: 0,
        },
        success: {
          type: DataTypes.INTEGER,
          allowNull : true,
          defaultValue: 0,
        },
        fail: {
          type: DataTypes.INTEGER,
          allowNull : true,
          defaultValue: 0,
        },
        shipmentId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        type: {
          type: DataTypes.INTEGER,
          allowNull : true,
        },
        type_name: {
          type: DataTypes.VIRTUAL,
          allowNull : true,
          get() {      
            const that: any = this;
            if(that.getDataValue('type')!== null){
              return ClearanceCreateName.get(that.getDataValue('type'));
            } 
            return that.getDataValue('type');
          }
        },
        // create information
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          get() {      
            const that: any = this;
            if(that.getDataValue('createdAt'))
              return moment(that.getDataValue('createdAt')).format(EConfigure.VIEW_FULL_TIME);
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if(that.getDataValue('updatedAt'))
              return moment(that.getDataValue('updatedAt')).format(EConfigure.VIEW_FULL_TIME);
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if(that.getDataValue('deletedAt'))
              return moment(that.getDataValue('deletedAt')).format(EConfigure.VIEW_FULL_TIME);
          }
        }
      }, {
        // Other model options go here
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        
      }
    )
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}


export default ClearanceCreateLog;