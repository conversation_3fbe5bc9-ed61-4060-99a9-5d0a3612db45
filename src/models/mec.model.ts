'use strict';

import { DataTypes, FindOptions, Model, Model<PERSON><PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { ImportDetail } from './importDetail.model';
import moment from 'moment';
import EConfigure from '../emuns/configures';
import { ActionName } from '../emuns/action';
import { Client, Country, Employee, ExportDetail, Hold, Manifest, Shipment, SortLabel, Station, User } from './index.model';
import { CustomerBusiness } from './index.model';
import { CustomerPersonal } from './index.model';
import { IExportDetailValidate } from './exportDetail.model';
import { Hub } from './hub.model';

export interface IMasterValidate {
  flightNo?: string;
  MAWB?: string;
  departureDate?: string;
  theFinalDestination?: string;
  departureCountry?: string;
  address4?: string;
  countryCode?: string;
  hubId?: number;
  warehouseId?: number;
  meanOfTransportationCode?: number;
}

export interface IMECMaster {
  flightNo?: string;
  MAWB?: string;
  departureDate?: string;
  theFinalDestination?: string;
  departureCountry?: string;
  address4?: string;
  countryCode?: string;
  customsWarehouseCode?: string;
  loadingPortCode?: string;
  customsOffice?: string;
  customsSubSection?: string;
  terminalName?: string;
  ftpId?: number;
  hubId?: number;
}

export interface IExportValidate {
  HAWB?: string;
  HAWBClearance?: string;
  stationId?: number;
  serviceId?: number;
  phase?: number;
  exporterCode?: string;
  exporterName?: string;
  exporterAddress?: string;
  exporterPostCode?: string;
  MAWB?: string;
  departureDate?: string;
  flightNo?: string;

  exporterTelephoneNumber?: string;
  consigneeCode?: string;
  consigneeName?: string;
  consigneeTelephoneNumber?: string;
  consigneeAddress?: string;
  consigneePostCode?: string;
  consigneeCountryCode?: string;

  unitOfMass?: string;
  weight?: number;
  currencyCode?: string;
  totalPrice?: string;
  piece?: number;

  valueClearanceVND?: number;
  priceVND?: number;
  customerBusinessId?: string;
  customerPersonalId?: string;
  classify?: string;
  note?: string;

  hubId?: number;
  warehouseId?: number;
  clientId?: number;
  station?: Station;

  items?: IExportDetailValidate[];

  externalBoxName?: string;
  internalBoxName?: string;
  originalOrderNumberClient?: string;
}

export interface IMECCreate {
  HAWB: string;
  declarationNo?: string;
  stationId: number;
  serviceId: number;
  phase: number;
  departureDate?: string;
  customsOffice?: string;
  customsSubSection?: string;
  exporterCode?: string;
  exporterName?: string;
  exporterFullName?: string;
  addressOfExporter?: string;
  postCode?: string;
  telephoneNumber?: string;
  consigneeCode?: string;
  consigneeName?: string;
  consigneeTelephoneNumber?: string;
  postCodeIdentification?: string;
  address1?: string;
  address2?: string;
  address3?: string;
  address4?: string;
  countryCode?: string;
  cargoPiece?: number;
  cargoWeight?: number;
  customsWarehouseCode?: string;
  theFinalDestination?: string;
  loadingPortCode?: string;
  currencyCodeOfTaxValue?: string;
  totalOfTaxValue?: number;

  MAWB?: string;
  flightNo?: string;

  originalOrderNumberClient?: string;
  valueClearanceVND?: number;
  priceVND?: number;

  customerBusinessId?: string;
  customerPersonalId?: string;
  classify?: string;
  notes?: string;
  HAWBClearance?: string;
  clientId?: any;
  hubId?: number;
  terminalName?: string;
  ftpId?: number;
  inspectionKindClassification?: any;
  dateCheckin?: string;
  dateClearanced?: string;
  dateCheckout?: string;
}

export interface IMEC {
  HAWB?: string;
  MAWB?: string;
  flightNo?: string;
  departureDate?: string;
  declarationNo?: string;
  stationId?: number;
  inspectionKindClassification?: number;
  customsOffice?: string;
  customsOfficeName?: string;
  customsSubSection?: string;
  declarationPlannedDate?: string;
  registeredTime?: string;
  registeredDateCorrection?: string;
  registeredTimeCorrection?: string;
  exporterCode?: string;
  exporterName?: string;
  exporterFullName?: string;
  postCode?: string;
  addressOfExporter?: string;
  telephoneNumber?: string;
  consigneeCode?: string;
  consigneeName?: string;
  consigneeTelephoneNumber?: string;
  postCodeIdentification?: string;
  address1?: string;
  address2?: string;
  address3?: string;
  address4?: string;
  countryCode?: string;
  agentCode?: string;
  agentName?: string;
  customsBrokerCode?: string;
  cargoPiece?: number;
  cargoWeight?: number;
  customsWarehouseCode?: string;
  customsWarehouseName?: string;
  theFinalDestination?: string;
  theFinalDestinationName?: string;
  loadingPortCode?: string;
  loadingPortName?: string;
  currencyCodeOfTaxValue?: string;
  totalOfTaxValue?: number;
  dateOfPermit?: string;
  timeOfPermit?: string;
  nameHeadCustomsOffice?: string;
  notes?: string;
  isError?: boolean;
  isECUSError?: boolean;
  messageErrorName?: string;
  messageError?: string;
  valueClearanceVND?: number;
  priceVND?: number;
  serviceId?: number;
  customerBusinessId?: string;
  customerPersonalId?: string;
  classify?: string;
  dateCheckin?: string;
  dateClearanced?: string;
  dateCheckout?: string;
  phase?: number;
  phase_name?: any;
  times?: number;
  originalOrderNumberClient?: string;
  dateAction?: string;
  typeAction?: string;
  terminalName?: string;
  ftpId?: number;
  customerBusiness: CustomerBusiness;
  customerPersonal?: CustomerPersonal;
  hubId?: number;
  HAWBClearance?: string;
  exportDetails: ExportDetail[];
  exportDetailItems?: ExportDetail[];
  employeeUpdateCargoName?: number;
  employeeUpdateCargo?: Employee;
  isHold?: boolean;
  reasonIds?: number[];
  noteHold?: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  isDeleted?: boolean;
  clientId?: number;
  hub?: Hub;
  manifest?: Manifest;
  country?: Country;
  holds?: Hold[];
  isPrioritize?: boolean;
  externalBoxName?: string;
  internalBoxName?: string;
  isHellmannSuccess?: boolean;
  hellmannSuccessDate?: string;
  warehouseAddress?: string;
  warehouseCheckin?: string;
  warehouseCheckout?: string;
  isSortLane?: boolean;
  totalPrint?: number;
  percentTaxPrint?: string;
}

export class MEC extends BaseModel implements IMEC {
  public static readonly ModelName: string = 'MEC';
  public static readonly TableName: string = 'clearance_mecs';
  public static readonly DefaultScope: FindOptions = {
    attributes: {
      exclude: ['deletedAt', 'isDeleted']
    }
  };

  public HAWB!: string;
  public MAWB!: string;
  public flightNo!: string;
  public departureDate!: string;
  public declarationNo!: string;
  public stationId!: number;
  public inspectionKindClassification!: number;
  public customsOffice!: string;
  public customsOfficeName!: string;
  public customsSubSection!: string;
  public declarationPlannedDate!: string;
  public registeredTime!: string;
  public registeredDateCorrection!: string;
  public registeredTimeCorrection!: string;
  public exporterCode!: string;
  public exporterName!: string;
  public exporterFullName!: string;
  public postCode!: string;
  public addressOfExporter!: string;
  public telephoneNumber!: string;
  public consigneeCode!: string;
  public consigneeName!: string;
  public consigneeTelephoneNumber!: string;
  public postCodeIdentification!: string;
  public address1!: string;
  public address2!: string;
  public address3!: string;
  public address4!: string;
  public countryCode!: string;
  public agentCode!: string;
  public agentName!: string;
  public customsBrokerCode!: string;
  public cargoPiece!: number;
  public cargoWeight!: number;
  public customsWarehouseCode!: string;
  public customsWarehouseName!: string;
  public theFinalDestination!: string;
  public theFinalDestinationName!: string;
  public loadingPortCode!: string;
  public loadingPortName!: string;
  public currencyCodeOfTaxValue!: string;
  public totalOfTaxValue!: number;
  public dateOfPermit!: string;
  public timeOfPermit!: string;
  public nameHeadCustomsOffice!: string;
  public notes!: string;
  public isError!: boolean;
  public isECUSError!: boolean;
  public messageErrorName!: string;
  public messageError!: string;
  public valueClearanceVND!: number;
  public priceVND!: number;
  public serviceId!: number;
  public customerBusinessId!: string;
  public customerPersonalId!: string;
  public classify!: string;
  public dateCheckin!: string;
  public dateClearanced!: string;
  public dateCheckout!: string;
  public phase!: number;
  public phase_name!: any;
  public times!: number;
  public originalOrderNumberClient!: string;
  public dateAction!: string;
  public typeAction!: string;
  public terminalName!: string;
  public ftpId!: number;
  public customerBusiness!: CustomerBusiness;
  public customerPersonal!: CustomerPersonal;
  public createdAt!: string;
  public updatedAt!: string;
  public deletedAt!: string;
  public isDeleted!: boolean;
  public hubId?: number;
  public HAWBClearance?: string;
  public exportDetails!: ExportDetail[];
  public clientId?: number;

  public employeeUpdateCargoName?: number;
  public isHold?: boolean;
  public reasonIds?: number[];
  public noteHold?: string;
  public employeeUpdateCargo?: Employee;
  public country?: Country;
  public exportDetailItems!: ExportDetail[];
  public inspectionKindTimes?: number;
  public clearanceDeclarationTimes?: number;
  public hub!: Hub;
  public manifest?: Manifest;
  public station?: Station;
  public holds?: Hold[];
  public isPrioritize?: boolean;

  public externalBoxName?: string;
  public internalBoxName?: string;
  public isHellmannSuccess?: boolean;
  public hellmannSuccessDate?: string;

  public warehouseAddress?: string;
  public warehouseCheckin?: string;
  public warehouseCheckout?: string;
  public isSortLane!: boolean;
  public totalPrint?: number;
  public percentTaxPrint?: string;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        HAWB: {
          type: DataTypes.STRING(50),
          unique: true,
          primaryKey: true,
          allowNull: true
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        flightNo: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        departureDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        declarationNo: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        stationId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        inspectionKindClassification: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        customsOfficeName: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        customsOffice: {
          type: DataTypes.STRING(8),
          allowNull: true
        },
        customsSubSection: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        declarationPlannedDate: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        registeredTime: {
          type: DataTypes.TIME,
          allowNull: true
        },
        registeredDateCorrection: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        registeredTimeCorrection: {
          type: DataTypes.TIME,
          allowNull: true
        },
        exporterCode: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        exporterName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        exporterFullName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        postCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        addressOfExporter: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        telephoneNumber: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        consigneeCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        consigneeName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        consigneeTelephoneNumber: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        postCodeIdentification: {
          type: DataTypes.STRING(15),
          allowNull: true
        },
        address1: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        address2: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        address3: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        address4: {
          type: DataTypes.STRING(60),
          allowNull: true
        },
        countryCode: {
          type: DataTypes.STRING(5),
          allowNull: true
        },
        agentCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        agentName: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        customsBrokerCode: {
          // Mã nhân viên hải quan
          type: DataTypes.STRING(255),
          allowNull: true
        },
        cargoPiece: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        cargoWeight: {
          type: DataTypes.DECIMAL(12, 3),
          allowNull: true
        },
        customsWarehouseCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        customsWarehouseName: {
          type: DataTypes.STRING(200),
          allowNull: true
        },
        theFinalDestination: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        theFinalDestinationName: {
          type: DataTypes.STRING(200),
          allowNull: true
        },
        loadingPortCode: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        loadingPortName: {
          type: DataTypes.STRING(200),
          allowNull: true
        },
        currencyCodeOfTaxValue: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        totalOfTaxValue: {
          type: DataTypes.DECIMAL(20, 3),
          allowNull: true
        },
        dateOfPermit: {
          type: DataTypes.DATEONLY,
          allowNull: true
        },
        timeOfPermit: {
          type: DataTypes.TIME,
          allowNull: true
        },
        nameHeadCustomsOffice: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        notes: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        // create information
        isError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        isECUSError: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        messageErrorName: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        messageError: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        valueClearanceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        priceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true
        },
        serviceId: {
          type: DataTypes.INTEGER,
          allowNull: false
        },
        customerBusinessId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        customerPersonalId: {
          type: DataTypes.UUID,
          allowNull: true
        },
        clientId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        classify: {
          // phân loại DOCUMENT, PARCEL
          type: DataTypes.STRING(4),
          allowNull: true,
          defaultValue: 'PAR'
        },
        dateCheckin: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCheckin')) {
              return moment(that.getDataValue('dateCheckin')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCheckin');
          }
        },
        dateClearanced: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateClearanced')) {
              return moment(that.getDataValue('dateClearanced')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateClearanced');
          }
        },
        dateCheckout: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateCheckout')) {
              return moment(that.getDataValue('dateCheckout')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateCheckout');
          }
        },
        phase: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        phase_name: {
          type: DataTypes.VIRTUAL,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('phase') !== null) {
              return ActionName.get(that.getDataValue('phase'));
            }
            return that.getDataValue('phase');
          }
        },
        times: {
          type: DataTypes.SMALLINT,
          allowNull: true
        },
        originalOrderNumberClient: {
          type: DataTypes.STRING(255),
          allowNull: true
        },
        terminalName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        ftpId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        dateAction: {
          type: DataTypes.DATE,
          allowNull: true,
          get() {
            const that: any = this;
            if (that.getDataValue('dateAction')) {
              return moment(that.getDataValue('dateAction')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('dateAction');
          }
        },
        typeAction: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        inspectionKindTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        clearanceDeclarationTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        clearancedTimes: {
          type: DataTypes.SMALLINT,
          defaultValue: 0
        },
        hubId: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        HAWBClearance: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        employeeUpdateCargoName: {
          type: DataTypes.INTEGER,
          allowNull: true
        },
        isHold: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: true
        },
        reasonIds: {
          type: DataTypes.ARRAY(DataTypes.INTEGER),
          allowNull: true
        },
        noteHold: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        isPrioritize: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        externalBoxName: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        internalBoxName: {
          type: DataTypes.STRING(33),
          allowNull: true
        },
        isHellmannSuccess: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        hellmannSuccessDate: {
          type: DataTypes.DATE,
          defaultValue: null
        },
        warehouseAddress: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        warehouseCheckin: {
          type: DataTypes.DATE,
          allowNull: true
        },
        warehouseCheckout: {
          type: DataTypes.DATE,
          allowNull: true
        },
        totalPrint: {
          type: DataTypes.INTEGER,
          allowNull: true,
          defaultValue: 0
        },
        percentTaxPrint: {
          type: DataTypes.STRING(10),
          allowNull: true
        },
        createdAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('createdAt')) {
              return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('createdAt');
          }
        },
        isSortLane: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('updatedAt')) {
              return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('updatedAt');
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('deletedAt')) {
              return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
            }
            return that.getDataValue('deletedAt');
          }
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        timestamps: true,
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          exportDetails: {
            include: [
              {
                model: ExportDetail,
                as: 'exportDetails'
              }
            ]
          },
          country: {
            include: [
              {
                model: Country,
                as: 'country'
              }
            ]
          },
          customerBusiness: {
            include: [
              {
                model: CustomerBusiness,
                as: 'customerBusiness'
              }
            ]
          },
          customerPersonal: {
            include: [
              {
                model: CustomerPersonal,
                as: 'customerPersonal'
              }
            ]
          },
          manifest: {
            include: [
              {
                model: Manifest,
                as: 'manifest',
                include: [
                  {
                    model: SortLabel,
                    as: 'sortLabel'
                  }
                ]
              }
            ]
          },
          employeeUpdateCargo: {
            include: [
              {
                model: Employee,
                as: 'employeeUpdateCargo',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['firstname', 'lastname']
                  }
                ]
              }
            ]
          },
          exportDetailItems: {
            include: [
              {
                model: ExportDetail,
                as: 'exportDetailItems',
                attributes: ['itemName', 'itemNameVN', 'productId']
              }
            ]
          },
          station: {
            include: [
              {
                model: Station,
                as: 'station'
              }
            ]
          },
          hub: {
            include: [
              {
                model: Hub,
                as: 'hub'
              }
            ]
          },
          holds: {
            include: [
              {
                model: Hold,
                as: 'holds',
                attributes: ['name'],
                on: Sequelize.literal('"holds"."id" = any("MEC"."reasonIds")')
              }
            ]
          },
          client: {
            include: [
              {
                model: Client,
                as: 'client'
              }
            ]
          },
          shipmentWeight: {
            include: [
              {
                model: Shipment,
                as: 'shipmentWeight',
                attributes: ['weightTotal', 'boxIds']
              }
            ]
          }
        }
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
    MEC.hasMany(ExportDetail, { as: 'exportDetails', foreignKey: 'HAWB', sourceKey: 'HAWB' });
    MEC.belongsTo(Country, { as: 'country', foreignKey: 'countryCode', targetKey: 'code' });
    MEC.belongsTo(CustomerPersonal, { as: 'customerPersonal', foreignKey: 'customerPersonalId' });
    MEC.belongsTo(CustomerBusiness, { as: 'customerBusiness', foreignKey: 'customerBusinessId' });
    MEC.belongsTo(Manifest, { as: 'manifest', foreignKey: 'HAWB' });
    MEC.belongsTo(Employee, { as: 'employeeUpdateCargo', foreignKey: 'employeeUpdateCargoName' });
    MEC.hasMany(ExportDetail, { as: 'exportDetailItems', foreignKey: 'HAWB', sourceKey: 'HAWB' });
    MEC.belongsTo(Station, { as: 'station', foreignKey: 'stationId' });
    MEC.belongsTo(Hub, { as: 'hub', foreignKey: 'hubId' });
    MEC.hasMany(Hold, { as: 'holds', constraints: false, foreignKey: 'reasonIds' });
    MEC.belongsTo(Client, { as: 'client', foreignKey: 'clientId', targetKey: 'id' });
    MEC.belongsTo(Shipment, { as: 'shipmentWeight', foreignKey: 'MAWB', targetKey: 'MAWB' });
  }
}
