'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IParameter {
  id?: number;
  name?: string;
  isActivated?: boolean;
  value?: string;
  unit?: string;
}

export class Parameter extends BaseModel implements IParameter {
  public static readonly ModelName: string = 'Parameter';
  public static readonly TableName: string = 'parameters';
  public static readonly DefaultScope: FindOptions = {};

  public id?: number;
  public name?: string;
  public isActivated?: boolean;
  public value?: string;
  public unit?: string;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: DataTypes.STRING(255),
          unique: true,
          allowNull : false
        },
        isActivated: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        value: {
          type: DataTypes.STRING(50),
          allowNull : false
        },
        unit: {
          type: DataTypes.STRING(50),
          allowNull : true
        }
      },
      {
        sequelize: sequelize,
        timestamps: false,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}