'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { SortLabel } from './index.model';

export interface IManifestArchive {
	HAWB?: string;
	orderNumber?: string;
	orderNumberClient?: string;
	originalOrderNumberClient?: string;
	noHAWB?: number;
	totalHAWB?: number;
	labelId?: number;
	labelNumber?: number;
	weightChargeKG?: number;
	substance?: string;
	isKeepCheckoutClearance?: boolean;
}

export class ManifestArchive extends BaseModel implements IManifestArchive {
	public static readonly ModelName: string = 'ManifestArchive';
	public static readonly TableName: string = 'manifests_archive';
	public static readonly DefaultScope: FindOptions = {
		'attributes': {
			'exclude': ['deletedAt', 'isDeleted']
		}
	};

	HAWB?: string;
	orderNumber?: string;
	orderNumberClient?: string;
	originalOrderNumberClient?: string;
	noHAWB?: number;
	totalHAWB?: number;
	labelId?: number;
	labelNumber?: number;
	weightChargeKG?: number;
	substance?: string;
	isKeepCheckoutClearance?: boolean;


	// region Static
	public static prepareInit(sequelize: Sequelize) {
		this.init({
			//header
			HAWB: {
				type: DataTypes.STRING(50),
				unique : true,
				primaryKey: true,
				allowNull : true
			},
			orderNumber: {
				type: DataTypes.STRING(20),
				allowNull : true,
			},
			orderNumberClient: {
				type: DataTypes.STRING(50),
				unique: true,
				allowNull : true,
			},
			noHAWB: {
				type: DataTypes.SMALLINT,
				allowNull : true,
			},
			totalHAWB: {
				type: DataTypes.SMALLINT,
				allowNull : true,
			},
			labelId: {
				type: DataTypes.INTEGER,
				allowNull : true,
			},
			labelNumber: {
				type: DataTypes.INTEGER,
				allowNull : true,
			},
			weightChargeKG: { //nặng tính phí theo kg
				type: DataTypes.DECIMAL(12, 3),
				allowNull : true,
				defaultValue: 0
			},
			originalOrderNumberClient: {
				type: DataTypes.STRING(50),
				allowNull : true,
			},
			substance: {
				type: DataTypes.STRING(10),
				allowNull: true,
			},
			isKeepCheckoutClearance: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
		},
		{
			timestamps: true,
			sequelize: sequelize,
			tableName: this.TableName,
			name: {
				singular: this.ModelName,
			},
			defaultScope: this.DefaultScope,
			comment: 'Model for the public accessible data of an import',
			scopes: {
				
			}
		},
	);
}

	public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
		// place to set model associations
		ManifestArchive.belongsTo(SortLabel, {as: 'sortLabel' , foreignKey: 'labelId'});
	}
}

