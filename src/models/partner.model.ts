'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';

export interface IPartner {
  id?: number;
  name?: string;
  code?: string;
  countryId?: number;
  url?: string;
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export class Partner extends BaseModel implements IPartner {
  public static readonly ModelName: string = 'Partner';
  public static readonly TableName: string = 'partners';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public name!: string;
  public code!: string;
  public countryId!: number;
  public url!: string;
  public isDeleted!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public readonly deletedAt!: Date;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
        },
        code: {
          type: DataTypes.STRING
        },
        cityId: {
          type: DataTypes.INTEGER,
        },
        url:{
          type: DataTypes.TEXT,
          allowNull : true,
        },
        isNotSendClearance: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updatedAt: {
          type: DataTypes.DATE,
        },
        deletedAt: {
          type: DataTypes.DATE,
        }
      },
      {
        sequelize: sequelize,
        timestamps: false,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}