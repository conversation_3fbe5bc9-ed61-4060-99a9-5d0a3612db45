'use strict'

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize, UUIDV4 } from 'sequelize';
import { BaseModel } from './base.model';
import EConfigure from '../emuns/configures';
import moment = require('moment');

export interface ICustomerPersonal {
  customerId?: string;
  customerPhone?: string;
  customerPassword?: string;
  customerFacebookId?: string;
  customerGoogleId?: string;
  customerSecret?: string;
  customerFirstName?: string;
  customerLastName?: string;
  customerIdentityCard?: string;
  customerGender?: number;
  customerEmail?: string;
  customerBirthDate?: Date;
  customerAddress?: string;
  customerAvatarPath?: string;
  customerStatus?: number;
  isChangePass?: boolean;
  countryId?: number;
  customerTsv?: string;
  customerTypeId?: number;
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export class CustomerPersonal extends BaseModel implements ICustomerPersonal {
  public static readonly ModelName: string = 'CustomerPersonal';
  public static readonly TableName: string = 'customer_personals';
  public static readonly DefaultScope: FindOptions = {};

  public customerId?: string;
  public customerPhone?: string;
  public customerPassword?: string;
  public customerFacebookId?: string;
  public customerGoogleId?: string;
  public customerSecret?: string;
  public customerFirstName?: string;
  public customerLastName?: string;
  public customerIdentityCard?: string;
  public customerGender?: number;
  public customerEmail?: string;
  public customerBirthDate?: Date;
  public customerAddress?: string;
  public customerAvatarPath?: string;
  public customerStatus?: number;
  public isChangePass?: boolean;
  public countryId?: number;
  public customerTsv?: string;
  public customerTypeId?: number;
  public isDeleted?: boolean;
  public createdAt?: Date;
  public updatedAt?: Date;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        customerId: {
          type: DataTypes.UUID,
          primaryKey: true,
          allowNull: false,
          defaultValue: UUIDV4
        },
    
        customerPhone: {
          type: DataTypes.STRING(20)
        },
        customerPassword: {
          type: DataTypes.STRING
        },
        customerFacebookId: {
          type: DataTypes.STRING
        },
        customerGoogleId: {
          type: DataTypes.STRING
        },
        customerSecret: {
          type: DataTypes.STRING(15)
        },
    
        customerFirstName: {
          type: DataTypes.STRING(200)
        },
        customerLastName: {
          type: DataTypes.STRING(200)
        },
        customerIdentityCard: {
          type: DataTypes.STRING(20)
        },
        customerAddress: {
          type: DataTypes.STRING
        },
        customerGender: {
          type: DataTypes.SMALLINT
        },
        customerEmail: {
          type: DataTypes.STRING(100)
        },
        customerBirthDate: {
          type: DataTypes.DATEONLY
        },
        customerAvatarPath: {
          type: DataTypes.STRING
        },
        customerStatus: {
          type: DataTypes.SMALLINT
        },
        isChangePass: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        countryId: {
          type: DataTypes.INTEGER
        },
        customerTsv: {
          type: 'tsvector'
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        createdAt: {
          type: DataTypes.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          get() {      
            const that: any = this;
            if(that.getDataValue('createdAt')){
              return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
            }
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if(that.getDataValue('updatedAt')){
              return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
            }
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if(that.getDataValue('deletedAt')){
              return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
            }
          }
        } 
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
  }
}

