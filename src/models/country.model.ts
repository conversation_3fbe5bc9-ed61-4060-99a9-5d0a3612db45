'use strict'

import { DataTypes, FindOptions, Model, ModelC<PERSON>, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import moment from 'moment';
import EConfigure from '../emuns/configures';

export interface ICountry {
  id?: number
  name?: string;
  prefixPhone?: string;
  code?: string;
  zoneId?: string;
  shortName?: string;
  fullName?: string;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class Country extends BaseModel implements ICountry {
  public static readonly ModelName: string = 'Country';
  public static readonly TableName: string = 'countries';
  public static readonly DefaultScope: FindOptions = {};

  public id?: number
  public name?: string;
  public prefixPhone?: string;
  public code?: string;
  public zoneId?: string;
  public shortName?: string;
  public fullName?: string;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init({
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
      },
      name: {
        type: DataTypes.STRING
      },
      prefixPhone: {
        type: DataTypes.STRING(8),
        allowNull: true
      },
      code: {
        type: DataTypes.STRING(2),
        allowNull: true
      },
      zoneId: {
        type: DataTypes.INTEGER,
      },
      shortName: {
        type: DataTypes.STRING(20),
        allowNull: true
      },
      fullName: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isActivated: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        get() {
          const that: any = this;
          if (that.getDataValue('createdAt')) {
            return moment(that.getDataValue('createdAt')).format(EConfigure.FULL_TIME);
          }
        }
      },
      updatedAt: {
        type: DataTypes.DATE,
        get() {
          const that: any = this;
          if (that.getDataValue('updatedAt')) {
            return moment(that.getDataValue('updatedAt')).format(EConfigure.FULL_TIME);
          }
        }
      },
      deletedAt: {
        type: DataTypes.DATE,
        get() {
          const that: any = this;
          if (that.getDataValue('deletedAt')) {
            return moment(that.getDataValue('deletedAt')).format(EConfigure.FULL_TIME);
          }
        }
      }
    },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
      },
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model>; }) {
    // place to set model associations
  }
}

