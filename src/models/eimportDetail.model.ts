'use strict';

import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import { BaseModel } from './base.model';
import { eMIC } from './index.model';

export interface eIDetailValidate {
  HSCode?: string;
  itemName?: string;
  itemNameVN?: string;
  quantity?: number;
  url?: string;
  invoiceValue?: number;
  invoiceUnitPrice?: number;
  currencyCode: string;
  priceQuantityUnit?: string;
  productId?: string;
  weight?: number;
  unitOfMass?: string;
  customerPersonalId?: string;
  originalUrl?: string;
  originalProductName?: string;
  placeOfOrigin?: string;
  otherLawCode?: string;
}

export interface eIImportDetailCreate {
  id?: number;
  HAWB?: string;
  HSCode?: string;
  itemName?: string;
  itemNameVN?: string;
  placeOfOrigin?: string;
  originalPlaceName?: string;
  importTaxClassification?: string;
  quantity1?: number;
  quantityUnitCode1?: string;
  invoiceValue?: number;
  invoiceUnitPrice?: number;
  classificationImportTax?: string;
  importTaxRate?: string;
  unitPriceCurrencyCode?: string;
  priceQuantityUnit?: string;
  url?: string;
  priceVND?: number;

  importTax?: string;
  importTaxCode?: string;
  importPrice?: number;

  VATTax?: string;
  VATTaxCode?: string;
  VATPrice?: number;

  environmentTax?: string;
  environmentTaxCode?: string;
  environmentPrice?: number;

  specialConsumptionTax?: string;
  specialConsumptionTaxCode?: string;
  specialConsumptionPrice?: number;

  quantity2?: number;
  quantityUnitCode2?: string;

  quantityUnitPrice?: string;
  productId?: string;
  weightKG?: any;
  position?: number;
  originalProductName?: any;

  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
  placeOfOrigin1?: string;
  originalUrl?: string;

  otherLawCode?: string;
}

export interface eIImportDetail {
  id: number;
  HAWB: string;
  HSCode: string;
  itemName: string;
  itemNameVN: string;
  //MIC
  placeOfOrigin: string;
  originalPlaceName: string;
  customsValue: string;
  // end MIC
  //IDA
  quantity1: number;
  quantityUnitCode1: string;
  valuationNos: any;

  invoiceValue: number;
  invoiceUnitPrice: number;
  unitPriceCurrencyCode: string;
  priceQuantityUnit: string;
  customsValueS: number;
  taxValueM: number;
  valueUnitPrice: number;
  quantityUnitPrice: string;
  classificationImportTax: string;
  importTaxRate: string;
  importTaxClassification: string;
  importTaxAmount: number;
  placeOfOriginCode: string;


  url: string;

  importTax: number;
  importTaxCode: string;
  importTaxFree: string;
  importTaxFreePrice: number;
  importPrice: number;
  priceAfterImportTax: number;
  priceBeforeVATTax: number;
  VATTax: number;
  VATTaxCode: string;
  VATTaxFree: string;
  VATTaxFreePrice: number;
  VATPrice: number;
  environmentTax: number;
  environmentTaxCode: string;
  environmentTaxFree: string;
  environmentTaxFreePrice: number;
  environmentPrice: number;
  specialConsumptionTax: number;
  specialConsumptionTaxCode: string;
  specialConsumptionTaxFree: string;
  specialConsumptionTaxFreePrice: number;
  specialConsumptionPrice: number;

  priceVND: number;
  productId?: string;
  weightKG?: number;

  quantity2: number;
  quantityUnitCode2: string;
  position?: number;
  originalUrl?: string;
  originalProductName?: string;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export class eImportDetail extends BaseModel implements eIImportDetail {
  public static readonly ModelName: string = 'eImportDetail';
  public static readonly TableName: string = 'clearance_eimport_details';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public HAWB!: string;
  public HSCode!: string;
  public itemName!: string;
  public itemNameVN!: string;
  public placeOfOrigin!: string;
  public originalPlaceName!: string;
  public customsValue!: string;
  public quantity1!: number;
  public quantityUnitCode1!: string;
  public valuationNos!: any;
  public invoiceValue!: number;
  public invoiceUnitPrice!: number;
  public unitPriceCurrencyCode!: string;
  public priceQuantityUnit!: string;
  public customsValueS!: number;
  public taxValueM!: number;
  public valueUnitPrice!: number;
  public quantityUnitPrice!: string;
  public classificationImportTax!: string;
  public importTaxRate!: string;
  public importTaxClassification!: string;
  public importTaxAmount!: number;
  public placeOfOriginCode!: string;
  public priceVND!: number;

  public importTax!: number;
  public importTaxCode!: string;
  public importTaxFree!: string;
  public importTaxFreePrice!: number;
  public importPrice!: number;
  public priceAfterImportTax!: number;
  public priceBeforeVATTax!: number;
  public VATTax!: number;
  public VATTaxCode!: string;
  public VATTaxFree!: string;
  public VATTaxFreePrice!: number
  public VATPrice!: number;
  public environmentTax!: number;
  public environmentTaxCode!: string;
  public environmentTaxFree!: string;
  public environmentTaxFreePrice!: number;
  public environmentPrice!: number;
  public specialConsumptionTax!: number;
  public specialConsumptionTaxCode!: string;
  public specialConsumptionTaxFree!: string;
  public specialConsumptionTaxFreePrice!: number;
  public specialConsumptionPrice!: number;
  public url!: string;
  public productId!: string;
  public weightKG!: number;

  public quantity2!: number;
  public quantityUnitCode2!: string;
  public position?: number;
  public originalProductName?: string;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: {
          type: DataTypes.INTEGER,
          unique: true,
          primaryKey: true,
          autoIncrement: true
        },
        HAWB: {
          type: DataTypes.STRING(30),
          allowNull: true
        },
        HSCode: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        itemName: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        itemNameVN: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        //MIC
        placeOfOrigin: {
          type: DataTypes.STRING(4),
          allowNull: true
        },
        originalPlaceName: {
          type: DataTypes.STRING(100),
          allowNull: true
        },
        customsValue: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        // end MIC
        //IDA
        quantity1: {
          type: DataTypes.DECIMAL(20, 6),
          allowNull: true,
          defaultValue: 0
        },
        quantityUnitCode1: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        quantity2: {
          type: DataTypes.DECIMAL(20, 6),
          allowNull: true,
          defaultValue: 0
        },
        quantityUnitCode2: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        valuationNos: {
          //Số của mục khai khoản điều chỉnh
          type: DataTypes.ARRAY(DataTypes.INTEGER),
          allowNull: true
        },

        invoiceValue: {
          type: DataTypes.DECIMAL(26, 6),
          allowNull: true,
          defaultValue: 0
        },
        invoiceUnitPrice: {
          type: DataTypes.DECIMAL(26, 6),
          allowNull: true,
          defaultValue: 0
        },
        unitPriceCurrencyCode: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        priceQuantityUnit: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        customsValueS: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        taxValueM: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        valueUnitPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        quantityUnitPrice: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        classificationImportTax: {
          type: DataTypes.STRING(2),
          allowNull: true
        },
        importTaxRate: {
          type: DataTypes.STRING(50),
          allowNull: true
        },
        importTaxClassification: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        importTaxAmount: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        placeOfOriginCode: {
          type: DataTypes.STRING(6),
          allowNull: true
        },
        url: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        priceVND: {
          // giá trị việt nam đồng thông quan
          type: DataTypes.DECIMAL(12, 2),
          allowNull: true,
          defaultValue: 0
        },
        importTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        importTaxCode: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        importTaxFree: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        importTaxFreePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        importPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        priceAfterImportTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        priceBeforeVATTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        VATTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        VATTaxCode: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        VATTaxFree: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        VATTaxFreePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        VATPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        environmentTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        environmentTaxCode: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        environmentTaxFree: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        environmentTaxFreePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        environmentPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        specialConsumptionTax: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true
        },
        specialConsumptionTaxCode: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        specialConsumptionTaxFree: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        specialConsumptionTaxFreePrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        specialConsumptionPrice: {
          type: DataTypes.DECIMAL(20, 4),
          allowNull: true,
          defaultValue: 0
        },
        productId: {
          type: DataTypes.STRING(255),
          allowNull: true,
        },
        weightKG: {
          type: DataTypes.DECIMAL(12, 4),
          allowNull: true,
        },
        position: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        originalUrl: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        originalProductName: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        // create information
        createdAt: {
          type: DataTypes.DATE
        },
        updatedAt: {
          type: DataTypes.DATE
        },
        deletedAt: {
          type: DataTypes.DATE
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        }
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          emicInbound: {
            include: [
              {
                model: eMIC,
                as: 'emicInbound'
              }
            ]
          },
        }
      }
    );
  }

  public static setAssociations(modelCtors: { [modelName: string]: ModelCtor<Model> }) {
    // place to set model associations
    eImportDetail.belongsTo(eMIC, { as: 'emicInbound', foreignKey: 'HAWB' });
  }
}
