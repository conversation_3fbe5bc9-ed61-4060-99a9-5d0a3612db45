'use strict'

import moment from 'moment';
import { DataTypes, FindOptions, Model, ModelCtor, Sequelize } from 'sequelize';
import Configure from '../emuns/configures';
import { BaseModel } from './base.model';
import { Employee, User } from './index.model';
import { Service } from './service.model';

export interface IMasterAnalyzer {
  id?: number;
  MAWB?: string;
  boxName?: string;
  employeeId?: number;
  totalIDA?: number;
  totalIDANone?: number;
  totalIDABlue?: number;
  totalIDAYellow?: number;
  totalIDARed?: number;
  totalMIC?: number;
  totalMICNone?: number;
  totalMICBlue?: number;
  totalMICYellow?: number;
  totalMICRed?: number;
  delivery?: string;
  isSucess?: boolean;
  // create information
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
  link?: string;
  note?: string;
  clientId?: number;
  IDARed?: string;
  IDAYellow?: string;
  IDANone?: string;
  IDAs?: string;
  MICRed?: string;
  MICYellow?: string;
  MICNone?: string;

  totalEDA?: number;
  totalEDANone?: number;
  totalEDABlue?: number;
  totalEDAYellow?: number;
  totalEDARed?: number;
  totalMEC?: number;
  totalMECNone?: number;
  totalMECBlue?: number;
  totalMECYellow?: number;
  totalMECRed?: number;
  MECs?: string;
  MECRed?: string;
  MECYellow?: string;
  MECNone?: string;
  EDAs?: string;
  EDARed?: string;
  EDAYellow?: string;
  EDANone?: string;
  serviceId?: number;
  totalHold?: number;
}

export class MasterAnalyzer extends BaseModel implements IMasterAnalyzer {
  public static readonly ModelName: string = 'MasterAnalyzer';
  public static readonly TableName: string = 'master_analyzers';
  public static readonly DefaultScope: FindOptions = {};

  public id!: number;
  public MAWB!: string;
  public boxName!: string;
  public employeeId!: number;
  public totalIDA!: number;
  public totalIDANone!: number;
  public totalIDABlue!: number;
  public totalIDAYellow!: number;
  public totalIDARed!: number;
  public totalMIC!: number;
  public totalMICNone!: number;
  public totalMICBlue!: number;
  public totalMICYellow!: number;
  public totalMICRed!: number;
  public delivery!: string;
  public isSucess!: boolean;
  public clientId!: number;
  public IDARed!: string;
  public IDAYellow!: string;
  public IDANone!: string;
  public IDAs!: string;
  public MICRed!: string;
  public MICYellow!: string;
  public MICNone!: string;

  public totalEDA!: number;
  public totalEDANone!: number;
  public totalEDABlue!: number;
  public totalEDAYellow!: number;
  public totalEDARed!: number;
  public totalMEC!: number;
  public totalMECNone!: number;
  public totalMECBlue!: number;
  public totalMECYellow!: number;
  public totalMECRed!: number;
  public MECs!: string;
  public MECRed!: string;
  public MECYellow!: string;
  public MECNone!: string;
  public EDAs!: string;
  public EDARed!: string;
  public EDAYellow!: string;
  public EDANone!: string;
  public serviceId!: number;
  public totalHold!: number;
  // create information
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;
  public isDeleted?: boolean;
  public link?: string;
  public note?: string;

  // region Static
  public static prepareInit(sequelize: Sequelize) {
    this.init(
      {
        id: { 
          type: DataTypes.INTEGER,
          primaryKey: true,
          unique: true,
          autoIncrement: true,
          autoIncrementIdentity: true 
        },
        MAWB: {
          type: DataTypes.STRING(50),
          allowNull : false
        },
        boxName: {
          type: DataTypes.STRING(255),
          allowNull : false
        },
        employeeId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        totalIDA: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalIDANone: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalIDABlue: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalIDAYellow: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalIDARed: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalMIC: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalMICNone: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalMICBlue: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalMICYellow: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        totalMICRed: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
        },
        delivery: {
          type: DataTypes.STRING(10),
          allowNull : true
        },
        isSucess: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        clientId: {
          type: DataTypes.INTEGER,
          allowNull : true
        },
        IDARed: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        IDAYellow: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        IDANone: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        IDAs: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        MICRed: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        MICYellow: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        MICNone: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        serviceId: {
          type: DataTypes.INTEGER,
          defaultValue: 3
        },
        totalHold: {
          type: DataTypes.BIGINT,
          allowNull : true
        },
        totalEDA: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalEDANone: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalEDABlue: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalEDAYellow: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalEDARed: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalMEC: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalMECNone: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalMECBlue: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalMECYellow: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        totalMECRed: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        MECs: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        MECRed: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        MECYellow: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        MECNone: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        EDAs: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        EDARed: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        EDAYellow: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        EDANone: {
          type: DataTypes.TEXT,
          allowNull : true
        },
        // create information
        createdAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('createdAt')) {
              return moment(that.getDataValue('createdAt')).format(Configure.FULL_TIME);
            }
            return that.getDataValue('createdAt');
          }
        },
        updatedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('updatedAt')) {
              return moment(that.getDataValue('updatedAt')).format(Configure.FULL_TIME);
            }
            return that.getDataValue('updatedAt');
          }
        },
        deletedAt: {
          type: DataTypes.DATE,
          get() {
            const that: any = this;
            if (that.getDataValue('deletedAt')) {
              return moment(that.getDataValue('deletedAt')).format(Configure.FULL_TIME);
            }
            return that.getDataValue('deletedAt');
          }
        },
        isDeleted: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        
      },
      {
        sequelize: sequelize,
        tableName: this.TableName,
        name: {
          singular: this.ModelName,
        },
        defaultScope: this.DefaultScope,
        comment: 'Model for the public accessible data of an import',
        scopes: {
          employee: {
            include: [
              {
                model: Employee,
                as: 'employee',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['firstname', 'lastname']
                  }
                ]
              }
            ]
          },
          service: {
            include: [
              {
                model: Service,
                as: 'service'
              }
            ]
          },
        }
      },
    );
  }

  public static setAssociations(modelCtors: {[modelName: string]: ModelCtor<Model>;}) {
    // place to set model associations
    MasterAnalyzer.belongsTo(Employee, { as: 'employee', foreignKey: 'employeeId' });
    MasterAnalyzer.belongsTo(Service, { as: 'service', foreignKey: 'serviceId' });
  }
}

