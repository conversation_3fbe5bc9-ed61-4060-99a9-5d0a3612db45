import dotenv from 'dotenv';
import { ActionKey } from '../emuns/action';
dotenv.config();

export const DefaultSetting = {
  TerminalLive: process.env.TERMINAL_LIVE || true
};

export const enum ClearanceTypeId {
  Tam = 1,
  ChinhThuc,
  ChinhSua,
  XacNhanChinhSua
}

export const enum ServiceId {
  outbound = 2,
  inbound = 3
}

export enum ClearanceType {
  MIC = 'MIC',
  MIE = 'MIE',

  IDA = 'IDA',
  IDC = 'IDC',
  IDA01 = 'IDA01',
  IDE = 'IDE',

  MEC = 'MEC',
  MEE = 'MEE',

  EDA = 'EDA',
  EDC = 'EDC',
  EDA01 = 'EDA01',
  EDE = 'EDE'
}

export const ClearanceTypeInfo = new Map<string, any>([
  [ClearanceType.MIC, { serviceId: ServiceId.inbound, isLowValue: true, clearanceTypeId: ClearanceTypeId.ChinhThuc }],
  [ClearanceType.MIE, { serviceId: ServiceId.inbound, isLowValue: true, clearanceTypeId: ClearanceTypeId.ChinhSua }],

  [ClearanceType.IDA, { serviceId: ServiceId.inbound, isLowValue: false, clearanceTypeId: ClearanceTypeId.Tam }],
  [ClearanceType.IDC, { serviceId: ServiceId.inbound, isLowValue: false, clearanceTypeId: ClearanceTypeId.ChinhThuc }],
  [ClearanceType.IDA01, { serviceId: ServiceId.inbound, isLowValue: false, clearanceTypeId: ClearanceTypeId.ChinhSua }],
  [ClearanceType.IDE, { serviceId: ServiceId.inbound, isLowValue: false, clearanceTypeId: ClearanceTypeId.XacNhanChinhSua }],

  [ClearanceType.MEC, { serviceId: ServiceId.outbound, isLowValue: true, clearanceTypeId: ClearanceTypeId.ChinhThuc }],
  [ClearanceType.MEE, { serviceId: ServiceId.outbound, isLowValue: true, clearanceTypeId: ClearanceTypeId.ChinhSua }],

  [ClearanceType.EDA, { serviceId: ServiceId.outbound, isLowValue: false, clearanceTypeId: ClearanceTypeId.Tam }],
  [ClearanceType.EDC, { serviceId: ServiceId.outbound, isLowValue: false, clearanceTypeId: ClearanceTypeId.ChinhThuc }],
  [ClearanceType.EDA01, { serviceId: ServiceId.outbound, isLowValue: false, clearanceTypeId: ClearanceTypeId.ChinhSua }],
  [ClearanceType.EDE, { serviceId: ServiceId.outbound, isLowValue: false, clearanceTypeId: ClearanceTypeId.XacNhanChinhSua }]
]);

export const ClearanceRabbit = {
  ExchangeDelay: 'clearance_exchange_delay',

  Push: 'clearance_push',
  Pull: 'clearance_pull',

  Pull_file: 'clearance_pull_file',

  Get_terminal: 'clearance_get_terminal',
  Pull_terminal: 'clearance_pull_terminal',

  Terminal: 'ecus_agent',
  ValidPush: 'clearance_valid_push',

  TMS_CLEARANCE: 'tms_clearance',

  UPDATE_TAX_CODE_NUMBER: 'updateTaxCodeNumber',
  UPDATE_MIC_TAX_CODE_NUMBER: 'updateMicTaxCodeNumber'
};

export enum ClearanceStatus {
  Xanh = 1,
  Vang = 2,
  Do = 3
}

export const ClearancePhase = new Map<number, string[]>([
  [ActionKey.ACCEPT_CLEARANCE, ['FG', 'AG', 'JF', 'LF']],
  [ActionKey.INSPECTION_KIND, ['FC', 'FE', 'AC', 'AE', 'JD', 'LD']]
]);

export const ClearanceDefaultData: any = {
  '01DD': 'CCHQCPNHN',
  '02DS': 'CPNHANHHCM',
  O9580: 'CONG TY CO PHAN CHUYEN PHAT NHANH HANG HOA SAI GON',
  U8563: 'CN CTCP CHUYEN PHAT NHANH HANG HOA SAI GON TAI HN',
  '02DSED5': 'CTY CP CPN SAI GON',
  '01DDEA1': 'CN HN CT CPN SAI GON',
  VNSGN: 'HO CHI MINH',
  VNHAN: 'HA NOI'
};
