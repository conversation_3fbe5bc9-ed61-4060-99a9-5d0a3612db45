import { ClearanceTypeId, ClearancePhase, ServiceId } from './enum';
import Configure from '../emuns/configures';
import { ActionKey } from '../emuns/action';
import { Database } from '../database';
import moment from 'moment';
import xml from 'xml-js';
import _ from 'lodash';
import fs from 'fs';
import XmlErrorMongo from './mongoDB/xmlError.mongo';
import Utilities from '../util/utilities';
import { Transaction } from 'sequelize';

export enum FormatType {
  ECUS2CPN,
  CPN2ECUS
}

enum TaxKey {
  VAT = 'VAT',
  Environment = 'environment',
  SpecialConsumption = 'specialConsumption',
  Import = 'import'
}

const TaxKeyList: string[] = [TaxKey.Environment, TaxKey.SpecialConsumption, TaxKey.VAT];
const TaxKeyAllList: string[] = [TaxKey.Environment, TaxKey.SpecialConsumption, TaxKey.VAT, TaxKey.Import];
const TaxKeyStringList: string[] = ['Thuế BVMT', 'Thuế TTĐB', 'Thuế GTGT', 'Thuế NK'];

export class ClearanceXml {
  private folderSend: string = Configure.CLEARANCE_FOLDER_SEND;
  private folderReceive: string = Configure.CLEARANCE_FOLDER_RECEIVE;

  private rawData: any;
  private rawDetailData: any;
  private formatType: FormatType;

  private structure: any;
  private isLowValue: boolean;

  private keyMap: any;
  private keyDetailMap: any;
  private detailKey: string;

  private taxKeyArray: string[] = [];
  private isTaxMap = false;

  constructor(formatType: FormatType, structure?: any, data?: any) {
    this.rawData = data;
    this.formatType = formatType;

    this.structure = structure;
    this.isLowValue = structure ? structure.isLowValue : false;

    this.keyMap = structure ? structure.keyMap : {};
    this.keyDetailMap = structure ? structure.keyDetailMap : {};

    this.detailKey = structure ? structure.model.detailKey : [];
  }

  getClearancePhase(filename: string, clearanceTypeId?: ClearanceTypeId | null): any {
    for (let [key, value] of ClearancePhase) {
      for (let idx = 0; idx < value.length; idx++) {
        let clearanceStr = value[idx];

        if (filename.includes(clearanceStr)) {
          return key;
        }
      }
    }

    if (clearanceTypeId && clearanceTypeId == ClearanceTypeId.Tam) {
      return ActionKey.SENT_INFOMATION;
    }

    return null;
  }

  private formatData(text: any, keyMap: any): any {
    let type = keyMap.type;

    if (['date', 'time', 'datetime'].includes(type)) {
      if (text) {
        let date = moment(text, keyMap.originalFormat);
        if (date.isValid()) {
          return date.format(keyMap.format);
        }
      }

      return null;
    }

    if (type == 'numeric') {
      let n = parseFloat(text) || 0;

      if (keyMap.isRound == true && keyMap.roundDecimal) {
        let round = parseFloat('1'.padEnd(parseFloat(keyMap.roundDecimal) + 1, '0'));
        return Math.ceil(round * n) / round;
      }
      return n != 0 ? n : null;
    }

    if (type == 'integer') {
      let n = parseInt(text) || 0;
      return n != 0 ? n : null;
    }

    if (type == 'string') {
      if (keyMap.trimCharacters && keyMap.trimCharacters.length > 0) {
        for (let idx = 0; idx < keyMap.trimCharacters.length; idx++) {
          let trimC = keyMap.trimCharacters[idx];

          text = text.replace(trimC.regex, trimC.replace);
        }
      }

      let maxLength = keyMap.max || 0;

      if (keyMap.isTrim && maxLength > 0) {
        let trimStart = 0;
        let trimEnd = keyMap.isTrimWord
          ? text.length > maxLength && text[maxLength] == keyMap.trimCharacter
            ? text.substr(trimStart, maxLength).lastIndexOf(keyMap.trimCharacter)
            : maxLength
          : maxLength;

        text = text.substr(trimStart, trimEnd);
      }

      return text;
    }

    return text;
  }

  private renderTaxData(formattedData: any, keyMap: any, isDetail?: boolean) {
    let taxKeyReplaces = keyMap ? keyMap.taxKeyReplaces : null;
    if (!taxKeyReplaces) {
      return formattedData;
    }

    let newKeys: any = {};

    for (const key in taxKeyReplaces) {
      const keyReplace = taxKeyReplaces[key];
      let keyData = keyReplace.key;

      if (key === 'taxKeyName') {
        let whileI = 0;
        let isContinue = true;

        while (isContinue) {
          let dataFullKey = whileI == 0 ? keyData : `${keyData}${whileI + 1}`;

          let val = isDetail ? (this.rawDetailData[dataFullKey] ? this.rawDetailData[dataFullKey]['_text'] : null) : this.rawData[dataFullKey] ? this.rawData[dataFullKey]['_text'] : null;
          if (val) {
            let keyPosition = TaxKeyStringList.findIndex((o) => o === val);
            newKeys[whileI] = keyPosition;

            whileI++;
          } else {
            isContinue = false;
          }
        }
      } else if (newKeys) {
        for (const k in newKeys) {
          let keyObject = k == '0' ? keyData : `${keyData}${parseInt(k) + 1}`;
          let keyTax = TaxKeyAllList[newKeys[k]] == TaxKey.VAT ? TaxKey.VAT : _.capitalize(TaxKeyAllList[newKeys[k]]);
          let keyModel = key.replace(/___/gi, keyTax);

          let value = isDetail ? (this.rawDetailData[keyObject] ? this.rawDetailData[keyObject]['_text'] : null) : this.rawData[keyObject] ? this.rawData[keyObject]['_text'] : null;

          formattedData[keyModel] = this.formatData(value, keyReplace);
        }
      }
    }

    formattedData.priceAfterImportTax = formattedData.valueUnitPrice + formattedData.importPrice;

    return formattedData;
  }

  /**
   * Xử lý dữ liệu
   *
   * @private
   * @param {*} data Dữ liệu cần xử lý (CPN send ECUS: xml; ECUS send CPN: model)
   */
  private renderData(fomattedData: any): any {
    for (const key in fomattedData) {
      let keyMap = this.keyMap[key];
      if (!keyMap) {
        continue;
      }

      if (keyMap.type == 'object') {
        fomattedData[key] = this.renderData(fomattedData[key]);
        continue;
      }

      if (keyMap.depend) {
        if (!this.rawData[keyMap.depend] || !parseFloat(this.rawData[keyMap.depend])) {
          continue;
        }
      }

      let k = keyMap ? keyMap['key'] : '';
      if (!k) {
        continue;
      }

      let value = this.rawData[k];

      if (keyMap.type == 'array') {
        if (value && value.length > 0) {
          for (let idx = 0; idx < value.length; idx++) {
            let val = value[idx];

            if (keyMap.subType == 'object') {
              let count = idx === 0 ? '' : idx + 1;
              fomattedData[`${key}${count}`] = val[keyMap.subKey];
            }
          }
        }

        continue;
      }

      if (keyMap.defaultField) {
        value = value || this.rawData[keyMap.defaultField];
      }

      if (this.formatType == FormatType.ECUS2CPN) {
        value = this.rawData[k] ? this.rawData[k]['_text'] : undefined;
      }

      if (keyMap.isDetail && this.formatType == FormatType.CPN2ECUS) {
        let length = this.rawData[this.detailKey].length;
        let max = Math.ceil((keyMap.max || 200) / length) - 2;

        if (keyMap.isJoin === true) {
          value = _.map(this.rawData[this.detailKey], (x) => {
            return this.formatData(x[k], { ...keyMap, max });
          }).join(', ');
        } else {
          value = this.rawData[this.detailKey][0] ? this.rawData[this.detailKey][0][k] : '';
        }
      }

      if (value) {
        fomattedData[key] = this.formatData(value, keyMap);
      }
    }

    if (this.isLowValue == false) {
      fomattedData = this.renderTaxData(fomattedData, this.keyMap);
    }

    return fomattedData;
  }

  private renderDetailData(fomattedData: any): any {
    for (const key in fomattedData) {
      let keyMap = this.keyDetailMap[key];
      if (!keyMap) {
        continue;
      }

      if (this.isLowValue == false && keyMap.isTax) {
        let taxKeyHandle = this.isTaxMap == true ? this.taxKeyArray : TaxKeyList;
        let len = taxKeyHandle.length;
        let knCount = 0;

        for (let taxIdx = 0; taxIdx < len; taxIdx++) {
          let taxKey = keyMap.key.replace(/___/gi, `${taxKeyHandle[taxIdx]}`);

          let taxValue = this.rawDetailData[taxKey];
          if (!taxValue) {
            continue;
          }

          if (!this.isTaxMap) {
            this.taxKeyArray.push(taxKeyHandle[taxIdx]);
          }

          let keySuffix = this.isTaxMap == true ? taxIdx : knCount;
          let newKey = knCount == 0 ? key : `${key}${keySuffix + 1}`;

          fomattedData[newKey] = this.formatData(taxValue, keyMap);
          knCount++;
        }

        this.isTaxMap = true;

        continue;
      }

      let k = keyMap ? keyMap['key'] : '';
      if (!k) {
        continue;
      }

      let value = this.rawDetailData[k];
      if (keyMap.defaultField) {
        value = value || this.rawDetailData[keyMap.defaultField];
      }

      if (this.formatType == FormatType.ECUS2CPN) {
        value = this.rawDetailData[k] ? this.rawDetailData[k]['_text'] : undefined;
      }

      if (value) {
        fomattedData[key] = this.formatData(value, keyMap);
      }
    }

    if (this.isLowValue == false) {
      fomattedData = this.renderTaxData(fomattedData, this.keyDetailMap, true);
    }

    return fomattedData;
  }

  private getFileName() {
    //this.rawData.clearanceType.replace('01', '')
    let name = `${this.rawData.HAWBClearance}_${this.rawData.clearanceType}`;

    if (this.rawData.isLowValue == false) {
      // if (this.rawData.clearanceTypeId == ClearanceTypeId.ChinhSua) {
      //   let times = `0${((this.rawData.times || 0) + 1).toString()}`;
      //   name += times;
      // }
      name += '_0000';
    }

    return name + '.XML';
  }

  private getXmlString(renderData: any) {
    try {
      let options = { compact: true, ignoreComment: true };
      return { isSuccess: true, data: `<?xml version="1.0" encoding="utf-8"?><Root>${xml.json2xml(JSON.stringify(renderData), options)}</Root>` };
    } catch (error) {
      return { isSuccess: false, message: (error as any).message };
    }
  }

  private saveFile(data: any): { folder: string; fileName: string } {
    if (!fs.existsSync(this.folderSend)) {
      fs.mkdirSync(this.folderSend, { recursive: true });
    }

    let fileName = this.getFileName();
    fs.writeFileSync(`${this.folderSend}/${fileName}`, data);

    return { folder: this.folderSend, fileName };
  }

  async createFile() {
    let renderData = this.renderData(this.structure.xmlStructure);

    if (this.isLowValue == false) {
      let renderDetails = [];
      let detailsData = this.rawData[this.detailKey];
      let detailLength = detailsData.length;

      this.taxKeyArray = [];
      this.isTaxMap = false;

      for (let idx = 0; idx < detailLength; idx++) {
        let detail = detailsData[idx];
        this.rawDetailData = detail;

        let detailFormatted = this.renderDetailData(this.structure.xmlStructureDetail);
        if (detailFormatted) {
          renderDetails.push(Object.assign({}, detailFormatted));
        }
      }

      if (renderDetails.length > 0) {
        renderData.Declaration.GoodItems.GoodItem = renderDetails;
      }
    }

    let xmlData = this.getXmlString(renderData);

    if (xmlData.isSuccess == true) {
      return { isSuccess: true, data: this.saveFile(xmlData.data) };
    } else {
      return xmlData;
    }
  }

  readFiles(fileNames: string[]) {
    let data: any = {};

    let fileLength = fileNames.length;
    if (fileLength == 0) {
      return { isSuccess: false };
    }

    let filesErr: string[] = [];

    for (let idx = 0; idx < fileLength; idx++) {
      const fileName = fileNames[idx];

      try {
        let filePath = this.folderReceive + fileName;

        if (!fs.existsSync(filePath)) {
          continue;
        }

        let hawb = fileName.substring(0, fileName.indexOf('_'));
        let dataXml = fs.readFileSync(filePath, { encoding: 'utf8' });

        let dataJson: any = JSON.parse(xml.xml2json(dataXml, { compact: true, ignoreAttributes: true }));
        dataJson.fileName = fileName;

        let prevData = data[hawb];
        if (!prevData) {
          data[hawb] = [dataJson];
        } else {
          data[hawb].push(dataJson);
        }
      } catch (error) {
        Utilities.sendTelegramNoti('READ_FILE', `FILE XML FAIL: ${fileName}`);
        console.log(` --- [%s] [READ_FILE] Đọc file xml thất bại ${fileName}: %o`, moment().format(Configure.FULL_TIME), error);

        filesErr.push(fileName);
        continue;
      }
    }

    return { isSuccess: true, data: data, filesErr };
  }

  async formatAndSaveData(modelDetail: any, modelData: any, dataXml: any[], clearanceTypeId: number, isComplete?: boolean) {
    let trans;

    let fileName = _(dataXml).map('fileName').join(',');

    try {
      let modelDetails = modelData[this.detailKey];
      let xmlDetails = [];

      let isUpdate = true;

      try {
        for (const key in dataXml) {
          let xml = dataXml[key];

          if (xml.fileName.includes('VAF801_') || xml.fileName.includes('VAF803_')) {
            if (dataXml.length == 1) {
              isUpdate = false;
            }
            continue;
          }

          if (xml.fileName.includes('Err')) {
            modelData.isError = true;
            modelData.isECUSError = true;
            modelData.messageErrorName = xml.ErrorList.Error.ErrorName['_text'];
            modelData.messageError = xml.ErrorList.Error.Solution['_text'];

            let errorDescription = await XmlErrorMongo.findOne({ key: modelData.messageErrorName, isActive: true });
            if (errorDescription) {
              modelData.messageError = modelData.messageError + ` Mô tả: ${errorDescription.description}`;
            }

            if (this.isLowValue == false && modelData.phase == ActionKey.SUBMIT_CUSTOM_CLEARANCE) {
              modelData.phase = ActionKey.SENT_INFOMATION;
            }

            continue;
          }

          let v = xml.Root.Declaration;

          this.rawData = v;
          let detail = xml.Root.Declaration.GoodItems.GoodItem;

          if (detail && _.size(detail) > 0) {
            if (_.isArray(detail)) {
              xmlDetails.push(...detail);
            } else {
              xmlDetails.push(detail);
            }
          }

          let convertData = this.renderData(modelData.get({ plain: true }));
          modelData = Object.assign(modelData, convertData);

          let phaseKey = xml.fileName.replace(modelData.HAWBClearance, '');
          let phase = this.getClearancePhase(phaseKey, clearanceTypeId);

          if (phase) {
            modelData.phase = phase;
          }

          modelData.isError = false;

          if (modelData.dateOfCompletion && modelData.timeCompletion) {
            modelData.dateClearanced = moment(`${modelData.dateOfCompletion} ${modelData.timeCompletion}`).format(Configure.FULL_TIME);
          } else if (modelData.dateOfCompletionOfInspection && modelData.timeOfCompletionOfInspection) {
            modelData.dateClearanced = moment(`${modelData.dateOfCompletionOfInspection} ${modelData.timeOfCompletionOfInspection}`).format(Configure.FULL_TIME);
          } else if (modelData.dateOfPermit && modelData.timeOfPermit) {
            modelData.dateClearanced = moment(`${modelData.dateOfPermit} ${modelData.timeOfPermit}`).format(Configure.FULL_TIME);
          }
        }
      } catch (error) {
        Utilities.sendTelegramNoti('SAVE_DATA', `READ FAILD: ${fileName} - ${error.message}`);
        return { isSuccess: false };
      }

      trans = await Database.Sequelize.transaction({ isolationLevel: Transaction.ISOLATION_LEVELS.SERIALIZABLE });

      if (isUpdate === true) {
        if (clearanceTypeId == ClearanceTypeId.XacNhanChinhSua && isComplete == false) {
          modelData.times = (modelData.times || 0) + 1;
        } else if (this.isLowValue == false && clearanceTypeId == ClearanceTypeId.Tam) {
          modelData.tempTimes = (modelData.tempTimes || 0) + 1;
        }

        if (xmlDetails.length > 0) {
          if (this.isLowValue) {
            for (let idx = 0; idx < modelDetails.length; idx++) {
              const detail: any = modelDetails[idx];

              if (_.size(xmlDetails[0]) <= 0) {
                continue;
              }

              if (detail.originalPlaceName !== undefined) {
                detail.originalPlaceName = xmlDetails[0].OriginalPlaceName['_text'];
              }

              if (detail.priceVND !== undefined) {
                detail.priceVND = parseFloat(modelData.valueClearanceVND) * parseFloat(detail.invoiceValue);
              }

              await detail.save({ transaction: trans });
            }
          } else {
            for (let idx = 0; idx < modelDetails.length; idx++) {
              let detail = modelDetails[idx];

              let xmlId = xmlDetails.findIndex((o: any) => {
                if (_.size(o) > 0) {
                  return detail.itemNameVN == o.ItemName['_text'] && detail.HSCode == o.HSCode['_text'] && detail.quantity1 == parseFloat(o.Quantity1['_text']);
                }
                return false;
              });

              if (xmlId < 0) {
                continue;
              }

              let xml = xmlDetails[xmlId];
              this.rawDetailData = xml;

              let data = this.renderDetailData(detail.get({ plain: true }));
              data = _(data)
                .pickBy(_.identity)
                .omitBy((o) => o == '0.0000')
                .value();

              await modelDetail.update(data, { where: { id: detail.id }, transaction: trans });
            }
          }
        }

        if (this.isLowValue && xmlDetails && xmlDetails[0]) {
          if (modelData.priceVND !== undefined) {
            let customsValue = xmlDetails[0].Customsvalue || xmlDetails[0].CustomsValue;
            modelData.priceVND = parseInt(customsValue['_text'].replace(/,/gi, ''));
          }

          if (modelData.totalOfTaxValue !== undefined) {
            modelData.totalOfTaxValue = parseFloat(xmlDetails[0].TotalOfTaxValue['_text'].replace(/,/gi, ''));
          }
        }

        modelData.dateAction = new Date();
        modelData.isEditProcessing = false;
      }

      await modelData.save({ transaction: trans });
      await trans.commit();

      return { isSuccess: true, data: modelData };
    } catch (error) {
      await trans.rollback();

      if (error.message == 'could not serialize access due to concurrent update') {
        return { isSuccess: false };
      }

      Utilities.sendTelegramNoti('SAVE_DATA', `UPDATE FAILD: ${fileName} - ${error.message}`);
      return { isSuccess: false };
    }
  }
}
