import { ClearanceTypeId, ClearancePhase, ServiceId } from './enum';
import Configure from '../emuns/configures';
import { ActionKey } from '../emuns/action';
import { Database } from '../database';
import moment from 'moment';
import xml from 'xml-js';
import _ from 'lodash';
import fs from 'fs';
import XmlErrorMongo from './mongoDB/xmlError.mongo';
import Utilities from '../util/utilities';
import { Transaction } from 'sequelize';

export enum FormatType {
  ECUS2CPN,
  CPN2ECUS
}

enum TaxKey {
  VAT = 'VAT',
  Environment = 'environment',
  SpecialConsumption = 'specialConsumption',
  Import = 'import',
  Export = 'export'
}

const TaxKeyList: string[] = [TaxKey.Environment, TaxKey.SpecialConsumption, TaxKey.VAT];
const TaxKeyAllList: string[] = [TaxKey.Environment, TaxKey.SpecialConsumption, TaxKey.VAT, TaxKey.Import];
const TaxKeyStringList: string[] = ['Thuế BVMT', 'Thuế TTĐB', 'Thuế GTGT', 'Thuế NK'];

export class ClearanceXml {
  private folderSend: string = Configure.CLEARANCE_FOLDER_SEND;
  // private folderReceive: string = Configure.CLEARANCE_FOLDER_RECEIVE;

  private rawData: any;
  private rawDetailData: any;
  private formatType: FormatType;

  private structure: any;
  private isLowValue: boolean;

  private keyMap: any;
  private keyDetailMap: any;
  private detailKey: string;

  private taxKeyArray: string[] = [];
  private isTaxMap = false;

  constructor(formatType: FormatType, structure?: any, data?: any) {
    this.rawData = data;
    this.formatType = formatType;

    this.structure = structure;
    this.isLowValue = structure ? structure.isLowValue : false;

    this.keyMap = structure ? structure.keyMap : {};
    this.keyDetailMap = structure ? structure.keyDetailMap : {};

    this.detailKey = structure ? structure.model.detailKey : [];
  }

  getClearancePhase(filename: string, clearanceTypeId?: ClearanceTypeId | null): any {
    for (let [key, value] of ClearancePhase) {
      for (let idx = 0; idx < value.length; idx++) {
        let clearanceStr = value[idx];

        if (filename.includes(clearanceStr)) {
          return key;
        }
      }
    }

    if (clearanceTypeId && clearanceTypeId == ClearanceTypeId.Tam) {
      return ActionKey.SENT_INFOMATION;
    }

    return null;
  }

  private formatData(text: any, keyMap: any): any {
    let type = keyMap.type;

    if (['date', 'time', 'datetime'].includes(type)) {
      if (text) {
        if (type == 'time') {
          text = _.padStart(text, 6, '0');
        }
        let date = moment(text, keyMap.originalFormat);
        if (date.isValid()) {
          return date.format(keyMap.format);
        }
      }

      return null;
    }

    if (type == 'numeric') {
      let n = parseFloat(text) || 0;

      if (keyMap.isRound == true && keyMap.roundDecimal) {
        let round = parseFloat('1'.padEnd(parseFloat(keyMap.roundDecimal) + 1, '0'));
        return Math.ceil(round * n) / round;
      }
      return n != 0 ? n : null;
    }

    if (type == 'integer') {
      let n = parseInt(text) || 0;
      return n != 0 ? n : null;
    }

    if (type == 'string') {
      if (keyMap.trimCharacters && keyMap.trimCharacters.length > 0) {
        for (let idx = 0; idx < keyMap.trimCharacters.length; idx++) {
          let trimC = keyMap.trimCharacters[idx];

          text = text.replace(trimC.regex, trimC.replace);
        }
      }

      let maxLength = keyMap.max || 0;

      if (keyMap.isTrim && maxLength > 0) {
        let textLength = text.length;
        let trimStart = 0;
        let trimEnd = textLength;

        if (keyMap.isTrimStart === true) {
          if (textLength > maxLength) {
            trimStart = textLength - maxLength;
          }
        } else {
          trimEnd = keyMap.isTrimWord
            ? textLength > maxLength && text[maxLength] == keyMap.trimCharacter
              ? text.substr(trimStart, maxLength).lastIndexOf(keyMap.trimCharacter)
              : maxLength
            : maxLength;
        }

        text = text.substr(trimStart, trimEnd);
      }

      return text;
    }

    return text;
  }

  private renderTaxData(formattedData: any, keyMap: any, isDetail?: boolean) {
    let taxKeyReplaces = keyMap ? keyMap.taxKeyReplaces : null;
    if (!taxKeyReplaces) {
      return formattedData;
    }

    let newKeys: any = {};

    for (const key in taxKeyReplaces) {
      const keyReplace = taxKeyReplaces[key];
      let keyData = keyReplace.key;

      if (key === 'taxKeyName') {
        let whileI = 0;
        let isContinue = true;

        while (isContinue) {
          let dataFullKey = whileI == 0 ? keyData : `${keyData}${whileI + 1}`;

          let val = isDetail ? (this.rawDetailData[dataFullKey] ? this.rawDetailData[dataFullKey]['_text'] : null) : this.rawData[dataFullKey] ? this.rawData[dataFullKey]['_text'] : null;
          if (val) {
            let keyPosition = TaxKeyStringList.findIndex((o) => o === val);
            newKeys[whileI] = keyPosition;

            whileI++;
          } else {
            isContinue = false;
          }
        }
      } else if (newKeys) {
        for (const k in newKeys) {
          let keyObject = k == '0' ? keyData : `${keyData}${parseInt(k) + 1}`;
          let keyTax = TaxKeyAllList[newKeys[k]] == TaxKey.VAT ? TaxKey.VAT : _.capitalize(TaxKeyAllList[newKeys[k]]);
          let keyModel = key.replace(/___/gi, keyTax);

          let value = isDetail ? (this.rawDetailData[keyObject] ? this.rawDetailData[keyObject]['_text'] : null) : this.rawData[keyObject] ? this.rawData[keyObject]['_text'] : null;

          formattedData[keyModel] = this.formatData(value, keyReplace);
        }
      }
    }

    formattedData.priceAfterImportTax = formattedData.valueUnitPrice + formattedData.importPrice;

    return formattedData;
  }

  /**
   * Xử lý dữ liệu
   *
   * @private
   * @param {*} data Dữ liệu cần xử lý (CPN send ECUS: xml; ECUS send CPN: model)
   */
  private renderData(fomattedData: any): any {
    for (const key in fomattedData) {
      let keyMap = this.keyMap[key];
      if (!keyMap) {
        continue;
      }

      if (keyMap.type == 'object') {
        fomattedData[key] = this.renderData(fomattedData[key]);
        continue;
      }

      if (keyMap.depend) {
        if (!this.rawData[keyMap.depend] || !parseFloat(this.rawData[keyMap.depend])) {
          continue;
        }
      }

      let k = keyMap ? keyMap['key'] : '';
      if (!k) {
        continue;
      }

      let value = this.rawData[k];

      if (keyMap.type == 'array') {
        if (value && value.length > 0) {
          for (let idx = 0; idx < value.length; idx++) {
            let val = value[idx];

            if (keyMap.subType == 'object') {
              let count = idx === 0 ? '' : idx + 1;
              fomattedData[`${key}${count}`] = val[keyMap.subKey];
            }
          }
        }

        continue;
      }

      if (keyMap.defaultField) {
        value = value || this.rawData[keyMap.defaultField];
      }

      if (this.formatType == FormatType.ECUS2CPN) {
        value = this.rawData[k] ? this.rawData[k]['_text'] : undefined;
      }

      if (keyMap.isDetail && this.formatType == FormatType.CPN2ECUS) {
        let length = this.rawData[this.detailKey].length;
        let max = Math.ceil((keyMap.max || 200) / length) - 2;

        if (keyMap.isJoin === true) {
          value = _.map(this.rawData[this.detailKey], (x) => {
            return this.formatData(x[k], { ...keyMap, max });
          }).join(', ');
        } else {
          value = this.rawData[this.detailKey][0] ? this.rawData[this.detailKey][0][k] : '';
        }
      }

      if (keyMap.isJoin === true) {
        fomattedData[key] = value;
      } else if (value) {
        fomattedData[key] = this.formatData(value, keyMap);
      }
    }

    if (this.isLowValue == false) {
      fomattedData = this.renderTaxData(fomattedData, this.keyMap);
    }

    return fomattedData;
  }

  private renderDetailData(fomattedData: any): any {
    for (const key in fomattedData) {
      let keyMap = this.keyDetailMap[key];
      if (!keyMap) {
        continue;
      }

      if (this.isLowValue == false && keyMap.isTax) {
        let taxKeyHandle = this.isTaxMap == true ? this.taxKeyArray : TaxKeyList;
        let len = taxKeyHandle.length;
        let knCount = 0;

        for (let taxIdx = 0; taxIdx < len; taxIdx++) {
          let taxKey = keyMap.key.replace(/___/gi, `${taxKeyHandle[taxIdx]}`);

          let taxValue = this.rawDetailData[taxKey];
          if (!taxValue) {
            continue;
          }

          if (!this.isTaxMap) {
            this.taxKeyArray.push(taxKeyHandle[taxIdx]);
          }

          let keySuffix = this.isTaxMap == true ? taxIdx : knCount;
          let newKey = knCount == 0 ? key : `${key}${keySuffix + 1}`;

          fomattedData[newKey] = this.formatData(taxValue, keyMap);
          knCount++;
        }

        this.isTaxMap = true;

        continue;
      }

      let k = keyMap ? keyMap['key'] : '';
      if (!k) {
        continue;
      }

      let value = this.rawDetailData[k];
      if (keyMap.defaultField) {
        value = value || this.rawDetailData[keyMap.defaultField];
      }

      if (this.formatType == FormatType.ECUS2CPN) {
        value = this.rawDetailData[k] ? this.rawDetailData[k]['_text'] : undefined;
      }

      if (value) {
        fomattedData[key] = this.formatData(value, keyMap);
      }
    }

    if (this.isLowValue == false) {
      fomattedData = this.renderTaxData(fomattedData, this.keyDetailMap, true);
    }

    return fomattedData;
  }

  private clearTax(data: any) {
    let taxKeyDb = ['totalImportTax', 'totalVATTax', 'totalEnvironmentTax', 'totalSpecialConsumptionTax', 'totalExportTax'];
    let taxKeyDetailDb = ['VATTax', 'VATPrice', 'environmentTax', 'environmentPrice', 'specialConsumptionTax', 'specialConsumptionPrice', 'importTax', 'importPrice', 'exportTax', 'exportPrice'];

    for (let idx = 0; idx < taxKeyDb.length; idx++) {
      let taxKey = taxKeyDb[idx];
      data[taxKey] = 0;
    }

    let detail = data[this.detailKey];
    for (let idx = 0; idx < detail.length; idx++) {
      for (let i = 0; i < taxKeyDetailDb.length; i++) {
        let taxKey = taxKeyDetailDb[i];
        data[this.detailKey][idx][taxKey] = 0;
      }
    }

    return data;
  }

  private getFileName(HAWBClearance: string) {
    //this.rawData.clearanceType.replace('01', '')
    let name = `${HAWBClearance}_${this.rawData.clearanceType}`;

    if (this.rawData.isLowValue == false) {
      // if (this.rawData.clearanceTypeId == ClearanceTypeId.ChinhSua) {
      //   let times = `0${((this.rawData.times || 0) + 1).toString()}`;
      //   name += times;
      // }
      name += '_0000';
    }

    return name + '.XML';
  }

  private getXmlString(renderData: any) {
    try {
      let options = { compact: true, ignoreComment: true };
      return { isSuccess: true, data: `<?xml version="1.0" encoding="utf-8"?><Root>${xml.json2xml(JSON.stringify(renderData), options)}</Root>` };
    } catch (error) {
      return { isSuccess: false, message: (error as any).message };
    }
  }

  private saveFile(data: any, HAWBClearance: string): { folder: string; fileName: string; HAWBClearance: string } {
    if (!fs.existsSync(this.folderSend)) {
      fs.mkdirSync(this.folderSend, { recursive: true });
    }

    let fileName = this.getFileName(HAWBClearance);
    fs.writeFileSync(`${this.folderSend}/${fileName}`, data);

    return { folder: this.folderSend, fileName, HAWBClearance };
  }

  async createFile() {
    let renderData = this.renderData(this.structure.xmlStructure);

    if (this.isLowValue == false) {
      let renderDetails = [];
      let detailsData = this.rawData[this.detailKey];
      let detailLength = detailsData.length;

      this.taxKeyArray = [];
      this.isTaxMap = false;

      for (let idx = 0; idx < detailLength; idx++) {
        let detail = detailsData[idx];
        this.rawDetailData = detail;

        let detailFormatted = this.renderDetailData(this.structure.xmlStructureDetail);
        if (detailFormatted) {
          renderDetails.push(Object.assign({}, detailFormatted));
        }
      }

      if (renderDetails.length > 0) {
        renderData.Declaration.GoodItems.GoodItem = renderDetails;
      }
    }

    let xmlData = this.getXmlString(renderData);

    if (xmlData.isSuccess == true) {
      return { isSuccess: true, data: this.saveFile(xmlData.data, renderData.ShipmentID) };
    } else {
      return xmlData;
    }
  }

  readFiles(fileName: string) {
    if (fileName.includes('VAF801_') || fileName.includes('VAF803_')) {
      return { isSuccess: false, fileName };
    }

    try {
      let filePath = Configure.CLEARANCE_FOLDER_RECEIVE + fileName;

      if (!fs.existsSync(filePath)) {
        return { isSuccess: false };
      }

      let dataXml = fs.readFileSync(filePath, { encoding: 'utf8' });

      let dataJson: any = JSON.parse(xml.xml2json(dataXml, { compact: true, ignoreAttributes: true }));

      return { isSuccess: true, data: dataJson, fileName };
    } catch (error) {
      console.log(` --- [%s] [CLEARANCE_READ_FILE_PATH] Đọc file xml thất bại ${fileName}: %o`, moment().format(Configure.FULL_TIME), error);
      if (error.code != 'ENOENT') {
        Utilities.sendTelegramNoti('READ_FILE', `FILE XML FAIL: ${fileName}: ${error.message}`);
      } else {
        console.log(` --- [%s] [READ_FILE] Đọc file xml thất bại ${fileName}: %o`, moment().format(Configure.FULL_TIME), error);
      }

      return { isSuccess: false };
    }
  }

  async formatAndSaveData(detailModel: any, dataUpdate: any, xml: any, clearanceTypeId: number, isComplete?: boolean) {
    let trans;

    try {
      let detailsUpdate = dataUpdate[this.detailKey];

      trans = await Database.Sequelize.transaction({ isolationLevel: Transaction.ISOLATION_LEVELS.SERIALIZABLE });

      //#region --- Error
      if (xml.ErrorList) {
        dataUpdate.isError = true;
        dataUpdate.isECUSError = true;

        dataUpdate.messageErrorName = xml.ErrorList.Error.ErrorName['_text'];
        dataUpdate.messageError = xml.ErrorList.Error.Solution['_text'];

        let errorDescription = await XmlErrorMongo.findOne({ key: dataUpdate.messageErrorName, isActive: true });
        if (errorDescription) {
          dataUpdate.messageError = dataUpdate.messageError + ` Mô tả: ${errorDescription.description}`;
        }

        if (this.isLowValue == false && dataUpdate.phase == ActionKey.SUBMIT_CUSTOM_CLEARANCE) {
          dataUpdate.phase = ActionKey.SENT_INFOMATION;
        }

        await dataUpdate.save({ transaction: trans });
        await trans.commit();

        return { isSuccess: true, data: dataUpdate };
      }
      //#endregion

      if (this.isLowValue == false) {
        dataUpdate = this.clearTax(dataUpdate);
      }

      this.rawData = xml.Root.Declaration;
      let xmlDetails = xml.Root.Declaration.GoodItems.GoodItem;

      let convertData = this.renderData(dataUpdate.get({ plain: true }));
      dataUpdate = Object.assign(dataUpdate, convertData);

      let phaseKey = xml.fileName.replace(dataUpdate.HAWBClearance, '');
      let phase = this.getClearancePhase(phaseKey, clearanceTypeId);

      if (phase) {
        dataUpdate.phase = phase;
      }

      //#region --- Model
      if (dataUpdate.dateOfCompletion && dataUpdate.timeCompletion) {
        dataUpdate.dateClearanced = moment(`${dataUpdate.dateOfCompletion} ${dataUpdate.timeCompletion}`).format(Configure.FULL_TIME);
      } else if (dataUpdate.dateOfCompletionOfInspection && dataUpdate.timeOfCompletionOfInspection) {
        dataUpdate.dateClearanced = moment(`${dataUpdate.dateOfCompletionOfInspection} ${dataUpdate.timeOfCompletionOfInspection}`).format(Configure.FULL_TIME);
      } else if (dataUpdate.dateOfPermit && dataUpdate.timeOfPermit) {
        dataUpdate.dateClearanced = moment(`${dataUpdate.dateOfPermit} ${dataUpdate.timeOfPermit}`).format(Configure.FULL_TIME);
      }

      if (clearanceTypeId == ClearanceTypeId.XacNhanChinhSua && isComplete == false) {
        dataUpdate.times = (dataUpdate.times || 0) + 1;
      } else if (this.isLowValue == false && clearanceTypeId == ClearanceTypeId.Tam) {
        dataUpdate.tempTimes = (dataUpdate.tempTimes || 0) + 1;
      }
      //#endregion

      if (dataUpdate.dateClearanced && dataUpdate.phase != ActionKey.ACCEPT_CLEARANCE) {
        Utilities.sendTelegramNoti('SAVE_DATA', `UPDATE PHASE: ${dataUpdate.HAWBClearance} - ${dataUpdate.phase} - ${phaseKey} - ${clearanceTypeId}`);
        // dataUpdate.phase = ActionKey.ACCEPT_CLEARANCE;
      }

      //#region --- Details
      if (this.isLowValue) {
        let xmlDetail = _.isArray(xmlDetails) ? xmlDetails[0] : xmlDetails;

        let itemNames: any = '';
        if (xmlDetail.ItemName['_text']) {
          itemNames = xmlDetail.ItemName['_text'].split(', ');

          if (itemNames.length > detailsUpdate.length) {
            for (let idx = detailsUpdate.length; idx < itemNames.length; idx++) {
              itemNames[detailsUpdate.length - 1] = itemNames[detailsUpdate.length - 1] + ', ' + itemNames[idx];
            }
          }
        }

        for (let idx = 0; idx < detailsUpdate.length; idx++) {
          let detailUpdate: any = detailsUpdate[idx];

          if (detailUpdate.originalPlaceName !== undefined) {
            detailUpdate.originalPlaceName = xmlDetail.OriginalPlaceName['_text'];
          }

          if (detailUpdate.priceVND !== undefined) {
            detailUpdate.priceVND = parseFloat(dataUpdate.valueClearanceVND) * parseFloat(detailUpdate.invoiceValue);
          }

          if (dataUpdate.phase == ActionKey.ACCEPT_CLEARANCE) {
            detailUpdate.itemNameVN = itemNames[idx] || '';
          }

          await detailUpdate.save({ transaction: trans });
        }

        if (dataUpdate.priceVND !== undefined) {
          let customsValue = xmlDetails.Customsvalue || xmlDetails.CustomsValue;
          if (customsValue) {
            dataUpdate.priceVND = parseInt(customsValue['_text'].replace(/,/gi, ''));
          }
        }

        if (dataUpdate.totalOfTaxValue !== undefined) {
          dataUpdate.totalOfTaxValue = parseFloat(xmlDetails.TotalOfTaxValue['_text'].replace(/,/gi, ''));
        }
      } else {
        for (let idx = 0; idx < detailsUpdate.length; idx++) {
          let detailUpdate = detailsUpdate[idx];

          if (_.isArray(xmlDetails)) {
            let xmlId = xmlDetails.findIndex((o: any) => {
              if (_.size(o) > 0) {
                return detailUpdate.position + 1 == parseInt(o.FieldNo['_text']);
              }
              return false;
            });

            if (xmlId < 0) {
              continue;
            }

            console.log(` --- [%s] [CLEARANCE_ITEM] ${dataUpdate.HAWB} - ${detailUpdate.itemNameVN} - ${xmlDetails[xmlId].ItemName['_text']}`, moment().format(Configure.FULL_TIME));

            this.rawDetailData = xmlDetails[xmlId];
          } else {
            if (detailUpdate.position + 1 == parseInt(xmlDetails.FieldNo['_text'])) {
              this.rawDetailData = xmlDetails;
            } else {
              continue;
            }
          }

          let data = this.renderDetailData(detailUpdate.get({ plain: true }));

          await detailModel.update(data, { where: { id: detailUpdate.id }, transaction: trans });
        }
      }
      //#endregion

      dataUpdate.dateAction = new Date();
      dataUpdate.isEditProcessing = false;
      dataUpdate.isError = false;

      await dataUpdate.save({ transaction: trans });
      await trans.commit();

      console.log(` --- [%s] [CLEARANCE_PHASE] ${dataUpdate.HAWB} - ${dataUpdate.phase} - ${phase} - ${phaseKey} - ${clearanceTypeId}`, moment().format(Configure.FULL_TIME));

      return { isSuccess: true, data: dataUpdate };
    } catch (error) {
      console.log(` --- [%s] [CLEARANCE_SAVE_FILE] UPDATE FAILD ${dataUpdate.HAWB}: %o`, moment().format(Configure.FULL_TIME), error);
      await trans.rollback();

      if (error.message == 'could not serialize access due to concurrent update' || error.message == 'could not serialize access due to read/write dependencies among transactions') {
        return { isSuccess: false };
      }

      Utilities.sendTelegramNoti('SAVE_DATA', `UPDATE FAILD: ${dataUpdate.HAWBClearance} - ${error.message}`);
      return { isSuccess: false };
    }
  }
}
