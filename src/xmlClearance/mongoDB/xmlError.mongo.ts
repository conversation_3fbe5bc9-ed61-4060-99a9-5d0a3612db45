import { Model, Document, model, Schema } from 'mongoose';

export interface XmlErrorDoc extends Document {
  key: string;
  description: string;
  isActive: boolean;
}

export interface XmlErrorModel extends Model<XmlErrorDoc> {}

let xmlErrorSchema = new Schema(
  {
    key: String,
    description: String,
    isActive: { type: Boolean, default: true }
  },
  { timestamps: true }
);

let XmlErrorMongo: XmlErrorModel = model<XmlErrorDoc, XmlErrorModel>('clearance_xml_errors', xmlErrorSchema);

export default XmlErrorMongo;
