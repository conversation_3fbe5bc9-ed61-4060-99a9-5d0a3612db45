import { Model, Document, model, Schema, Mongoose } from 'mongoose';

export interface XmlStructureDoc extends Document {
  name: string;
  serviceId: number;
  isLowValue: boolean;
  isSendEcus: boolean;
  clearanceTypeId: number[];
  model: any;
  modelDetail: any;
  xmlStructure: any;
  xmlStructureDetail: any;
  keyMap: any;
  keyDetailMap: any;
  maxSplit: number;
}

export interface XmlStructureModel extends Model<XmlStructureDoc> {
  getStructure(serviceId: number, isLowValue: boolean): any;
  createStructureDefault(data: any): any;
}

let xmlStructureSchema = new Schema(
  {
    name: String,
    serviceId: Number,
    isLowValue: Boolean,
    isSendEcus: Boolean,
    clearanceTypeId: [Number],
    model: Schema.Types.Mixed,
    modelDetail: Schema.Types.Mixed,
    xmlStructure: Schema.Types.Mixed,
    xmlStructureDetail: Schema.Types.Mixed,
    keyMap: Schema.Types.Mixed,
    keyDetailMap: Schema.Types.Mixed,
    maxSplit: Number
  },
  { timestamps: true }
);

xmlStructureSchema.statics.createStructureDefault = function (data: any) {
  return this.create(data);
};

xmlStructureSchema.statics.getStructure = function (serviceId: number, isLowValue: boolean) {
  return this.findOne({ serviceId: serviceId, isLowValue: isLowValue }).sort({ createdAt: -1 });
};

let XmlStructureMongo: XmlStructureModel = model<XmlStructureDoc, XmlStructureModel>('clearance_xml_structure', xmlStructureSchema);

export default XmlStructureMongo;
