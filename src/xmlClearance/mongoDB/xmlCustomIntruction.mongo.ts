import { Model, Document, model, Schema, Mongoose } from 'mongoose';

export interface XmlCustomIntructionDoc extends Document {
  hawb: string;
  hawbClearance: string;

  fileName: string;

  message: any;
}

export interface XmlCustomIntructionModel extends Model<XmlCustomIntructionDoc> { }

let xmlCustomIntructionSchema = new Schema(
  {
    hawb: String,
    hawbClearance: String,

    fileName: String,

    message: Schema.Types.Mixed
  },
  { timestamps: true }
);

let XmlCustomIntructionMongo: XmlCustomIntructionModel = model<XmlCustomIntructionDoc, XmlCustomIntructionModel>('clearance_xml_customs_intruction', xmlCustomIntructionSchema);

export default XmlCustomIntructionMongo;
