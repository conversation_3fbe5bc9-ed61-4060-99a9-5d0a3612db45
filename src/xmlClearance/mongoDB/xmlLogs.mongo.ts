import { Model, Document, model, Schema, Mongoose } from 'mongoose';

export interface XmlLogDoc extends Document {
  hawb: string;
  hawbClearance: string;
  warehouseCode: string;

  clearanceType: string;

  serviceId: number;
  isLowValue: boolean;
  clearanceTypeId: number;

  terminalServe?: string;
  terminalFolder?: string;

  subFolder?: string;

  fileNameSend?: string;
  dateSend?: Date;

  fileNameReceive?: string[];
  dateReceive?: Date;

  timesSend?: number;
  timesReceive?: number;

  isComplete?: boolean;
}

export interface XmlLogModel extends Model<XmlLogDoc> {
  logSend(model: any, terminal: any, fileSend: string, subFolder: string): any;
  logReceive(fileReceive: string, terminal: any): any;
}

let xmlLogSchema = new Schema(
  {
    hawb: String,
    hawbClearance: String,
    warehouseCode: String,

    clearanceType: String,

    serviceId: Number,
    isLowValue: Boolean,
    clearanceTypeId: Number,

    terminalServe: String,
    terminalFolder: String,

    subFolder: String,

    fileNameSend: String,
    dateSend: Date,

    fileNameReceive: [String],
    dateReceive: Date,

    timesSend: { type: Number, default: 0 },
    timesReceive: { type: Number, default: 0 },

    isComplete: { type: Boolean, default: false }
  },
  { timestamps: true }
);

xmlLogSchema.statics.logSend = async function (model: any, terminal: any, fileSend: string, subFolder?: string) {
  let data = await XmlLogMongo.updateOne(
    {
      hawb: model.HAWB,
      hawbClearance: model.HAWBClearance,
      warehouseCode: model.customsWarehouseCode,
      fileNameSend: fileSend,
      isComplete: false
    },
    {
      hawb: model.HAWB,
      hawbClearance: model.HAWBClearance,
      warehouseCode: model.customsWarehouseCode,
      serviceId: model.serviceId,
      isLowValue: model.isLowValue,
      clearanceType: model.clearanceType,
      clearanceTypeId: model.clearanceTypeId,
      terminalServe: terminal.host,
      terminalFolder: terminal.folerName,
      fileNameSend: fileSend,
      dateSend: new Date(),
      $inc: { timesSend: 1 },
      subFolder: subFolder
    },
    { upsert: true, setDefaultsOnInsert: true }
  );

  return data;
};

xmlLogSchema.statics.logReceive = async function (fileReceive: string, terminal: any) {
  return await XmlLogMongo.findOneAndUpdate(
    { hawbClearance: fileReceive.substring(0, fileReceive.indexOf('_')), terminalServe: terminal.host, terminalFolder: terminal.folerName },
    { dateReceive: new Date(), $push: { fileNameReceive: fileReceive } },
    { sort: { createdAt: -1 } }
  );
};

let XmlLogMongo: XmlLogModel = model<XmlLogDoc, XmlLogModel>('clearance_xml_logs', xmlLogSchema);

export default XmlLogMongo;
