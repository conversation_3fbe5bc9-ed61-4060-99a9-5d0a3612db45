import Configure from '../emuns/configures';
import XmlLogMongo from './mongoDB/xmlLogs.mongo';
import XmlStructureMongo from './mongoDB/xmlstructure.mongo';
import _ from 'lodash';
import fs from 'fs';
import moment from 'moment';
import path from 'path';
import { ActionKey } from '../emuns/action';
import { BaseModel } from '../models/base.model';
import { ClearanceTypeId, ServiceId, ClearanceTypeInfo, ClearanceRabbit, DefaultSetting, ClearanceType, ClearanceDefaultData } from './enum';
import { ClearanceXml, FormatType } from './clearanceXml';
import { Ftp } from '../util/ftp';
import { RabbitSend } from '../util/rabbit';
import { Op, Sequelize } from 'sequelize';
import { TMMAPI } from './tmm.api';
import { ImportDetail, ImportTransaction, MEC, MIC, TaxCode, Terminal, Warehouse } from '../models/index.model';
import { EcusFolder } from '../models/ecusFolder.model';
import Axios from 'axios';
import Utilities from '../util/utilities';
import ImportClearanceService from '../modules/v1/importClearance/importClearance.service';
import XmlCustomIntructionMongo from './mongoDB/xmlCustomIntruction.mongo';

type NonAbstract<T> = { [P in keyof T]: T[P] };
type Constructor<T> = new () => T;
type NonAbstractTypeOfModel<T> = Constructor<T> & NonAbstract<typeof BaseModel>;

export interface ClearanceResult {
  isSuccess: boolean;
  data?: any;
  message?: string;
}

export class Clearance {
  private numberSend = 10;

  //#region --- Private Function
  private getClearanceType(serviceId: number, isLowValue: boolean, clearanceTypeId: ClearanceTypeId) {
    let type: any = {};
    for (let [key, value] of ClearanceTypeInfo) {
      if (value.serviceId == serviceId && value.isLowValue == isLowValue && value.clearanceTypeId == clearanceTypeId) {
        type[key] = value;
        break;
      }
    }

    return type;
  }

  private getClearanceTypeName(string: string) {
    for (const key in ClearanceType) {
      if (string.includes(key)) {
        return ClearanceTypeInfo.get(key);
      }
    }

    if (string.includes('_VAD800')) {
      return ClearanceTypeInfo.get(ClearanceType.IDA);
    }

    return null;
  }

  private unLinkFile(data: any[], isRemove: boolean) {
    for (let idx = 0; idx < data.length; idx++) {
      const fileName = data[idx]['fileName'] || data[idx];

      if (!fs.existsSync(`${Configure.CLEARANCE_FOLDER_RECEIVE}/${fileName}`)) {
        continue;
      }

      if (isRemove) {
        fs.unlinkSync(`${Configure.CLEARANCE_FOLDER_RECEIVE}/${fileName}`);
        continue;
      }

      if (!fs.existsSync(`${Configure.CLEARANCE_FOLDER_RECEIVE}BK`)) {
        fs.mkdirSync(`${Configure.CLEARANCE_FOLDER_RECEIVE}BK`, { recursive: true });
      }

      fs.renameSync(`${Configure.CLEARANCE_FOLDER_RECEIVE}/${fileName}`, `${Configure.CLEARANCE_FOLDER_RECEIVE}BK/${fileName}`);
    }
  }

  private async updateClearanceTMS(hawb: string[]) {
    let details = await ImportDetail.findAll({ where: { HAWB: hawb }, attributes: ['specialConsumptionPrice', 'environmentPrice', 'VATPrice', 'importPrice', 'itemName', 'HAWB'], raw: true });
    if (details.length <= 0) {
      Utilities.sendTelegramError('[UPDATE_CLEARANCE_TMS]', `Không tìm thấy sản phẩm cho HAWB: ${hawb.join(', ')}`);
    }

    await new RabbitSend().send(ClearanceRabbit.TMS_CLEARANCE, [details], { durable: true, autoDelete: false });
  }

  private async updateManifest(HAWB: string, phase: number, serviceId: number, isLowValue: boolean) {
    try {
      if (phase == ActionKey.ACCEPT_CLEARANCE) {
        if (serviceId == ServiceId.outbound) {
          new TMMAPI().manifestOutboundUpdate([HAWB]);
        }

        if (serviceId == ServiceId.inbound) {
          new TMMAPI().manifestInboundUpdate([HAWB], 7);
        }

        this.updateClearanceTMS([HAWB]);
        new ImportClearanceService().handlePushWebHook([HAWB], Configure.CLEARANCED);
        new RabbitSend().delayMs(ClearanceRabbit.UPDATE_TAX_CODE_NUMBER, [HAWB], Math.floor(Math.random() * 150 + 50), { durable: true, autoDelete: false });

        // if (isLowValue == true && serviceId == ServiceId.inbound) {
        //   new RabbitSend().delayMs(ClearanceRabbit.UPDATE_MIC_TAX_CODE_NUMBER, [HAWB], 1, { durable: true, autoDelete: false });
        // }
      }

      if (phase == ActionKey.INSPECTION_KIND) {
        if (serviceId == ServiceId.outbound) {
          new TMMAPI().manifestOutboundUpdate([HAWB]);
        }

        if (serviceId == ServiceId.inbound) {
          new TMMAPI().manifestInboundUpdate([HAWB], 6);
        }
      }
    } catch (error) {
      console.log(` --- [%s] [updateManifest]: %o`, moment().format(Configure.FULL_TIME), error);
    }
  }

  private async updateTaxCode(dataUpdate: any) {
    try {
      if (dataUpdate.serviceId == ServiceId.outbound) {
        //#region --- Cập nhật MST khách hàng doanh nghiệp
        let taxCode = dataUpdate.exporterCode;

        if (!taxCode || taxCode != Configure.IMPORTER_CODE) {
          if (dataUpdate.isError === false) {
            let tax = await TaxCode.findOrBuild({ where: { code: taxCode }, defaults: { code: taxCode, name_vn: dataUpdate.exporterName, address: dataUpdate.addressOfExporter } });

            tax[0].code = taxCode;
            tax[0].name_vn = dataUpdate.exporterName;
            tax[0].address = dataUpdate.addressOfExporter;
            tax[0].isClearanced = true;

            await tax[0].save();
          } else {
            await TaxCode.destroy({ where: { code: taxCode, isClearanced: false } });
          }
        }
        //#endregion
      }
    } catch (error) {
      console.log(` --- [%s] [updateTaxCode]: %o`, moment().format(Configure.FULL_TIME), error);
    }
  }
  //#endregion

  // --- Lấy lại thông điệp
  async getHawbData(hawbs: string | string[], clearanceType: string) {
    try {
      let hawb = _.isArray(hawbs) ? _.join(hawbs, ',') : hawbs;
      if (!hawb) {
        return { isSuccess: false, message: 'Không tìm thấy HAWB' };
      }

      let ecusUrl = process.env.ECUS_URL || 'https://devapi.globex.vn/customs-resp/details';
      let response = await Axios.get(ecusUrl, { params: { hawb, type_id: clearanceType == 'MIC' ? 1 : clearanceType == 'MEC' ? 2 : null }, headers: { 'content-type': 'application/json' } });

      let data = response.data;

      if (data.success == false) {
        console.error(` --- [%s] [clearance_getHAWBData]: %o`, moment().format(Configure.FULL_TIME), data);
        return { isSuccess: false, message: `Lấy lại thông điệp thất bại` };
      }

      let hawbError: string[] = [];
      let hawbSuccess: string[] = [];
      let hawbsOutbound: string[] = [];
      let hawbsInbound6: string[] = [];
      let hawbsInbound7: string[] = [];

      for (const hawb in data.data) {
        let hawbData = data.data[hawb];

        if (hawbData.error) {
          hawbError.push(`${hawb}: ${hawbData.error}`);
          continue;
        }

        let clearanceType = hawbData.DeclarationType;

        hawbData = _.mapKeys(hawbData, function (v, k) {
          return _.lowerFirst(k);
        });

        if (!hawbData.currencyExchangeRate) {
          delete hawbData.currencyExchangeRate;
        } else {
          hawbData.valueClearanceVND = parseFloat(hawbData.currencyExchangeRate);
        }

        if (clearanceType == 'MEC' || clearanceType == 'MEE') {
          try {
            let mec = await MEC.findOne({ where: { HAWBClearance: hawb } });
            if (!mec) {
              hawbError.push(`${hawb}: Không tìm thấy dữ liệu MEC HAWBClearance`);
              continue;
            }

            Object.assign(mec, hawbData);

            mec.isError = false;

            if (hawbData.valueClearanceVND > 0) {
              mec.priceVND = parseFloat(hawbData.valueClearanceVND) * mec.totalOfTaxValue;
            }

            if (mec.exporterCode == '9999999999998') {
              mec.exporterName = 'Cá nhân - Tổ chức không có mã số thuế';
            }

            if (mec.declarationPlannedDate) {
              mec.declarationPlannedDate = moment(hawbData.declarationPlannedDate, 'DDMMYYYY').format('YYYY-MM-DD');
            }

            if (mec.registeredTime) {
              mec.registeredTime = moment(hawbData.registeredTime, 'HHmmss').format('HH:mm:ss');
            }

            if (hawbData.dateOfPermit && hawbData.timeOfPermit) {
              mec.dateOfPermit = moment(hawbData.dateOfPermit, 'DDMMYYYY').format('YYYY-MM-DD');
              mec.timeOfPermit = moment(hawbData.timeOfPermit, 'HHmmss').format('HH:mm:ss');

              mec.dateClearanced = moment(`${hawbData.dateOfPermit} ${hawbData.timeOfPermit}`, 'DDMMYYYY HHmmss').format('YYYY-MM-DD HH:mm:ss');

              mec.phase = ActionKey.ACCEPT_CLEARANCE;
            }

            if (!mec.customsOfficeName) {
              mec.customsOfficeName = ClearanceDefaultData[mec.customsOffice] || null;
            }
            if (!mec.agentName) {
              mec.agentName = ClearanceDefaultData[mec.agentCode] || null;
            }
            if (!mec.customsWarehouseName) {
              mec.customsWarehouseName = ClearanceDefaultData[mec.customsWarehouseCode] || null;
            }
            if (!mec.loadingPortName) {
              mec.loadingPortName = ClearanceDefaultData[mec.loadingPortCode] || null;
            }

            await mec.save();

            if ([ActionKey.ACCEPT_CLEARANCE, ActionKey.INSPECTION_KIND].includes(mec.phase)) {
              hawbsOutbound.push(mec.HAWB);
            }

            hawbSuccess.push(hawb);
          } catch (error) {
            console.log(` --- [%s] [error][clearance_getHAWBData]: Cập nhật MEC thất bại %o`, moment().format(Configure.FULL_TIME), error);
            hawbError.push(`${hawb}: Cập nhật MEC thất bại`);
          }

          continue;
        }

        if (clearanceType == 'MIC' || clearanceType == 'MIE') {
          try {
            let mic = await MIC.findOne({ where: { HAWBClearance: hawb } });
            if (!mic) {
              hawbError.push(`${hawb}: Không tìm thấy dữ liệu MIC`);
              continue;
            }

            Object.assign(mic, hawbData);
            mic.isError = false;

            if (hawbData.valueClearanceVND > 0) {
              mic.priceVND = parseFloat(hawbData.valueClearanceVND) * mic.totalInvoicePrice;
            }

            if (mic.importerCode == '9999999999998') {
              mic.importerName = 'Cá nhân - Tổ chức không có mã số thuế';
            }

            if (hawbData.declarationPlannedDate) {
              mic.declarationPlannedDate = moment(hawbData.declarationPlannedDate, 'DDMMYYYY').format('YYYY-MM-DD');
            }

            if (hawbData.registeredTime) {
              mic.registeredTime = moment(hawbData.registeredTime, 'HHmmss').format('HH:mm:ss');
            }

            if (mic.dateOfCompletion) {
              mic.dateOfCompletion = moment(hawbData.dateOfCompletion, 'DDMMYYYY').format('YYYY-MM-DD');
            }

            if (mic.timeCompletion) {
              mic.timeCompletion = moment(hawbData.timeCompletion, 'HHmmss').format('HH:mm:ss');
            }

            if (hawbData.dateOfPermit && hawbData.timeOfPermit) {
              mic.dateOfPermit = moment(hawbData.dateOfPermit, 'DDMMYYYY').format('YYYY-MM-DD');
              mic.timeOfPermit = moment(hawbData.timeOfPermit, 'HHmmss').format('HH:mm:ss');

              mic.dateClearanced = moment(`${hawbData.dateOfPermit} ${hawbData.timeOfPermit}`, 'DDMMYYYY HHmmss').format('YYYY-MM-DD HH:mm:ss');

              mic.phase = ActionKey.ACCEPT_CLEARANCE;
            }

            if (!mic.customsOfficeName) {
              mic.customsOfficeName = ClearanceDefaultData[mic.customsOffice] || null;
            }
            if (!mic.agentName) {
              mic.agentName = ClearanceDefaultData[mic.agentCode] || null;
            }
            if (!mic.customsClearanceName) {
              mic.customsClearanceName = ClearanceDefaultData[mic.customsWarehouseCode] || null;
            }
            if (!mic.loadingLocationName) {
              mic.loadingLocationName = ClearanceDefaultData[mic.loadingLocationName] || null;
            }
            if (!mic.portOfDischargeName) {
              mic.portOfDischargeName = ClearanceDefaultData[mic.portOfDischarge] || null;
            }

            await mic.save();

            if (mic.phase == ActionKey.SENT_INFOMATION) {
              hawbsInbound6.push(mic.HAWB);
            }

            if (mic.phase == ActionKey.ACCEPT_CLEARANCE) {
              hawbsInbound7.push(mic.HAWB);
            }

            hawbSuccess.push(hawb);
          } catch (error) {
            console.error(` --- [%s] [clearance_getHAWBData]: Cập nhật MIC thất bại %o`, moment().format(Configure.FULL_TIME), error);
            hawbError.push(`${hawb}: Cập nhật MIC thất bại`);
          }

          continue;
        }
      }

      if (_.size(hawbsOutbound)) {
        try {
          let updateManifest = await new TMMAPI().manifestOutboundUpdate(hawbsOutbound);
          console.log(' --- [%s] [clearance][saveMEC][outbound]: %o', moment().format(Configure.FULL_TIME), JSON.stringify(updateManifest));
        } catch (error) {
          console.error(' --- [%s] [error][clearance][saveMEC][outbound]: %o', moment().format(Configure.FULL_TIME), error);
        }
      }

      if (_.size(hawbsInbound6)) {
        try {
          let updateManifest = await new TMMAPI().manifestInboundUpdate(hawbsInbound6, 6);
          console.log(' --- [%s] [clearance][saveMIC]: %o', moment().format(Configure.FULL_TIME), JSON.stringify(updateManifest));
        } catch (error) {
          console.error(' --- [%s] [error][clearance][saveMIC]: %o', moment().format(Configure.FULL_TIME), error);
        }
      }

      if (_.size(hawbsInbound7)) {
        try {
          let updateManifest = await new TMMAPI().manifestInboundUpdate(hawbsInbound7, 7);
          console.log(' --- [%s] [clearance][saveMIC]: %o', moment().format(Configure.FULL_TIME), JSON.stringify(updateManifest));
        } catch (error) {
          console.error(' --- [%s] [error][clearance][saveMIC]: %o', moment().format(Configure.FULL_TIME), error);
        }
      }

      return { isSuccess: true, hawbSuccess: hawbSuccess, message: hawbError };
    } catch (error) {
      console.error(` --- [%s] [clearance_getHAWBData]: Cập nhật thất bại %o`, moment().format(Configure.FULL_TIME), error);
      return { isSuccess: false, message: 'Lỗi' };
    }
  }

  // --- Lấy lại thông điệp ver cũ
  async getHawbData1(hawbs: string | string[]) {
    try {
      let hawb = _.isArray(hawbs) ? _.join(hawbs, ',') : hawbs;
      if (!hawb) {
        return { isSuccess: false, message: 'Không tìm thấy HAWB' };
      }

      let ecusUrl = process.env.ECUS_URL || 'https://devapi.globex.vn/customs-resp/';

      let response = await Axios.get(ecusUrl + 'details', {
        params: { hawb },
        headers: { 'content-type': 'application/json' }
      });

      let data = response.data;

      if (data.success == false) {
        console.log(` --- [%s] [error][clearance_getHAWBData]: %o`, moment().format(Configure.FULL_TIME), data);
        return { isSuccess: false, message: `Lấy lại HAWB thất bại` };
      }

      let hawbError: string[] = [];
      let hawbSuccess: string[] = [];
      let hawbsOutbound: string[] = [];
      let hawbsInbound6: string[] = [];
      let hawbsInbound7: string[] = [];

      for (const hawb in data.data) {
        let hawbData = data.data[hawb];

        if (hawbData.error) {
          hawbError.push(`${hawb}: ${hawbData.error}`);
          continue;
        }

        let clearanceType = hawbData.DeclarationType;
        let clearanceCode = hawbData.DeclarationCode;

        hawbData = _.mapKeys(hawbData, function (v, k) {
          return _.lowerFirst(k);
        });

        if (!hawbData.currencyExchangeRate) {
          delete hawbData.currencyExchangeRate;
        } else {
          hawbData.valueClearanceVND = parseFloat(hawbData.currencyExchangeRate);
        }

        if (!hawbData.NameHeadCustomsOffice) {
          delete hawbData.NameHeadCustomsOffice;
        }

        if (!hawbData.CurrencyCode) {
          delete hawbData.CurrencyCode;
        }

        if (clearanceType == 'MEC' || clearanceType == 'MEE') {
          try {
            let mec = await MEC.findOne({ where: { HAWBClearance: hawb } });
            if (!mec) {
              hawbError.push(`${hawb}: Không tìm thấy dữ liệu MEC HAWBClearance`);
              continue;
            }

            let clearanceTypeId = mec.phase == ActionKey.SUBMIT_INFOMATION ? ClearanceTypeId.Tam : null;

            Object.assign(mec, hawbData);

            if (hawbData.valueClearanceVND > 0) {
              mec.priceVND = parseFloat(hawbData.valueClearanceVND) * mec.totalOfTaxValue;
            }

            let phase = new ClearanceXml(FormatType.ECUS2CPN).getClearancePhase(clearanceCode, clearanceTypeId);
            if (phase) {
              mec.phase = phase;
            }

            mec.isError = false;

            if (mec.exporterCode == '9999999999998') {
              mec.exporterName = 'Cá nhân - Tổ chức không có mã số thuế';
            }

            if (mec.declarationPlannedDate) {
              mec.declarationPlannedDate = moment(hawbData.declarationPlannedDate, 'DDMMYYYY').format('YYYY-MM-DD');
            }

            if (mec.registeredTime) {
              mec.registeredTime = moment(hawbData.registeredTime, 'HHmmss').format('HH:mm:ss');
            }

            if (mec.phase == ActionKey.ACCEPT_CLEARANCE) {
              mec.dateOfPermit = moment(hawbData.dateOfPermit, 'DDMMYYYY').format('YYYY-MM-DD');
              mec.timeOfPermit = moment(hawbData.timeOfPermit, 'HHmmss').format('HH:mm:ss');

              mec.dateClearanced = moment(`${hawbData.dateOfPermit} ${hawbData.timeOfPermit}`, 'DDMMYYYY HHmmss').format('YYYY-MM-DD HH:mm:ss');
            }

            if (!mec.customsOfficeName) {
              mec.customsOfficeName = ClearanceDefaultData[mec.customsOffice] || null;
            }
            if (!mec.agentName) {
              mec.agentName = ClearanceDefaultData[mec.agentCode] || null;
            }
            if (!mec.customsWarehouseName) {
              mec.customsWarehouseName = ClearanceDefaultData[mec.customsWarehouseCode] || null;
            }
            if (!mec.loadingPortName) {
              mec.loadingPortName = ClearanceDefaultData[mec.loadingPortCode] || null;
            }

            await mec.save();

            if ([ActionKey.ACCEPT_CLEARANCE, ActionKey.INSPECTION_KIND].includes(mec.phase)) {
              hawbsOutbound.push(mec.HAWB);
            }

            hawbSuccess.push(hawb);
          } catch (error) {
            console.log(` --- [%s] [error][clearance_getHAWBData]: Cập nhật MEC thất bại %o`, moment().format(Configure.FULL_TIME), error);
            hawbError.push(`${hawb}: Cập nhật MEC thất bại`);
          }

          continue;
        }

        if (clearanceType == 'MIC' || clearanceType == 'MIE') {
          try {
            let mic = await MIC.findOne({ where: { HAWBClearance: hawb } });
            if (!mic) {
              hawbError.push(`${hawb}: Không tìm thấy dữ liệu MIC`);
              continue;
            }

            let clearanceTypeId = mic.phase == ActionKey.SUBMIT_INFOMATION ? ClearanceTypeId.Tam : null;

            Object.assign(mic, hawbData);

            if (hawbData.valueClearanceVND > 0) {
              mic.priceVND = parseFloat(hawbData.valueClearanceVND) * mic.totalInvoicePrice;
            }

            let phase = new ClearanceXml(FormatType.ECUS2CPN).getClearancePhase(clearanceCode, clearanceTypeId);
            if (phase) {
              mic.phase = phase;
            }
            mic.isError = false;

            if (mic.importerCode == '9999999999998') {
              mic.importerName = 'Cá nhân - Tổ chức không có mã số thuế';
            }

            if (hawbData.declarationPlannedDate) {
              mic.declarationPlannedDate = moment(hawbData.declarationPlannedDate, 'DDMMYYYY').format('YYYY-MM-DD');
            }

            if (hawbData.registeredTime) {
              mic.registeredTime = moment(hawbData.registeredTime, 'HHmmss').format('HH:mm:ss');
            }

            if (mic.phase == ActionKey.ACCEPT_CLEARANCE) {
              mic.dateOfPermit = moment(hawbData.dateOfPermit, 'DDMMYYYY').format('YYYY-MM-DD');
              mic.timeOfPermit = moment(hawbData.timeOfPermit, 'HHmmss').format('HH:mm:ss');

              mic.dateOfCompletion = moment(hawbData.dateOfCompletion, 'DDMMYYYY').format('YYYY-MM-DD');
              mic.timeCompletion = moment(hawbData.timeCompletion, 'HHmmss').format('HH:mm:ss');

              mic.dateClearanced = moment(`${hawbData.dateOfCompletion} ${hawbData.timeCompletion}`, 'DDMMYYYY HHmmss').format('YYYY-MM-DD HH:mm:ss');
            }

            if (!mic.customsOfficeName) {
              mic.customsOfficeName = ClearanceDefaultData[mic.customsOffice] || null;
            }
            if (!mic.agentName) {
              mic.agentName = ClearanceDefaultData[mic.agentCode] || null;
            }
            if (!mic.customsClearanceName) {
              mic.customsClearanceName = ClearanceDefaultData[mic.customsWarehouseCode] || null;
            }
            if (!mic.loadingLocationName) {
              mic.loadingLocationName = ClearanceDefaultData[mic.loadingLocationName] || null;
            }
            if (!mic.portOfDischargeName) {
              mic.portOfDischargeName = ClearanceDefaultData[mic.portOfDischarge] || null;
            }

            await mic.save();

            if (mic.phase == ActionKey.SENT_INFOMATION) {
              hawbsInbound6.push(mic.HAWB);
            }

            if (mic.phase == ActionKey.ACCEPT_CLEARANCE) {
              hawbsInbound7.push(mic.HAWB);
            }

            hawbSuccess.push(hawb);
          } catch (error) {
            console.log(` --- [%s] [error][clearance_getHAWBData]: Cập nhật MIC thất bại %o`, moment().format(Configure.FULL_TIME), error);
            hawbError.push(`${hawb}: Cập nhật MIC thất bại`);
          }

          continue;
        }
      }

      if (_.size(hawbsOutbound)) {
        try {
          let updateManifest = await new TMMAPI().manifestOutboundUpdate(hawbsOutbound);
          console.log(' --- [%s] [clearance][saveMEC][outbound]: %o', moment().format(Configure.FULL_TIME), JSON.stringify(updateManifest));
        } catch (error) {
          console.error(' --- [%s] [error][clearance][saveMEC][outbound]: %o', moment().format(Configure.FULL_TIME), error);
        }
      }

      if (_.size(hawbsInbound6)) {
        try {
          let updateManifest = await new TMMAPI().manifestInboundUpdate(hawbsInbound6, 6);
          console.log(' --- [%s] [clearance][saveMIC]: %o', moment().format(Configure.FULL_TIME), JSON.stringify(updateManifest));
        } catch (error) {
          console.error(' --- [%s] [error][clearance][saveMIC]: %o', moment().format(Configure.FULL_TIME), error);
        }
      }

      if (_.size(hawbsInbound7)) {
        try {
          let updateManifest = await new TMMAPI().manifestInboundUpdate(hawbsInbound7, 7);
          console.log(' --- [%s] [clearance][saveMIC]: %o', moment().format(Configure.FULL_TIME), JSON.stringify(updateManifest));
        } catch (error) {
          console.error(' --- [%s] [error][clearance][saveMIC]: %o', moment().format(Configure.FULL_TIME), error);
        }
      }

      return { isSuccess: true, hawbSuccess: hawbSuccess, message: hawbError };
    } catch (error) {
      console.log(` --- [%s] [error][clearance_getHAWBData]: Cập nhật thất bại %o`, moment().format(Configure.FULL_TIME), error);
      return { isSuccess: false, message: 'Lỗi' };
    }
  }

  //#region --- FILE SERVER
  async getPendingFile() {
    let list = fs.readdirSync(Configure.CLEARANCE_FOLDER_RECEIVE);
    _.pull(list, 'BK');

    return { isSuccess: true, data: { filePending: list.length, files: _.take(list, 50) } };
  }

  async getBackupFile() {
    let list = fs.readdirSync(Configure.CLEARANCE_FOLDER_RECEIVE + '/BK');

    return { isSuccess: true, data: { filePending: list.length, files: list } };
  }

  async moveBackupFile(file: string) {
    let folder = Configure.CLEARANCE_FOLDER_RECEIVE;
    let backup = Configure.CLEARANCE_FOLDER_RECEIVE + '/BK';

    let move = fs.renameSync(`${backup}/${file}`, `${folder}/${file}`);

    return { isSuccess: true, data: { move } };
  }

  async removeFile(file: string) {
    this.unLinkFile([file], true);

    return { isSuccess: true };
  }

  async readFile(file: string) {
    let clearanceFile = new ClearanceXml(FormatType.ECUS2CPN).readFiles(file);

    return { isSuccess: true, data: clearanceFile };
  }
  //#endregion

  //#region --- CPN TO ECUS
  // 1. Yêu cầu đẩy tờ khai lên ecus
  async push(serviceId: ServiceId, isLowValue: boolean, clearanceTypeId: ClearanceTypeId, hawbs: string[], isPrioritize?: boolean): Promise<ClearanceResult> {
    try {
      //#region --- Kiểm tra thông tin đẩy tờ khai
      let clearanceTypeInfo = this.getClearanceType(serviceId, isLowValue, clearanceTypeId);
      if (_.size(clearanceTypeInfo) <= 0) {
        console.log(` --- [%s] [CLEARANCE_PUSH_XML][clearanceTypeInfo][${serviceId}][${isLowValue}][${clearanceTypeId}]`, moment().format(Configure.FULL_TIME));
        return { isSuccess: false, message: `Trạng thái thông quan không hợp lệ!` };
      }

      let structure = await XmlStructureMongo.findOne({ serviceId: serviceId, isLowValue: isLowValue, isSendEcus: true, clearanceTypeId: { $in: [clearanceTypeId] } });
      if (!structure) {
        console.log(` --- [%s] [error][clearance][push][structure]: Không tìm thấy cấu trúc file`, moment().format(Configure.FULL_TIME));
        return { isSuccess: false, message: `Không tìm thấy thông tin dịch vụ!` };
      }

      let fileName = structure.model.file;
      if (!fileName) {
        console.log(` --- [%s] [error][clearance][xml][push][filename_model]: Không tìm thấy thông tin dịch vụ`, moment().format(Configure.FULL_TIME));
        return { isSuccess: false, message: `Không tìm thấy thông tin dịch vụ!` };
      }
      //#endregion

      //#region --- Kiểm tra thông tin hawb thông quan
      const models = await import(path.join(__dirname, '..', 'models', fileName));
      let model = models[structure.model.class];

      structure.model.attributes.push([Sequelize.literal(`${clearanceTypeId}`), 'clearanceTypeId'], [Sequelize.literal(`${isLowValue}`), 'isLowValue']);

      let hawbClearance = await model.findAll({ attributes: [...structure.model.attributes, ['terminalName', 'warehosuseId'], 'ftpId'], where: { HAWB: hawbs }, raw: true });
      if (_.size(hawbClearance) != _.size(hawbs)) {
        return { isSuccess: false, message: `Số lượng HAWB không chính xác` };
      }
      //#endregion

      //#region --- Chỉ thị hải quan
      let hawbCustomInstruction = _(hawbClearance)
        .map((x: any) => {
          if (x.clearanceMsg) {
            return x.HAWB;
          }
          return null;
        })
        .filter()
        .value();

      if (hawbCustomInstruction.length > 0) {
        return { isSuccess: false, message: `Tờ khai có chỉ thị hải quan: ${hawbCustomInstruction.join(', ')}` };
      }
      //#endregion

      //#region --- List HAWB đã gửi TK (có ftpId)
      let hawbFTP = _.filter(hawbClearance, (x) => x.ftpId);

      if (hawbFTP.length > 0) {
        let hawbGroupFTP = _.groupBy(hawbFTP, 'ftpId');

        let terminalIds = Object.keys(hawbGroupFTP);
        let terminals = await Terminal.findAll({ where: { id: terminalIds }, attributes: { exclude: ['createdAt', 'updatedAt', 'deletedAt', 'isDeleted'] }, raw: true });

        for (const terminalId in hawbGroupFTP) {
          let hawbFTP = hawbGroupFTP[terminalId];
          let terminal = _.find(terminals, (x) => x.id == parseFloat(terminalId));

          if (!terminal) {
            let hawbNumber = _(hawbFTP).map('hawbClearance').join();
            Utilities.sendTelegramError('[clearance][CPN2ECUS]', `Không tìm thấy terminal cho hawbClearance (ftpId): ${hawbNumber}`);

            continue;
          }

          let chunkData = [];
          if (hawbFTP.length > this.numberSend) {
            chunkData = _.chunk(hawbFTP, this.numberSend);
          } else {
            chunkData = [hawbFTP];
          }

          let hawbData = _.map(chunkData, (x) => {
            return { data: x, terminal };
          });
          await new RabbitSend().send(ClearanceRabbit.Push, hawbData, { durable: true, autoDelete: false });
        }
      }
      //#endregion

      //#region --- List HAWB chưa gửi TK (chưa có ftpId)
      let hawbNoFTP = _.filter(hawbClearance, (x) => !x.ftpId);

      if (hawbNoFTP.length > 0) {
        let warehouseIds = _(hawbNoFTP).map('warehosuseId').compact().uniq().value();

        //#region --- Lấy mã kho + terminal
        let warehouses = await Warehouse.findAll({
          attributes: ['id', 'terminalIds'],
          where: { id: warehouseIds, isActivated: true },
          include: [
            {
              model: Terminal,
              as: 'terminals',
              on: Sequelize.literal('"terminals"."id" = any("Warehouse"."terminalIds")'),
              required: true,
              where: { [Op.or]: [{ isLive: true }, { isPrioritize: true }] },
              attributes: { exclude: ['createdAt', 'updatedAt', 'deletedAt', 'isDeleted'] }
            }
          ],
          order: [Sequelize.literal(`RANDOM() `)]
        });

        if (warehouses.length <= 0 || warehouses.length != _.size(warehouseIds)) {
          Utilities.sendTelegramError('[clearance][CPN2ECUS]', `Không tìm thấy warehouse và terminal: ${warehouseIds.join()}`);
          return { isSuccess: false, message: `Không tìm thấy terminal` };
        }
        //#endregion

        let hawbGroupWarehouse = _.groupBy(hawbNoFTP, 'warehosuseId');

        for (const warehouseId in hawbGroupWarehouse) {
          let hawbWarehouse = hawbGroupWarehouse[warehouseId];

          let warehouse: any = _.find(warehouses, (x) => x.id == parseFloat(warehouseId));
          if (!warehouse) {
            Utilities.sendTelegramError('[clearance][CPN2ECUS]', `Không tìm thấy warehouse: ${warehouseId}`);
            continue;
          }

          let terminalsWarehouse = warehouse.terminals;
          if (!terminalsWarehouse || terminalsWarehouse.length <= 0) {
            Utilities.sendTelegramError('[clearance][CPN2ECUS]', `Không tìm thấy terminal: ${warehouse.terminalIds.join()}`);
            continue;
          }

          let terminals = [];
          if (isPrioritize == true) {
            terminals = _.filter(terminalsWarehouse, (x) => x.isPrioritize == true);
          } else {
            terminals = _.filter(terminalsWarehouse, (x) => x.isLive == true && x.isPrioritize == false);
          }

          if (terminals.length <= 0) {
            Utilities.sendTelegramError('[clearance][CPN2ECUS]', `Không tìm thấy terminal đã lọc: ${warehouse.terminalIds.join()}`);
            continue;
          }

          let rateWeight = _.sumBy(terminals, 'ratePush');
          rateWeight = rateWeight > 0 ? rateWeight : terminals.length;

          let hawbs = _.clone(hawbWarehouse);

          while (_.size(hawbs) > 0) {
            let hawbsPick = _.take(hawbs, rateWeight);
            hawbs = _.drop(hawbs, rateWeight);

            for (let idx = 0; idx < terminals.length; idx++) {
              let terminal = terminals[idx];

              let hawbNoPick = terminal.ratePush;

              let hawbPerTerminal = _.take(hawbsPick, hawbNoPick);
              hawbsPick = _.drop(hawbsPick, hawbNoPick);

              if (!hawbPerTerminal || hawbPerTerminal.length <= 0) {
                continue;
              }

              await new RabbitSend().send(ClearanceRabbit.Push, [{ data: hawbPerTerminal, terminal }], { durable: true, autoDelete: false });
            }
          }
        }
      }
      //#endregion

      return { isSuccess: true };
    } catch (error) {
      console.log(' --- [%s] [error][clearance][xml][push]: %o', moment().format(Configure.FULL_TIME), error);
      return { isSuccess: false, message: (error as any).message };
    }
  }

  // 2. Xử lý data -> xml + đẩy tờ khai lên ecus
  async handling(object: any) {
    let { data, terminal } = object;

    let hawbs = _.map(data, 'HAWB');

    let serviceId = data[0].serviceId;
    let isLowValue = data[0].isLowValue;
    let clearanceTypeId = data[0].clearanceTypeId;

    let structure = await XmlStructureMongo.findOne({ serviceId: serviceId, isLowValue: isLowValue, isSendEcus: true, clearanceTypeId: { $in: [clearanceTypeId] } });
    if (!structure) {
      console.log(` --- [%s] [HANDLING_STRUCTURE]: Không tìm thấy cấu trúc file`, moment().format(Configure.FULL_TIME));
      Utilities.sendTelegramNoti('HANDLING_STRUCTURE', ` Không tìm thấy cấu trúc file: ${hawbs.join(', ')}`);
      return { isSuccess: false };
    }

    //#region --- Thông tin gửi Tờ khai
    let clearanceTypeInfo = this.getClearanceType(serviceId, isLowValue, clearanceTypeId);
    if (_.size(clearanceTypeInfo) <= 0) {
      console.log(` --- [%s] [HANDLING_CLEARANCE_TYPE]: Trạng thái thông quan không hợp lệ ${clearanceTypeInfo}`, moment().format(Configure.FULL_TIME));
      Utilities.sendTelegramNoti('HANDLING_CLEARANCE_TYPE', ` Trạng thái thông quan không hợp lệ: ${hawbs.join(', ')}`);
      return { isSuccess: false };
    }

    let clearanceType: string = Object.keys(clearanceTypeInfo)[0];

    let fileName = structure.model.file;
    if (!fileName) {
      console.log(` --- [%s] [HANDLING_STRUCTURE_MODEL]:  Không tìm thấy file model`, moment().format(Configure.FULL_TIME));
      Utilities.sendTelegramNoti('HANDLING_STRUCTURE_MODEL', `  Không tìm thấy file model: ${hawbs.join(', ')}`);
      return { isSuccess: false };
    }
    //#endregion

    try {
      //#region --- Thông tin Tờ khai
      const models = await import(path.join(__dirname, '..', 'models', fileName));
      let model = models[structure.model.class];
      let order = null;

      if (structure.model.order && structure.model.sort) {
        order = [[Sequelize.col(`"${structure.model.detailKey}"."${structure.model.order}"`), structure.model.sort]];
      }
      //#endregion

      let hawbClearance = await model.scope(structure.model.scope).findAll({
        attributes: {
          include: [
            [Sequelize.literal(`'${clearanceType}'`), 'clearanceType'],
            [Sequelize.literal(`${clearanceTypeId}`), 'clearanceTypeId'],
            [Sequelize.literal(`${isLowValue}`), 'isLowValue']
          ]
        },
        where: { HAWB: hawbs },
        order: order
      });

      if (!hawbClearance || hawbClearance.length == 0) {
        console.log(` --- [%s] [HANDLING_HAWBCLEARANCE]:  Không tìm thấy dữ liệu`, moment().format(Configure.FULL_TIME));
        Utilities.sendTelegramNoti('HANDLING_HAWBCLEARANCE', `  Không tìm thấy dữ liệu: ${hawbs.join(', ')}`);
        return { isSuccess: false };
      }

      let hawbXml: string[] = [];

      let ecusFoler = await EcusFolder.findAll({ attributes: ['MAWB', 'HAWBs', 'folder'], where: { isActive: true } });

      let ftp = new Ftp(terminal.host, terminal.username, terminal.password, terminal.port, terminal.isTLS);
      await ftp.connect();

      for (let idx = 0; idx < hawbClearance.length; idx++) {
        let model = hawbClearance[idx];

        try {
          model.dateAction = new Date();
          model.ftpId = terminal.id;

          if (structure.model.class == 'MIC') {
            model.cargoWeight = model.cargoWeight == 0 ? 0.1 : model.cargoWeight;
          }

          await model.save();

          let clearanceTypeId = model.get('clearanceTypeId');
          let isLowValue = model.get('isLowValue');

          let xml: any = await new ClearanceXml(FormatType.CPN2ECUS, structure, model.get({ plain: true })).createFile();

          if (xml.isSuccess) {
            let folder: any = _.find(ecusFoler, (x) => (x.MAWB && x.MAWB == model.MAWB) || (x.HAWBs && x.HAWBs.includes(model.HAWB)));

            let subFolder = '';
            if (folder) {
              subFolder = folder.folder;

              try {
                await ftp.createFolder(`${terminal.folerName}/${subFolder}`);
              } catch (error) {}
            }

            XmlLogMongo.logSend(
              {
                ...model.get({ plain: true }),
                HAWBClearance: xml.data.HAWBClearance,
                isLowValue,
                clearanceType,
                clearanceTypeId
              },
              terminal,
              xml.data.fileName,
              subFolder
            );

            await ftp.uploadFile(terminal.folerName, xml.data.folder, xml.data.fileName, subFolder);

            fs.unlinkSync(`${xml.data.folder}/${xml.data.fileName}`);

            hawbXml.push(model.HAWB);
          } else {
            if (clearanceTypeId == ClearanceTypeId.ChinhSua) {
              model.times -= 1;
            } else if (isLowValue == false && clearanceTypeId == ClearanceTypeId.Tam) {
              model.tempTimes -= 1;
            }

            model.isError = true;
            model.messageError = 'Tạo file XML thất bại!';

            await model.save();
          }
        } catch (error) {
          Utilities.sendTelegramNoti('SEND_FILE_HAND', model.HAWB + ': ' + error.message);
          console.log(' --- [%s] [error][clearance][xml][handling]: %o', moment().format(Configure.FULL_TIME), error);
        }
      }

      ftp.close();

      if (hawbXml.length > 0) {
        let dataValid = { fileName: fileName, modelClass: structure.model.class, clearanceTypeId: clearanceTypeId, hawbs: hawbXml };
        new RabbitSend().delayMs(ClearanceRabbit.ValidPush, [dataValid], 5 * 60, { durable: true, autoDelete: false });
      }

      return { isSuccess: true };
    } catch (error) {
      Utilities.sendTelegramNoti('SEND_FILE', hawbs.join(', '));
      console.log(' --- [%s] [error][clearance][xml][handling]: %o', moment().format(Configure.FULL_TIME), error);
      return { isSuccess: false };
    }
  }

  async updateNoReply(data: any) {
    try {
      let { fileName, modelClass, hawbs, clearanceTypeId } = data;
      const models = await import(path.join(__dirname, '..', 'models', fileName));
      let model = models[modelClass];

      let log = await XmlLogMongo.find({ hawb: { $in: hawbs }, isComplete: false, clearanceTypeId: clearanceTypeId }, null, { lean: true });

      let hawbsErr;
      if (log) {
        hawbsErr = _(log).map('hawb').uniq().value();
        if (hawbsErr.length > 0) {
          await model.update({ isError: true, messageError: 'ECUS không phản hồi' }, { where: { HAWB: hawbsErr } });
        }
      }

      console.log(` --- [%s] [clearance][xml][updateNoReply]: ${JSON.stringify(hawbsErr)}`, moment().format(Configure.FULL_TIME));
    } catch (error) {
      console.log(' --- [%s] [error][clearance][xml][updateNoReply]: %o', moment().format(Configure.FULL_TIME), error);
    }
  }
  //#endregion

  //#region --- ECUS TO CPN

  //#region --- DOWNLOAD FILE
  async getTerminal() {
    try {
      if (!fs.existsSync(Configure.CLEARANCE_FOLDER_RECEIVE)) {
        fs.mkdirSync(Configure.CLEARANCE_FOLDER_RECEIVE, { recursive: true });
      }

      new RabbitSend().send(ClearanceRabbit.Get_terminal, ['1'], {}, { expiration: 20 * 1000 });
    } catch (error) {
      Utilities.sendTelegramNoti('CLEARANCE_PULL', error.message);
    }
  }

  async pullTerminal(terminal: any) {
    try {
      let ftp = new Ftp(terminal.host, terminal.username, terminal.password, parseInt(terminal.port), terminal.isTLS);
      await ftp.connect();

      await ftp.downloadAndBackup(terminal.folerName, Configure.CLEARANCE_FTP_FOLDER_SUB_RECEIVE, Configure.CLEARANCE_FOLDER_RECEIVE);

      ftp.close();
    } catch (error) {
      Utilities.sendTelegramNoti('FTP_CONNECTION', `${error.message} - ${JSON.stringify(terminal)}`);
    }
  }
  //#endregion

  //#region --- READ FILE
  // --- Lấy file đang pending
  async getDownloadFile() {
    let list = fs.readdirSync(Configure.CLEARANCE_FOLDER_RECEIVE);
    _.pull(list, 'BK');

    if (list.length > 0) {
      let numberSplit = list.length > 100 ? 100 : Math.ceil(list.length / 4);
      let listFile = _.sampleSize(list, numberSplit);
      new RabbitSend().send(ClearanceRabbit.Pull_file, [listFile], {}, { expiration: 10 * 1000 });
    }
  }

  // --- Xử lý xml: cập nhật dữ liệu
  async saveFile(fileName: string) {
    await new Promise((f) => setTimeout(f, 50));

    fileName = _.trim(fileName, '"');

    let clearanceFile = new ClearanceXml(FormatType.ECUS2CPN).readFiles(fileName);

    if (clearanceFile.isSuccess == false) {
      if (clearanceFile.fileName) {
        this.unLinkFile([clearanceFile.fileName], true);
      }
      return { isSuccess: false };
    }

    let dataXML: any = clearanceFile.data;
    let hawbClearance;

    dataXML.fileName = fileName;

    try {
      hawbClearance = dataXML.Root ? dataXML.Root.ShipmentID._text : dataXML.ErrorList.Error.ErrorCode._text;

      if (!hawbClearance) {
        hawbClearance = fileName.substring(0, fileName.indexOf('_'));
        if (!hawbClearance) {
          Utilities.sendTelegramNoti('CLEARANCE_READ_HAWB', `File: ${fileName}`);
          return { isSuccess: false };
        }
      }

      let xmlMongo = await XmlLogMongo.findOne({ hawbClearance: hawbClearance }).sort({ createdAt: -1 });
      if (!xmlMongo) {
        xmlMongo = new Clearance().getClearanceTypeName(fileName);
        if (!xmlMongo) {
          Utilities.sendTelegramNoti('CLEARANCE_READ_HAWB_LOG', `${hawbClearance}`);
          return { isSuccess: false };
        }
      }

      let HAWB = xmlMongo.hawb;
      let { serviceId, isLowValue, clearanceTypeId } = xmlMongo;

      let structure = await XmlStructureMongo.findOne({ serviceId: serviceId, isLowValue: isLowValue, isSendEcus: false });
      if (!structure) {
        Utilities.sendTelegramNoti('CLEARANCE_READ_STRUCTURE', `STRUCTURE NOT FOUND: ${hawbClearance} - ${serviceId}`);

        return { isSuccess: false };
      }

      const models = await import(path.join(__dirname, '..', 'models', structure.model.file));
      let model: NonAbstractTypeOfModel<BaseModel> = models[structure.model.class];

      const modelDetails = await import(path.join(__dirname, '..', 'models', structure.modelDetail.file));
      let modelDetail: NonAbstractTypeOfModel<BaseModel> = modelDetails[structure.modelDetail.class];

      let dataUpdate: any;
      if (HAWB) {
        dataUpdate = await model.scope(structure.model.scope).findByPk(HAWB);
      } else {
        dataUpdate = await model.scope(structure.model.scope).findOne({ where: { HAWBClearance: hawbClearance } });
      }

      if (!dataUpdate) {
        Utilities.sendTelegramNoti('CLEARANCE_READ_HAWB_DB', `${hawbClearance} - ${HAWB} (${structure.model.scope})`);
        return { isSuccess: false };
      }

      if (fileName.includes('VAD800_')) {
        let customsIntruction = await new Clearance().customsIntruction(dataUpdate, clearanceFile);
        this.unLinkFile([fileName], true);

        return customsIntruction;
      }

      let saveXML = await new ClearanceXml(FormatType.ECUS2CPN, structure).formatAndSaveData(modelDetail, dataUpdate, dataXML, clearanceTypeId, xmlMongo.isComplete);

      if (saveXML.isSuccess == true) {
        dataUpdate = saveXML.data;

        if (HAWB) {
          await xmlMongo.updateOne({ isComplete: true });
        }

        let isLowValue = structure ? structure.isLowValue : false;
        this.updateManifest(dataUpdate.HAWB, dataUpdate.phase, dataUpdate.serviceId, isLowValue);
        this.updateTaxCode(dataUpdate);

        let logs = dataUpdate.get({ plain: true });

        ImportTransaction.create({ ...logs, action: dataUpdate.isError ? ActionKey.RECEIVE_ECUS : dataUpdate.phase, createdAt: null, updatedAt: null });

        this.unLinkFile([fileName], true);
      }

      return { isSuccess: saveXML.isSuccess };
    } catch (error) {
      Utilities.sendTelegramNoti('CLEARANCE_READ_FILE', hawbClearance + ': ' + error.message);

      return { isSuccess: false };
    }
  }
  //#endregion

  //#region --- Chỉ thị hải quan
  async customsIntruction(dataModel: any, clearanceData: any) {
    Utilities.sendTelegramNoti('CLEARANCE_CUSTOMS_INTRUCTION', dataModel.hawbClearance);

    let { fileName, data } = clearanceData;

    let dataMsg = data.Root.Declaration;
    let dataMsgNew: any = {};
    let errMsg: any = [];

    let instructionKey = ['DateCustomsInstruction', 'CaseCustomsInstruction', 'ContentCustomsInstruction'];

    try {
      _.map(dataMsg, function (value, key) {
        if (!value['_text']) {
          return true;
        }

        if (key == 'GoodItems') {
          dataMsgNew[key] = value;
        } else if (!_.isEmpty(value)) {
          dataMsgNew[key] = value['_text'];
        }

        _.map(instructionKey, (x: string) => {
          if (key.includes(x)) {
            let index = key.replace(x, '') || 0;
            if (!errMsg[index]) {
              errMsg[index] = [];
            }

            errMsg[index].push(value['_text']);
          }
        });
      });

      await XmlCustomIntructionMongo.create({ hawb: dataModel.HAWB, hawbClearance: dataModel.HAWBClearance, fileName, message: dataMsgNew });

      dataModel.clearanceMsg = fileName.substring(fileName.indexOf('_') + 1, fileName.indexOf('__'));
      dataModel.errMsg =
        errMsg.length > 0
          ? _.join(
              _.map(errMsg, (x) => _.join(x, ', ')),
              ';'
            )
          : '';
      await dataModel.save();

      return true;
    } catch (error) {
      console.log(` --- [%s] [CLEARANCE_CUSTOM_INSTRUCTION]: %o`, moment().format(Configure.FULL_TIME), error);
      Utilities.sendTelegramNoti('CLEARANCE_CUSTOM_INSTRUCTION', `${dataModel.hawbClearance}: ${error.message}`);

      return false;
    }
  }
  //#endregion

  //#endregion
}
