import Configure from '../emuns/configures';
import moment from 'moment';
import dotenv from 'dotenv';
import Axios, { Method } from 'axios';
dotenv.config();

export class OMMAPI {
  private ommUrl = process.env.OMM_URL || 'http://localhost:3000/api/v1/';

  private apiRequest = async (method: Method, url: string, data?: any): Promise<any> => {
    try {
      url = `${this.ommUrl}${url}`;

      let query: any = {};

      if (method == 'post' || method == 'put' || method == 'delete' || method == 'patch') {
        query = { data: data };
      } else if (method == 'get') {
        query = { params: data };
      }

      let axios = await Axios({
        method: method,
        url: url,
        headers: {
          'content-type': 'application/json',
          iauthorization: Configure.TMM_API_IAUTHORIZATION
        },
        ...query
      });

      return await axios.data;
    } catch (error) {
      console.log(` --- [%s] [error][omm][api][${url}]: %o`, moment().format(Configure.FULL_TIME), error);
      throw error.response.data;
    }
  };

  async partnerStatus(hawbs: string[], statusId: number) {
    try {
      return await this.apiRequest('post', '/partner/status', { hawbs: hawbs, statusId: statusId });
    } catch (error) {
      throw error;
    }
  }
}
