import Configure from '../emuns/configures';
import moment from 'moment';
import dotenv from 'dotenv';
import Axios, { Method } from 'axios';
dotenv.config();

export class TMMAPI {
  private tmmUrl = process.env.TMM_URL || 'https://devapi.globex.vn/tmm/api/v1/';

  private apiRequest = async (method: Method, url: string, data?: any): Promise<any> => {
    try {
      url = `${this.tmmUrl}${url}`;

      let query: any = {};

      if (method == 'post' || method == 'put' || method == 'delete' || method == 'patch') {
        query = { data: data };
      } else if (method == 'get') {
        query = { params: data };
      }

      let axios = await Axios({
        method: method,
        url: url,
        headers: {
          'content-type': 'application/json',
          iauthorization: Configure.TMM_API_IAUTHORIZATION
        },
        ...query
      });

      return await axios.data;
    } catch (error) {
      console.log(` --- [%s] [error][tmm][api][${url}]: %o`, moment().format(Configure.FULL_TIME), error);
      throw error.response.data;
    }
  };

  async manifestOutboundUpdate(hawbs: string[]) {
    try {
      return await this.apiRequest('put', 'manifests/outboundClearance', { HAWBs: hawbs });
    } catch (error) {
      throw error;
    }
  }

  async manifestInboundUpdate(hawbs: string[], phase: number) {
    try {
      return await this.apiRequest('put', 'manifests/inboundClearance', { HAWBs: hawbs, clearanceStatus: phase });
    } catch (error) {
      throw error;
    }
  }
}
