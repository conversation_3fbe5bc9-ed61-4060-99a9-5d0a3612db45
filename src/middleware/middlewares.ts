import { Request, Response, NextFunction } from 'express';
import moment from 'moment';
import httpStatus from 'http-status';
import jwt from 'jsonwebtoken';
import HttpException from '../https/exception';
import HttpResponse from '../https/response';
import EMessage from '../emuns/messages';
import EConfigure from '../emuns/configures';
import Configure from '../util/configure';
import RedisPromise from '../util/redis.promise';
import { config } from 'dotenv';
import Utilities from '../util/utilities';

class Middlewares {
  public async CheckToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    console.log(` \n --- [%s] %s`, moment().format(moment().format(EConfigure.FULL_TIME)), `${req.method} - ${req.originalUrl}`);
    console.log(` --- [%s] Data: %o`, moment().format(moment().format(EConfigure.FULL_TIME)), req.body);
    try {
      config();
      const configure: Configure = new Configure();
      const headers: any = req.headers;
      if (headers.iauthorization) {
        let token: string = headers.iauthorization;
        if (!token.startsWith('Bearer ')) {
          return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, EMessage.AUTHORIZATION_WRONG_TYPE));
        } else {
          let identity = token.replace(/^Bearer/, '').trim();
          if (identity !== configure.internalSecretKey()) {
            return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, EMessage.INTERNAL_AUTHORIZATION_WRONG_TOKEN));
          } else {
            res.locals.identity = identity;
            next();
          }
        }
      } else {
        if (!headers.authorization && !headers.secret) {
          return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, EMessage.HEADER_MISSING));
        }
        let token: string = headers.authorization;
        const secret: string = headers.secret;
        if (!token.startsWith('Bearer ')) {
          return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, EMessage.AUTHORIZATION_WRONG_TYPE));
        }
        try {
          token = token.replace(/^Bearer/, '').trim();
          const payload: any = jwt.verify(token, `${configure.secretKey()}${secret}`);
          const { identity, environments } = payload;
          if (!environments.includes(process.env.DOMAIN)) {
            return HttpResponse.sendMessage(res, httpStatus.UNAUTHORIZED, new HttpException(false, EMessage.UNAUTHORIZED));
          }
          try {
            const userInfo: any = await RedisPromise.getData(identity);
            if (!userInfo) {
              return HttpResponse.sendMessage(res, EConfigure.LOGIN_TIMEOUT_CODE, new HttpException(false, EMessage.LOGIN_TIMEOUT));
            }
            res.locals.identity = identity;
            next();
          } catch (error) {
            Utilities.sendDiscordErr('[service][middleware][checkToken][getRedis]', `${secret} - ${(error as any).message}`);
            console.log(' \t --- [%s] [error][middleware][check_token][get_redis]: %o', moment().format(EConfigure.FULL_TIME), error);
          }
        } catch (error) {
          console.log(' \t --- [%s] [error][middleware][check_token][verify_token]: %o', moment().format(EConfigure.FULL_TIME), error);
          return HttpResponse.sendMessage(res, EConfigure.EXPIRED_TIME_CODE, new HttpException(false, EMessage.TOKEN_EXPIRED_TIME));
        }
      }
    } catch (error) {
      console.log(' \t --- [%s] [error][middleware][check_token]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.exceptionError(res, (error as any).message);
    }
  }

  public async CheckTokenGateway(req: Request, res: Response, next: NextFunction): Promise<void> {
    console.log(` \n --- [%s] %s`, moment().format(moment().format(EConfigure.FULL_TIME)), `${req.method} - ${req.originalUrl}`);
    console.log(` --- [%s] Data: %o`, moment().format(moment().format(EConfigure.FULL_TIME)), req.body);
    try {
      config();
      const configure: Configure = new Configure();
      const headers: any = req.headers;
      if (!headers.authorization && !headers.secret) {
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, EMessage.HEADER_MISSING));
      }
      let token: string = headers.authorization;
      const secret: string = headers.secret;
      if (!token.startsWith('Bearer ')) {
        return HttpResponse.sendMessage(res, httpStatus.BAD_REQUEST, new HttpException(false, EMessage.AUTHORIZATION_WRONG_TYPE));
      }
      try {
        token = token.replace(/^Bearer/, '').trim();
        const payload: any = jwt.verify(token, `${configure.secretKey()}${secret}`);
        const { identity, environments } = payload;
        if (!environments.includes('gateway')) {
          return HttpResponse.sendMessage(res, httpStatus.UNAUTHORIZED, new HttpException(false, EMessage.UNAUTHORIZED));
        }
        try {
          const userInfo: any = await RedisPromise.getData(identity);
          if (!userInfo) {
            return HttpResponse.sendMessage(res, EConfigure.LOGIN_TIMEOUT_CODE, new HttpException(false, EMessage.LOGIN_TIMEOUT));
          }
          res.locals.identity = identity;
          next();
        } catch (error) {
          Utilities.sendDiscordErr('[service][middleware][CheckTokenGateway][getRedis]', `${secret} - ${(error as any).message}`);
          console.log(' \t --- [%s] [error][middleware][check_tokenGateway][get_redis]: %o', moment().format(EConfigure.FULL_TIME), error);
        }
      } catch (error) {
        console.log(' \t --- [%s] [error][middleware][check_tokenGateway][verify_token]: %o', moment().format(EConfigure.FULL_TIME), error);
        return HttpResponse.sendMessage(res, EConfigure.EXPIRED_TIME_CODE, new HttpException(false, EMessage.TOKEN_EXPIRED_TIME));
      }
    } catch (error) {
      console.log(' \t --- [%s] [error][middleware][check_tokenGateway]: %o', moment().format(EConfigure.FULL_TIME), error);
      return HttpResponse.exceptionError(res, (error as any).message);
    }
  }

  public CheckPermission(keyPermission: string) {
    return async (req: Request, res: Response, next: NextFunction): Promise<any> => {
      try {
        const { identity } = res.locals;
        if (identity === process.env.INTERNAL_AUTHEN_KEY) {
          next();
        } else {
          const userInfo: any = await RedisPromise.getData(identity);
          if (!userInfo) {
            return HttpResponse.sendMessage(res, EConfigure.LOGIN_TIMEOUT_CODE, new HttpException(false, EMessage.LOGIN_TIMEOUT));
          }
          const { permissions } = userInfo;
          if (!permissions.includes(keyPermission)) {
            return HttpResponse.sendMessage(res, httpStatus.FORBIDDEN, new HttpException(false, EMessage.PERMISSION_DENIED));
          }
          next();
        }
      } catch (error) {
        console.log(` \t --- [%s] [error][middleware][check_permission][${keyPermission}]: %o1`, moment().format(EConfigure.FULL_TIME), error);
        return HttpResponse.exceptionError(res, (error as any).message);
      }
    }
  }

  public ExceptionMiddleware(error: HttpException, request: Request, response: Response, next: NextFunction): void {
    response.status(httpStatus.INTERNAL_SERVER_ERROR).send(EMessage.EXCEPTION_MESSAGE)
  }
}

export default Middlewares;