'use strict'

import Optional from "./optional";
import OrderBy from "./orderBy";
import Query from "./query";
import Where from "./where";
import EMessage from "../emuns/messages";
import ECofigure from "../emuns/configures";

interface IParserUrl {
  sort?: string,
  limit?: string,
  offset?: string,
  relation?: string,
  attribute?: string,
}

class ParserUrl {
  private params: IParserUrl;
  constructor(params: any) {
    this.params = params;
  }

  public handleParameter(): Optional {
    try {
      const optional = new Optional();
      const { limit, offset, sort, relation, attribute } = this.params;
      
      if(limit) {
        optional.setLimit(Number(limit));
        delete this.params[ECofigure.LIMIT];
      }
      if(offset) {
        optional.setOffset(Number(offset));
        delete this.params[ECofigure.OFFSET];
      }

      if(sort) {
        const orderBys = this.hanldeSort(sort.split(ECofigure.COMMA));
        optional.setOrderby(orderBys);
        delete this.params[ECofigure.SORT];
      }
      if(relation) {
        const arrRelation = this.handleComma(relation);
        optional.setRelation(arrRelation);
        delete this.params[ECofigure.RELATION];
      }
      if(attribute) {
        const arrRelation = this.handleComma(attribute);
        optional.setAttributes(arrRelation);
        delete this.params['attribute'];
      }
      optional.setWhere(this.handleQuery(this.params));
      return optional;
    } catch (error) {
      console.log(error);
      throw new Error(EMessage.FAIL_URL_HANDLE);
    }
    
  }

  private hanldeSort(arrData: Array<string>): Array<OrderBy>{
    return arrData.map((element: any) => {
      const sortParts = element.split(ECofigure.STAND);
      const [field, condition] = sortParts;
      const sortCondition = Query.getOrder(condition);
      return new OrderBy(field, sortCondition);
    });
  }

  private handleComma (objData: string): string[] {
    return objData.split(ECofigure.COMMA)
  }

  private handleQuery(data: any): any {
    const wheres: Array<any> = Object.keys(data).map((key: string) => {
      return Query.parseQuery(key, data[key]);
    });
    const arrWhere: any = [];
    wheres.forEach(element => {
      if(Array.isArray(element)) {
        element.forEach((item: Where) => {
          arrWhere.push(item);
        });
      } else {
        arrWhere.push(element);
      }
    });
    return arrWhere;
  }

}

export default ParserUrl;