'use strict'

class Where {
  private clause: string;
  private key: string;
  private operator: string;
  private value: any;
  constructor(clause: string, key: string, operator:string, value: any) {
    this.clause = clause;
    this.key = key;
    this.operator = operator;
    this.value = value;
  }

  public setValue(value: any) {
    this.value = value;
  }

  public getClause(): string {
    return this.clause;
  }

  public getKey(): string {
    return this.key;
  }

  public getOperator(): string {
    return this.operator;
  }

  public getValue(): any {
    return this.value;
  }
}

export default Where;