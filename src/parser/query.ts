'use strict'

import Where from './where';
import ECofigure from "../emuns/configures";

class Query {
  static getOperator (operator: string) {
    const OPERATORS: any = {
      eq: ECofigure.EQUAL,
      ne: ECofigure.NOT_EQUAL,
      eqn: ECofigure.EQUAL_NUMBER,
      nen: ECofigure.NOT_EQUAL_NUMBER,
      gt: ECofigure.GREATER_THAN,
      gte: ECofigure.GREATER_THAN_EQUAL,
      lt: ECofigure.LESS_THAN,
      lte: ECofigure.LESS_THAN_EQUAL,
      like: ECofigure.LIKE,
      ilike: ECofigure.ILIKE,
      ilikestart: ECofigure.ILIKE_START,
      ilikeend: ECofigure.ILIKE_END,
      in: ECofigure.IN,
      notin: ECofigure.NOT_IN,
      is: ECofigure.IS,
      not: ECofigure.NOT,
      or: ECofigure.OR,
      between: ECofigure.BETWEEN,
      nbetween: ECofigure.NOT_BETWEEN,
    };
    if (OPERATORS[operator]) {
      return OPERATORS[operator];
    }
    return false;
  }

  static getClause(clause: string) {
    const CONDITIONS: any = {
      or: ECofigure.OR,
      and: ECofigure.AND,
    };
    if (CONDITIONS[clause]) {
      return CONDITIONS[clause];
    }
    return ECofigure.AND;
  }

  static getOrder(order: string) {
    const ORDERS: any = {
      asc: ECofigure.ASCENDING,
      desc: ECofigure.DESCENDING,
    };
    if (ORDERS[order]) {
      return ORDERS[order];
    }
    return ECofigure.ASCENDING;
  }

  static parseQuery (strField: string, strOperator: string): any {
    let field: string;
    let clause: string;
    let operator: any;
    let value: string;
    if(strField.includes(ECofigure.STAND)) {
      [field, clause] = strField.split(ECofigure.STAND);
      clause = this.getClause(clause.toLowerCase());
    } else {
      field = strField;
      clause = this.getClause(ECofigure.AND);
    }
    if(typeof strOperator === "string" && strOperator.includes(ECofigure.STAND)) {
      [operator, value] = strOperator.split(ECofigure.STAND);
      operator = this.getOperator(operator.toLowerCase());
      return new Where(clause, field, operator, value);
    } else {
      if(Array.isArray(strOperator)) {
        const arrWhere = strOperator.map(element => {          
          if(element.includes(ECofigure.STAND)) {
            [operator, value] = element.split(ECofigure.STAND);
            operator = this.getOperator(operator.toLowerCase());
          } else {
            operator = this.getOperator(ECofigure.EQUAL);
            value = element;
          }
          return new Where(clause, field, operator, value);
        });
        
        return arrWhere;
      } else {
        operator = this.getOperator(ECofigure.EQUAL);
        value = strOperator;
        return new Where(clause, field, operator, value);
      }
    }
  }
}

export default Query;