'use strict'

import OrderBy from './orderBy';
import EConfigure from '../emuns/configures';
import Where from './where';
import GroupWhere from './groupWhere';

class Optional {
  private attributes: any[] = [];
  private limit: number = EConfigure.LIMIT_DEFAULT;
  private offset: number = 0;
  private orderBy: Array<OrderBy> = [];
  private relation: Array<string> = [];
  private where: Array<Where> = [];
  private group: string[] = [];
  private groupWhere: any;
  private queryRaw: boolean = false;

  public setQueryRaw(status: boolean) {
    this.queryRaw = status;
  }

  public getQueryRaw() {
    return this.queryRaw;
  }

  public setAttributes(attributes: any[]) {
    this.attributes = attributes;
  }

  public getAttributes() {
    return this.attributes;
  }
  
  public setLimit(limit: number) {
    this.limit = limit;
  }
  
  public getLimit(): number {
    return this.limit;
  }

  public setOffset(offset: number) {
    this.offset = offset;
  }
  
  public getOffset(): number {
    return this.offset;
  }

  public setOrderby(orderBy: Array<OrderBy>) {
    this.orderBy = orderBy;
  }

  public getOrderby(): Array<OrderBy> {
    return this.orderBy;
  }

  public setRelation(relation: string[]) {
    this.relation = relation;
  }

  public getRelation(): string[] {
    return this.relation;
  }

  public setWhere(where: Where[]) {
    this.where = where;
  }

  public getWhere(): Where[] {
    return this.where;
  }

  public getGroup(): string[] {
    return this.group;
  }

  public setGroup(group: string[]) {
    this.group = group;
  }

  public setGroupWhere(groupWhere: any) {
    this.groupWhere = groupWhere;
  }

  public getGroupWhere(): GroupWhere {
    return this.groupWhere;
  }
}

export default Optional;