'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'inspectionKindTimes',
        {
          type: Sequelize.SMALLINT,
          defaultValue: 0,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'clearanceDeclarationTimes',
        {
          type: Sequelize.SMALLINT,
          defaultValue: 0,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'clearancedTimes',
        {
          type: Sequelize.SMALLINT,
          defaultValue: 0,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'inspectionKindTimes',
        {
          type: Sequelize.SMALLINT,
          defaultValue: 0,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'clearanceDeclarationTimes',
        {
          type: Sequelize.SMALLINT,
          defaultValue: 0,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'clearancedTimes',
        {
          type: Sequelize.SMALLINT,
          defaultValue: 0,
        },
      ),
    ]);
  },
};