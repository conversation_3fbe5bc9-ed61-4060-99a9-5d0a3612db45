'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'isHold',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'isHold',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'reasonIds',
        {
          type: Sequelize.ARRAY(Sequelize.INTEGER),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'reasonIds',
        {
          type: Sequelize.ARRAY(Sequelize.INTEGER),
          allowNull : true,
        },
      ),
    ]);
  },
};