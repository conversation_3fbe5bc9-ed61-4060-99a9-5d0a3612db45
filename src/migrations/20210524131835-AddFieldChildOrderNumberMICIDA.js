'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'childOrderNumber',
        {
          type: Sequelize.STRING(30),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'childOrderNumber',
        {
          type: Sequelize.STRING(30),
          allowNull : true
        },
      )
    ]);
  },
};