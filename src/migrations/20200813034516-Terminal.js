'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_terminals', {
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      host: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      username: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      password: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      userId: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      folerName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      isLive: {
        type: Sequelize.BOOLEAN,
        allowNull : true,
        defaultValue: true,
      },
      handleNumber: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      // create information
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_terminals');
  }
};
