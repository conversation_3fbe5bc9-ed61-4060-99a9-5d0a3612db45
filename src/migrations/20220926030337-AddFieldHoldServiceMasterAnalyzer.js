'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'master_analyzers',
        'totalHold',
        {
          type: Sequelize.BIGINT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'serviceId',
        {
          type: Sequelize.INTEGER,
          defaultValue: 3
        },
      ),
    ]);
  },
};