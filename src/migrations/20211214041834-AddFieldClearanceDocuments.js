'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn('clearance_documents', 'HAWBClearance', {
        type: Sequelize.STRING
      }),
      queryInterface.addColumn('clearance_documents', 'SoTN', {
        type: Sequelize.STRING
      }),
      queryInterface.addColumn('clearance_documents', 'NgayTN', {
        type: Sequelize.DATEONLY
      })
    ]);
  }
};
