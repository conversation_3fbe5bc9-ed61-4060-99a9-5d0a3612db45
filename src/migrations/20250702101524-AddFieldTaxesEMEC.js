'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    queryInterface.addColumn(
      'clearance_emecs',
      'taxCodeNumber',
      {
        type: Sequelize.STRING(100),
        allowNull : true
      }
    ),
    queryInterface.addColumn(
      'clearance_emecs',
      'printDate',
      {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
    ),
    queryInterface.addColumn(
      'clearance_emecs',
      'dateTaxCodeNumber',
      {
        type: Sequelize.DATE,
        allowNull : true
      }
    )
  }
};