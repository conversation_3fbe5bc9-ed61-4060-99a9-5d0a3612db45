'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_edas', {
      HAWB: {
        type: Sequelize.STRING(30),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      MAWB: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      declarationNo: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      inspectionKindClassification: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      stationId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      declarationKindCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      meansOfTransportationCode: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      declarationPlannedDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      registeredTime: {
        type: Sequelize.TIME,
        allowNull : true
      },
      //
      dateOfCompletionOfInspection: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      timeOfCompletionOfInspection: {
        type: Sequelize.TIME,
        allowNull : true
      },
      dateOfPermit: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      timeOfPermit: {
        type: Sequelize.TIME,
        allowNull : true
      },
      nameOfHeadCustomsOffice: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      //
      managementNumberUser: {
        type: Sequelize.STRING(8),
        allowNull : true
      },
      representativeTaxCode: {
        type: Sequelize.STRING(8),
        allowNull : true
      },
      cargoClassificationCode: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      customsOffice: {
        type: Sequelize.STRING(8),
        allowNull : true
      },
      customsOfficeName: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      customsSubSection: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      exporterCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      exporterName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      exporterFullName: {
        type: Sequelize.STRING(255),
        allowNull : true,
      },
      postCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      addressOfExporter: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      telephoneNumber: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      exporterContractorCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      exporterContractorName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      consigneeCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      consigneeName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      consigneeTelephoneNumber: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      postCodeIdentification: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      address1: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      address2: {
        type: Sequelize.STRING(60),
        allowNull : true
      },
      address3: {
        type: Sequelize.STRING(60),
        allowNull : true
      },
      address4: {
        type: Sequelize.STRING(60),
        allowNull : true
      },
      countryCode: {
        type: Sequelize.STRING(5),
        allowNull : true
      },
      plannedDeclarantCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      agentName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      cargoNo: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      cargoPiece: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      pieceUnitCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      cargoWeightGross: {
        type: Sequelize.DECIMAL(12, 3),
        allowNull : true
      },
      weightUnitCodeGross: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      customsWarehouseCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      customsClearanceWarehouseName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      theFinalDestinationCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      theFinalDestinationName: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      loadingPortCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      loadingPortName: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      loadingPlannedVesselName: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      departurePlannedDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      invoiceClassificationCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      termOfPayment: {
        type: Sequelize.STRING(7),
        allowNull : true
      },
      invoicePriceConditionCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      invoiceCurrencyCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      totalInvoicePrice: {
        type: Sequelize.DECIMAL(12, 3),
        allowNull : true
      },
      invoicePriceKindCode: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      currencyCodeOfTaxValue: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      totalOfTaxValue: {
        type: Sequelize.DECIMAL(12, 3),
        allowNull : true
      },
      taxPayer: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      codeOfExtendingDueDate: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      startDate: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      arrivalDateOfTransport: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      destinationLocationForBondedTransport: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      totalBasicPriceInput: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      paymentClassification: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      numberOfDeclaration: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      structure: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull : true
      },      
      // create information
      isError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },  
      isECUSError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      messageErrorName: {
        type: Sequelize.TEXT,
        allowNull : true
      }, 
      messageError: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      valueClearanceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      priceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      serviceId: {
        type: Sequelize.INTEGER,
        allowNull : false,
      },
      customerBusinessId: {
        type: Sequelize.UUID,
        allowNull : true,
      },
      customerPersonalId: {
        type: Sequelize.UUID,
        allowNull : true,
      },
      classify: { // phân loại DOCUMENT, PARCEL
        type: Sequelize.STRING(4),
        allowNull : true,
        defaultValue: 'PAR',
      },
      dateCheckin: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateClearanced: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateCheckout: {
        type: Sequelize.DATE,
        allowNull : true
      },
      phase: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      times: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      tempTimes: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      originalOrderNumberClient: {
        type: Sequelize.STRING(255),
        allowNull : true,
      },
      terminalName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      dateAction: {
        type: Sequelize.DATE,
        allowNull : true,
      },
      typeAction:{
        type: Sequelize.STRING(10),
        allowNull : true
      },
      inspectionKindTimes: {
        type: Sequelize.SMALLINT,
        defaultValue: 0,
      },
      clearanceDeclarationTimes: {
        type: Sequelize.SMALLINT,
        defaultValue: 0,
      },
      clearancedTimes: {
        type: Sequelize.SMALLINT,
        defaultValue: 0,
      },
      isEDCed: { // đánh dấu EDA đã gửi EDC
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isEditProcessing: { // đánh dấu EDA0x phải chờ ECUS xử lý xong mới được EDA0x tiếp
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isIEDAed: { // đánh đấu EDA đã EDA0x rồi mới được EDE
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      //
      subjectExportCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      numberExport: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalExportTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      subjectVATCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      numberVAT: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalVATTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      subjectEnvironmentCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      numberEnvironment: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalEnvironmentTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      subjectSpecialConsumptionCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      numberSpecialConsumption: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalSpecialConsumptionTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      totalTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_edas');
  }
};
