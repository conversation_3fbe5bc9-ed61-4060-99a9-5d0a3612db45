'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.changeColumn(
        'clearance_idas',
        'totalInvoicePrice',
        {
          type: Sequelize.DECIMAL(26, 6),
          allowNull : true
        },
      ),
      queryInterface.changeColumn(
        'clearance_idas',
        'totalOfProportionalDistributionOnTaxValue',
        {
          type: Sequelize.DECIMAL(26, 6),
          allowNull : true
        },
      ),
      
    ]);
  },
};