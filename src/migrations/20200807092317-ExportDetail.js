'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_export_details', { 
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      HAWB: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      HSCode: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      itemName: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      itemNameVN: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      placeOfOriginCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      quantity1: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      quantityUnitCode1: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      unitPriceCurrencyCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      priceQuantityUnit: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      customsValueS: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      customsValueCurrencyCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      taxValueM: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      customsValueUnitPrice: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      customsUnitPriceCurrency: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      quantityUnitOfCustoms: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      emportTaxRate: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      exportTaxAmount: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      exportTaxAmountCurrencyCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },

      invoiceUnitPrice: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      invoiceValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      valueClearanceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      priceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      url: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      productId: {
        type: Sequelize.STRING(255),
        allowNull : true,
      },
      weightKG: {
        type: Sequelize.DECIMAL(12, 4),
        allowNull : true,
      },
      otherLawCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      exportTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true
      },
      exportTaxCode: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      exportTaxFree: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      exportTaxFreePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      exportPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true,
        defaultValue: 0
      },
      priceAfterImportTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      priceBeforeVATTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      VATTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true
      },
      VATTaxCode: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      VATTaxFree: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      VATTaxFreePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      VATPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true,
        defaultValue: 0
      },
      environmentTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true
      },
      environmentTaxCode: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      environmentTaxFree: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      environmentTaxFreePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      environmentPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true,
        defaultValue: 0
      },
      specialConsumptionTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true
      },
      specialConsumptionTaxCode: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      specialConsumptionTaxFree: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      specialConsumptionTaxFreePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      specialConsumptionPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true,
        defaultValue: 0
      },
      // create information
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_export_details');
  }
};
