'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'master_analyzers',
        'clientId',
        {
          type: Sequelize.INTEGER,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'IDARed',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'IDAYellow',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'IDANone',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      )
    ]);
  },
};