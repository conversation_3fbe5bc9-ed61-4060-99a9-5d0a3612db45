'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mecs',
        'employeeUpdateCargoName',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'isHold',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'reasonIds',
        {
          type: Sequelize.ARRAY(Sequelize.INTEGER),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'noteHold',
        {
          type: Sequelize.TEXT,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'employeeUpdateCargoName',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'isHold',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'reasonIds',
        {
          type: Sequelize.ARRAY(Sequelize.INTEGER),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'noteHold',
        {
          type: Sequelize.TEXT,
          allowNull : true,
        },
      ),
    ]);
  },
};