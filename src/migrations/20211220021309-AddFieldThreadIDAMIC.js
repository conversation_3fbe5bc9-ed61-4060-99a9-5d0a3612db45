'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'threadCode',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'threadName',
        {
          type: Sequelize.STRING(200),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'threadColor',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'threadUrl',
        {
          type: Sequelize.STRING(255),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'threadCode',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'threadName',
        {
          type: Sequelize.STRING(200),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'threadColor',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'threadUrl',
        {
          type: Sequelize.STRING(255),
          allowNull : true
        },
      ),
    ]);
  },
};