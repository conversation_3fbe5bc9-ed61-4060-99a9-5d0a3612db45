'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    queryInterface.addColumn('clearance_mics', 'ftpId', { type: Sequelize.INTEGER, comment: 'clearance_terminal' }),
      queryInterface.addColumn('clearance_mecs', 'ftpId', { type: Sequelize.INTEGER, comment: 'clearance_terminal' }),
      queryInterface.addColumn('clearance_idas', 'ftpId', { type: Sequelize.INTEGER, comment: 'clearance_terminal' }),
      queryInterface.addColumn('clearance_edas', 'ftpId', { type: Sequelize.INTEGER, comment: 'clearance_terminal' });
  }
};
