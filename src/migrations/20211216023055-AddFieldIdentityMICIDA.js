'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'identity',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'identity',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      )
    ]);
  },
};