'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_mics', {
      //header
      HAWB: {
        type: Sequelize.STRING(30),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      MAWB: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      flightNo: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      declarationNo: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      stationId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      //
      classificationIndividualOrganization: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      classificationOfIndividualOrganization: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      inspectionKindClassification: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      customsOffice: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      customsOfficeName: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      customsSubSection: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      declarationPlannedDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      registeredTime: {
        type: Sequelize.TIME,
        allowNull : true
      },
      registeredDateCorrection: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      registeredTimeCorrection: {
        type: Sequelize.TIME,
        allowNull : true
      },
      importerCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      importerName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      postCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      addressOfImporter: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      telephoneNumberOfImporter: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      consignorCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      consignorName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      postCodeIdentification: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      address1: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      address4: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      countryCode: {
        type: Sequelize.STRING(5),
        allowNull : true
      },
      agentCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      agentName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      customsBrokerCode: { // Mã nhân viên hải quan
        type: Sequelize.STRING(255),
        allowNull : true
      },
      nameHeadCustomsOffice: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      //MIC Cargo
      cargoPiece: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      cargoWeight: {
        type: Sequelize.DECIMAL(12, 4),
        allowNull : true
      },
      customsWarehouseCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      customsClearanceName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      arrivalDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      unloadingPort: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      portOfDischarge: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      portOfDischargeName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      loadingLocationCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      loadingLocationName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      currencyCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      currencyExchangeRate: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      // invoice price
      invoicePriceKind: { // phân loại giá hóa đơn
        type: Sequelize.STRING(2),
        allowNull : true
      },
      invoicePriceCondition: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      invoiceCurrencyCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      totalInvoicePrice: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      // end invoice price
      //bao hiểm
      freightDemarcation: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      freightCurrency: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      freight: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      // end bao hiểm
      insuranceDemarcation: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      insuranceCurrency: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      insuranceAmount: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      dateOfPermit: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      timeOfPermit: {
        type: Sequelize.TIME,
        allowNull : true
      },
      dateOfCompletion: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      timeCompletion: {
        type: Sequelize.TIME,
        allowNull : true
      },
      isError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },  
      isECUSError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      messageErrorName: {
        type: Sequelize.TEXT,
        allowNull : true
      }, 
      messageError: {
        type: Sequelize.TEXT,
        allowNull : true
      },   
      // notes
      notes: {
        type: Sequelize.TEXT,
        allowNull : true
      },  
      paymentStatementNo: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      treasuryAccountNo: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      treasuryName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      dateIssuedStatement: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },    
      // create information
      valueClearanceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      priceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      serviceId: {
        type: Sequelize.INTEGER,
        allowNull : false,
      },
      customerBusinessId: {
        type: Sequelize.UUID,
        allowNull : true,
      },
      customerPersonalId: {
        type: Sequelize.UUID,
        allowNull : true,
      },
      classify: { // phân loại DOCUMENT, PARCEL
        type: Sequelize.STRING(4),
        allowNull : true,
        defaultValue: 'PAR',
      },
      dateCustomClearance: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateCheckin: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateClearanced: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateCheckout: {
        type: Sequelize.DATE,
        allowNull : true
      },
      phase: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      times: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_mics');
  }
};
