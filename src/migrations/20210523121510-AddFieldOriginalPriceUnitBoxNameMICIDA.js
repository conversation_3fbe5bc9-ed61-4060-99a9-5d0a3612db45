'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'originalPrice',
        {
          type: Sequelize.DECIMAL(20, 3),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'currencyOriginalPrice',
        {
          type: Sequelize.STRING(5),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'internalBoxName',
        {
          type: Sequelize.STRING(33),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'externalBoxName',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      
      queryInterface.addColumn(
        'clearance_idas',
        'originalPrice',
        {
          type: Sequelize.DECIMAL(20, 3),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'currencyOriginalPrice',
        {
          type: Sequelize.STRING(5),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'internalBoxName',
        {
          type: Sequelize.STRING(33),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'externalBoxName',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
    ]);
  },
};