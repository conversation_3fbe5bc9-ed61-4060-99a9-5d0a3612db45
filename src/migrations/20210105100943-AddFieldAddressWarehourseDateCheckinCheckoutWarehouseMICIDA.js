'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'warehouseAddress',
        {
          type: Sequelize.STRING(255),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'warehouseCheckin',
        {
          type: Sequelize.DATE,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'warehouseCheckout',
        {
          type: Sequelize.DATE,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'warehouseAddress',
        {
          type: Sequelize.STRING(255),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'warehouseCheckin',
        {
          type: Sequelize.DATE,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'warehouseCheckout',
        {
          type: Sequelize.DATE,
          allowNull : true,
        },
      ),
    ]);
  },
};