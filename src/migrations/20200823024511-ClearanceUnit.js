'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_units', {
      code: {
        type: Sequelize.STRING(50),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      name: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      nameVN: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      position: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_units');
  }
};
