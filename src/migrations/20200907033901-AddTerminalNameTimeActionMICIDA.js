'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'terminalName',
        {
          type: Sequelize.STRING(100),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'dateAction',
        {
          type: Sequelize.DATE,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'terminalName',
        {
          type: Sequelize.STRING(100),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'dateAction',
        {
          type: Sequelize.DATE,
          allowNull : true
        },
      ),
    ]);
  },
};