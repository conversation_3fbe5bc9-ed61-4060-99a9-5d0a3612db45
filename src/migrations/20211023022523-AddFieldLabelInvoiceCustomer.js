'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'labelCustomer',
        {
          type: Sequelize.STRING(5000),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'invoiceCustomer',
        {
          type: Sequelize.STRING(5000),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'labelCustomer',
        {
          type: Sequelize.STRING(5000),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'invoiceCustomer',
        {
          type: Sequelize.STRING(5000),
          allowNull : true
        },
      ),
    ]);
  },
};