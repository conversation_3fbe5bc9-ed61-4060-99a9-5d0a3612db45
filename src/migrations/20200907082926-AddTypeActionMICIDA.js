'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'typeAction',
        {
          type: Sequelize.STRING(10),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'typeAction',
        {
          type: Sequelize.STRING(10),
          allowNull : true
        },
      ),
    ]);
  },
};