'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'originalOrderNumberClient',
        {
          type: Sequelize.STRING(255),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'originalOrderNumberClient',
        {
          type: Sequelize.STRING(255),
          allowNull : true,
        },
      ),
    ]);
  },
};