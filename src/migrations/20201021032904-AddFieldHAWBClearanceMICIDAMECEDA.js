'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mecs',
        'HAWBClearance',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        }
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'HAWBClearance',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        }
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'HAWBClearance',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        }
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'HAWBClearance',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        }
      ),
    ]);
  },
};