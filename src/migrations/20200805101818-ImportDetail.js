'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_import_details', { 
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      HAWB: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      HSCode: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      itemName: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      itemNameVN: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      //MIC
      placeOfOrigin: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      originalPlaceName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      customsValue: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      // end MIC
      //IDA
      quantity1: {
        type: Sequelize.DECIMAL(20, 6),
        allowNull : true,
        defaultValue: 0
      },
      quantityUnitCode1: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      valuationNos: { //Số của mục khai khoản điều chỉnh
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        allowNull : true
      },

      invoiceValue: {
        type: Sequelize.DECIMAL(12, 3),
        allowNull : true,
        defaultValue: 0
      },
      invoiceUnitPrice: {
        type: Sequelize.DECIMAL(12, 3),
        allowNull : true,
        defaultValue: 0
      },
      unitPriceCurrencyCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      priceQuantityUnit: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      customsValueS: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      taxValueM: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      valueUnitPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      quantityUnitPrice: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      classificationImportTax: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      importTaxRate: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      importTaxClassification: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      importTaxAmount: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      placeOfOriginCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      
      url: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      priceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true,
        defaultValue: 0
      },
      importTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      importTaxCode: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      importTaxFree: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      importTaxFreePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      importPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      priceAfterImportTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      priceBeforeVATTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      VATTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      VATTaxCode: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      VATTaxFree: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      VATTaxFreePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      VATPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      environmentTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      environmentTaxCode: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      environmentTaxFree: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      environmentTaxFreePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      environmentPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      specialConsumptionTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      specialConsumptionTaxCode: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      specialConsumptionTaxFree: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      specialConsumptionTaxFreePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      specialConsumptionPrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      // create information
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_import_details');
  }
};
