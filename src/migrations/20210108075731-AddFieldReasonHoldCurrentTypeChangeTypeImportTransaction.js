'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_import_transactions',
        'currentClassify',
        {
          type: Sequelize.STRING(20),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_import_transactions',
        'changeClassify',
        {
          type: Sequelize.STRING(20),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_import_transactions',
        'holdId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_import_transactions',
        'warehouseAddress',
        {
          type: Sequelize.STRING(255),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_import_transactions',
        'stationId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_import_transactions',
        'hubId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
    ]);
  },
};