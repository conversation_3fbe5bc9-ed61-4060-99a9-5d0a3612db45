'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'master_analyzers',
        'totalEDA',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalEDANone',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalEDABlue',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalEDAYellow',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalEDARed',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalMEC',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalMECNone',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalMECBlue',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalMECYellow',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'totalMECRed',
        {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'MECs',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'MECRed',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'MECYellow',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'MECNone',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'EDAs',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'EDARed',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'EDAYellow',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'EDANone',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      )
    ]);
  },
};