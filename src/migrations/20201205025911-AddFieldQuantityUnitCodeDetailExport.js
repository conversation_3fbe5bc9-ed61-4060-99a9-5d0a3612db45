'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_export_details',
        'quantity2',
        {
          type: Sequelize.DECIMAL(20, 6),
          allowNull: true,
          defaultValue: 0
        },
        
      ),
      queryInterface.addColumn(
        'clearance_export_details',
        'quantityUnitCode2',
        {
          type: Sequelize.STRING(6),
          allowNull: true
        },
      ),
    ]);
  },
};