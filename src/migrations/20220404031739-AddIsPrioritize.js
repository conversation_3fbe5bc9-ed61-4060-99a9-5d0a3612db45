'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'isPrioritize',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'isPrioritize',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
      ),

      queryInterface.addColumn(
        'clearance_idas',
        'isPrioritize',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'isPrioritize',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
      ),
    ]);
  },
};