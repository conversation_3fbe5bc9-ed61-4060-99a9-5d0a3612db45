'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.changeColumn(
        'clearance_mics',
        'identity',
        {
          type: Sequelize.STRING(1000),
          allowNull : true,
        },
      ),
      queryInterface.changeColumn(
        'clearance_idas',
        'identity',
        {
          type: Sequelize.STRING(1000),
          allowNull : true,
        },
      ),
    ]);
  },
};