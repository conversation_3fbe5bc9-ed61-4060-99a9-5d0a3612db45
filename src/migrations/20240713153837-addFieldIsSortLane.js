'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_edas',
        'isSortLane',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'isSortLane',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'isSortLane',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'isSortLane',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
      ),
    ]);
  },
};