'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_hscode_details', {
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      hsCode: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      name: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      nameVN: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      unitCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      unitName: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      importTaxValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      importTaxCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      VATValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      VATCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      specialConsumptionTaxValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      specialConsumptionTaxCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      environmentTaxPrice: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      environmentTaxCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_hscode_details');
  }
};
