'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'address2',
        {
          type: Sequelize.STRING(60),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'address3',
        {
          type: Sequelize.STRING(60),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'address2',
        {
          type: Sequelize.STRING(60),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'address3',
        {
          type: Sequelize.STRING(60),
          allowNull : true,
        },
      ),
    ]);
  },
};