'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    queryInterface.addColumn(
      'clearance_mics',
      'taxCodeNumber',
      {
        type: Sequelize.STRING(100),
        allowNull : true
      }
    ),
    queryInterface.addColumn(
      'clearance_mics',
      'printDate',
      {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
    ),
    queryInterface.addColumn(
      'clearance_mics',
      'dateTaxCodeNumber',
      {
        type: Sequelize.DATE,
        allowNull : true
      }
    )
  }
};