'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_log_print', {
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      HAWB: {
        type: Sequelize.STRING(30),
        allowNull : false
      },
      employeeId: {
        type: Sequelize.INTEGER,
        allowNull : false
      },
      isInspectionKindPrint: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isClearanceDeclarationPrint: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isClearancedPrint: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      // create information
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
