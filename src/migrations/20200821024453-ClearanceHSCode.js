'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_hscodes', {
      hsCode: {
        type: Sequelize.STRING(50),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      name: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      importTaxValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      importTaxCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      VATValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      VATCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      specialConsumptionTaxValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      specialConsumptionTaxCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      environmentTaxPrice: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      environmentTaxCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_hscodes');
  }
};
