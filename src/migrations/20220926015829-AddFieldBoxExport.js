'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mecs',
        'internalBoxName',
        {
          type: Sequelize.STRING(33),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'externalBoxName',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'internalBoxName',
        {
          type: Sequelize.STRING(33),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'externalBoxName',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
    ]);
  },
};