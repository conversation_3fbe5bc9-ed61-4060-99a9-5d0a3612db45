'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_vat_codes', {
      code: {
        type: Sequelize.STRING(50),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      name: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      percentValue: {
        type: Sequelize.DECIMAL(12, 4),
        allowNull : true
      },
      position: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_vat_codes');
  }
};
