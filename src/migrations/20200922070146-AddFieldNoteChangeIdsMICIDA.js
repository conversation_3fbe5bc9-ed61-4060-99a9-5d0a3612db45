'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'noteChanges',
        {
          type: Sequelize.ARRAY(Sequelize.JSON),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'noteChanges',
        {
          type: Sequelize.ARRAY(Sequelize.JSON),
          allowNull : true,
        },
      ),
    ]);
  },
};