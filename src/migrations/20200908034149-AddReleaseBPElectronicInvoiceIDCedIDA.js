'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'electronicInvoiceReceiptNo',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'releaseBeforePermitRequestReasonCode',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'isIDCed',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
      ),
    ]);
  },
};