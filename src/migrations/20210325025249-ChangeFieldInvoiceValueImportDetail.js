'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.changeColumn(
        'clearance_import_details',
        'invoiceUnitPrice',
        {
          type: Sequelize.DECIMAL(26, 6),
          allowNull : true
        },
      ),
      queryInterface.changeColumn(
        'clearance_import_details',
        'invoiceValue',
        {
          type: Sequelize.DECIMAL(26, 6),
          allowNull : true
        },
      ),
      
    ]);
  },
};