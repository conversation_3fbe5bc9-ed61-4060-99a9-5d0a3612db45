'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('master_analyzers', {
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      MAWB: {
        type: Sequelize.STRING(50),
        allowNull : false
      },
      boxName: {
        type: Sequelize.STRING(255),
        allowNull : false
      },
      employeeId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      totalIDA: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalIDANone: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalIDABlue: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalIDAYellow: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalIDARed: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalMIC: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalMICNone: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalMICBlue: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalMICYellow: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalMICRed: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      delivery: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      isSucess: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      // create information
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
