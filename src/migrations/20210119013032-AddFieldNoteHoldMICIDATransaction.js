'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'noteHold',
        {
          type: Sequelize.TEXT,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'noteHold',
        {
          type: Sequelize.TEXT,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_import_transactions',
        'noteHold',
        {
          type: Sequelize.TEXT,
          allowNull : true,
        },
      ),
    ]);
  },
};