'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'master_analyzers',
        'MICRed',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'MICYellow',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'master_analyzers',
        'MICNone',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      )
    ]);
  },
};