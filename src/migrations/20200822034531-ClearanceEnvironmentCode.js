'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_environment_codes', {
      code: {
        type: Sequelize.STRING(50),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      name: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      unit: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      unitPriceValue: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      position: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_environment_codes');
  }
};
