'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'orderTypeId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
          defaultValue: 0,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'orderTypeId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
          defaultValue: 0,
        },
      ),
    ]);
  },
};