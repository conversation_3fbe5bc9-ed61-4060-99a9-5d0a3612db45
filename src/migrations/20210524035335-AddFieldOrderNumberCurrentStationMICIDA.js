'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'currentStation',
        {
          type: Sequelize.INTEGER,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'orderNumber',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),

      queryInterface.addColumn(
        'clearance_idas',
        'currentStation',
        {
          type: Sequelize.INTEGER,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'orderNumber',
        {
          type: Sequelize.STRING(50),
          allowNull : true
        },
      ),
    ]);
  },
};