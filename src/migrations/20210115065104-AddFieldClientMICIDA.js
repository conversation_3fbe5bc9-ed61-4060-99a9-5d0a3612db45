'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'clientId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'clientId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'clientId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'clientId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      )
    ]);
  },
};