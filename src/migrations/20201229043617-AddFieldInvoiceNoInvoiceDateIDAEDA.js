'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'invoiceNo',
        {
          type: Sequelize.STRING(255),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'invoiceDate',
        {
          type: Sequelize.DATEONLY,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'invoiceNo',
        {
          type: Sequelize.STRING(255),
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'invoiceDate',
        {
          type: Sequelize.DATEONLY,
          allowNull : true,
        },
      ),
    ]);
  },
};