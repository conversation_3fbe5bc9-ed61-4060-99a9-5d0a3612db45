'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mecs',
        'hubId',
        {
          type: Sequelize.INTEGER,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'hubId',
        {
          type: Sequelize.INTEGER,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'hubId',
        {
          type: Sequelize.INTEGER,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mics',
        'hubId',
        {
          type: Sequelize.INTEGER,
          allowNull : true
        },
      ),
    ]);
  },
};