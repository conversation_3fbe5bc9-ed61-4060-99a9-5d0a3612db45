'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_warehouses', {
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      companyId: {
        type: Sequelize.INTEGER,
        allowNull : false
      },
      stationId: {
        type: Sequelize.INTEGER,
        allowNull : false
      },
      code: {
        type: Sequelize.STRING(50),
        allowNull : false
      },
      customsOffice: {
        type: Sequelize.STRING(10),
        allowNull : false
      },
      customsSubSection: {
        type: Sequelize.STRING(8),
        allowNull : false
      },
      agencyCode: {
        type: Sequelize.STRING(10),
        allowNull: true,
      },
      terminalIds: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        allowNull : false
      },
      feePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      isActivated: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      loadingLocationCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      unloadingPortCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      // create information
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_warehouses');
  }
};
