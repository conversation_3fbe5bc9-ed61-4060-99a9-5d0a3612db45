'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_documents', {
      id: { type: Sequelize.INTEGER, primaryKey: true, unique: true, autoIncrement: true, autoIncrementIdentity: true },
      HAWB: { type: Sequelize.STRING },

      documentTypeId: { type: Sequelize.SMALLINT },

      url: { type: Sequelize.STRING },

      sendTimes: { type: Sequelize.SMALLINT, defaultValue: 0 },
      successTimes: { type: Sequelize.SMALLINT, defaultValue: 0 },
      errorTimes: { type: Sequelize.SMALLINT, defaultValue: 0 },

      messageError: { type: Sequelize.STRING },

      statusId: Sequelize.SMALLINT,

      createdAt: {
        type: Sequelize.DATE
      },
      updatedAt: {
        type: Sequelize.DATE
      },
      deletedAt: {
        type: Sequelize.DATE
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
