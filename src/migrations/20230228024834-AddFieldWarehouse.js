'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mecs',
        'warehouseAddress',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'warehouseCheckin',
        {
          type: Sequelize.DATE,
          allowNull: true
        },
      ),
      queryInterface.addColumn(
        'clearance_mecs',
        'warehouseCheckout',
        {
          type: Sequelize.DATE,
          allowNull: true
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'warehouseAddress',
        {
          type: Sequelize.TEXT,
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'warehouseCheckin',
        {
          type: Sequelize.DATE,
          allowNull: true
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'warehouseCheckout',
        {
          type: Sequelize.DATE,
          allowNull: true
        },
      ),
    ]);
  },
};