'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_mecs', {
      HAWB: {
        type: Sequelize.STRING(30),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      MAWB: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      flightNo: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      departureDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      declarationNo: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      stationId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      inspectionKindClassification: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      customsOfficeName: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      customsOffice: {
        type: Sequelize.STRING(8),
        allowNull : true
      },
      customsSubSection: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      declarationPlannedDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      registeredTime: {
        type: Sequelize.TIME,
        allowNull : true
      },
      registeredDateCorrection: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      registeredTimeCorrection: {
        type: Sequelize.TIME,
        allowNull : true
      },
      exporterCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      exporterName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      exporterFullName: {
        type: Sequelize.STRING(255),
        allowNull : true,
      },
      postCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      addressOfExporter: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      telephoneNumber: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      consigneeCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      consigneeName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      consigneeTelephoneNumber: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      postCodeIdentification: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      address1: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      address2: {
        type: Sequelize.STRING(60),
        allowNull : true
      },
      address3: {
        type: Sequelize.STRING(60),
        allowNull : true
      },
      address4: {
        type: Sequelize.STRING(60),
        allowNull : true
      },
      countryCode: {
        type: Sequelize.STRING(5),
        allowNull : true
      },
      agentCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      agentName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      customsBrokerCode: { // Mã nhân viên hải quan
        type: Sequelize.STRING(255),
        allowNull : true
      },
      cargoPiece: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      cargoWeight: {
        type: Sequelize.DECIMAL(12, 3),
        allowNull : true
      },
      customsWarehouseCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      customsWarehouseName: {
        type: Sequelize.STRING(200),
        allowNull : true
      },
      theFinalDestination: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      theFinalDestinationName: {
        type: Sequelize.STRING(200),
        allowNull : true
      },
      loadingPortCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      loadingPortName: {
        type: Sequelize.STRING(200),
        allowNull : true
      },
      currencyCodeOfTaxValue: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      totalOfTaxValue: {
        type: Sequelize.DECIMAL(20, 3),
        allowNull : true
      },
      dateOfPermit: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      timeOfPermit: {
        type: Sequelize.TIME,
        allowNull : true
      },
      nameHeadCustomsOffice: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull : true
      },      
      // create information
      isError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },  
      isECUSError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      messageErrorName: {
        type: Sequelize.TEXT,
        allowNull : true
      }, 
      messageError: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      valueClearanceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      priceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      serviceId: {
        type: Sequelize.INTEGER,
        allowNull : false,
      },
      customerBusinessId: {
        type: Sequelize.UUID,
        allowNull : true,
      },
      customerPersonalId: {
        type: Sequelize.UUID,
        allowNull : true,
      },
      classify: { // phân loại DOCUMENT, PARCEL
        type: Sequelize.STRING(4),
        allowNull : true,
        defaultValue: 'PAR',
      },
      dateCheckin: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateClearanced: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateCheckout: {
        type: Sequelize.DATE,
        allowNull : true
      },
      phase: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      times: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      originalOrderNumberClient: {
        type: Sequelize.STRING(255),
        allowNull : true,
      },
      terminalName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      dateAction: {
        type: Sequelize.DATE,
        allowNull : true,
      },
      typeAction:{
        type: Sequelize.STRING(10),
        allowNull : true
      },
      inspectionKindTimes: {
        type: Sequelize.SMALLINT,
        defaultValue: 0,
      },
      clearanceDeclarationTimes: {
        type: Sequelize.SMALLINT,
        defaultValue: 0,
      },
      clearancedTimes: {
        type: Sequelize.SMALLINT,
        defaultValue: 0,
      },
      //
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_mecs');
  }
};
