'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_import_transactions', {
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      HAWB: {
        type: Sequelize.STRING(30),
        allowNull : false
      },
      detailId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      MAWB: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      declarationNo: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      inspectionKindClassification: { // Phan luong
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      action: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      employeeId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      isError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isECUSError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isMessageTax: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      messageErrorName: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      messageError: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      typeAction: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      classify: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      data: {
        type: Sequelize.JSON,
        allowNull : true
      },
      newData: {
        type: Sequelize.JSON,
        allowNull : true
      },
      // create information
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_import_transactions');
  }
};
