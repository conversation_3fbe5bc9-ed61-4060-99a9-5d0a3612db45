'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mecs',
        'hellmannSuccessDate',
        {
          type: Sequelize.DATE,
          defaultValue: null
        },
      ),
      queryInterface.addColumn(
        'clearance_edas',
        'hellmannSuccessDate',
        {
          type: Sequelize.DATE,
          defaultValue: null
        },
      ),
    ]);
  },
};