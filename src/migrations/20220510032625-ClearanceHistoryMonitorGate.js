'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_history_monitor_gate', {
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      title: {
        type: Sequelize.STRING(50),
        allowNull : false
      },
      employeeId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      printType: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      successDate: {
        type: Sequelize.DATE,
      },
      hubId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      isSucess: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      link: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      // create information
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
