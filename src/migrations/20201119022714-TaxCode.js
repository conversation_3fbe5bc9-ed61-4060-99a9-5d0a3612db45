'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_tax_codes', {
      id: {
        type: Sequelize.INTEGER,
        unique : true,
        primaryKey: true,
        autoIncrement: true
      },
      name_vn: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      code: {
        type: Sequelize.STRING(20),
        allowNull : true
      },
      address: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      name: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_tax_codes');
  }
};
