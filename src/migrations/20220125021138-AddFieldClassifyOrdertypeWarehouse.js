'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_warehouses',
        'orderTypeId',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
          defaultValue: 0,
        },
      ),
      queryInterface.addColumn(
        'clearance_warehouses',
        'kindleCode',
        {
          type: Sequelize.STRING(10),
          allowNull : true
        },
      )
    ]);
  },
};