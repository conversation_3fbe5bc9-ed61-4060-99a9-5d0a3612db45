'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('clearance_idas', {
      //header
      HAWB: {
        type: Sequelize.STRING(30),
        unique : true,
        primaryKey: true,
        allowNull : true
      },
      MAWB: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      declarationNo: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      stationId: {
        type: Sequelize.INTEGER,
        allowNull : true
      },
      //
      declarationKindCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      firstDeclarationNo: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      cargoClassificationCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      meansOfTransportationCode: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      classificationOfIndividualOrganization: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      inspectionKindClassification: { // Phan luong
        type: Sequelize.STRING(3),
        allowNull : true
      },
      representativeTaxCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      customsOffice: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      customsOfficeName: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      customsSubSection: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      
      declarationPlannedDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      registeredTime: {
        type: Sequelize.TIME,
        allowNull : true
      },
      registeredDateOfCorrection: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      registeredTimeOfCorrection: {
        type: Sequelize.TIME,
        allowNull : true
      },
      importerCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      importerName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      postCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      addressOfImporter: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      telephoneNumberOfImporter: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      importContractorCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      importContractorName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      consignorCode: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      consignorName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      postCodeIdentification: {
        type: Sequelize.STRING(15),
        allowNull : true
      },
      address1: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      address4: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      countryCode: {
        type: Sequelize.STRING(5),
        allowNull : true
      },
      exportConsignerName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      plannedDeclarantCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      agentCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      agentName: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      customsBrokerCode: { // Mã nhân viên hải quan
        type: Sequelize.STRING(255),
        allowNull : true
      },
      nameHeadCustoms: {
        type: Sequelize.STRING(255),
        allowNull : true
      },
      cargoNo: {
        type: Sequelize.STRING(30),
        allowNull : true
      },
      cargoPiece: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      pieceUnitCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      cargoWeightGross: {
        type: Sequelize.DECIMAL(12, 4),
        allowNull : true
      },
      weightUnitCodeGross: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      customsWarehouseCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      customsClearanceWarehouseName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      loadingVesselAircraftName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      arrivalDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      unloadingPortCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      unloadingPortName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      loadingLocationCode: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      loadingLocationName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      invoiceClassificationCode: {
        type: Sequelize.STRING(2),
        allowNull : true
      },

      // invoice price
      invoicePriceKindCode: { // phân loại giá hóa đơn
        type: Sequelize.STRING(2),
        allowNull : true
      },
      invoicePriceConditionCode: {
        type: Sequelize.STRING(6),
        allowNull : true
      },
      invoiceCurrencyCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      totalInvoicePrice: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      valuationDeclarationDemarcationCode:{
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalOfTaxValue: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      totalOfProportional: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      totalBasicPrice: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      totalOfProportionalDistributionOnTaxValue: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true
      },
      taxPayer: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      codeOfExtendingDueDateForPayment: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      paymentClassification: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      structure: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      numberOfDeclarationColumn: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      //bao hiểm
      freightDemarcationCode: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      freightCurrencyCode: {
        type: Sequelize.STRING(4),
        allowNull : true
      },
      freight: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      freightExchangeRate: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      insuranceDemarcationCode: {
        type: Sequelize.STRING(2),
        allowNull : true
      },
      managementNumberUser: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      // end bao hiểm
      dateOfPermit: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      timeOfPermit: {
        type: Sequelize.TIME,
        allowNull : true
      },
      dateCompletionInspection: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      timeCompletionInspection: {
        type: Sequelize.TIME,
        allowNull : true
      },
      isError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isECUSError: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      messageErrorName: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      messageError: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      isMessageTax: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      feeClearance: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      // notes
      notes: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      paymentStatementNo: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      numberOfDeferrableDays: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      deferrableDurationStartDate: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      treasuryAccountNo: {
        type: Sequelize.STRING(10),
        allowNull : true
      },
      treasuryName: {
        type: Sequelize.STRING(100),
        allowNull : true
      },
      arrearagesRate: {
        type: Sequelize.TEXT,
        allowNull : true
      },
      dateOfIssuedStatement: {
        type: Sequelize.DATEONLY,
        allowNull : true
      },
      subjectImportCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      numberImport: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalImportTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      subjectVATCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      numberVAT: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalVATTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      subjectEnvironmentCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      numberEnvironment: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalEnvironmentTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      subjectSpecialConsumptionCode: {
        type: Sequelize.STRING(3),
        allowNull : true
      },
      numberSpecialConsumption: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      totalSpecialConsumptionTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      totalTax: {
        type: Sequelize.DECIMAL(20, 4),
        allowNull : true,
        defaultValue: 0
      },
      termOfPayment: {
        type: Sequelize.STRING(50),
        allowNull : true
      },
      // create information
      valueClearanceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      priceVND: { // giá trị việt nam đồng thông quan
        type: Sequelize.DECIMAL(12, 2),
        allowNull : true
      },
      serviceId: {
        type: Sequelize.INTEGER,
        allowNull : false,
      },
      customerBusinessId: {
        type: Sequelize.UUID,
        allowNull : true,
      },
      customerPersonalId: {
        type: Sequelize.UUID,
        allowNull : true,
      },
      classify: { // phân loại DOCUMENT, PARCEL
        type: Sequelize.STRING(4),
        allowNull : true,
        defaultValue: 'PAR',
      },
      dateCustomClearance: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateCheckin: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateClearanced: {
        type: Sequelize.DATE,
        allowNull : true
      },
      dateCheckout: {
        type: Sequelize.DATE,
        allowNull : true
      },
      phase: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      times: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      tempTimes: {
        type: Sequelize.SMALLINT,
        allowNull : true
      },
      createdAt: {
        type: Sequelize.DATE,
      },
      updatedAt: {
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('clearance_idas');
  }
};
