'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_idas',
        'otherLawCode',
        {
          type: Sequelize.STRING(10),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'otherLawCode2',
        {
          type: Sequelize.STRING(10),
          allowNull : true
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'otherLawCode3',
        {
          type: Sequelize.STRING(10),
          allowNull : true
        },
      ),
    ]);
  },
};