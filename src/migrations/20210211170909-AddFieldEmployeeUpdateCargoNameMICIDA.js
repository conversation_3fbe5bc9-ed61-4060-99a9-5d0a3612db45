'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.addColumn(
        'clearance_mics',
        'employeeUpdateCargoName',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
      queryInterface.addColumn(
        'clearance_idas',
        'employeeUpdateCargoName',
        {
          type: Sequelize.INTEGER,
          allowNull : true,
        },
      ),
    ]);
  },
};