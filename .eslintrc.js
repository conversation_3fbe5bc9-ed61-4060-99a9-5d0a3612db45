module.exports = {
  env: {
    node: true,
    es6: true,
  },
  extends: [
    'airbnb-base',
  ],
  globals: {
    Atomics: 'readonly',
    Shared<PERSON><PERSON>y<PERSON>uffer: 'readonly',
    use: true
  },
  parserOptions: {
    ecmaVersion: 2018
  },
  rules: {
    "strict": "off",
    "no-console": "off",
    "max-len": ["error", { "code": 150 }],
    "camelcase": "off",
    "no-loop-func": "error",
    "space-before-function-paren": ["error", { "anonymous": "never", "named": "always" }],
    "class-methods-use-this": "off",
    "global-require": "off",
    "arrow-parens": ["error", "as-needed"],
    "no-param-reassign": ["error", { "props": false }],
    "object-curly-newline": ["error", {
      "ObjectPattern": { "multiline": true },
      "ImportDeclaration": { "minProperties": 3, "consistent": false, "multiline": true },
      "ExportDeclaration": { "multiline": true, "minProperties": 3 }
    }],
    "no-use-before-define": ["error", { "functions": false, "classes": true }],
    "prefer-const": ["error", {
      "destructuring": "all",
      "ignoreReadBeforeAssign": false
    }],
    "no-useless-constructor": "error"
  },
};
