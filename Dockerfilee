FROM registry.globex.vn/node:16-slim

### TIMEZONE
### CREATE DIRECTORY FOR THE CONTAINER
WORKDIR /src
COPY package*.json /src/
COPY yarn.lock /src/
ENV OPENSSL_CONF=/dev/null
# RUN touch .env
RUN yarn add ts-node -g
RUN npm i sequelize-cli && apt update && apt install -y fontconfig curl wget bzip2 libssl1.1 libssl-dev  && npm install -g html-pdf
# RUN   wget -c https://bitbucket.org/ariya/phantomjs/downloads/phantomjs-2.1.1-linux-x86_64.tar.bz2 -O - | tar -jxf - && \
#      cp phantomjs-2.1.1-linux-x86_64/bin/phantomjs /usr/local/bin/phantomjs

# COPY --from=tms /usr/local/bin/phantomjs /usr/local/bin/phantomjs

COPY package*.json /src/
COPY yarn.lock /src/
 
# INSTALL ALL PACKAGES
RUN yarn install

# Copy all other source code to work directory
COPY . /src

ADD localtime /etc/localtime

RUN yarn build

# RUN mkdir /src/seeders
ADD entry /entry
RUN chmod +x /entry

ENTRYPOINT ["/entry"]

#EXpose port
EXPOSE 3000
